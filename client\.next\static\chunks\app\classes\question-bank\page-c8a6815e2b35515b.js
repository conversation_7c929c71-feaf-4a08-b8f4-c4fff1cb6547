(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2839],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(95155);s(12115);var l=s(6874),r=s.n(l),i=s(66766),n=s(29911);let o=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(r(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:l}=e;return(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(r(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:l,children:(0,a.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},l)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(r(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(r(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(r(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>h,l6:()=>c,yv:()=>d});var a=s(95155);s(12115);var l=s(59824),r=s(66474),i=s(5196),n=s(47863),o=s(59434);function c(e){let{...t}=e;return(0,a.jsx)(l.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,a.jsx)(l.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:i,...n}=e;return(0,a.jsxs)(l.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[i,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:s,position:r="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(p,{})]})})}function m(e){let{className:t,children:s,...r}=e;return(0,a.jsxs)(l.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(l.p4,{children:s})]})}function x(e){let{className:t,...s}=e;return(0,a.jsx)(l.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(n.A,{className:"size-4"})})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(l.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(r.A,{className:"size-4"})})}},59794:(e,t,s)=>{Promise.resolve().then(s.bind(s,84230))},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>r});var a=s(95155);s(12115);var l=s(59434);function r(e){let{className:t,type:s,...r}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},62525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var a=s(12115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=a.createContext&&a.createContext(l),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(this,arguments)}function o(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,a)}return s}function c(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?o(Object(s),!0).forEach(function(t){var a,l,r;a=e,l=t,r=s[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(l))in a?Object.defineProperty(a,l,{value:r,enumerable:!0,configurable:!0,writable:!0}):a[l]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>a.createElement(u,n({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,s)=>a.createElement(t.tag,c({key:s},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var s,{attr:l,size:r,title:o}=e,d=function(e,t){if(null==e)return{};var s,a,l=function(e,t){if(null==e)return{};var s={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)s=r[a],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(l[s]=e[s])}return l}(e,i),u=r||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),a.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,l,d,{className:s,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&a.createElement("title",null,o),e.children)};return void 0!==r?a.createElement(r.Consumer,null,e=>t(e)):t(l)}},84230:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>F});var a=s(95155),l=s(12115),r=s(62177),i=s(90221),n=s(55594),o=s(85127),c=s(36268),d=s(11032),u=s(30285),h=s(54165),m=s(59409),x=s(62523),p=s(90010),j=s(89917),v=s(62525),b=s(42355),g=s(13052),f=s(56671),w=s(55077);let y=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{let a=new URLSearchParams({page:e.toString(),limit:t.toString(),...s.medium&&{medium:s.medium},...s.standard&&{standard:s.standard},...s.level&&{level:s.level},...s.subject&&{subject:s.subject}}).toString(),l=await w.S.get("/questionBank?".concat(a),{headers:{"Server-Select":"uwhizServer"}});return console.log("Get Question Bank Response:",l.data),{success:!0,data:l.data}}catch(e){var a,l;return{success:!1,error:"Failed to fetch question bank: ".concat((null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)||e.message)}}},N=async()=>{try{let e=await w.S.get("/constant/Subject");return{success:!0,data:e.data}}catch(s){var e,t;return{success:!1,error:"Failed to fetch subject data: ".concat((null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||s.message)}}},S=async()=>{try{let e=await w.S.get("/constant/classroom");return{success:!0,data:e.data}}catch(s){var e,t;return{success:!1,error:"Failed to fetch classroom Data: ".concat((null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||s.message)}}},O=async e=>{try{let t=await w.S.post("/questionBank",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,s;return{success:!1,error:"Failed to create question: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}},k=async(e,t)=>{try{let s=await w.S.put("/questionBank/".concat(e),t,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){var s,a;return{success:!1,error:"Failed to create question: ".concat((null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||e.message)}}},C=async e=>{try{let t=await w.S.delete("/questionBank/".concat(e),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,s;return{success:!1,error:"Failed to delete question: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}};var A=s(70347),q=s(7583);let P=n.Ik({question:n.Yj().min(1,{message:"Question is required"}).max(500,{message:"Question cannot exceed 500 characters"}),optionOne:n.Yj().min(1,{message:"Option 1 is required"}).max(100,{message:"Option 1 cannot exceed 100 characters"}),optionTwo:n.Yj().min(1,{message:"Option 2 is required"}).max(100,{message:"Option 2 cannot exceed 100 characters"}),optionThree:n.Yj().min(1,{message:"Option 3 is required"}).max(100,{message:"Option 3 cannot exceed 100 characters"}),optionFour:n.Yj().min(1,{message:"Option 4 is required"}).max(100,{message:"Option 4 cannot exceed 100 characters"}),correctAnswer:n.k5(["optionOne","optionTwo","optionThree","optionFour"],{errorMap:()=>({message:"Please select a correct answer"})}),chapter:n.k5(["Polynomial","Statics","Probability"],{errorMap:()=>({message:"Please select a chapter"})}).optional()}),E={question:"",optionOne:"",optionTwo:"",optionThree:"",optionFour:"",correctAnswer:"optionOne",chapter:void 0};function F(){let[e,t]=(0,l.useState)([]),[s,n]=(0,l.useState)(!0),[w,F]=(0,l.useState)(!1),[z,T]=(0,l.useState)(null),[L,_]=(0,l.useState)([]),[M,I]=(0,l.useState)([]),[D,H]=(0,l.useState)(!0),[Q,K]=(0,l.useState)(null),[V,Y]=(0,l.useState)(null),[$,R]=(0,l.useState)(1),[G,U]=(0,l.useState)(1),[B,W]=(0,l.useState)(0),[Z,J]=(0,l.useState)(10),[X,ee]=(0,l.useState)(!1),[et,es]=(0,l.useState)("ENGLISH"),[ea,el]=(0,l.useState)("EASY"),[er,ei]=(0,l.useState)(""),[en,eo]=(0,l.useState)(""),[ec,ed]=(0,l.useState)(null),[eu,eh]=(0,l.useState)(!1),{register:em,handleSubmit:ex,reset:ep,control:ej,formState:{errors:ev}}=(0,r.mN)({resolver:(0,i.u)(P),defaultValues:E});(0,l.useEffect)(()=>{{let e=JSON.parse(localStorage.getItem("user")||"{}");ed((null==e?void 0:e.id)||null)}},[]);let eb=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];n(!0);let a=await y(e,Z,s?{medium:et,standard:er||void 0,level:ea,subject:en||void 0}:{});a.success&&a.data?(t(a.data.data),U(a.data.pagination.totalPages),W(a.data.pagination.totalQuestions),R(e)):(t([]),U(1),W(0),f.toast.error(a.error||"Failed to fetch questions")),n(!1)},eg=async()=>{H(!0),K(null);try{let e=await N();if(e.success&&e.data){let t=e.data.details.map(e=>e.value).filter(e=>e&&""!==e.trim());_(t),t.length>0?eo(t[0]):eo("")}else K(e.error||"Failed to fetch subjects"),eo("");let t=await S();if(t.success&&t.data){let e=t.data.details.map(e=>e.value).filter(e=>e&&""!==e.trim());I(e),e.length>0?ei(e[0]):ei("")}else K((Q?Q+"; ":"")+(t.error||"Failed to fetch classrooms")),ei("")}catch(e){K("Error fetching constants"),console.error("Error fetching constants:",e),ei(""),eo("")}finally{H(!1)}};(0,l.useEffect)(()=>{eg()},[]),(0,l.useEffect)(()=>{eb($,eu)},[$,Z,eu]);let ef=async e=>{let t;if(ee(!0),Y(null),!er||!en){Y("Please select a valid standard and subject."),f.toast.error("Please select a valid standard and subject."),ee(!1);return}if(!ec){Y("Class ID is not available. Please ensure you are logged in."),f.toast.error("Class ID is not available. Please ensure you are logged in."),ee(!1);return}let s={...e,medium:et,level:ea,standard:er,subject:en,classID:ec},a=e.chapter?{...s,chapter:e.chapter}:s;(t=z?await k(z.id.toString(),a):await O(a)).success?(eb($,eu),F(!1),ep(E),T(null),f.toast.success(z?"Question updated successfully!":"Question created successfully!")):(Y(t.error||"Failed to save question"),f.toast.error(t.error||"Failed to save question")),ee(!1)},ew=e=>{T(e);let{question:t,optionOne:s,optionTwo:a,optionThree:l,optionFour:r,correctAnswer:i,chapter:n}=e;ep({question:t,optionOne:s,optionTwo:a,optionThree:l,optionFour:r,correctAnswer:i,chapter:n||void 0}),F(!0)},ey=async e=>{let t=await C(e.toString());t.success?(eb($,eu),f.toast.success("Question deleted successfully!")):f.toast.error(t.error||"Failed to delete question")},eN=e=>{F(e),e||(ep(E),T(null),Y(null))},eS=[{accessorKey:"question",header:"Question"},{accessorKey:"optionOne",header:"Option 1"},{accessorKey:"optionTwo",header:"Option 2"},{accessorKey:"optionThree",header:"Option 3"},{accessorKey:"optionFour",header:"Option 4"},{accessorKey:"correctAnswer",header:"Correct Answer"},{accessorKey:"medium",header:"Medium"},{accessorKey:"standard",header:"Standard"},{accessorKey:"subject",header:"Subject"},{accessorKey:"level",header:"Level"},{accessorKey:"chapter",header:"Chapter"},{id:"actions",header:"Actions",cell:e=>{let{row:t}=e;return(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>ew(t.original),"aria-label":"Edit question",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})}),(0,a.jsxs)(p.Lt,{children:[(0,a.jsx)(p.tv,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"ghost",size:"sm","aria-label":"Delete question",children:(0,a.jsx)(v.A,{className:"h-4 w-4 text-red-500"})})}),(0,a.jsxs)(p.EO,{children:[(0,a.jsxs)(p.wd,{children:[(0,a.jsx)(p.r7,{children:"Are you sure?"}),(0,a.jsx)(p.$v,{children:"This action cannot be undone. This will permanently delete the question."})]}),(0,a.jsxs)(p.ck,{children:[(0,a.jsx)(p.Zr,{children:"Cancel"}),(0,a.jsx)(p.Rx,{onClick:()=>ey(t.original.id),children:"Delete"})]})]})]})]})}}],eO=(0,c.N4)({data:e,columns:eS,getCoreRowModel:(0,d.HT)()});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.default,{}),(0,a.jsxs)("div",{className:"p-5 pb-10 px-10",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Question Bank"}),(0,a.jsxs)(h.lG,{open:w,onOpenChange:eN,children:[(0,a.jsx)(h.zM,{asChild:!0,children:(0,a.jsx)(u.$,{onClick:()=>{T(null),ep(E),F(!0)},"aria-label":"Add new question",children:"Add Question"})}),(0,a.jsxs)(h.Cf,{children:[(0,a.jsx)(h.c7,{children:(0,a.jsx)(h.L3,{children:z?"Update Question":"Create Question"})}),Q?(0,a.jsxs)("p",{className:"text-red-500 text-sm",children:["Cannot load form due to missing data: ",Q]}):(0,a.jsxs)(a.Fragment,{children:[V&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:V}),(0,a.jsxs)("form",{onSubmit:ex(ef),className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Question"}),(0,a.jsx)(x.p,{...em("question"),placeholder:"Enter question",disabled:D||X,className:"w-full","aria-label":"Question input"}),ev.question&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:ev.question.message})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Option 1"}),(0,a.jsx)(x.p,{...em("optionOne"),placeholder:"Enter option 1",disabled:D||X,"aria-label":"Option 1 input"}),ev.optionOne&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:ev.optionOne.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Option 2"}),(0,a.jsx)(x.p,{...em("optionTwo"),placeholder:"Enter option 2",disabled:D||X,"aria-label":"Option 2 input"}),ev.optionTwo&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:ev.optionTwo.message})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Option 3"}),(0,a.jsx)(x.p,{...em("optionThree"),placeholder:"Enter option 3",disabled:D||X,"aria-label":"Option 3 input"}),ev.optionThree&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:ev.optionThree.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Option 4"}),(0,a.jsx)(x.p,{...em("optionFour"),placeholder:"Enter option 4",disabled:D||X,"aria-label":"Option 4 input"}),ev.optionFour&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:ev.optionFour.message})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Correct Answer"}),(0,a.jsx)(r.xI,{name:"correctAnswer",control:ej,render:e=>{let{field:t}=e;return(0,a.jsxs)(m.l6,{onValueChange:t.onChange,value:t.value,disabled:D||X,children:[(0,a.jsx)(m.bq,{"aria-label":"Correct answer selector",children:(0,a.jsx)(m.yv,{placeholder:"Select correct answer"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"optionOne",children:"Option 1"}),(0,a.jsx)(m.eb,{value:"optionTwo",children:"Option 2"}),(0,a.jsx)(m.eb,{value:"optionThree",children:"Option 3"}),(0,a.jsx)(m.eb,{value:"optionFour",children:"Option 4"})]})]})}}),ev.correctAnswer&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:ev.correctAnswer.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Chapter"}),(0,a.jsx)(r.xI,{name:"chapter",control:ej,render:e=>{let{field:t}=e;return(0,a.jsxs)(m.l6,{onValueChange:t.onChange,value:t.value||"",disabled:D||X,children:[(0,a.jsx)(m.bq,{"aria-label":"Chapter selector",children:(0,a.jsx)(m.yv,{placeholder:"Select chapter"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"Polynomial",children:"Polynomial"}),(0,a.jsx)(m.eb,{value:"Statics",children:"Statics"}),(0,a.jsx)(m.eb,{value:"Probability",children:"Probability"})]})]})}}),ev.chapter&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:ev.chapter.message})]})]}),(0,a.jsxs)(h.Es,{children:[(0,a.jsx)(u.$,{variant:"outline",onClick:()=>eN(!1),disabled:X,children:"Cancel"}),(0,a.jsx)(u.$,{type:"submit",disabled:D||X,children:X?"Saving...":"Save"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-5 mb-4 w-full",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-5 w-full",children:[(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Medium"}),(0,a.jsxs)(m.l6,{onValueChange:e=>es(e),value:et,disabled:D,children:[(0,a.jsx)(m.bq,{"aria-label":"Medium selector",className:"w-full",children:(0,a.jsx)(m.yv,{placeholder:"Select medium"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"ENGLISH",children:"English"}),(0,a.jsx)(m.eb,{value:"GUJARATI",children:"Gujarati"})]})]})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Level"}),(0,a.jsxs)(m.l6,{onValueChange:e=>el(e),value:ea,disabled:D,children:[(0,a.jsx)(m.bq,{"aria-label":"Level selector",className:"w-full",children:(0,a.jsx)(m.yv,{placeholder:"Select level"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"EASY",children:"Easy"}),(0,a.jsx)(m.eb,{value:"MEDIUM",children:"Medium"}),(0,a.jsx)(m.eb,{value:"HARD",children:"Hard"})]})]})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Standard"}),(0,a.jsxs)(m.l6,{onValueChange:e=>ei(e),value:er,disabled:D||0===M.length,children:[(0,a.jsx)(m.bq,{"aria-label":"Standard selector",className:"w-full",children:(0,a.jsx)(m.yv,{placeholder:D?"Loading standards...":0===M.length?"No standards available":"Select standard"})}),(0,a.jsx)(m.gC,{children:M.length>0?M.map(e=>(0,a.jsx)(m.eb,{value:e,children:e},e)):(0,a.jsx)(m.eb,{value:"no-standards",disabled:!0,children:"No standards available"})})]})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{className:"block text-sm font-medium",children:"Subject"}),(0,a.jsxs)(m.l6,{onValueChange:e=>eo(e),value:en,disabled:D||0===L.length,children:[(0,a.jsx)(m.bq,{"aria-label":"Subject selector",className:"w-full",children:(0,a.jsx)(m.yv,{placeholder:D?"Loading subjects...":0===L.length?"No subjects available":"Select subject"})}),(0,a.jsx)(m.gC,{children:L.length>0?L.map(e=>(0,a.jsx)(m.eb,{value:e,children:e},e)):(0,a.jsx)(m.eb,{value:"no-subjects",disabled:!0,children:"No subjects available"})})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-5 justify-start",children:[(0,a.jsx)(u.$,{onClick:()=>{R(1),eh(!0),eb(1,!0)},disabled:D||s,children:"Search"}),(0,a.jsx)(u.$,{variant:"outline",onClick:()=>{es("ENGLISH"),el("EASY"),ei(M[0]||""),eo(L[0]||""),R(1),eh(!1),eb(1,!1)},disabled:D||s,children:"Reset"})]})]}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(o.XI,{children:[(0,a.jsx)(o.A0,{children:eO.getHeaderGroups().map(e=>(0,a.jsx)(o.Hj,{children:e.headers.map(e=>(0,a.jsx)(o.nd,{children:(0,c.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(o.BF,{children:s?(0,a.jsx)(o.Hj,{children:(0,a.jsx)(o.nA,{colSpan:eS.length,className:"text-center",children:"Loading..."})}):eO.getRowModel().rows.length>0?eO.getRowModel().rows.map(e=>(0,a.jsx)(o.Hj,{children:e.getVisibleCells().map(e=>(0,a.jsx)(o.nA,{children:(0,c.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(o.Hj,{children:(0,a.jsx)(o.nA,{colSpan:eS.length,className:"text-center",children:"No data available"})})})]})}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[B," entries"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(m.l6,{value:Z.toString(),onValueChange:e=>{J(Number(e)),R(1)},children:[(0,a.jsx)(m.bq,{children:(0,a.jsx)(m.yv,{})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"10",children:"10"}),(0,a.jsx)(m.eb,{value:"20",children:"20"}),(0,a.jsx)(m.eb,{value:"50",children:"50"})]})]}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>{$>1&&R($-1)},disabled:1===$,"aria-label":"Previous page",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"text-sm",children:["Page ",$," of ",G]}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>{$<G&&R($+1)},disabled:$===G,"aria-label":"Next page",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(q.default,{})]})}},85127:(e,t,s)=>{"use strict";s.d(t,{A0:()=>i,BF:()=>n,Hj:()=>o,XI:()=>r,nA:()=>d,nd:()=>c});var a=s(95155);s(12115);var l=s(59434);function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,l.cn)("w-full caption-bottom text-sm",t),...s})})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,l.cn)("[&_tr]:border-b",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,l.cn)("[&_tr:last-child]:border-0",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,l.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,l.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,l.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}},89917:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,1342,4520,8273,347,8441,1684,7358],()=>t(59794)),_N_E=e.O()}]);