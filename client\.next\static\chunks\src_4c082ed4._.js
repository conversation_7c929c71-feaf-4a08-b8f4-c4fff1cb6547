(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_4c082ed4._.js", {

"[project]/src/store/slices/userSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearUser": (()=>clearUser),
    "default": (()=>__TURBOPACK__default__export__),
    "setUser": (()=>setUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const storedUser = ("TURBOPACK compile-time truthy", 1) ? localStorage.getItem('user') : ("TURBOPACK unreachable", undefined);
const initialState = {
    user: storedUser ? JSON.parse(storedUser) : null,
    isAuthenticated: !!storedUser
};
const userSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'user',
    initialState,
    reducers: {
        setUser: (state, action)=>{
            state.user = action.payload.user;
            state.isAuthenticated = true;
            localStorage.setItem('user', JSON.stringify(action.payload.user));
        },
        clearUser: (state)=>{
            state.user = null;
            state.isAuthenticated = false;
            localStorage.removeItem('user');
        }
    }
});
const { setUser, clearUser } = userSlice.actions;
const __TURBOPACK__default__export__ = userSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/formProgressSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "FormId": (()=>FormId),
    "completeForm": (()=>completeForm),
    "default": (()=>__TURBOPACK__default__export__),
    "setCurrentStep": (()=>setCurrentStep)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
var FormId = /*#__PURE__*/ function(FormId) {
    FormId["PROFILE"] = "about";
    FormId["DESCRIPTION"] = "description";
    FormId["PHOTO_LOGO"] = "photo_logo";
    FormId["EDUCATION"] = "education";
    FormId["EXPERIENCE"] = "experience";
    FormId["CERTIFICATES"] = "certificates";
    FormId["TUTIONCLASS"] = "tution_class";
    FormId["ADDRESS"] = "address";
    return FormId;
}({});
const initialState = {
    completedSteps: 0,
    totalSteps: 8,
    currentStep: 1,
    completedForms: {
        ["about"]: false,
        ["description"]: false,
        ["photo_logo"]: false,
        ["education"]: false,
        ["certificates"]: false,
        ["experience"]: false,
        ["tution_class"]: false,
        ["address"]: false
    }
};
const formProgressSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'formProgress',
    initialState,
    reducers: {
        completeForm: (state, action)=>{
            const formId = action.payload;
            if (!state.completedForms[formId]) {
                state.completedForms[formId] = true;
                state.completedSteps = Math.min(state.completedSteps + 1, state.totalSteps);
            }
        },
        setCurrentStep: (state, action)=>{
            state.currentStep = action.payload;
        }
    }
});
const { completeForm, setCurrentStep } = formProgressSlice.actions;
const __TURBOPACK__default__export__ = formProgressSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/axios.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosInstance": (()=>axiosInstance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
;
const baseURL = ("TURBOPACK compile-time value", "http://localhost:4005/api/v1") || 'http://localhost:4005/api/v1';
const baseURL2 = ("TURBOPACK compile-time value", "http://localhost:4006") || 'http://localhost:4006';
console.log('Axios baseURL:', baseURL);
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL,
    headers: {
        'Content-Type': 'application/json'
    },
    withCredentials: true
});
axiosInstance.interceptors.request.use((config)=>{
    const serverSelect = config.headers['Server-Select'];
    config.baseURL = serverSelect === 'uwhizServer' ? baseURL2 : baseURL;
    const studentToken = localStorage.getItem('studentToken');
    if (studentToken) {
        config.headers.Authorization = `Bearer ${studentToken}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
axiosInstance.interceptors.response.use((response)=>response, (error)=>{
    if (error.response && error.response.status === 401) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.response.data.message || 'Unauthorized');
        localStorage.removeItem('user');
        localStorage.removeItem('studentToken');
        localStorage.removeItem('student_data');
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.replace('/?authError=1');
        }
    }
    return Promise.reject(error);
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/thunks/classThunks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchClassDetails": (()=>fetchClassDetails)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
;
const fetchClassDetails = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('class/fetchClassDetails', async (userId, { rejectWithValue })=>{
    try {
        const res = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosInstance"].get(`/classes/details/${userId}`);
        return res.data;
    } catch (err) {
        return rejectWithValue(err.response?.data || 'Fetch failed');
    }
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/classSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "setClassData": (()=>setClassData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$classThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/thunks/classThunks.ts [app-client] (ecmascript)");
;
;
const initialState = {
    classData: null,
    loading: false,
    error: null
};
const classSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'class',
    initialState,
    reducers: {
        setClassData (state, action) {
            state.classData = action.payload;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$classThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchClassDetails"].pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$classThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchClassDetails"].fulfilled, (state, action)=>{
            state.loading = false;
            state.classData = action.payload;
        }).addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$classThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchClassDetails"].rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const { setClassData } = classSlice.actions;
const __TURBOPACK__default__export__ = classSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/thunks/studentProfileThunks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchStudentProfile": (()=>fetchStudentProfile),
    "updateStudentProfile": (()=>updateStudentProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
;
const fetchStudentProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('studentProfile/fetchStudentProfile', async (_, { rejectWithValue })=>{
    try {
        const studentToken = localStorage.getItem('studentToken');
        if (!studentToken) {
            return rejectWithValue('No authentication token found');
        }
        const res = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosInstance"].get('/student-profile/all-data', {
            headers: {
                'Authorization': `Bearer ${studentToken}`
            }
        });
        if (res.data && typeof res.data === 'object') {
            // Check if the response has a data property (API wrapper format)
            if (res.data.success !== undefined && res.data.data !== undefined) {
                return res.data.data;
            }
            return res.data;
        }
        return null;
    } catch (err) {
        if (err.response?.status === 404) {
            return null;
        }
        const errorMessage = err.response?.data?.message || 'Failed to fetch student data';
        return rejectWithValue(errorMessage);
    }
});
const updateStudentProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('studentProfile/updateStudentProfile', async (jsonData, { rejectWithValue })=>{
    try {
        const studentToken = localStorage.getItem('studentToken');
        if (!studentToken) {
            return rejectWithValue('No authentication token found');
        }
        const url = '/student-profile/combined';
        const method = 'put';
        const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosInstance"])({
            method,
            url,
            data: jsonData,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${studentToken}`
            }
        });
        if (res.data && typeof res.data === 'object') {
            // Check if the response has a data property (API wrapper format)
            if (res.data.success !== undefined && res.data.data !== undefined) {
                return res.data.data;
            }
            return res.data;
        }
        return null;
    } catch (err) {
        const errorMsg = err.response?.data?.message || 'Failed to update student profile';
        return rejectWithValue(errorMsg);
    }
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/studentProfileSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearStudentProfileData": (()=>clearStudentProfileData),
    "default": (()=>__TURBOPACK__default__export__),
    "setStudentProfileData": (()=>setStudentProfileData),
    "updateProfilePhoto": (()=>updateProfilePhoto)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$studentProfileThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/thunks/studentProfileThunks.ts [app-client] (ecmascript)");
;
;
const initialState = {
    profileData: null,
    loading: false,
    error: null
};
const studentProfileSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'studentProfile',
    initialState,
    reducers: {
        setStudentProfileData (state, action) {
            state.profileData = action.payload;
            // Persist to localStorage for photo data
            if ("object" !== 'undefined' && action.payload?.profile?.photo) {
                try {
                    localStorage.setItem('student_profile_photo', action.payload.profile.photo);
                } catch (error) {
                    console.error('Failed to persist photo to localStorage:', error);
                }
            }
        },
        updateProfilePhoto (state, action) {
            if (state.profileData?.profile) {
                state.profileData.profile.photo = action.payload;
                // Persist to localStorage
                if ("TURBOPACK compile-time truthy", 1) {
                    try {
                        if (action.payload) {
                            localStorage.setItem('student_profile_photo', action.payload);
                        } else {
                            localStorage.removeItem('student_profile_photo');
                        }
                    } catch (error) {
                        console.error('Failed to persist photo to localStorage:', error);
                    }
                }
            }
        },
        clearStudentProfileData (state) {
            state.profileData = null;
            state.loading = false;
            state.error = null;
            // Clear persisted photo
            if ("TURBOPACK compile-time truthy", 1) {
                try {
                    localStorage.removeItem('student_profile_photo');
                } catch (error) {
                    console.error('Failed to clear photo from localStorage:', error);
                }
            }
        }
    },
    extraReducers: (builder)=>{
        builder// Fetch student profile
        .addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$studentProfileThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchStudentProfile"].pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$studentProfileThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchStudentProfile"].fulfilled, (state, action)=>{
            state.loading = false;
            // Ensure we're handling the payload correctly
            if (action.payload) {
                state.profileData = action.payload;
            }
        }).addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$studentProfileThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchStudentProfile"].rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        })// Update student profile
        .addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$studentProfileThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateStudentProfile"].pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$studentProfileThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateStudentProfile"].fulfilled, (state, action)=>{
            state.loading = false;
            // Ensure we're handling the payload correctly
            if (action.payload) {
                state.profileData = action.payload;
            }
        }).addCase(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$thunks$2f$studentProfileThunks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateStudentProfile"].rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const { setStudentProfileData, updateProfilePhoto, clearStudentProfileData } = studentProfileSlice.actions;
const __TURBOPACK__default__export__ = studentProfileSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "store": (()=>store)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$userSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/userSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/formProgressSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$classSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/classSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$studentProfileSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/studentProfileSlice.ts [app-client] (ecmascript)");
;
;
;
;
;
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        user: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$userSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        formProgress: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        class: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$classSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        studentProfile: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$studentProfileSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    }
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReduxProvider": (()=>ReduxProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/index.ts [app-client] (ecmascript)");
'use client';
;
;
;
function ReduxProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"],
        children: children
    }, void 0, false, {
        fileName: "[project]/src/store/provider.tsx",
        lineNumber: 7,
        columnNumber: 10
    }, this);
}
_c = ReduxProvider;
var _c;
__turbopack_context__.k.register(_c, "ReduxProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const Toaster = ({ ...props })=>{
    _s();
    const { theme = 'system' } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            '--normal-bg': 'var(--popover)',
            '--normal-text': 'var(--popover-foreground)',
            '--normal-border': 'var(--border)'
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
_s(Toaster, "bbCbBsvL7+LiaR8ofHlkcwveh/Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Toaster;
;
var _c;
__turbopack_context__.k.register(_c, "Toaster");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/gtag.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "GA_TRACKING_ID": (()=>GA_TRACKING_ID),
    "event": (()=>event),
    "pageview": (()=>pageview)
});
const GA_TRACKING_ID = 'G-N06ZRQXN1Y';
const pageview = (url)=>{
    window.gtag('config', GA_TRACKING_ID, {
        page_path: url
    });
};
const event = ({ action, category, label, value })=>{
    window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/AnalyticsProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AnalyticsProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gtag$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/gtag.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function AnalyticsProvider() {
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnalyticsProvider.useEffect": ()=>{
            const url = pathname + searchParams.toString();
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gtag$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["pageview"])(url);
        }
    }["AnalyticsProvider.useEffect"], [
        pathname,
        searchParams
    ]);
    return null;
}
_s(AnalyticsProvider, "h6p6PpCFmP4Mu5bIMduBzSZThBE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c = AnalyticsProvider;
var _c;
__turbopack_context__.k.register(_c, "AnalyticsProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_4c082ed4._.js.map