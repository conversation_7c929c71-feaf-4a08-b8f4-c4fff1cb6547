import type { Metadata } from "next";
import StoreInnerPage from "./StoreInnerPage";

export const metadata: Metadata = {
  title: "UEST Store - Educational Products & Stationery | Pay with UEST Coins",
  description: "Shop premium educational products, stationery, toys, and sports items at UEST Store. Pay with your earned UEST coins for exclusive discounts. Quality products for students.",
  keywords: [
    "UEST Store",
    "educational products",
    "student stationery",
    "educational toys",
    "sports equipment",
    "UEST coins",
    "student shopping",
    "educational materials",
    "school supplies",
    "learning resources"
  ],
  openGraph: {
    title: "UEST Store - Educational Products & Stationery | Pay with UEST Coins",
    description: "Shop premium educational products, stationery, toys, and sports items at UEST Store. Pay with your earned UEST coins for exclusive discounts.",
    url: "https://www.uest.in/store",
    type: "website",
    images: [
      {
        url: "https://www.uest.in/logo.png",
        width: 800,
        height: 600,
        alt: "UEST Store - Educational Products",
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: "https://www.uest.in/store",
  },
};

export default function StorePage() {
  return <StoreInnerPage />;
}