(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4452],{41226:()=>{},42509:(e,s,a)=>{Promise.resolve().then(a.bind(a,42590))},42590:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>z});var t=a(95155),l=a(12115),r=a(31291),n=a(30285),i=a(39414),c=a(7583),d=a(70347),o=a(19320),x=a(51154),m=a(18186),h=a(26661),u=a(97982),g=a(69037),p=a(95717),f=a(17580),j=a(32105),y=a(53896),N=a(4516),b=a(35695),w=a(24944),v=a(46523),A=a(56671),_=a(66766),k=a(22429),S=a(55077),P=a(8019),C=a(53784);let O={hidden:{opacity:0,y:40},show:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}}},z=()=>{var e,s,a,z;let{examid:E}=(0,b.useParams)(),[L,D]=(0,l.useState)(null),[M,F]=(0,l.useState)(!0),[q,R]=(0,l.useState)(!1),[B,I]=(0,l.useState)(!1),[V,W]=(0,l.useState)(null),[T,Y]=(0,l.useState)(null),[$,G]=(0,l.useState)(null),[H,U]=(0,l.useState)(!1),[J,K]=(0,l.useState)(0),[Q,X]=(0,l.useState)(!1),Z=()=>{try{let e=localStorage.getItem("student_data");return e?JSON.parse(e).id:null}catch(e){return null}};(0,l.useEffect)(()=>{(async()=>{if(Z())try{let e=await (0,k.J)();e.success&&G(e.data)}catch(e){console.error("Error fetching discount info:",e)}})()},[]),(0,l.useEffect)(()=>{(async()=>{if(E){F(!0);try{let e=Z(),s=await (0,v.LP)(Number(E),null!=e?e:void 0),a=!1;if(e){let t=await (0,P.o)(e,s.id);a=!1!==t.success&&t}D({...s,hasAttempted:a})}catch(e){console.error("Error fetching exam:",e),A.toast.error(e.message||"Failed to load exam details")}finally{F(!1)}}})()},[E]);let ee=e=>{W(e),Y(null),I(!0)},es=async()=>{try{return(await S.S.get("/coins/get-total-coins/student")).data.coins}catch(e){A.toast.error("Failed to load coin data. Please try again."),console.error("Error fetching data",e)}},ea=async()=>{if(!V||H)return;let e=Z();if(!e){A.toast.error("Please log in as a student to apply for an exam");return}let s=await es();try{let s=await (0,r.n)(V.id,String(e));s.application&&(D(e=>{var s;return e?{...e,totalApplicants:(e.totalApplicants||0)+1,hasApplied:!0,isMaxLimitReached:(e.totalApplicants||0)+1>=(null!==(s=e.total_student_intake)&&void 0!==s?s:0)}:null}),I(!1),R(!0),A.toast.success(s.message||"Successfully applied for the exam"),Y(null))}catch(t){let e=t.message||"Error applying for exam";if(A.toast.error(e),e.includes("Required Coin for Applying in Exam")){var a;Y(e);let t=null!==(a=Number(V.coins_required))&&void 0!==a?a:0;(null==$?void 0:$.hasDiscount)&&(t*=1-$.discountPercentage/100),K(Math.floor(Math.floor(t)-s))}else I(!1)}},et=()=>{R(!1),I(!1),W(null),Y(null)},el=async()=>{U(!0);try{let{order:e}=(await S.S.post("/coins/create-order",{amount:100*J})).data,s={key:"rzp_test_Opr6M8CKpK12pF",amount:e.amount,currency:"INR",name:"Uest Coins",description:"Add Uest Coins",order_id:e.id,handler:async function(e){try{X(!0),await S.S.post("/coins/verify",{razorpay_order_id:e.razorpay_order_id,razorpay_payment_id:e.razorpay_payment_id,razorpay_signature:e.razorpay_signature,amount:100*J}),A.toast.success("Coins added successfully!"),ea(),X(!1)}catch(e){A.toast.error("Payment verification failed")}finally{X(!1)}},theme:{color:"#f97316"}};new window.Razorpay(s).open()}catch(e){A.toast.error("Payment initialization failed")}finally{U(!1)}};return((0,l.useEffect)(()=>{let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,document.body.appendChild(e)},[]),M)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(x.A,{className:"h-5 w-5 animate-spin"})}):L?(0,t.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,t.jsx)(d.default,{}),(0,t.jsx)("main",{className:"flex-1 bg-white text-black",children:(0,t.jsxs)(o.P.div,{initial:"hidden",animate:"show",variants:{hidden:{},show:{transition:{staggerChildren:.15}}},className:"max-w-6xl mx-auto px-6 py-16 space-y-16",children:[(0,t.jsxs)(o.P.section,{variants:O,className:"text-center space-y-3",children:[(0,t.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold flex items-center justify-center gap-3 text-customOrange",children:[(0,t.jsx)(m.A,{className:"w-8 h-8"})," ",L.exam_name]}),(0,t.jsxs)("p",{className:"text-gray-600 text-lg flex items-center justify-center gap-3",children:[(0,t.jsx)(h.A,{className:"w-5 h-5"}),L.start_date?new Date(L.start_date).toLocaleDateString():"TBD",(0,t.jsx)(u.A,{className:"w-5 h-5"}),L.start_date?new Date(L.start_date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):"TBD"," ","| UEST"]})]}),(0,t.jsxs)(o.P.section,{variants:O,className:"text-center space-y-4",children:[(0,t.jsxs)("div",{className:"inline-flex items-center gap-2 bg-customOrange text-white px-4 py-1 rounded-full font-semibold shadow-sm",children:[(0,t.jsx)(g.A,{className:"w-4 h-4"})," First Prize"]}),(0,t.jsxs)("h2",{className:"text-5xl font-extrabold text-black flex items-center justify-center gap-2",children:[(0,t.jsx)(p.A,{className:"w-8 h-8 text-green-600"}),null==L?void 0:null===(s=L.UwhizPriceRank[0])||void 0===s?void 0:null===(e=s.price)||void 0===e?void 0:e.toLocaleString("en-IN")]})]}),(0,t.jsxs)(o.P.section,{variants:O,className:"text-center",children:[(0,t.jsxs)("div",{className:"inline-flex items-center gap-2 bg-gray-100 border border-gray-300 px-4 py-2 rounded-full text-gray-800 font-medium text-lg",children:[(0,t.jsx)(f.A,{className:"w-5 h-5"})," For Students: Std 1 to 12"]}),(0,t.jsx)("div",{className:"mt-4 flex justify-center flex-wrap gap-3",children:["Maths","Science","English"].map(e=>(0,t.jsxs)("span",{className:"inline-flex items-center gap-2 bg-black text-white px-4 py-1.5 rounded-full text-sm font-semibold",children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),e]},e))})]}),(0,t.jsxs)(o.P.section,{variants:O,className:"border border-orange-300 rounded-xl p-6 flex flex-col gap-6 bg-white shadow-md",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row flex-wrap items-center justify-between gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xl font-semibold text-customOrange",children:[(0,t.jsx)(p.A,{className:"w-5 h-5"}),"Entry Fee:"," ",L.coins_required?(null==$?void 0:$.hasDiscount)?(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"line-through text-gray-500 text-sm",children:L.coins_required}),(0,t.jsx)("span",{className:"text-green-600 font-bold",children:(0,k.w)(L.coins_required,$.discountPercentage)}),(0,t.jsxs)("span",{className:"text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full",children:[$.discountPercentage,"% OFF"]})]}):L.coins_required:"Free"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-2xl font-bold text-black",children:[(0,t.jsx)(m.A,{className:"w-6 h-6 text-yellow-500"}),"Prize Pool: ₹5 Lakh"]}),(0,t.jsx)("span",{className:"text-sm font-medium text-red-500 whitespace-nowrap",children:"Limited Seats Available"})]}),(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsx)("p",{className:"text-sm font-semibold text-gray-700 mb-1 tracking-wide",children:"Students Joined"}),(0,t.jsx)(w.k,{value:((e,s)=>0===s?0:Math.min(s,100))(L.totalApplicants,null!==(z=L.total_student_intake)&&void 0!==z?z:0),className:"[&>*]:bg-customOrange bg-slate-300 h-3 rounded-full"})]}),(0,t.jsx)(i.A,{hasAttempted:L.hasAttempted,exam:L,hasApplied:L.hasApplied,isMaxLimitReached:L.isMaxLimitReached,onApplyClick:()=>ee(L)})]}),(0,t.jsx)("div",{className:"bg-orange-50 dark:bg-orange-900 border border-orange-300 rounded-xl p-5 md:p-6 shadow-sm my-6 space-y-4",children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-4",children:[(0,t.jsx)(_.default,{src:"/cellular_world.jpeg",alt:"Cellular World Logo",width:80,height:80,className:"rounded-md object-contain"}),(0,t.jsxs)("div",{className:"text-center md:text-left flex-1",children:[(0,t.jsx)("h2",{className:"text-lg sm:text-xl md:text-2xl font-bold",children:"Apply Now & Win Premium Headphones Worth ₹5000! \uD83C\uDFA7"}),(0,t.jsxs)("p",{className:"text-sm sm:text-base text-customOrange-800 dark:text-orange-200 mt-1",children:["Secure a"," ",(0,t.jsx)("span",{className:"text-orange-600 font-semibold",children:"rank between 2nd and 11th"})," ","to claim your exclusive headphone gift, courtesy of Cellular World!"]})]})]})})}),(0,t.jsx)("div",{className:"bg-orange-50 dark:bg-orange-900 border border-orange-300 rounded-xl p-5 md:p-6 shadow-sm my-6 space-y-4",children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-4",children:[(0,t.jsx)(_.default,{src:"/rb-news.png",alt:"RB News Logo",width:80,height:80,className:"rounded-md object-contain"}),(0,t.jsxs)("div",{className:"text-center md:text-left flex-1",children:[(0,t.jsx)("h2",{className:"text-lg sm:text-xl md:text-2xl font-bold",children:"Apply Now & Win Exclusive Gift Hampers"}),(0,t.jsxs)("p",{className:"text-sm sm:text-base text-customOrange-800 dark:text-orange-200 mt-1",children:["Secure a"," ",(0,t.jsx)("span",{className:"text-orange-600 font-semibold",children:"rank between 12th to 40th"})," ","to Grab your exclusive Gift Hampers By RB News!"]})]})]})})}),(0,t.jsxs)(o.P.section,{variants:O,className:"relative flex flex-col gap-6 p-4 sm:p-6 bg-gray-50 dark:bg-gray-900 rounded-xl border border-orange-300 shadow-sm",children:[(0,t.jsx)(_.default,{src:"/nalanda.png",alt:"Nalanda Vidyalaya",width:90,height:90,className:"absolute top-4 right-4 object-contain hover:scale-105 transition-transform duration-300 drop-shadow-md"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2 text-center md:text-left",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center md:justify-start gap-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-orange-500"}),(0,t.jsx)("span",{children:"Sponsored By:"})]}),(0,t.jsx)("h2",{className:"text-xl sm:text-2xl font-bold text-orange-700 dark:text-orange-400",children:"NALANDA VIDYALAYA"}),(0,t.jsxs)("div",{className:"flex items-center justify-center md:justify-start gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 text-orange-400"}),(0,t.jsx)("span",{children:"Rajkot - Morbi Hwy, Near Ajanta Quartz, Virpar, Morbi, Gujarat"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,t.jsx)(_.default,{src:"/nalanda01.jpg",alt:"Nalanda Vidyalaya Image 1",width:400,height:300,className:"rounded-lg object-cover w-full h-48"}),(0,t.jsx)(_.default,{src:"/nalanda02.png",alt:"Nalanda Vidyalaya Image 2",width:400,height:300,className:"rounded-lg object-cover w-full h-48"}),(0,t.jsx)(_.default,{src:"/nalanda03.png",alt:"Nalanda Vidyalaya Image 3",width:400,height:300,className:"rounded-lg object-cover w-full h-48"})]})]})]})]})}),(0,t.jsx)(c.default,{}),q&&V&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-xl max-w-md w-full",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-green-600 mb-4",children:"Application Successful!"}),(0,t.jsxs)("p",{className:"text-gray-700 mb-6",children:["You have successfully applied for"," ",(0,t.jsx)("strong",{children:V.exam_name}),"."]}),(0,t.jsx)(n.$,{onClick:et,className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg",children:"Close"})]})}),B&&V&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-2xl max-w-md w-full",children:[(0,t.jsx)("h2",{className:"text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2",children:"Are You Sure?"}),(0,t.jsxs)("p",{className:"text-gray-700 text-lg mb-6 leading-relaxed",children:["Do you want to apply for"," ",(0,t.jsx)("strong",{className:"text-customOrange",children:V.exam_name}),"?",null!=V.coins_required&&(0,t.jsxs)("span",{children:[" ","This will cost"," ",(null==$?void 0:$.hasDiscount)?(0,t.jsxs)("span",{children:[(0,t.jsx)("span",{className:"line-through text-gray-500",children:V.coins_required})," ",(0,t.jsx)("strong",{className:"text-green-600",children:(0,k.w)(V.coins_required,$.discountPercentage)})," ",(0,t.jsxs)("span",{className:"text-green-600 text-sm",children:["(",$.discountPercentage,"% discount applied)"]})]}):(0,t.jsx)("strong",{className:"text-customOrange",children:V.coins_required})," ","coins."]})]}),T&&(0,t.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg mb-6 border border-red-200",children:[(0,t.jsxs)("div",{className:"flex gap-5 items-center",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,t.jsx)("p",{className:"text-red-600 text-sm font-medium",children:T})]}),(0,t.jsx)(n.$,{onClick:()=>el(),className:"mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:Q,children:Q?(0,t.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(x.A,{className:"animate-spin w-5 h-5"}),"Processing..."]}):"Add Coins"})]}),Z()?(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(n.$,{onClick:ea,className:"w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:!!T||H,children:H?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Yes, Apply"}),(0,t.jsx)(n.$,{onClick:et,className:"w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg",children:"Cancel"})]}):(0,t.jsx)(n.$,{onClick:()=>C.default.push("/student-login?redirect=/uwhiz-info/".concat(E)),className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",children:"Login to Apply"})]})})]}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Exam not found"})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,2265,4955,3335,347,8903,8441,1684,7358],()=>s(42509)),_N_E=e.O()}]);