(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3694],{1595:(e,s,a)=>{Promise.resolve().then(a.bind(a,73136))},35169:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},54717:(e,s,a)=>{"use strict";a.d(s,{Lq:()=>l,dI:()=>r});var t=a(55077);let r=async e=>{try{let s=new URLSearchParams;(null==e?void 0:e.category)&&s.append("category",e.category),(null==e?void 0:e.status)&&s.append("status",e.status),(null==e?void 0:e.search)&&s.append("search",e.search),s.append("status","ACTIVE");let a=await t.S.get("/admin/store?".concat(s.toString()));return{success:!0,data:a.data.data}}catch(e){var s,a;return{success:!1,error:(null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||e.message||"Failed to fetch store items"}}},l=async e=>{try{let s=await t.S.get("/admin/store/".concat(e));return{success:!0,data:s.data.data}}catch(e){var s,a;return{success:!1,error:(null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||e.message||"Failed to fetch store item"}}}},73136:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>E});var t=a(95155),r=a(12115),l=a(35695),c=a(66766),i=a(35169),n=a(93550),d=a(37108),o=a(87712),m=a(84616),u=a(27809),x=a(81586),h=a(30285),g=a(66695),j=a(26126),f=a(68856),v=a(56671),p=a(70347),b=a(7583),N=a(59434),y=a(54717),w=a(28844),k=a(40960),S=a(90010);let E=e=>{var s,a;let{params:E}=e,O=(0,l.useRouter)(),[P,A]=(0,r.useState)(null),[C,I]=(0,r.useState)([]),[F,L]=(0,r.useState)(!0),[W,_]=(0,r.useState)(""),[D,T]=(0,r.useState)(1),[U,$]=(0,r.useState)(!1),[R,Z]=(0,r.useState)(!1),[q,z]=(0,r.useState)(!1),[B,V]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{let{id:e}=await E;_(e)})()},[E]),(0,r.useEffect)(()=>{Z((0,N.wR)().isAuth)},[]),(0,r.useEffect)(()=>{(async()=>{try{if(!W)return;L(!0);let e=await y.Lq(W);if(!e.success){v.toast.error(e.error||"Failed to load product"),O.push("/store");return}A(e.data);let s=await y.dI();if(s.success&&s.data){let e=s.data.filter(e=>e.id!==W).slice(0,4);I(e)}}catch(e){console.error("Error fetching data:",e),v.toast.error("Failed to load product"),O.push("/store")}finally{L(!1)}})()},[W,O]);let Y=e=>{if(!(e<1)){if(P&&e>P.availableStock){v.toast.error("Only ".concat(P.availableStock," items available"));return}T(e)}},H=async()=>{if(!R){v.toast.error("Please login to add items to cart");return}if(P){if(0===P.availableStock){v.toast.error("Item is out of stock");return}if(D>P.availableStock){v.toast.error("Only ".concat(P.availableStock," items available"));return}try{$(!0),(await w.bE(P.id,D)).success?(v.toast.success("".concat(D," item(s) added to cart!")),window.dispatchEvent(new CustomEvent("cartUpdated")),T(1)):v.toast.error("Only ".concat(P.availableStock," items available"))}catch(e){console.error("Error adding to cart:",e),v.toast.error("Item is out of stock")}finally{$(!1)}}},M=async()=>{if(P)try{var e,s,a;V(!0);let t=P.coinPrice*D,r={cartItems:[{id:P.id,name:P.name,coinPrice:P.coinPrice,quantity:D,image:P.image||""}],totalCoins:t},l=await k.wE(r);if(!l.success){if("PROFILE_NOT_APPROVED"===l.error){let e=(null===(a=l.data)||void 0===a?void 0:a.message)||"Your profile is not approved yet. Please complete your profile and wait for admin approval.";v.toast.error(e),z(!1);return}throw Error(l.error)}let c=(null===(e=l.data)||void 0===e?void 0:e.orderId)||(null===(s=l.data)||void 0===s?void 0:s.firstOrderId)||"Unknown";v.toast.success("Order placed successfully! Order ID: ".concat(c.slice(-8),". Coins deducted. Your order is pending admin approval.")),z(!1),T(1);let i=await y.Lq(W);i.success&&A(i.data);let n=await y.dI();if(n.success&&n.data){let e=n.data.filter(e=>e.id!==W).slice(0,4);I(e)}}catch(e){console.error("Error details:",e),v.toast.error(e.message||"Purchase failed"),z(!1)}finally{V(!1)}};return F?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.default,{}),(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)(f.E,{className:"h-10 w-32 mb-6"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsx)(f.E,{className:"h-96 w-full rounded-lg"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(f.E,{className:"h-8 w-3/4"}),(0,t.jsx)(f.E,{className:"h-4 w-full"}),(0,t.jsx)(f.E,{className:"h-4 w-2/3"}),(0,t.jsx)(f.E,{className:"h-6 w-1/4"}),(0,t.jsx)(f.E,{className:"h-10 w-full"})]})]})]})}),(0,t.jsx)(b.default,{})]}):P?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.default,{}),(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)(h.$,{variant:"ghost",onClick:()=>O.push("/store"),className:"mb-6 hover:bg-muted",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Back to Store"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12",children:[(0,t.jsx)(g.Zp,{className:"overflow-hidden",children:(0,t.jsx)(g.Wu,{className:"p-0",children:(0,t.jsxs)("div",{className:"relative h-96 lg:h-[500px] bg-muted/30 flex items-center justify-center",children:[(0,t.jsx)(c.default,{src:(null===(s=P.image)||void 0===s?void 0:s.startsWith("http"))?P.image:"".concat("http://localhost:4005/").concat((null===(a=P.image)||void 0===a?void 0:a.startsWith("/"))?P.image.substring(1):P.image||"uploads/store/placeholder.jpg"),alt:P.name,className:"object-contain w-full h-full",width:500,height:500,onError:e=>{e.target.src="/logo.png"}}),0===P.availableStock&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,t.jsx)(j.E,{variant:"destructive",className:"text-lg px-4 py-2",children:"Out of Stock"})})]})})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:P.name}),(0,t.jsx)(j.E,{variant:"secondary",className:"text-sm",children:P.category})]}),(0,t.jsx)("p",{className:"text-muted-foreground text-lg leading-relaxed",children:P.description})]}),(0,t.jsxs)("div",{className:"bg-card p-6 rounded-lg border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(n.A,{className:"w-6 h-6 text-customOrange"}),(0,t.jsxs)("span",{className:"text-3xl font-bold text-customOrange",children:[P.coinPrice," coins"]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pay with your UEST coins"})]}),(0,t.jsxs)("div",{className:"bg-card p-4 rounded-lg border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(d.A,{className:"w-5 h-5 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium text-card-foreground",children:"Stock Information"})]}),(0,t.jsx)("div",{className:"space-y-1 text-sm",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Available:"}),(0,t.jsxs)("span",{className:"font-medium ".concat(0===P.availableStock?"text-red-500":"text-green-600"),children:[P.availableStock," units"]})]})})]}),P.availableStock>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-card-foreground mb-2",children:"Quantity"}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(h.$,{variant:"outline",size:"sm",onClick:()=>Y(D-1),disabled:D<=1,children:(0,t.jsx)(o.A,{className:"w-4 h-4"})}),(0,t.jsx)("span",{className:"w-12 text-center font-medium text-lg",children:D}),(0,t.jsx)(h.$,{variant:"outline",size:"sm",onClick:()=>Y(D+1),disabled:D>=P.availableStock,children:(0,t.jsx)(m.A,{className:"w-4 h-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Total Cost:"}),(0,t.jsxs)("span",{className:"font-bold text-lg text-customOrange flex items-center",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 mr-1"}),P.coinPrice*D," coins"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsx)(h.$,{onClick:H,disabled:U||!R,variant:"outline",className:"w-full border-customOrange text-customOrange hover:bg-customOrange hover:text-white py-3 text-lg",children:U?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"}),"Adding to Cart..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"w-5 h-5 mr-2"}),"Add to Cart"]})}),(0,t.jsxs)(h.$,{onClick:()=>{if(!R){v.toast.error("Please login to purchase items");return}if(P){if(0===P.availableStock){v.toast.error("Item is out of stock");return}if(D>P.availableStock){v.toast.error("Only ".concat(P.availableStock," items available"));return}z(!0)}},disabled:!R,className:"w-full bg-customOrange hover:bg-orange-600 text-white py-3 text-lg",children:[(0,t.jsx)(x.A,{className:"w-5 h-5 mr-2"}),"Buy Now"]})]}),!R&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Please login to purchase items"})]})]}),0===P.availableStock&&(0,t.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 p-4 rounded-lg",children:(0,t.jsx)("p",{className:"text-destructive font-medium text-center",children:"This item is currently out of stock"})})]})]}),(0,t.jsx)(g.Zp,{className:"mb-8",children:(0,t.jsxs)(g.Wu,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-card-foreground mb-4",children:"Product Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Category"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:P.category})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Status"}),(0,t.jsx)(j.E,{variant:"ACTIVE"===P.status?"default":"secondary",children:P.status})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Added On"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:new Date(P.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Last Updated"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:new Date(P.updatedAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]})}),C.length>0&&(0,t.jsx)(g.Zp,{className:"mb-8",children:(0,t.jsxs)(g.Wu,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-card-foreground mb-6",children:"Latest Items"}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:C.map(e=>{var s,a;return(0,t.jsxs)(g.Zp,{className:"overflow-hidden group bg-card border shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer",onClick:()=>O.push("/store/".concat(e.id)),children:[(0,t.jsxs)("div",{className:"relative h-48 bg-muted/30 flex items-center justify-center",children:[(0,t.jsx)(c.default,{src:(null===(s=e.image)||void 0===s?void 0:s.startsWith("http"))?e.image:"".concat("http://localhost:4005/").concat((null===(a=e.image)||void 0===a?void 0:a.startsWith("/"))?e.image.substring(1):e.image||"uploads/store/placeholder.jpg"),alt:e.name,className:"object-contain w-full h-full",width:200,height:200,onError:e=>{e.target.src="/logo.png"}}),0===e.availableStock&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,t.jsx)(j.E,{variant:"destructive",className:"text-sm",children:"Out of Stock"})})]}),(0,t.jsx)(g.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsx)("h3",{className:"font-semibold text-card-foreground text-sm line-clamp-2 group-hover:text-customOrange transition-colors",children:e.name}),(0,t.jsx)(j.E,{variant:"secondary",className:"text-xs ml-2 shrink-0",children:e.category})]}),(0,t.jsx)("p",{className:"text-muted-foreground text-xs line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 text-customOrange"}),(0,t.jsx)("span",{className:"font-bold text-customOrange text-sm",children:e.coinPrice})]}),(0,t.jsxs)("span",{className:"text-xs ".concat(0===e.availableStock?"text-red-500":"text-green-600"),children:[e.availableStock," left"]})]})]})})]},e.id)})})]})})]})}),(0,t.jsx)(b.default,{}),(0,t.jsx)(S.Lt,{open:q,onOpenChange:z,children:(0,t.jsxs)(S.EO,{children:[(0,t.jsxs)(S.wd,{children:[(0,t.jsx)(S.r7,{children:"Confirm Direct Purchase"}),(0,t.jsxs)(S.$v,{children:["Are you sure you want to purchase this item directly for ",(0,t.jsxs)("strong",{children:[(null==P?void 0:P.coinPrice)*D," coins"]}),"?",(0,t.jsx)("br",{}),(0,t.jsx)("br",{}),(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{children:[null==P?void 0:P.name," x",D]}),(0,t.jsxs)("span",{children:[(null==P?void 0:P.coinPrice)*D," coins"]})]})}),(0,t.jsx)("br",{}),"This action cannot be undone and coins will be deducted from your account immediately."]})]}),(0,t.jsxs)(S.ck,{children:[(0,t.jsx)(S.Zr,{children:"Cancel"}),(0,t.jsx)(S.Rx,{onClick:M,disabled:B,className:"bg-customOrange hover:bg-customOrange/90",children:B?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Processing..."]}):"Confirm Purchase"})]})]})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.default,{}),(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground mb-4",children:"Product Not Found"}),(0,t.jsxs)(h.$,{onClick:()=>O.push("/store"),className:"bg-customOrange hover:bg-orange-600",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Back to Store"]})]})}),(0,t.jsx)(b.default,{})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,347,5691,8441,1684,7358],()=>s(1595)),_N_E=e.O()}]);