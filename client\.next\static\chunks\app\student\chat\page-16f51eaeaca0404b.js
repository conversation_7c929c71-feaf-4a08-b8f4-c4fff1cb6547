(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[569],{66455:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(95155),r=a(12115),i=a(35695),l=a(26473),n=a(59434);function c(){let[e,t]=(0,r.useState)(!1),[a,c]=(0,r.useState)(""),[u,o]=(0,r.useState)(""),d=(0,i.useSearchParams)(),m=d.get("userId"),f=d.get("userName");return(0,r.useEffect)(()=>{let e=(0,n.xh)();if(t(e),e){let e=localStorage.getItem("student_data");if(e)try{let t=JSON.parse(e),a="".concat(t.firstName," ").concat(t.lastName)||t.email.split("@")[0];c(a),o(t.id)}catch(e){console.error("Error parsing student data:",e)}}else{let e=localStorage.getItem("user");if(e)try{let a=JSON.parse(e),s="".concat(a.firstName," ").concat(a.lastName)||a.email.split("@")[0];c(s),o(a.id),t(!0)}catch(e){console.error("Error parsing user data:",e)}}},[]),(0,s.jsx)(l.A,{userType:"student",isAuthenticated:e,username:a,userId:u,loginPath:"/",initialSelectedUserId:m||void 0,initialSelectedUserName:f||void 0})}function u(){return(0,s.jsx)(r.Suspense,{children:(0,s.jsx)(c,{})})}},93058:(e,t,a)=>{Promise.resolve().then(a.bind(a,66455))}},e=>{var t=t=>e(e.s=t);e.O(0,[2666,7040,5186,4212,5513,7632,1701,6473,8441,1684,7358],()=>t(93058)),_N_E=e.O()}]);