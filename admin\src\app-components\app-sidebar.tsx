'use client';

import * as React from 'react';
import Image from 'next/image';
import {
  LayoutDashboardIcon,
  MessageSquareText,
  Brain,
  Notebook,
  UserCheck,
  Star,
  ClipboardListIcon,
  ActivitySquareIcon,
  Camera,
  PenSquare,
  Share2,
  Settings,
  Bell,
  MessageSquareTextIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  Activity,
  ListOrdered,
} from 'lucide-react';

import { NavMain } from '@/app-components/nav-main';
import { NavUser } from '@/app-components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import Link from 'next/link';

const user = {
  name: 'uest admin',
  email: '<EMAIL>',
  avatar: '/avatars/shadcn.jpg',
};

const navMain = [
  {
    title: 'Classes',
    icon: LayoutDashboardIcon,
    children: [
      { title: 'List', url: '/dashboard', icon: LayoutDashboardIcon },
      { title: 'Testimonials', url: '/testimonials', icon: MessageSquareText },
      { title: 'Thoughts', url: '/classes-thoughts', icon: Brain },
      { title: 'Blogs', url: '/blog', icon: Notebook },
    ],
  },
  {
    title: 'Students',
    icon: UserCheck,
    children: [
      { title: 'Details', url: '/student-details', icon: UserCheck },
      { title: 'Reviews', url: '/reviews', icon: Star },
    ],
  },
  {
    title: 'Exams',
    icon: ClipboardListIcon,
    children: [
      { title: 'Details', url: '/exam-detail', icon: ClipboardListIcon },
      { title: 'Question Bank', url: '/question-bank', icon: ActivitySquareIcon },
      { title: 'Photo Monitoring', url: '/exam-monitoring', icon: Camera },
      { title: 'Daily Quiz Questions', url: '/mock-question-bank', icon: PenSquare },
      {title: 'Daily Quiz Details', url: '/dailyquiz-leaderbord', icon:ListOrdered},
    ],
  },
  {
    title: 'Store',
    icon: ShoppingBagIcon,
    children: [
      { title: 'Items', url: '/store', icon: ShoppingBagIcon },
      { title: 'Orders', url: '/store-orders', icon: ShoppingCartIcon },
    ],
  },
  {
    title: 'Referrals Management',
    icon: Share2,
    url: '/referral-management',
  },
  {
    title: 'Constants Management',
    icon: Settings,
    url: '/constants'
  },
  {
    title: 'Chats',
    icon: MessageSquareTextIcon,
    url: '/chat'
  },
  {
    title: 'Notifications',
    icon: Bell,
    url: '/notifications'
  },
  {
    title: 'Activity',
    icon: Activity,
    url: '/activity-logs'
  },
];

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <Link href="/">
                <Image
                  src="/logo.jpeg"
                  alt="App Logo"
                  width={130}
                  height={40}
                  className="rounded-md"
                />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <NavMain items={navMain} />
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
    </Sidebar>
  );
}