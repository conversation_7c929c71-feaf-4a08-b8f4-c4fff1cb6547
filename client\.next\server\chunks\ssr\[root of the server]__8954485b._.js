module.exports = {

"[project]/.next-internal/server/app/classes-details/[id]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/not-found.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/classes-details/[id]/InnerPage.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/classes-details/[id]/InnerPage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/classes-details/[id]/InnerPage.tsx <module evaluation>", "default");
}}),
"[project]/src/app/classes-details/[id]/InnerPage.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/classes-details/[id]/InnerPage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/classes-details/[id]/InnerPage.tsx", "default");
}}),
"[project]/src/app/classes-details/[id]/InnerPage.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$classes$2d$details$2f5b$id$5d2f$InnerPage$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/classes-details/[id]/InnerPage.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$classes$2d$details$2f5b$id$5d2f$InnerPage$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/classes-details/[id]/InnerPage.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$classes$2d$details$2f5b$id$5d2f$InnerPage$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/axios.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosInstance": (()=>axiosInstance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-rsc] (ecmascript)");
;
;
const baseURL = ("TURBOPACK compile-time value", "http://localhost:4005/api/v1") || 'http://localhost:4005/api/v1';
const baseURL2 = ("TURBOPACK compile-time value", "http://localhost:4006") || 'http://localhost:4006';
console.log('Axios baseURL:', baseURL);
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].create({
    baseURL,
    headers: {
        'Content-Type': 'application/json'
    },
    withCredentials: true
});
axiosInstance.interceptors.request.use((config)=>{
    const serverSelect = config.headers['Server-Select'];
    config.baseURL = serverSelect === 'uwhizServer' ? baseURL2 : baseURL;
    // Only access localStorage in the browser
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return config;
}, (error)=>Promise.reject(error));
axiosInstance.interceptors.response.use((response)=>response, (error)=>{
    if (error.response && error.response.status === 401) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toast"].error(error.response.data.message || 'Unauthorized');
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    return Promise.reject(error);
});
}}),
"[project]/src/store/slices/formProgressSlice.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FormId": (()=>FormId),
    "completeForm": (()=>completeForm),
    "default": (()=>__TURBOPACK__default__export__),
    "setCurrentStep": (()=>setCurrentStep)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-rsc] (ecmascript) <locals>");
;
var FormId = /*#__PURE__*/ function(FormId) {
    FormId["PROFILE"] = "about";
    FormId["DESCRIPTION"] = "description";
    FormId["PHOTO_LOGO"] = "photo_logo";
    FormId["EDUCATION"] = "education";
    FormId["EXPERIENCE"] = "experience";
    FormId["CERTIFICATES"] = "certificates";
    FormId["TUTIONCLASS"] = "tution_class";
    FormId["ADDRESS"] = "address";
    return FormId;
}({});
const initialState = {
    completedSteps: 0,
    totalSteps: 8,
    currentStep: 1,
    completedForms: {
        ["about"]: false,
        ["description"]: false,
        ["photo_logo"]: false,
        ["education"]: false,
        ["certificates"]: false,
        ["experience"]: false,
        ["tution_class"]: false,
        ["address"]: false
    }
};
const formProgressSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'formProgress',
    initialState,
    reducers: {
        completeForm: (state, action)=>{
            const formId = action.payload;
            if (!state.completedForms[formId]) {
                state.completedForms[formId] = true;
                state.completedSteps = Math.min(state.completedSteps + 1, state.totalSteps);
            }
        },
        setCurrentStep: (state, action)=>{
            state.currentStep = action.payload;
        }
    }
});
const { completeForm, setCurrentStep } = formProgressSlice.actions;
const __TURBOPACK__default__export__ = formProgressSlice.reducer;
}}),
"[project]/src/lib/helper.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "convertTo24HourFormat": (()=>convertTo24HourFormat),
    "evaluateCompletedForms": (()=>evaluateCompletedForms),
    "parseAndJoinArray": (()=>parseAndJoinArray),
    "safeParseArray": (()=>safeParseArray),
    "verifyJWT": (()=>verifyJWT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/formProgressSlice.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/verify.js [app-rsc] (ecmascript)");
;
;
const evaluateCompletedForms = (classData, dispatch)=>{
    if (classData.contactNo) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].PROFILE));
    }
    if (classData.ClassAbout?.tutorBio?.length > 50) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].DESCRIPTION));
    }
    if (classData.ClassAbout?.profilePhoto && classData.ClassAbout?.classesLogo) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].PHOTO_LOGO));
    }
    if (classData.education?.length > 0) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].EDUCATION));
    }
    if (classData.certificates?.length > 0) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].CERTIFICATES));
    }
    if (classData.experience?.length > 0) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].EXPERIENCE));
    }
    if (classData.tuitionClasses?.length > 0) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].TUTIONCLASS));
    }
    if (classData.address) {
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeForm"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$formProgressSlice$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FormId"].ADDRESS));
    }
};
function convertTo24HourFormat(time12h) {
    if (!time12h) return '';
    const [time, modifier] = time12h.split(' ');
    let [hours, minutes] = time.split(':');
    if (hours === '12') hours = '00';
    if (modifier === 'PM') hours = String(parseInt(hours) + 12);
    return `${hours.padStart(2, '0')}:${minutes}`;
}
const safeParseArray = (value)=>{
    if (!value) return [];
    try {
        const parsed = typeof value === 'string' ? JSON.parse(value) : value;
        return Array.isArray(parsed) ? parsed : [
            parsed
        ];
    } catch  {
        return [
            value
        ]; // fallback to treating as plain string
    }
};
const parseAndJoinArray = (value)=>{
    try {
        const parsed = typeof value === 'string' ? JSON.parse(value) : value;
        return Array.isArray(parsed) ? parsed.join(', ') : parsed || 'N/A';
    } catch  {
        return value || 'N/A';
    }
};
const secret = new TextEncoder().encode('secret123');
async function verifyJWT(token) {
    try {
        const { payload } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jwtVerify"])(token, secret);
        return payload;
    } catch (e) {
        console.error('Invalid token:', e);
        return null;
    }
}
}}),
"[project]/src/app/classes-details/[id]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TeacherDetailsPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$classes$2d$details$2f5b$id$5d2f$InnerPage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/classes-details/[id]/InnerPage.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$helper$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/helper.ts [app-rsc] (ecmascript)");
;
;
;
;
async function fetchTeacherData(id) {
    try {
        const res = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["axiosInstance"].get(`classes/details/${id}`);
        return res.data;
    } catch (err) {
        console.error("Failed to fetch teacher data for SEO", err);
        return null;
    }
}
async function generateMetadata({ params }) {
    const { id } = params;
    const data = await fetchTeacherData(id);
    const { firstName, lastName, ClassAbout, tuitionClasses } = data;
    const fullName = `${firstName} ${lastName}`;
    const profileImg = ClassAbout?.profilePhoto ? `${"TURBOPACK compile-time value", "http://localhost:4005/"}${ClassAbout.profilePhoto}` : "/default-teacher-profile.jpg";
    const catchyHeadline = ClassAbout?.catchyHeadline || "Professional Educator";
    const tutorBio = ClassAbout?.tutorBio || "Explore the expertise of our educators.";
    const tuitionDetails = tuitionClasses?.length > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$helper$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseAndJoinArray"])(tuitionClasses[0].subject || tuitionClasses[0].details || []) : "Educational Services";
    return {
        title: `${fullName} - ${catchyHeadline} | Uest`,
        description: `${tutorBio} Specializing in ${tuitionDetails}. Book your classes with ${fullName} on Uest today!`,
        keywords: [
            fullName,
            catchyHeadline,
            tuitionDetails,
            "online education",
            "tuition classes",
            "Uest",
            "teacher profile"
        ],
        openGraph: {
            title: `${fullName} - ${catchyHeadline} | Uest`,
            description: `${tutorBio} Specializing in ${tuitionDetails}.`,
            url: `https://www.uest.in/classes-details/${id}`,
            type: "website",
            images: [
                {
                    url: profileImg,
                    width: 800,
                    height: 600,
                    alt: `${fullName} Profile`
                }
            ]
        },
        robots: {
            index: true,
            follow: true
        }
    };
}
function TeacherDetailsPage({ params }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$classes$2d$details$2f5b$id$5d2f$InnerPage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/app/classes-details/[id]/page.tsx",
        lineNumber: 69,
        columnNumber: 10
    }, this);
}
}}),
"[project]/src/app/classes-details/[id]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/classes-details/[id]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__8954485b._.js.map