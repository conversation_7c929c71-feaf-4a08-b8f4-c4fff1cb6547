(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3980],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var a=s(95155);s(12115);var l=s(6874),i=s.n(l),r=s(66766),n=s(29911);let d=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(i(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(r.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:l}=e;return(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(i(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:l,children:(0,a.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},l)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(i(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(r.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(i(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(i(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},18717:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(95155),l=s(12115),i=s(59434),r=s(86214),n=s(35695),d=s(70347),c=s(7583),o=s(19320),x=s(66766);let h=e=>{let{totalCoins:t,badgeSrc:s,badgeAlt:l}=e;return null===t?null:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm",children:[(0,a.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:20,height:20,className:"w-5 h-5"}),t," Coins"]}),s&&l&&(0,a.jsx)(o.P.div,{animate:{scale:[1,1.05,1]},transition:{repeat:1/0,duration:1.5,ease:"easeInOut"},className:"w-12 h-12 flex items-center justify-center",children:(0,a.jsx)(x.default,{src:s,alt:l,width:48,height:48,className:"object-contain"})})]})};var m=s(95811);let u=function(){var e,t,s,u;let g=(0,n.useParams)().studentId,[f,v]=(0,l.useState)(null),[j,p]=(0,l.useState)(!1),[b,y]=(0,l.useState)(null),[w,N]=(0,l.useState)(null),[C,k]=(0,l.useState)(null),[S,L]=(0,l.useState)(null),[P,D]=(0,l.useState)(1);(0,l.useEffect)(()=>{(async()=>{if(!g){y("Student ID is required"),p(!1);return}p(!0),y(null);try{var e;let t=await (0,r.S)(g,P,10,{isWeekly:!1});if(t.success&&(null===(e=t.data)||void 0===e?void 0:e.data)){v(t.data.data);let{mockExamResults:e}=t.data.data,s=e.reduce((e,t)=>e+(t.coinEarnings||0),0);N(s);let a=null,l=null;s>=100&&s<=499?(a="/scholer.svg",l="Scholar Badge"):s>=500&&s<=999?(a="/Mastermind.svg",l="Mastermind Badge"):s>=1e3&&(a="/Achiever.svg",l="Achiever Badge"),k(a),L(l)}else y(t.error||"Failed to fetch daily quiz results")}catch(e){y(e.message||"An unexpected error occurred")}finally{p(!1)}})()},[g,P]);let F=e=>new Date(e).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit",year:"numeric"}).split("/").join("-");return j?(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center text-xl font-medium text-gray-700 animate-pulse",children:"Loading daily quiz results..."})}):b?(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-xl font-medium text-red-600 bg-red-50 p-4 rounded-lg shadow-md",children:["Error: ",b]})}):f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.default,{}),(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen py-16 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-4xl font-extrabold text-gray-800 text-center mb-10 tracking-tight",children:"Daily Quiz Results"}),(0,a.jsx)(o.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},className:"mb-10 rounded-3xl p-[2px] bg-gradient-to-tr from-orange-400 via-yellow-400 to-amber-500",children:(0,a.jsx)("div",{className:"rounded-3xl bg-white/80 backdrop-blur-md p-6 md:p-8 shadow-xl",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start justify-between gap-6 relative",children:[(0,a.jsx)(o.P.div,{animate:{scale:[1,1.1,1],rotate:[0,1,-1,0]},transition:{repeat:1/0,duration:2,ease:"easeInOut"},className:"w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-inner ring-2 ring-amber-200",children:(0,a.jsx)("span",{className:"text-3xl",children:"\uD83D\uDD25"})}),(0,a.jsxs)("div",{className:"text-center sm:text-left flex-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-neutral-800 mb-1 tracking-tight",children:"Daily Streak"}),(0,a.jsxs)("p",{className:"text-4xl font-extrabold text-amber-600 tracking-wider",children:[f.streak.streakCount," ",1===f.streak.streakCount?"Day":"Days"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1 italic",children:"Stay consistent and keep growing"}),(0,a.jsx)(m.A,{badge:f.badge})]}),(0,a.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,a.jsx)("div",{className:"px-4 py-2 bg-white rounded-full shadow ring-1 ring-amber-300 text-sm font-medium text-amber-600",children:"Consistency Reward"}),(0,a.jsx)(h,{totalCoins:w,badgeSrc:C,badgeAlt:S})]})]})})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsx)("div",{className:"space-y-4",children:0===f.mockExamResults.length?(0,a.jsx)("div",{className:"text-center text-gray-500 text-lg font-medium py-8",children:"No daily quiz results found."}):f.mockExamResults.map((e,t)=>(0,a.jsxs)("div",{className:(0,i.cn)("flex items-center justify-between p-5 rounded-xl transition-all duration-200",0===t?"bg-orange-50 border-l-4 border-orange-500":"bg-gray-50 hover:bg-gray-100 hover:shadow-md"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-5",children:[(0,a.jsxs)("div",{className:(0,i.cn)("w-12 h-12 flex items-center justify-center rounded-full font-semibold text-sm shadow-sm",0===t?"bg-orange-500 text-white":"bg-gray-200 text-gray-700"),children:["#",(P-1)*10+t+1]}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:(0,i.cn)("font-semibold",0===t?"text-lg text-gray-800":"text-base text-gray-700"),children:F(e.createdAt)})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[e.coinEarnings>=0&&(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm",children:[(0,a.jsx)(x.default,{src:"/uest_coin.png",alt:"Coin",width:20,height:20,className:"w-5 h-5"}),e.coinEarnings]}),(0,a.jsxs)("div",{className:(0,i.cn)("font-semibold text-orange-600 bg-orange-100 px-4 py-1.5 rounded-full shadow-sm",0===t?"text-base":"text-sm"),children:[e.score,"/10"]})]})]},e.id))}),(null==f?void 0:null===(e=f.pagination)||void 0===e?void 0:e.totalPages)>1&&(0,a.jsxs)("div",{className:"flex justify-center gap-6 mt-8",children:[(0,a.jsx)("button",{onClick:()=>{P>1&&D(P-1)},disabled:1===P,className:(0,i.cn)("bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",1===P?"opacity-50 cursor-not-allowed":"hover:bg-orange-600 hover:scale-105"),children:"Previous"}),(0,a.jsxs)("span",{className:"self-center text-gray-600 font-medium",children:["Page ",P," of ",(null==f?void 0:null===(t=f.pagination)||void 0===t?void 0:t.totalPages)||1]}),(0,a.jsx)("button",{onClick:()=>{(null==f?void 0:f.pagination)&&P<f.pagination.totalPages&&D(P+1)},disabled:P===((null==f?void 0:null===(s=f.pagination)||void 0===s?void 0:s.totalPages)||1),className:(0,i.cn)("bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",P===((null==f?void 0:null===(u=f.pagination)||void 0===u?void 0:u.totalPages)||1)?"opacity-50 cursor-not-allowed":"hover:bg-orange-600 hover:scale-105"),children:"Next"})]})]})]})}),(0,a.jsx)(c.default,{})]}):(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center text-xl font-medium text-gray-700",children:"No data available"})})}},50240:(e,t,s)=>{Promise.resolve().then(s.bind(s,18717))},86214:(e,t,s)=>{"use strict";s.d(t,{S:()=>i,q:()=>l});var a=s(55077);let l=async e=>{try{let t=await a.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,s;return{success:!1,error:"Failed to save mock exam result: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message)}}},i=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};try{let i=new URLSearchParams({page:t.toString(),limit:s.toString(),...void 0!==l.isWeekly&&{isWeekly:l.isWeekly.toString()}}).toString(),r=await a.S.get("/mock-exam-result/".concat(e,"?").concat(i),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:r.data}}catch(e){var i,r;return{success:!1,error:"Failed to get mock exam result: ".concat((null===(r=e.response)||void 0===r?void 0:null===(i=r.data)||void 0===i?void 0:i.message)||e.message)}}}},95811:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(95155),l=s(19320);s(12115);let i=e=>{let{count:t}=e;return(0,a.jsxs)("svg",{className:"h-10 w-10 sm:h-12 sm:w-12",viewBox:"0 0 1550 1808",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M574.764 165.821C692.4 74.0944 857.6 74.0944 975.236 165.821L1262.05 386.652C1354.36 459.21 1407.96 571.58 1407.96 690.458V1117.54C1407.96 1236.42 1354.36 1348.79 1262.05 1421.35L975.236 1642.18C857.6 1733.91 692.4 1733.91 574.764 1642.18L287.951 1421.35C195.639 1348.79 142.037 1236.42 142.037 1117.54V690.458C142.037 571.58 195.639 459.21 287.951 386.652L574.764 165.821Z",fill:"#FDFEF9"}),(0,a.jsx)("mask",{id:"mask0",maskUnits:"userSpaceOnUse",x:"71",y:"180",width:"1408",height:"1484",children:(0,a.jsx)("path",{d:"M574.315 248.124C692.703 157.412 857.297 157.412 975.685 248.124L1262.5 468.955C1354.81 541.513 1408.41 653.884 1408.41 772.762V1199.84C1408.41 1318.72 1354.81 1431.09 1262.5 1503.65L975.685 1724.48C857.297 1815.19 692.703 1815.19 574.315 1724.48L287.502 1503.65C195.19 1431.09 141.588 1318.72 141.588 1199.84V772.762C141.588 653.884 195.19 541.513 287.502 468.955L574.315 248.124Z",fill:"#CCCCCC"})}),(0,a.jsx)("g",{mask:"url(#mask0)",children:(0,a.jsx)("rect",{x:"48",y:"146",width:"1454",height:"821",fill:"#CCCCCC"})}),(0,a.jsx)("path",{d:"M658.6 1303.6C658.6 1257.73 713.743 1221.6 783 1221.6C852.257 1221.6 907.4 1257.73 907.4 1303.6C907.4 1328.13 861.847 1343.6 783 1343.6C704.153 1343.6 658.6 1328.13 658.6 1303.6Z",fill:"#CCCCCC"}),(0,a.jsx)("path",{d:"M771.553 390C809.071 390 837.553 418.482 837.553 456C837.553 475.694 829.359 495.486 814.097 508.145L776.947 539.162C772.316 543.05 766.79 545.197 761.062 545.197C755.335 545.197 749.809 543.05 745.178 539.162L707.303 508.09C691.856 495.367 683.553 475.418 683.553 455.5C683.553 417.982 712.035 390 749.553 390H771.553Z",fill:"white"}),(0,a.jsx)("path",{d:"M774.5 828C743.884 828 719 852.884 719 883.5C719 914.116 743.884 939 774.5 939C805.116 939 830 914.116 830 883.5C830 852.884 805.116 828 774.5 828Z",fill:"#CCCCCC"}),(0,a.jsx)("path",{d:"M1043.07 450.583L1028.87 437.844C1028.55 437.56 1028.07 437.593 1027.79 437.917L1002.56 467.325C1002.28 467.648 1002.31 468.13 1002.64 468.413L1016.84 481.152C1017.16 481.436 1017.64 481.403 1017.93 481.079L1043.16 451.671C1043.44 451.348 1043.41 450.866 1043.07 450.583Z",fill:"white"}),(0,a.jsx)("path",{d:"M1044.49 451.93C1044.25 451.709 1043.87 451.743 1043.66 452.012L1018.13 484.509C1017.92 484.778 1017.95 485.158 1018.22 485.371L1020.46 487.151C1020.73 487.365 1021.11 487.331 1021.32 487.062L1046.85 454.565C1047.06 454.296 1047.03 453.916 1046.76 453.702L1044.49 451.93Z",fill:"white"}),(0,a.jsx)("path",{d:"M1044.53 453.612L1018.39 486.109C1018.17 486.385 1018.2 486.794 1018.46 487.033L1023.27 491.522C1023.53 491.76 1023.95 491.73 1024.18 491.454L1050.32 458.957C1050.54 458.681 1050.51 458.272 1050.25 458.033L1045.44 453.544C1045.18 453.306 1044.77 453.336 1044.53 453.612Z",fill:"white"}),(0,a.jsx)("text",{x:"50%",y:"80%",textAnchor:"middle",fill:"#222",fontSize:"300",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:t})]})};var r=s(66766);function n(e){var t;let{badge:s}=e;return(null==s?void 0:null===(t=s.badges)||void 0===t?void 0:t.length)?(0,a.jsx)("div",{className:"flex gap-3 mt-2",children:s.badges.map((e,t)=>{var s,n,d;return(0,a.jsx)(l.P.div,{className:"relative",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},children:"DailyStreak"===e.badgeType?(0,a.jsx)(i,{count:null!==(s=e.count)&&void 0!==s?s:0}):(0,a.jsx)(r.default,{src:null!==(n=e.badgeSrc)&&void 0!==n?n:"/placeholder.png",alt:null!==(d=e.badgeAlt)&&void 0!==d?d:"Badge",width:48,height:48,className:"object-contain sm:w-12 sm:h-12 w-10 h-10"})},t)})}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,2265,347,8441,1684,7358],()=>t(50240)),_N_E=e.O()}]);