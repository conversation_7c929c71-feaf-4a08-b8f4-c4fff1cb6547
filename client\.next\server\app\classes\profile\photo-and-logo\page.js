(()=>{var e={};e.id=7624,e.ids=[7624],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3416:(e,t,r)=>{"use strict";r.d(t,{sG:()=>u,hO:()=>d});var o=r(43210),n=r(51215),i=r(98599),a=r(60687),s=o.forwardRef((e,t)=>{let{children:r,...n}=e,i=o.Children.toArray(r),s=i.find(p);if(s){let e=s.props.children,r=i.map(t=>t!==s?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(l,{...n,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,a.jsx)(l,{...n,ref:t,children:r})});s.displayName="Slot";var l=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),a=function(e,t){let r={...t};for(let o in t){let n=e[o],i=t[o];/^on[A-Z]/.test(o)?n&&i?r[o]=(...e)=>{i(...e),n(...e)}:n&&(r[o]=n):"style"===o?r[o]={...n,...i}:"className"===o&&(r[o]=[n,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(a.ref=t?(0,i.t)(t,e):e),o.cloneElement(r,a)}return o.Children.count(r)>1?o.Children.only(null):null});l.displayName="SlotClone";var c=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function p(e){return o.isValidElement(e)&&e.type===c}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=o.forwardRef((e,r)=>{let{asChild:o,...n}=e,i=o?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...n,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function d(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(43210),n=r(60687);function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>o.createContext(e));return function(r){let n=r?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,i){let a=o.createContext(i),s=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,c=r?.[e]?.[s]||a,p=o.useMemo(()=>l,Object.values(l));return(0,n.jsx)(c.Provider,{value:p,children:i})};return l.displayName=t+"Provider",[l,function(r,n){let l=n?.[e]?.[s]||a,c=o.useContext(l);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:o})=>{let n=r(e)[`__scope${o}`];return{...t,...n}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}},12304:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx","Separator")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21540:e=>{var t,r,o,n,i,a,s,l,c,p,u,d,h,f,m,v=!1;function g(){if(!v){v=!0;var e=navigator.userAgent,g=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e),x=/(Mac OS X)|(Windows)|(Linux)/.exec(e);if(d=/\b(iPhone|iP[ao]d)/.exec(e),h=/\b(iP[ao]d)/.exec(e),p=/Android/i.exec(e),f=/FBAN\/\w+;/i.exec(e),m=/Mobile/i.exec(e),u=!!/Win64/.exec(e),g){(t=g[1]?parseFloat(g[1]):g[5]?parseFloat(g[5]):NaN)&&document&&document.documentMode&&(t=document.documentMode);var w=/(?:Trident\/(\d+.\d+))/.exec(e);a=w?parseFloat(w[1])+4:t,r=g[2]?parseFloat(g[2]):NaN,o=g[3]?parseFloat(g[3]):NaN,i=(n=g[4]?parseFloat(g[4]):NaN)&&(g=/(?:Chrome\/(\d+\.\d+))/.exec(e))&&g[1]?parseFloat(g[1]):NaN}else t=r=o=i=n=NaN;if(x){if(x[1]){var y=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);s=!y||parseFloat(y[1].replace("_","."))}else s=!1;l=!!x[2],c=!!x[3]}else s=l=c=!1}}var x={ie:function(){return g()||t},ieCompatibilityMode:function(){return g()||a>t},ie64:function(){return x.ie()&&u},firefox:function(){return g()||r},opera:function(){return g()||o},webkit:function(){return g()||n},safari:function(){return x.webkit()},chrome:function(){return g()||i},windows:function(){return g()||l},osx:function(){return g()||s},linux:function(){return g()||c},iphone:function(){return g()||d},mobile:function(){return g()||d||h||p||m},nativeApp:function(){return g()||f},android:function(){return g()||p},ipad:function(){return g()||h}};e.exports=x},21820:e=>{"use strict";e.exports=require("os")},23546:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes\\\\profile\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx","default")},23562:(e,t,r)=>{"use strict";r.d(t,{k:()=>a});var o=r(60687);r(43210);var n=r(25177),i=r(4780);function a({className:e,value:t,...r}){return(0,o.jsx)(n.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...r,children:(0,o.jsx)(n.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},25177:(e,t,r)=>{"use strict";r.d(t,{C1:()=>C,bL:()=>y});var o=r(43210),n=r(11273),i=r(3416),a=r(60687),s="Progress",[l,c]=(0,n.A)(s),[p,u]=l(s),d=o.forwardRef((e,t)=>{var r,o;let{__scopeProgress:n,value:s=null,max:l,getValueLabel:c=m,...u}=e;(l||0===l)&&!x(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let d=x(l)?l:100;null===s||w(s,d)||console.error((o=`${s}`,`Invalid prop \`value\` of value \`${o}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=w(s,d)?s:null,f=g(h)?c(h,d):void 0;return(0,a.jsx)(p,{scope:n,value:h,max:d,children:(0,a.jsx)(i.sG.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":g(h)?h:void 0,"aria-valuetext":f,role:"progressbar","data-state":v(h,d),"data-value":h??void 0,"data-max":d,...u,ref:t})})});d.displayName=s;var h="ProgressIndicator",f=o.forwardRef((e,t)=>{let{__scopeProgress:r,...o}=e,n=u(h,r);return(0,a.jsx)(i.sG.div,{"data-state":v(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...o,ref:t})});function m(e,t){return`${Math.round(e/t*100)}%`}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function x(e){return g(e)&&!isNaN(e)&&e>0}function w(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=h;var y=d,C=f},27910:e=>{"use strict";e.exports=require("stream")},28029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var o=r(60687),n=r(35950),i=r(85814),a=r.n(i),s=r(16189),l=r(54864),c=r(5336);function p({items:e}){let t=(0,s.usePathname)(),{completedForms:r}=(0,l.d4)(e=>e.formProgress),n=e=>e.toLowerCase().replace(/ & /g,"_").replace(/\s+/g,"_");return(0,o.jsx)("nav",{className:"space-y-1",children:e.map((i,s)=>{let l=n(i.title),p=t===i.href,u=s>0&&!r[n(e[s-1].title)];return(0,o.jsxs)(a(),{href:u?"#":i.href,className:`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${p?"bg-muted text-primary":u?"text-gray-400 cursor-not-allowed":"text-muted-foreground hover:text-primary"}`,onClick:e=>{u&&e.preventDefault()},children:[(0,o.jsx)("span",{children:i.title}),r[l]&&(0,o.jsx)(c.A,{size:16,className:"text-green-500"})]},i.href)})})}var u=r(23562),d=r(43210),h=r(28527);r(36097),r(35817);var f=r(29523),m=r(90269),v=r(46303);let g=[{title:"About",href:"/classes/profile"},{title:"Description",href:"/classes/profile/description"},{title:"Address",href:"/classes/profile/address"},{title:"Photo & Logo",href:"/classes/profile/photo-and-logo"},{title:"Education",href:"/classes/profile/education"},{title:"Experience",href:"/classes/profile/experience"},{title:"Certificates",href:"/classes/profile/certificates"},{title:"Tution Class",href:"/classes/profile/tution-class"}];function x({children:e}){let{completedSteps:t,totalSteps:r}=(0,l.d4)(e=>e.formProgress),{user:i}=function(){let e=(0,l.d4)(e=>e.user.isAuthenticated);return(0,s.useRouter)(),{user:e}}(),{user:a}=(0,l.d4)(e=>e.user);(0,l.wA)();let[c,x]=(0,d.useState)(!1),[w,y]=(0,d.useState)(!1),[C,b]=(0,d.useState)("");if(!i)return null;let S=t/r*100,P=100===Math.round(S),R=async()=>{try{x(!0),await h.S.post(`/classes-profile/send-for-review/${a.id}`),y(!0)}catch(e){console.error("Error sending for review:",e)}finally{x(!1)}};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(m.default,{}),(0,o.jsxs)("div",{className:"space-y-6 p-10 pb-4 md:block",children:[(0,o.jsxs)("div",{className:"space-y-0.5",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Edit Profile"}),(0,o.jsx)("p",{className:"text-muted-foreground",children:"Start creating your public profile. Your progress will be automatically saved as you complete each section. You can return at any time to finish your registration."})]}),(0,o.jsx)(u.k,{value:S,className:"h-2"}),(0,o.jsxs)("p",{className:"text-sm text-muted-foreground",children:[Math.round(S),"% complete"]}),P&&(0,o.jsx)("div",{className:"mt-4",children:w?(0,o.jsx)(f.$,{className:"bg-gray-400 text-white cursor-not-allowed",disabled:!0,children:"APPROVED"===C?"Profile Approved ✅":"Profile Sent for Review"}):(0,o.jsx)(f.$,{className:"bg-green-600 hover:bg-green-700 text-white",disabled:c,onClick:R,children:"Send for Review"})}),(0,o.jsx)(n.Separator,{className:"my-6"}),(0,o.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[(0,o.jsx)("aside",{className:"-mx-4 lg:w-1/6 pb-12",children:(0,o.jsx)(p,{items:g})}),(0,o.jsx)("div",{className:"flex justify-center w-full",children:(0,o.jsx)("div",{className:"flex-1 lg:max-w-2xl pb-12",children:e})})]})]}),(0,o.jsx)(v.default,{})]})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30906:(e,t,r)=>{"use strict";var o,n=r(43779);n.canUseDOM&&(o=document.implementation&&document.implementation.hasFeature&&!0!==document.implementation.hasFeature("","")),e.exports=function(e,t){if(!n.canUseDOM||t&&!("addEventListener"in document))return!1;var r="on"+e,i=r in document;if(!i){var a=document.createElement("div");a.setAttribute(r,"return;"),i="function"==typeof a[r]}return!i&&o&&"wheel"===e&&(i=document.implementation.hasFeature("Events.wheel","3.0")),i}},33873:e=>{"use strict";e.exports=require("path")},34065:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>d,tree:()=>c});var o=r(65239),n=r(48088),i=r(88170),a=r.n(i),s=r(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let c={children:["",{children:["classes",{children:["profile",{children:["photo-and-logo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81864)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,23546)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,3792)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},d=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/classes/profile/photo-and-logo/page",pathname:"/classes/profile/photo-and-logo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35817:(e,t,r)=>{"use strict";r.d(t,{Ow:()=>i,Wz:()=>n,sA:()=>a});var o=r(50346);let n=(e,t)=>{e.contactNo&&t((0,o.ac)(o._3.PROFILE)),e.ClassAbout?.tutorBio?.length>50&&t((0,o.ac)(o._3.DESCRIPTION)),e.ClassAbout?.profilePhoto&&e.ClassAbout?.classesLogo&&t((0,o.ac)(o._3.PHOTO_LOGO)),e.education?.length>0&&t((0,o.ac)(o._3.EDUCATION)),e.certificates?.length>0&&t((0,o.ac)(o._3.CERTIFICATES)),e.experience?.length>0&&t((0,o.ac)(o._3.EXPERIENCE)),e.tuitionClasses?.length>0&&t((0,o.ac)(o._3.TUTIONCLASS)),e.address&&t((0,o.ac)(o._3.ADDRESS))},i=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch{return[e]}},a=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch{return e||"N/A"}};new TextEncoder().encode("secret123")},36161:(e,t,r)=>{Promise.resolve().then(r.bind(r,23546))},42123:(e,t,r)=>{"use strict";r.d(t,{b:()=>p});var o=r(43210);r(51215);var n=r(11329),i=r(60687),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.TL)(`Primitive.${t}`),a=o.forwardRef((e,o)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...a,ref:o})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),s="horizontal",l=["horizontal","vertical"],c=o.forwardRef((e,t)=>{var r;let{decorative:o,orientation:n=s,...c}=e,p=(r=n,l.includes(r))?n:s;return(0,i.jsx)(a.div,{"data-orientation":p,...o?{role:"none"}:{"aria-orientation":"vertical"===p?p:void 0,role:"separator"},...c,ref:t})});c.displayName="Separator";var p=c},43779:e=>{"use strict";var t=!!("undefined"!=typeof window&&window.document&&window.document.createElement);e.exports={canUseDOM:t,canUseWorkers:"undefined"!=typeof Worker,canUseEventListeners:t&&!!(window.addEventListener||window.attachEvent),canUseViewport:t&&!!window.screen,isInWorker:!t}},45989:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes\\\\profile\\\\photo-and-logo\\\\photo-and-logo.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx","default")},47788:(e,t,r)=>{Promise.resolve().then(r.bind(r,65969)),Promise.resolve().then(r.bind(r,35950))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60398:(e,t,r)=>{"use strict";var o=r(21540),n=r(30906);function i(e){var t=0,r=0,o=0,n=0;return"detail"in e&&(r=e.detail),"wheelDelta"in e&&(r=-e.wheelDelta/120),"wheelDeltaY"in e&&(r=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=r,r=0),o=10*t,n=10*r,"deltaY"in e&&(n=e.deltaY),"deltaX"in e&&(o=e.deltaX),(o||n)&&e.deltaMode&&(1==e.deltaMode?(o*=40,n*=40):(o*=800,n*=800)),o&&!t&&(t=o<1?-1:1),n&&!r&&(r=n<1?-1:1),{spinX:t,spinY:r,pixelX:o,pixelY:n}}i.getEventType=function(){return o.firefox()?"DOMMouseScroll":n("wheel")?"wheel":"mousewheel"},e.exports=i},60940:(e,t,r)=>{Promise.resolve().then(r.bind(r,45989)),Promise.resolve().then(r.bind(r,12304))},61170:(e,t,r)=>{"use strict";r.d(t,{b:()=>p});var o=r(43210);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var i=r(60687),a=Symbol("radix.slottable");function s(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...i}=e;if(o.isValidElement(r)){var a;let e,s;let l=(a=r,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let r={...t};for(let o in t){let n=e[o],i=t[o];/^on[A-Z]/.test(o)?n&&i?r[o]=(...e)=>{i(...e),n(...e)}:n&&(r[o]=n):"style"===o?r[o]={...n,...i}:"className"===o&&(r[o]=[n,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==o.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,o=e.map(e=>{let o=n(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():n(e[t],null)}}}}(t,l):l),o.cloneElement(r,c)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:n,...a}=e,l=o.Children.toArray(n),c=l.find(s);if(c){let e=c.props.children,n=l.map(t=>t!==c?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,i.jsx)(t,{...a,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),a=o.forwardRef((e,o)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...a,ref:o})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),c=o.forwardRef((e,t)=>(0,i.jsx)(l.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var p=c},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65969:(e,t,r)=>{"use strict";r.d(t,{default:()=>T});var o=r(60687),n=r(63442),i=r(27605),a=r(45880),s=r(30474),l=r(43210),c=r(54864),p=r(50346),u=r(16189),d=r(52581),h=r(4363),f=r(77995),m=r.n(f);function v(e,t,r,o,n){void 0===n&&(n=0);var i=S(t.width,t.height,n),a=i.width,s=i.height;return{x:g(e.x,a,r.width,o),y:g(e.y,s,r.height,o)}}function g(e,t,r,o){var n=t*o/2-r/2;return P(e,-n,n)}function x(e,t){return Math.sqrt(Math.pow(e.y-t.y,2)+Math.pow(e.x-t.x,2))}function w(e,t){return 180*Math.atan2(t.y-e.y,t.x-e.x)/Math.PI}function y(e,t){return Math.min(e,Math.max(0,t))}function C(e,t){return t}function b(e,t){return{x:(t.x+e.x)/2,y:(t.y+e.y)/2}}function S(e,t,r){var o=r*Math.PI/180;return{width:Math.abs(Math.cos(o)*e)+Math.abs(Math.sin(o)*t),height:Math.abs(Math.sin(o)*e)+Math.abs(Math.cos(o)*t)}}function P(e,t,r){return Math.min(Math.max(e,t),r)}function R(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.filter(function(e){return"string"==typeof e&&!!(e.length>0)}).join(" ").trim()}var E=function(e){function t(){var r=null!==e&&e.apply(this,arguments)||this;return r.cropperRef=l.createRef(),r.imageRef=l.createRef(),r.videoRef=l.createRef(),r.containerPosition={x:0,y:0},r.containerRef=null,r.styleRef=null,r.containerRect=null,r.mediaSize={width:0,height:0,naturalWidth:0,naturalHeight:0},r.dragStartPosition={x:0,y:0},r.dragStartCrop={x:0,y:0},r.gestureZoomStart=0,r.gestureRotationStart=0,r.isTouching=!1,r.lastPinchDistance=0,r.lastPinchRotation=0,r.rafDragTimeout=null,r.rafPinchTimeout=null,r.wheelTimer=null,r.currentDoc="undefined"!=typeof document?document:null,r.currentWindow="undefined"!=typeof window?window:null,r.resizeObserver=null,r.state={cropSize:null,hasWheelJustStarted:!1,mediaObjectFit:void 0},r.initResizeObserver=function(){if(void 0!==window.ResizeObserver&&r.containerRef){var e=!0;r.resizeObserver=new window.ResizeObserver(function(t){if(e){e=!1;return}r.computeSizes()}),r.resizeObserver.observe(r.containerRef)}},r.preventZoomSafari=function(e){return e.preventDefault()},r.cleanEvents=function(){r.currentDoc&&(r.currentDoc.removeEventListener("mousemove",r.onMouseMove),r.currentDoc.removeEventListener("mouseup",r.onDragStopped),r.currentDoc.removeEventListener("touchmove",r.onTouchMove),r.currentDoc.removeEventListener("touchend",r.onDragStopped),r.currentDoc.removeEventListener("gesturemove",r.onGestureMove),r.currentDoc.removeEventListener("gestureend",r.onGestureEnd),r.currentDoc.removeEventListener("scroll",r.onScroll))},r.clearScrollEvent=function(){r.containerRef&&r.containerRef.removeEventListener("wheel",r.onWheel),r.wheelTimer&&clearTimeout(r.wheelTimer)},r.onMediaLoad=function(){var e=r.computeSizes();e&&(r.emitCropData(),r.setInitialCrop(e)),r.props.onMediaLoaded&&r.props.onMediaLoaded(r.mediaSize)},r.setInitialCrop=function(e){if(r.props.initialCroppedAreaPercentages){var t,o,n,i,a,s,l,c=(t=r.props.initialCroppedAreaPercentages,o=r.mediaSize,n=r.props.rotation,i=r.props.minZoom,a=r.props.maxZoom,s=S(o.width,o.height,n),{crop:{x:(l=P(e.width/s.width*(100/t.width),i,a))*s.width/2-e.width/2-s.width*l*(t.x/100),y:l*s.height/2-e.height/2-s.height*l*(t.y/100)},zoom:l}),p=c.crop,u=c.zoom;r.props.onCropChange(p),r.props.onZoomChange&&r.props.onZoomChange(u)}else if(r.props.initialCroppedAreaPixels){var d,h,f,m,v,g,x,w,y,C=(d=r.props.initialCroppedAreaPixels,h=r.mediaSize,f=r.props.rotation,m=r.props.minZoom,v=r.props.maxZoom,void 0===f&&(f=0),g=S(h.naturalWidth,h.naturalHeight,f),w=P((x=h.width>h.height?h.width/h.naturalWidth:h.height/h.naturalHeight,e.height>e.width?e.height/(d.height*x):e.width/(d.width*x)),m,v),y=e.height>e.width?e.height/d.height:e.width/d.width,{crop:{x:((g.width-d.width)/2-d.x)*y,y:((g.height-d.height)/2-d.y)*y},zoom:w}),p=C.crop,u=C.zoom;r.props.onCropChange(p),r.props.onZoomChange&&r.props.onZoomChange(u)}},r.computeSizes=function(){var e,t,o,n,i,a,s=r.imageRef.current||r.videoRef.current;if(s&&r.containerRef){r.containerRect=r.containerRef.getBoundingClientRect(),r.saveContainerPosition();var l,c,p,u,d,f,m,v,g,x,w,y=r.containerRect.width/r.containerRect.height,C=(null===(e=r.imageRef.current)||void 0===e?void 0:e.naturalWidth)||(null===(t=r.videoRef.current)||void 0===t?void 0:t.videoWidth)||0,b=(null===(o=r.imageRef.current)||void 0===o?void 0:o.naturalHeight)||(null===(n=r.videoRef.current)||void 0===n?void 0:n.videoHeight)||0,P=s.offsetWidth<C||s.offsetHeight<b,R=C/b,E=void 0;if(P)switch(r.state.mediaObjectFit){default:case"contain":E=y>R?{width:r.containerRect.height*R,height:r.containerRect.height}:{width:r.containerRect.width,height:r.containerRect.width/R};break;case"horizontal-cover":E={width:r.containerRect.width,height:r.containerRect.width/R};break;case"vertical-cover":E={width:r.containerRect.height*R,height:r.containerRect.height}}else E={width:s.offsetWidth,height:s.offsetHeight};r.mediaSize=(0,h.Cl)((0,h.Cl)({},E),{naturalWidth:C,naturalHeight:b}),r.props.setMediaSize&&r.props.setMediaSize(r.mediaSize);var j=r.props.cropSize?r.props.cropSize:(l=r.mediaSize.width,c=r.mediaSize.height,p=r.containerRect.width,u=r.containerRect.height,d=r.props.aspect,void 0===(f=r.props.rotation)&&(f=0),v=(m=S(l,c,f)).width,g=m.height,(x=Math.min(v,p))>(w=Math.min(g,u))*d?{width:w*d,height:w}:{width:x,height:x/d});return((null===(i=r.state.cropSize)||void 0===i?void 0:i.height)!==j.height||(null===(a=r.state.cropSize)||void 0===a?void 0:a.width)!==j.width)&&r.props.onCropSizeChange&&r.props.onCropSizeChange(j),r.setState({cropSize:j},r.recomputeCropPosition),r.props.setCropSize&&r.props.setCropSize(j),j}},r.saveContainerPosition=function(){if(r.containerRef){var e=r.containerRef.getBoundingClientRect();r.containerPosition={x:e.left,y:e.top}}},r.onMouseDown=function(e){r.currentDoc&&(e.preventDefault(),r.currentDoc.addEventListener("mousemove",r.onMouseMove),r.currentDoc.addEventListener("mouseup",r.onDragStopped),r.saveContainerPosition(),r.onDragStart(t.getMousePoint(e)))},r.onMouseMove=function(e){return r.onDrag(t.getMousePoint(e))},r.onScroll=function(e){r.currentDoc&&(e.preventDefault(),r.saveContainerPosition())},r.onTouchStart=function(e){r.currentDoc&&(r.isTouching=!0,(!r.props.onTouchRequest||r.props.onTouchRequest(e))&&(r.currentDoc.addEventListener("touchmove",r.onTouchMove,{passive:!1}),r.currentDoc.addEventListener("touchend",r.onDragStopped),r.saveContainerPosition(),2===e.touches.length?r.onPinchStart(e):1===e.touches.length&&r.onDragStart(t.getTouchPoint(e.touches[0]))))},r.onTouchMove=function(e){e.preventDefault(),2===e.touches.length?r.onPinchMove(e):1===e.touches.length&&r.onDrag(t.getTouchPoint(e.touches[0]))},r.onGestureStart=function(e){r.currentDoc&&(e.preventDefault(),r.currentDoc.addEventListener("gesturechange",r.onGestureMove),r.currentDoc.addEventListener("gestureend",r.onGestureEnd),r.gestureZoomStart=r.props.zoom,r.gestureRotationStart=r.props.rotation)},r.onGestureMove=function(e){if(e.preventDefault(),!r.isTouching){var o=t.getMousePoint(e),n=r.gestureZoomStart-1+e.scale;if(r.setNewZoom(n,o,{shouldUpdatePosition:!0}),r.props.onRotationChange){var i=r.gestureRotationStart+e.rotation;r.props.onRotationChange(i)}}},r.onGestureEnd=function(e){r.cleanEvents()},r.onDragStart=function(e){var t,o;r.dragStartPosition={x:e.x,y:e.y},r.dragStartCrop=(0,h.Cl)({},r.props.crop),null===(o=(t=r.props).onInteractionStart)||void 0===o||o.call(t)},r.onDrag=function(e){var t=e.x,o=e.y;r.currentWindow&&(r.rafDragTimeout&&r.currentWindow.cancelAnimationFrame(r.rafDragTimeout),r.rafDragTimeout=r.currentWindow.requestAnimationFrame(function(){if(r.state.cropSize&&void 0!==t&&void 0!==o){var e=t-r.dragStartPosition.x,n=o-r.dragStartPosition.y,i={x:r.dragStartCrop.x+e,y:r.dragStartCrop.y+n},a=r.props.restrictPosition?v(i,r.mediaSize,r.state.cropSize,r.props.zoom,r.props.rotation):i;r.props.onCropChange(a)}}))},r.onDragStopped=function(){var e,t;r.isTouching=!1,r.cleanEvents(),r.emitCropData(),null===(t=(e=r.props).onInteractionEnd)||void 0===t||t.call(e)},r.onWheel=function(e){if(r.currentWindow&&(!r.props.onWheelRequest||r.props.onWheelRequest(e))){e.preventDefault();var o=t.getMousePoint(e),n=m()(e).pixelY,i=r.props.zoom-n*r.props.zoomSpeed/200;r.setNewZoom(i,o,{shouldUpdatePosition:!0}),r.state.hasWheelJustStarted||r.setState({hasWheelJustStarted:!0},function(){var e,t;return null===(t=(e=r.props).onInteractionStart)||void 0===t?void 0:t.call(e)}),r.wheelTimer&&clearTimeout(r.wheelTimer),r.wheelTimer=r.currentWindow.setTimeout(function(){return r.setState({hasWheelJustStarted:!1},function(){var e,t;return null===(t=(e=r.props).onInteractionEnd)||void 0===t?void 0:t.call(e)})},250)}},r.getPointOnContainer=function(e,t){var o=e.x,n=e.y;if(!r.containerRect)throw Error("The Cropper is not mounted");return{x:r.containerRect.width/2-(o-t.x),y:r.containerRect.height/2-(n-t.y)}},r.getPointOnMedia=function(e){var t=e.x,o=e.y,n=r.props,i=n.crop,a=n.zoom;return{x:(t+i.x)/a,y:(o+i.y)/a}},r.setNewZoom=function(e,t,o){var n=(void 0===o?{}:o).shouldUpdatePosition;if(r.state.cropSize&&r.props.onZoomChange){var i=P(e,r.props.minZoom,r.props.maxZoom);if(void 0===n||n){var a=r.getPointOnContainer(t,r.containerPosition),s=r.getPointOnMedia(a),l={x:s.x*i-a.x,y:s.y*i-a.y},c=r.props.restrictPosition?v(l,r.mediaSize,r.state.cropSize,i,r.props.rotation):l;r.props.onCropChange(c)}r.props.onZoomChange(i)}},r.getCropData=function(){var e,t,o,n,i,a,s,l,c,p,u,d,f,m,g;return r.state.cropSize?(e=r.props.restrictPosition?v(r.props.crop,r.mediaSize,r.state.cropSize,r.props.zoom,r.props.rotation):r.props.crop,t=r.mediaSize,o=r.state.cropSize,n=r.getAspect(),i=r.props.zoom,a=r.props.rotation,s=r.props.restrictPosition,void 0===a&&(a=0),void 0===s&&(s=!0),l=s?y:C,c=S(t.width,t.height,a),p=S(t.naturalWidth,t.naturalHeight,a),u={x:l(100,((c.width-o.width/i)/2-e.x/i)/c.width*100),y:l(100,((c.height-o.height/i)/2-e.y/i)/c.height*100),width:l(100,o.width/c.width*100/i),height:l(100,o.height/c.height*100/i)},d=Math.round(l(p.width,u.width*p.width/100)),f=Math.round(l(p.height,u.height*p.height/100)),m=p.width>=p.height*n?{width:Math.round(f*n),height:f}:{width:d,height:Math.round(d/n)},g=(0,h.Cl)((0,h.Cl)({},m),{x:Math.round(l(p.width-m.width,u.x*p.width/100)),y:Math.round(l(p.height-m.height,u.y*p.height/100))}),{croppedAreaPercentages:u,croppedAreaPixels:g}):null},r.emitCropData=function(){var e=r.getCropData();if(e){var t=e.croppedAreaPercentages,o=e.croppedAreaPixels;r.props.onCropComplete&&r.props.onCropComplete(t,o),r.props.onCropAreaChange&&r.props.onCropAreaChange(t,o)}},r.emitCropAreaChange=function(){var e=r.getCropData();if(e){var t=e.croppedAreaPercentages,o=e.croppedAreaPixels;r.props.onCropAreaChange&&r.props.onCropAreaChange(t,o)}},r.recomputeCropPosition=function(){if(r.state.cropSize){var e=r.props.restrictPosition?v(r.props.crop,r.mediaSize,r.state.cropSize,r.props.zoom,r.props.rotation):r.props.crop;r.props.onCropChange(e),r.emitCropData()}},r.onKeyDown=function(e){var t,o,n=r.props,i=n.crop,a=n.onCropChange,s=n.keyboardStep,l=n.zoom,c=n.rotation,p=s;if(r.state.cropSize){e.shiftKey&&(p*=.2);var u=(0,h.Cl)({},i);switch(e.key){case"ArrowUp":u.y-=p,e.preventDefault();break;case"ArrowDown":u.y+=p,e.preventDefault();break;case"ArrowLeft":u.x-=p,e.preventDefault();break;case"ArrowRight":u.x+=p,e.preventDefault();break;default:return}r.props.restrictPosition&&(u=v(u,r.mediaSize,r.state.cropSize,l,c)),e.repeat||null===(o=(t=r.props).onInteractionStart)||void 0===o||o.call(t),a(u)}},r.onKeyUp=function(e){var t,o;switch(e.key){case"ArrowUp":case"ArrowDown":case"ArrowLeft":case"ArrowRight":e.preventDefault();break;default:return}r.emitCropData(),null===(o=(t=r.props).onInteractionEnd)||void 0===o||o.call(t)},r}return(0,h.C6)(t,e),t.prototype.componentDidMount=function(){this.currentDoc&&this.currentWindow&&(this.containerRef&&(this.containerRef.ownerDocument&&(this.currentDoc=this.containerRef.ownerDocument),this.currentDoc.defaultView&&(this.currentWindow=this.currentDoc.defaultView),this.initResizeObserver(),void 0===window.ResizeObserver&&this.currentWindow.addEventListener("resize",this.computeSizes),this.props.zoomWithScroll&&this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}),this.containerRef.addEventListener("gesturestart",this.onGestureStart)),this.currentDoc.addEventListener("scroll",this.onScroll),this.props.disableAutomaticStylesInjection||(this.styleRef=this.currentDoc.createElement("style"),this.styleRef.setAttribute("type","text/css"),this.props.nonce&&this.styleRef.setAttribute("nonce",this.props.nonce),this.styleRef.innerHTML=".reactEasyCrop_Container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  user-select: none;\n  touch-action: none;\n  cursor: move;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.reactEasyCrop_Image,\n.reactEasyCrop_Video {\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\n}\n\n.reactEasyCrop_Contain {\n  max-width: 100%;\n  max-height: 100%;\n  margin: auto;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n.reactEasyCrop_Cover_Horizontal {\n  width: 100%;\n  height: auto;\n}\n.reactEasyCrop_Cover_Vertical {\n  width: auto;\n  height: 100%;\n}\n\n.reactEasyCrop_CropArea {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  box-sizing: border-box;\n  box-shadow: 0 0 0 9999em;\n  color: rgba(0, 0, 0, 0.5);\n  overflow: hidden;\n}\n\n.reactEasyCrop_CropAreaRound {\n  border-radius: 50%;\n}\n\n.reactEasyCrop_CropAreaGrid::before {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 0;\n  bottom: 0;\n  left: 33.33%;\n  right: 33.33%;\n  border-top: 0;\n  border-bottom: 0;\n}\n\n.reactEasyCrop_CropAreaGrid::after {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 33.33%;\n  bottom: 33.33%;\n  left: 0;\n  right: 0;\n  border-left: 0;\n  border-right: 0;\n}\n",this.currentDoc.head.appendChild(this.styleRef)),this.imageRef.current&&this.imageRef.current.complete&&this.onMediaLoad(),this.props.setImageRef&&this.props.setImageRef(this.imageRef),this.props.setVideoRef&&this.props.setVideoRef(this.videoRef),this.props.setCropperRef&&this.props.setCropperRef(this.cropperRef))},t.prototype.componentWillUnmount=function(){var e,t;this.currentDoc&&this.currentWindow&&(void 0===window.ResizeObserver&&this.currentWindow.removeEventListener("resize",this.computeSizes),null===(e=this.resizeObserver)||void 0===e||e.disconnect(),this.containerRef&&this.containerRef.removeEventListener("gesturestart",this.preventZoomSafari),this.styleRef&&(null===(t=this.styleRef.parentNode)||void 0===t||t.removeChild(this.styleRef)),this.cleanEvents(),this.props.zoomWithScroll&&this.clearScrollEvent())},t.prototype.componentDidUpdate=function(e){e.rotation!==this.props.rotation?(this.computeSizes(),this.recomputeCropPosition()):e.aspect!==this.props.aspect?this.computeSizes():e.objectFit!==this.props.objectFit?this.computeSizes():e.zoom!==this.props.zoom?this.recomputeCropPosition():(null===(t=e.cropSize)||void 0===t?void 0:t.height)!==(null===(r=this.props.cropSize)||void 0===r?void 0:r.height)||(null===(o=e.cropSize)||void 0===o?void 0:o.width)!==(null===(n=this.props.cropSize)||void 0===n?void 0:n.width)?this.computeSizes():((null===(i=e.crop)||void 0===i?void 0:i.x)!==(null===(a=this.props.crop)||void 0===a?void 0:a.x)||(null===(s=e.crop)||void 0===s?void 0:s.y)!==(null===(l=this.props.crop)||void 0===l?void 0:l.y))&&this.emitCropAreaChange(),e.zoomWithScroll!==this.props.zoomWithScroll&&this.containerRef&&(this.props.zoomWithScroll?this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}):this.clearScrollEvent()),e.video!==this.props.video&&(null===(c=this.videoRef.current)||void 0===c||c.load());var t,r,o,n,i,a,s,l,c,p=this.getObjectFit();p!==this.state.mediaObjectFit&&this.setState({mediaObjectFit:p},this.computeSizes)},t.prototype.getAspect=function(){var e=this.props,t=e.cropSize,r=e.aspect;return t?t.width/t.height:r},t.prototype.getObjectFit=function(){var e,t,r,o;if("cover"===this.props.objectFit){if((this.imageRef.current||this.videoRef.current)&&this.containerRef){this.containerRect=this.containerRef.getBoundingClientRect();var n=this.containerRect.width/this.containerRect.height;return((null===(e=this.imageRef.current)||void 0===e?void 0:e.naturalWidth)||(null===(t=this.videoRef.current)||void 0===t?void 0:t.videoWidth)||0)/((null===(r=this.imageRef.current)||void 0===r?void 0:r.naturalHeight)||(null===(o=this.videoRef.current)||void 0===o?void 0:o.videoHeight)||0)<n?"horizontal-cover":"vertical-cover"}return"horizontal-cover"}return this.props.objectFit},t.prototype.onPinchStart=function(e){var r=t.getTouchPoint(e.touches[0]),o=t.getTouchPoint(e.touches[1]);this.lastPinchDistance=x(r,o),this.lastPinchRotation=w(r,o),this.onDragStart(b(r,o))},t.prototype.onPinchMove=function(e){var r=this;if(this.currentDoc&&this.currentWindow){var o=t.getTouchPoint(e.touches[0]),n=t.getTouchPoint(e.touches[1]),i=b(o,n);this.onDrag(i),this.rafPinchTimeout&&this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout),this.rafPinchTimeout=this.currentWindow.requestAnimationFrame(function(){var e=x(o,n),t=r.props.zoom*(e/r.lastPinchDistance);r.setNewZoom(t,i,{shouldUpdatePosition:!1}),r.lastPinchDistance=e;var a=w(o,n),s=r.props.rotation+(a-r.lastPinchRotation);r.props.onRotationChange&&r.props.onRotationChange(s),r.lastPinchRotation=a})}},t.prototype.render=function(){var e,t=this,r=this.props,o=r.image,n=r.video,i=r.mediaProps,a=r.cropperProps,s=r.transform,c=r.crop,p=c.x,u=c.y,d=r.rotation,f=r.zoom,m=r.cropShape,v=r.showGrid,g=r.style,x=g.containerStyle,w=g.cropAreaStyle,y=g.mediaStyle,C=r.classes,b=C.containerClassName,S=C.cropAreaClassName,P=C.mediaClassName,E=null!==(e=this.state.mediaObjectFit)&&void 0!==e?e:this.getObjectFit();return l.createElement("div",{onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart,ref:function(e){return t.containerRef=e},"data-testid":"container",style:x,className:R("reactEasyCrop_Container",b)},o?l.createElement("img",(0,h.Cl)({alt:"",className:R("reactEasyCrop_Image","contain"===E&&"reactEasyCrop_Contain","horizontal-cover"===E&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===E&&"reactEasyCrop_Cover_Vertical",P)},i,{src:o,ref:this.imageRef,style:(0,h.Cl)((0,h.Cl)({},y),{transform:s||"translate(".concat(p,"px, ").concat(u,"px) rotate(").concat(d,"deg) scale(").concat(f,")")}),onLoad:this.onMediaLoad})):n&&l.createElement("video",(0,h.Cl)({autoPlay:!0,playsInline:!0,loop:!0,muted:!0,className:R("reactEasyCrop_Video","contain"===E&&"reactEasyCrop_Contain","horizontal-cover"===E&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===E&&"reactEasyCrop_Cover_Vertical",P)},i,{ref:this.videoRef,onLoadedMetadata:this.onMediaLoad,style:(0,h.Cl)((0,h.Cl)({},y),{transform:s||"translate(".concat(p,"px, ").concat(u,"px) rotate(").concat(d,"deg) scale(").concat(f,")")}),controls:!1}),(Array.isArray(n)?n:[{src:n}]).map(function(e){return l.createElement("source",(0,h.Cl)({key:e.src},e))})),this.state.cropSize&&l.createElement("div",(0,h.Cl)({ref:this.cropperRef,style:(0,h.Cl)((0,h.Cl)({},w),{width:this.state.cropSize.width,height:this.state.cropSize.height}),tabIndex:0,onKeyDown:this.onKeyDown,onKeyUp:this.onKeyUp,"data-testid":"cropper",className:R("reactEasyCrop_CropArea","round"===m&&"reactEasyCrop_CropAreaRound",v&&"reactEasyCrop_CropAreaGrid",S)},a)))},t.defaultProps={zoom:1,rotation:0,aspect:4/3,maxZoom:3,minZoom:1,cropShape:"rect",objectFit:"contain",showGrid:!0,style:{},classes:{},mediaProps:{},cropperProps:{},zoomSpeed:1,restrictPosition:!0,zoomWithScroll:!0,keyboardStep:1},t.getMousePoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},t.getTouchPoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},t}(l.Component),j=r(80942),z=r(29523),D=r(89667),N=r(28527),M=r(20672);let A=["image/jpeg","image/png","image/gif","image/webp"],_=a.z.object({photo:a.z.instanceof(File).refine(e=>e?.size<2097152,{message:"Photo must be less than 2MB"}).refine(e=>!e||A.includes(e.type),{message:"Only image files (JPEG, PNG, GIF, WEBP) are allowed"}).optional(),logo:a.z.instanceof(File).refine(e=>e?.size<1048576,{message:"Logo must be less than 1MB"}).refine(e=>!e||A.includes(e.type),{message:"Only image files (JPEG, PNG, GIF, WEBP) are allowed"}).optional()});function T(){let[e,t]=(0,l.useState)(null),[r,a]=(0,l.useState)(null),[h,f]=(0,l.useState)(null),[m,v]=(0,l.useState)(null),[g,x]=(0,l.useState)(null),[w,y]=(0,l.useState)(null),[C,b]=(0,l.useState)({x:0,y:0}),[S,P]=(0,l.useState)(1),[R,T]=(0,l.useState)(null),O=(0,i.mN)({resolver:(0,n.u)(_),mode:"onChange",defaultValues:{photo:void 0,logo:void 0}}),W=(0,c.wA)(),L=(0,u.useRouter)(),I=(e,t)=>{let r=e.target.files?.[0];if(r){if(!A.includes(r.type)){d.toast.error("Only image files (JPEG, PNG, GIF, WEBP) are allowed"),e.target.value="";return}let o=new FileReader;o.onload=()=>{y(o.result),x(t)},o.readAsDataURL(r)}},F=async(e,t)=>{let r=new window.Image;r.src=e,await new Promise(e=>r.onload=e);let o=document.createElement("canvas"),n=o.getContext("2d");return o.width=t.width,o.height=t.height,n.drawImage(r,t.x,t.y,t.width,t.height,0,0,t.width,t.height),new Promise(e=>{o.toBlob(t=>{t&&e(new File([t],"cropped-image.jpg",{type:"image/jpeg"}))},"image/jpeg")})},G=async()=>{if(w&&R)try{let e=await F(w,R),r=URL.createObjectURL(e);"photo"===g?(O.setValue("photo",e,{shouldValidate:!0}),t(r)):"logo"===g&&(O.setValue("logo",e,{shouldValidate:!0}),a(r)),x(null),y(null),b({x:0,y:0}),P(1)}catch(e){console.error("Error cropping image:",e),d.toast.error("Failed to crop image")}},{user:k}=(0,c.d4)(e=>e.user),U=async e=>{try{let t=new FormData;e.photo&&t.append("profilePhoto",e.photo),e.logo&&t.append("classesLogo",e.logo),await N.S.post("/classes-profile/images",t,{headers:{"Content-Type":"multipart/form-data"}}),await W((0,M.V)(k.id)),d.toast.success("Photos uploaded successfully!"),W((0,p.ac)(p._3.PHOTO_LOGO)),L.push("/classes/profile/education")}catch(e){console.error("Error uploading files:",e),d.toast.error("Failed to upload files")}};return(0,c.d4)(e=>e.class.classData),(0,o.jsxs)("div",{children:[(0,o.jsx)(j.lV,{...O,children:(0,o.jsxs)("form",{onSubmit:O.handleSubmit(U),className:"space-y-6",children:[(0,o.jsx)(j.zB,{control:O.control,name:"photo",render:()=>(0,o.jsxs)(j.eI,{children:[(0,o.jsx)(j.lR,{children:"Profile Photo"}),(0,o.jsx)(j.MJ,{children:(0,o.jsx)(D.p,{type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>I(e,"photo")})}),e||h?(0,o.jsx)(s.default,{src:e||h,alt:"Profile Preview",width:120,height:120,className:"rounded-full mt-2 border"}):null,(0,o.jsx)(j.C5,{})]})}),(0,o.jsx)(j.zB,{control:O.control,name:"logo",render:()=>(0,o.jsxs)(j.eI,{children:[(0,o.jsx)(j.lR,{children:"Classes Logo"}),(0,o.jsx)(j.MJ,{children:(0,o.jsx)(D.p,{type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>I(e,"logo")})}),r||m?(0,o.jsx)(s.default,{src:r||m,alt:"Logo Preview",width:120,height:120,className:"rounded-md mt-2 border bg-white"}):null,(0,o.jsx)(j.C5,{})]})}),(0,o.jsx)(z.$,{type:"submit",children:"Upload"})]})}),g&&w&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg w-[90%] max-w-[500px]",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Crop Image"}),(0,o.jsx)("div",{className:"relative w-full h-[300px]",children:(0,o.jsx)(E,{image:w,crop:C,zoom:S,aspect:"photo"===g?1:4/3,onCropChange:b,onZoomChange:P,onCropComplete:(e,t)=>{T(t)}})}),(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsx)("input",{type:"range",min:1,max:3,step:.1,value:S,onChange:e=>P(Number(e.target.value)),className:"w-full"})}),(0,o.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,o.jsx)(z.$,{variant:"outline",onClick:()=>x(null),children:"Cancel"}),(0,o.jsx)(z.$,{onClick:G,children:"Save Crop"})]})]})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},77995:(e,t,r)=>{e.exports=r(60398)},79551:e=>{"use strict";e.exports=require("url")},80942:(e,t,r)=>{"use strict";r.d(t,{lV:()=>p,MJ:()=>g,Rr:()=>x,zB:()=>d,eI:()=>m,lR:()=>v,C5:()=>w});var o=r(60687),n=r(43210),i=r(11329),a=r(27605),s=r(4780),l=r(61170);function c({className:e,...t}){return(0,o.jsx)(l.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}let p=a.Op,u=n.createContext({}),d=({...e})=>(0,o.jsx)(u.Provider,{value:{name:e.name},children:(0,o.jsx)(a.xI,{...e})}),h=()=>{let e=n.useContext(u),t=n.useContext(f),{getFieldState:r}=(0,a.xW)(),o=(0,a.lN)({name:e.name}),i=r(e.name,o);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:`${s}-form-item`,formDescriptionId:`${s}-form-item-description`,formMessageId:`${s}-form-item-message`,...i}},f=n.createContext({});function m({className:e,...t}){let r=n.useId();return(0,o.jsx)(f.Provider,{value:{id:r},children:(0,o.jsx)("div",{"data-slot":"form-item",className:(0,s.cn)("grid gap-2",e),...t})})}function v({className:e,...t}){let{error:r,formItemId:n}=h();return(0,o.jsx)(c,{"data-slot":"form-label","data-error":!!r,className:(0,s.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...t})}function g({...e}){let{error:t,formItemId:r,formDescriptionId:n,formMessageId:a}=h();return(0,o.jsx)(i.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${n} ${a}`:`${n}`,"aria-invalid":!!t,...e})}function x({className:e,...t}){let{formDescriptionId:r}=h();return(0,o.jsx)("p",{"data-slot":"form-description",id:r,className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function w({className:e,...t}){let{error:r,formMessageId:n}=h(),i=r?String(r?.message??""):t.children;return i?(0,o.jsx)("p",{"data-slot":"form-message",id:n,className:(0,s.cn)("text-destructive text-sm",e),...t,children:i}):null}},81630:e=>{"use strict";e.exports=require("http")},81864:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var o=r(37413),n=r(12304),i=r(45989);function a(){return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-medium",children:"Upload your image and classes logo"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose a photo that will help learners get to know you."})]}),(0,o.jsx)(n.Separator,{}),(0,o.jsx)(i.default,{})]})}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},94990:(e,t,r)=>{Promise.resolve().then(r.bind(r,28029))},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>i});var o=r(43210);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,o=e.map(e=>{let o=n(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():n(e[t],null)}}}}function a(...e){return o.useCallback(i(...e),e)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,7013,2105,9191,2800,7200],()=>r(34065));module.exports=o})();