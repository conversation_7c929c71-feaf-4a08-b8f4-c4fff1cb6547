"use strict";exports.id=3099,exports.ids=[3099],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(43210);r(60687);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}},3416:(e,t,r)=>{r.d(t,{sG:()=>c,hO:()=>f});var n=r(43210),o=r(51215),l=r(98599),i=r(60687),a=n.forwardRef((e,t)=>{let{children:r,...o}=e,l=n.Children.toArray(r),a=l.find(d);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(s,{...o,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,l.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===u}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,l=n?a:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function f(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},11273:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(43210),o=r(60687);function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;r=[...r,l];let s=t=>{let{scope:r,children:l,...s}=t,u=r?.[e]?.[a]||i,d=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:d,children:l})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},13495:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(43210);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},50039:(e,t,r)=>{r.d(t,{UC:()=>tn,In:()=>tt,q7:()=>tl,VF:()=>ta,p4:()=>ti,ZL:()=>tr,bL:()=>e8,wn:()=>tu,PP:()=>ts,l9:()=>e7,WT:()=>te,LM:()=>to});var n,o=r(43210),l=r(51215);function i(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(70569),s=r(72031),u=r(98599),d=r(11273),c=r(43),f=r(3416),p=r(13495),m=r(60687),v="dismissableLayer.update",h=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:s,onInteractOutside:d,onDismiss:c,...y}=e,x=o.useContext(h),[b,E]=o.useState(null),C=b?.ownerDocument??globalThis?.document,[,S]=o.useState({}),j=(0,u.s)(t,e=>E(e)),R=Array.from(x.layers),[N]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),P=R.indexOf(N),A=b?R.indexOf(b):-1,T=x.layersWithOutsidePointerEventsDisabled.size>0,L=A>=P,D=function(e,t=globalThis?.document){let r=(0,p.c)(e),n=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){w("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",l.current),l.current=n,t.addEventListener("click",l.current,{once:!0})):n()}else t.removeEventListener("click",l.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",l.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...x.branches].some(e=>e.contains(t));!L||r||(i?.(e),d?.(e),e.defaultPrevented||c?.())},C),k=function(e,t=globalThis?.document){let r=(0,p.c)(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&w("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(s?.(e),d?.(e),e.defaultPrevented||c?.())},C);return function(e,t=globalThis?.document){let r=(0,p.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{A===x.layers.size-1&&(l?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},C),o.useEffect(()=>{if(b)return r&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(b)),x.layers.add(b),g(),()=>{r&&1===x.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[b,C,r,x]),o.useEffect(()=>()=>{b&&(x.layers.delete(b),x.layersWithOutsidePointerEventsDisabled.delete(b),g())},[b,x]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,m.jsx)(f.sG.div,{...y,ref:j,style:{pointerEvents:T?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,D.onPointerDownCapture)})});function g(){let e=new CustomEvent(v);document.dispatchEvent(e)}function w(e,t,r,{discrete:n}){let o=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,f.hO)(o,l):o.dispatchEvent(l)}y.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(h),n=o.useRef(null),l=(0,u.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,m.jsx)(f.sG.div,{...e,ref:l})}).displayName="DismissableLayerBranch";var x=0;function b(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var E="focusScope.autoFocusOnMount",C="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},j=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:l,onUnmountAutoFocus:i,...a}=e,[s,d]=o.useState(null),c=(0,p.c)(l),v=(0,p.c)(i),h=o.useRef(null),y=(0,u.s)(t,e=>d(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(g.paused||!s)return;let t=e.target;s.contains(t)?h.current=t:P(h.current,{select:!0})},t=function(e){if(g.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||P(h.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&P(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,g.paused]),o.useEffect(()=>{if(s){A.add(g);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(E,S);s.addEventListener(E,c),s.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(P(n,{select:t}),document.activeElement!==r)return}(R(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&P(s))}return()=>{s.removeEventListener(E,c),setTimeout(()=>{let t=new CustomEvent(C,S);s.addEventListener(C,v),s.dispatchEvent(t),t.defaultPrevented||P(e??document.body,{select:!0}),s.removeEventListener(C,v),A.remove(g)},0)}}},[s,c,v,g]);let w=o.useCallback(e=>{if(!r&&!n||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,l]=function(e){let t=R(e);return[N(t,e),N(t.reverse(),e)]}(t);n&&l?e.shiftKey||o!==l?e.shiftKey&&o===n&&(e.preventDefault(),r&&P(l,{select:!0})):(e.preventDefault(),r&&P(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,g.paused]);return(0,m.jsx)(f.sG.div,{tabIndex:-1,...a,ref:y,onKeyDown:w})});function R(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function N(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function P(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}j.displayName="FocusScope";var A=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=T(e,t)).unshift(t)},remove(t){e=T(e,t),e[0]?.resume()}}}();function T(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var L=r(96963),D=r(4503),k=r(25605),I=o.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...l}=e;return(0,m.jsx)(f.sG.svg,{...l,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,m.jsx)("polygon",{points:"0,0 30,0 15,10"})})});I.displayName="Arrow";var O=r(66156),M="Popper",[F,W]=(0,d.A)(M),[B,H]=F(M),V=e=>{let{__scopePopper:t,children:r}=e,[n,l]=o.useState(null);return(0,m.jsx)(B,{scope:t,anchor:n,onAnchorChange:l,children:r})};V.displayName=M;var _="PopperAnchor",G=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...l}=e,i=H(_,r),a=o.useRef(null),s=(0,u.s)(t,a);return o.useEffect(()=>{i.onAnchorChange(n?.current||a.current)}),n?null:(0,m.jsx)(f.sG.div,{...l,ref:s})});G.displayName=_;var K="PopperContent",[$,z]=F(K),U=o.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:l=0,align:i="center",alignOffset:a=0,arrowPadding:s=0,avoidCollisions:d=!0,collisionBoundary:c=[],collisionPadding:v=0,sticky:h="partial",hideWhenDetached:y=!1,updatePositionStrategy:g="optimized",onPlaced:w,...x}=e,b=H(K,r),[E,C]=o.useState(null),S=(0,u.s)(t,e=>C(e)),[j,R]=o.useState(null),N=function(e){let[t,r]=o.useState(void 0);return(0,O.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(j),P=N?.width??0,A=N?.height??0,T="number"==typeof v?v:{top:0,right:0,bottom:0,left:0,...v},L=Array.isArray(c)?c:[c],I=L.length>0,M={padding:T,boundary:L.filter(Z),altBoundary:I},{refs:F,floatingStyles:W,placement:B,isPositioned:V,middlewareData:_}=(0,D.we)({strategy:"fixed",placement:n+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(0,k.ll)(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[(0,D.cY)({mainAxis:l+A,alignmentAxis:a}),d&&(0,D.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?(0,D.ER)():void 0,...M}),d&&(0,D.UU)({...M}),(0,D.Ej)({...M,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:l}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${r}px`),i.setProperty("--radix-popper-available-height",`${n}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${l}px`)}}),j&&(0,D.UE)({element:j,padding:s}),J({arrowWidth:P,arrowHeight:A}),y&&(0,D.jD)({strategy:"referenceHidden",...M})]}),[G,z]=Q(B),U=(0,p.c)(w);(0,O.N)(()=>{V&&U?.()},[V,U]);let q=_.arrow?.x,Y=_.arrow?.y,X=_.arrow?.centerOffset!==0,[ee,et]=o.useState();return(0,O.N)(()=>{E&&et(window.getComputedStyle(E).zIndex)},[E]),(0,m.jsx)("div",{ref:F.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:V?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,m.jsx)($,{scope:r,placedSide:G,onArrowChange:R,arrowX:q,arrowY:Y,shouldHideArrow:X,children:(0,m.jsx)(f.sG.div,{"data-side":G,"data-align":z,...x,ref:S,style:{...x.style,animation:V?void 0:"none"}})})})});U.displayName=K;var q="PopperArrow",Y={top:"bottom",right:"left",bottom:"top",left:"right"},X=o.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=z(q,r),l=Y[o.placedSide];return(0,m.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,m.jsx)(I,{...n,ref:t,style:{...n.style,display:"block"}})})});function Z(e){return null!==e}X.displayName=q;var J=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,l=o.arrow?.centerOffset!==0,i=l?0:e.arrowWidth,a=l?0:e.arrowHeight,[s,u]=Q(r),d={start:"0%",center:"50%",end:"100%"}[u],c=(o.arrow?.x??0)+i/2,f=(o.arrow?.y??0)+a/2,p="",m="";return"bottom"===s?(p=l?d:`${c}px`,m=`${-a}px`):"top"===s?(p=l?d:`${c}px`,m=`${n.floating.height+a}px`):"right"===s?(p=`${-a}px`,m=l?d:`${f}px`):"left"===s&&(p=`${n.floating.width+a}px`,m=l?d:`${f}px`),{data:{x:p,y:m}}}});function Q(e){let[t,r="center"]=e.split("-");return[t,r]}var ee=o.forwardRef((e,t)=>{let{container:r,...n}=e,[i,a]=o.useState(!1);(0,O.N)(()=>a(!0),[]);let s=r||i&&globalThis?.document?.body;return s?l.createPortal((0,m.jsx)(f.sG.div,{...n,ref:t}),s):null});ee.displayName="Portal";var et=o.forwardRef((e,t)=>{let{children:r,...n}=e,l=o.Children.toArray(r),i=l.find(eo);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,m.jsx)(er,{...n,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,m.jsx)(er,{...n,ref:t,children:r})});et.displayName="Slot";var er=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),l=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(l.ref=t?(0,u.t)(t,e):e),o.cloneElement(r,l)}return o.Children.count(r)>1?o.Children.only(null):null});er.displayName="SlotClone";var en=({children:e})=>(0,m.jsx)(m.Fragment,{children:e});function eo(e){return o.isValidElement(e)&&e.type===en}var el=r(65551),ei=o.forwardRef((e,t)=>(0,m.jsx)(f.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ei.displayName="VisuallyHidden";var ea=r(63376),es=r(11490),eu=[" ","Enter","ArrowUp","ArrowDown"],ed=[" ","Enter"],ec="Select",[ef,ep,em]=(0,s.N)(ec),[ev,eh]=(0,d.A)(ec,[em,W]),ey=W(),[eg,ew]=ev(ec),[ex,eb]=ev(ec),eE=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:h,form:y}=e,g=ey(t),[w,x]=o.useState(null),[b,E]=o.useState(null),[C,S]=o.useState(!1),j=(0,c.jH)(d),[R=!1,N]=(0,el.i)({prop:n,defaultProp:l,onChange:i}),[P,A]=(0,el.i)({prop:a,defaultProp:s,onChange:u}),T=o.useRef(null),D=!w||y||!!w.closest("form"),[k,I]=o.useState(new Set),O=Array.from(k).map(e=>e.props.value).join(";");return(0,m.jsx)(V,{...g,children:(0,m.jsxs)(eg,{required:h,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:(0,L.B)(),value:P,onValueChange:A,open:R,onOpenChange:N,dir:j,triggerPointerDownPosRef:T,disabled:v,children:[(0,m.jsx)(ef.Provider,{scope:t,children:(0,m.jsx)(ex,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{I(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{I(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),D?(0,m.jsxs)(e2,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>A(e.target.value),disabled:v,form:y,children:[void 0===P?(0,m.jsx)("option",{value:""}):null,Array.from(k)]},O):null]})})};eE.displayName=ec;var eC="SelectTrigger",eS=o.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...l}=e,i=ey(r),s=ew(eC,r),d=s.disabled||n,c=(0,u.s)(t,s.onTriggerChange),p=ep(r),v=o.useRef("touch"),[h,y,g]=e9(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=e4(t,e,r);void 0!==n&&s.onValueChange(n.value)}),w=e=>{d||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,m.jsx)(G,{asChild:!0,...i,children:(0,m.jsx)(f.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":e3(s.value)?"":void 0,...l,ref:c,onClick:(0,a.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==v.current&&w(e)}),onPointerDown:(0,a.m)(l.onPointerDown,e=>{v.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,a.m)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&eu.includes(e.key)&&(w(),e.preventDefault())})})})});eS.displayName=eC;var ej="SelectValue",eR=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:l,placeholder:i="",...a}=e,s=ew(ej,r),{onValueNodeHasChildrenChange:d}=s,c=void 0!==l,p=(0,u.s)(t,s.onValueNodeChange);return(0,O.N)(()=>{d(c)},[d,c]),(0,m.jsx)(f.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:e3(s.value)?(0,m.jsx)(m.Fragment,{children:i}):l})});eR.displayName=ej;var eN=o.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,m.jsx)(f.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});eN.displayName="SelectIcon";var eP=e=>(0,m.jsx)(ee,{asChild:!0,...e});eP.displayName="SelectPortal";var eA="SelectContent",eT=o.forwardRef((e,t)=>{let r=ew(eA,e.__scopeSelect),[n,i]=o.useState();return((0,O.N)(()=>{i(new DocumentFragment)},[]),r.open)?(0,m.jsx)(ek,{...e,ref:t}):n?l.createPortal((0,m.jsx)(eL,{scope:e.__scopeSelect,children:(0,m.jsx)(ef.Slot,{scope:e.__scopeSelect,children:(0,m.jsx)("div",{children:e.children})})}),n):null});eT.displayName=eA;var[eL,eD]=ev(eA),ek=o.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:s,side:d,sideOffset:c,align:f,alignOffset:p,arrowPadding:v,collisionBoundary:h,collisionPadding:g,sticky:w,hideWhenDetached:E,avoidCollisions:C,...S}=e,R=ew(eA,r),[N,P]=o.useState(null),[A,T]=o.useState(null),L=(0,u.s)(t,e=>P(e)),[D,k]=o.useState(null),[I,O]=o.useState(null),M=ep(r),[F,W]=o.useState(!1),B=o.useRef(!1);o.useEffect(()=>{if(N)return(0,ea.Eq)(N)},[N]),o.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??b()),document.body.insertAdjacentElement("beforeend",e[1]??b()),x++,()=>{1===x&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),x--}},[]);let H=o.useCallback(e=>{let[t,...r]=M().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&A&&(A.scrollTop=0),r===n&&A&&(A.scrollTop=A.scrollHeight),r?.focus(),document.activeElement!==o))return},[M,A]),V=o.useCallback(()=>H([D,N]),[H,D,N]);o.useEffect(()=>{F&&V()},[F,V]);let{onOpenChange:_,triggerPointerDownPosRef:G}=R;o.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(G.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(G.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||_(!1),document.removeEventListener("pointermove",t),G.current=null};return null!==G.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,_,G]),o.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[K,$]=e9(e=>{let t=M().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=e4(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),z=o.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==R.value&&R.value===t||n)&&(k(e),n&&(B.current=!0))},[R.value]),U=o.useCallback(()=>N?.focus(),[N]),q=o.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==R.value&&R.value===t||n)&&O(e)},[R.value]),Y="popper"===n?eO:eI,X=Y===eO?{side:d,sideOffset:c,align:f,alignOffset:p,arrowPadding:v,collisionBoundary:h,collisionPadding:g,sticky:w,hideWhenDetached:E,avoidCollisions:C}:{};return(0,m.jsx)(eL,{scope:r,content:N,viewport:A,onViewportChange:T,itemRefCallback:z,selectedItem:D,onItemLeave:U,itemTextRefCallback:q,focusSelectedItem:V,selectedItemText:I,position:n,isPositioned:F,searchRef:K,children:(0,m.jsx)(es.A,{as:et,allowPinchZoom:!0,children:(0,m.jsx)(j,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(l,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,m.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,m.jsx)(Y,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...S,...X,onPlaced:()=>W(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,a.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});ek.displayName="SelectContentImpl";var eI=o.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...l}=e,a=ew(eA,r),s=eD(eA,r),[d,c]=o.useState(null),[p,v]=o.useState(null),h=(0,u.s)(t,e=>v(e)),y=ep(r),g=o.useRef(!1),w=o.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:C}=s,S=o.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&p&&x&&b&&E){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==a.dir){let n=o.left-t.left,l=r.left-n,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),c=i(l,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let n=t.right-o.right,l=window.innerWidth-r.right-n,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),c=i(l,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let l=y(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),m=parseInt(c.paddingTop,10),v=parseInt(c.borderBottomWidth,10),h=f+m+u+parseInt(c.paddingBottom,10)+v,w=Math.min(5*b.offsetHeight,h),C=window.getComputedStyle(x),S=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,N=b.offsetHeight/2,P=f+m+(b.offsetTop+N);if(P<=R){let e=l.length>0&&b===l[l.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-R,N+(e?j:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+v);d.style.height=P+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;d.style.top="0px";let t=Math.max(R,f+x.offsetTop+(e?S:0)+N);d.style.height=t+(h-P)+"px",x.scrollTop=P-R+x.offsetTop}d.style.margin="10px 0",d.style.minHeight=w+"px",d.style.maxHeight=s+"px",n?.(),requestAnimationFrame(()=>g.current=!0)}},[y,a.trigger,a.valueNode,d,p,x,b,E,a.dir,n]);(0,O.N)(()=>S(),[S]);let[j,R]=o.useState();(0,O.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let N=o.useCallback(e=>{e&&!0===w.current&&(S(),C?.(),w.current=!1)},[S,C]);return(0,m.jsx)(eM,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:g,onScrollButtonChange:N,children:(0,m.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,m.jsx)(f.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});eI.displayName="SelectItemAlignedPosition";var eO=o.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...l}=e,i=ey(r);return(0,m.jsx)(U,{...i,...l,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eO.displayName="SelectPopperPosition";var[eM,eF]=ev(eA,{}),eW="SelectViewport",eB=o.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...l}=e,i=eD(eW,r),s=eF(eW,r),d=(0,u.s)(t,i.onViewportChange),c=o.useRef(0);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,m.jsx)(ef.Slot,{scope:r,children:(0,m.jsx)(f.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let l=o+e,i=Math.min(n,l),a=l-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eB.displayName=eW;var eH="SelectGroup",[eV,e_]=ev(eH);o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,L.B)();return(0,m.jsx)(eV,{scope:r,id:o,children:(0,m.jsx)(f.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})}).displayName=eH;var eG="SelectLabel";o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=e_(eG,r);return(0,m.jsx)(f.sG.div,{id:o.id,...n,ref:t})}).displayName=eG;var eK="SelectItem",[e$,ez]=ev(eK),eU=o.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:l=!1,textValue:i,...s}=e,d=ew(eK,r),c=eD(eK,r),p=d.value===n,[v,h]=o.useState(i??""),[y,g]=o.useState(!1),w=(0,u.s)(t,e=>c.itemRefCallback?.(e,n,l)),x=(0,L.B)(),b=o.useRef("touch"),E=()=>{l||(d.onValueChange(n),d.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,m.jsx)(e$,{scope:r,value:n,disabled:l,textId:x,isSelected:p,onItemTextChange:o.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,m.jsx)(ef.ItemSlot,{scope:r,value:n,disabled:l,textValue:v,children:(0,m.jsx)(f.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":y?"":void 0,"aria-selected":p&&y,"data-state":p?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...s,ref:w,onFocus:(0,a.m)(s.onFocus,()=>g(!0)),onBlur:(0,a.m)(s.onBlur,()=>g(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{b.current=e.pointerType,l?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(ed.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eU.displayName=eK;var eq="SelectItemText",eY=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:i,...a}=e,s=ew(eq,r),d=eD(eq,r),c=ez(eq,r),p=eb(eq,r),[v,h]=o.useState(null),y=(0,u.s)(t,e=>h(e),c.onItemTextChange,e=>d.itemTextRefCallback?.(e,c.value,c.disabled)),g=v?.textContent,w=o.useMemo(()=>(0,m.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value),[c.disabled,c.value,g]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=p;return(0,O.N)(()=>(x(w),()=>b(w)),[x,b,w]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(f.sG.span,{id:c.textId,...a,ref:y}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(a.children,s.valueNode):null]})});eY.displayName=eq;var eX="SelectItemIndicator",eZ=o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ez(eX,r).isSelected?(0,m.jsx)(f.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eZ.displayName=eX;var eJ="SelectScrollUpButton",eQ=o.forwardRef((e,t)=>{let r=eD(eJ,e.__scopeSelect),n=eF(eJ,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,u.s)(t,n.onScrollButtonChange);return(0,O.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,m.jsx)(e6,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eQ.displayName=eJ;var e0="SelectScrollDownButton",e1=o.forwardRef((e,t)=>{let r=eD(e0,e.__scopeSelect),n=eF(e0,e.__scopeSelect),[l,i]=o.useState(!1),a=(0,u.s)(t,n.onScrollButtonChange);return(0,O.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,m.jsx)(e6,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});e1.displayName=e0;var e6=o.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...l}=e,i=eD("SelectScrollButton",r),s=o.useRef(null),u=ep(r),d=o.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return o.useEffect(()=>()=>d(),[d]),(0,O.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,m.jsx)(f.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,a.m)(l.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,a.m)(l.onPointerLeave,()=>{d()})})});o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,m.jsx)(f.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var e5="SelectArrow";function e3(e){return""===e||void 0===e}o.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ey(r),l=ew(e5,r),i=eD(e5,r);return l.open&&"popper"===i.position?(0,m.jsx)(X,{...o,...n,ref:t}):null}).displayName=e5;var e2=o.forwardRef((e,t)=>{let{value:r,...n}=e,l=o.useRef(null),i=(0,u.s)(t,l),a=function(e){let t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return o.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,m.jsx)(ei,{asChild:!0,children:(0,m.jsx)("select",{...n,ref:i,defaultValue:r})})});function e9(e){let t=(0,p.c)(e),r=o.useRef(""),n=o.useRef(0),l=o.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=o.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,l,i]}function e4(e,t,r){var n,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,o=Math.max(i,0),n.map((e,t)=>n[(o+t)%n.length]));1===l.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==r?s:void 0}e2.displayName="BubbleSelect";var e8=eE,e7=eS,te=eR,tt=eN,tr=eP,tn=eT,to=eB,tl=eU,ti=eY,ta=eZ,ts=eQ,tu=e1},65551:(e,t,r)=>{r.d(t,{i:()=>l});var n=r(43210),o=r(13495);function l({prop:e,defaultProp:t,onChange:r=()=>{}}){let[l,i]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[l]=r,i=n.useRef(l),a=(0,o.c)(t);return n.useEffect(()=>{i.current!==l&&(a(l),i.current=l)},[l,i,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,s=a?e:l,u=(0,o.c)(r);return[s,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else i(t)},[a,e,i,u])]}},66156:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(43210),o=globalThis?.document?n.useLayoutEffect:()=>{}},70569:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},72031:(e,t,r)=>{r.d(t,{N:()=>c});var n=r(43210),o=r(11273),l=r(98599),i=r(60687),a=n.forwardRef((e,t)=>{let{children:r,...o}=e,l=n.Children.toArray(r),a=l.find(d);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(s,{...o,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,l.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===u}function c(e){let t=e+"CollectionProvider",[r,s]=(0,o.A)(t),[u,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,o=n.useRef(null),l=n.useRef(new Map).current;return(0,i.jsx)(u,{scope:t,itemMap:l,collectionRef:o,children:r})};c.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=d(f,r),s=(0,l.s)(t,o.collectionRef);return(0,i.jsx)(a,{ref:s,children:n})});p.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",h=n.forwardRef((e,t)=>{let{scope:r,children:o,...s}=e,u=n.useRef(null),c=(0,l.s)(t,u),f=d(m,r);return n.useEffect(()=>(f.itemMap.set(u,{ref:u,...s}),()=>void f.itemMap.delete(u))),(0,i.jsx)(a,{[v]:"",ref:c,children:o})});return h.displayName=m,[{Provider:c,Slot:p,ItemSlot:h},function(t){let r=d(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},96963:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(43210),l=r(66156),i=(n||(n=r.t(o,2)))["useId".toString()]||(()=>void 0),a=0;function s(e){let[t,r]=o.useState(i());return(0,l.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},98599:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>l});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}}};