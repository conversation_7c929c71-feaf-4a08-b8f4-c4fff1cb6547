exports.id=5876,exports.ids=[5876],exports.modules={11:(t,e,r)=>{"use strict";var i=r(15390),n=r(50969),s=r(68298),a=r(88875),o=r(91231),u=r(16424);i({target:"Promise",stat:!0,forced:r(99501)},{all:function(t){var e=this,r=a.f(e),i=r.resolve,h=r.reject,c=o(function(){var r=s(e.resolve),a=[],o=0,c=1;u(t,function(t){var s=o++,u=!1;c++,n(r,e,t).then(function(t){!u&&(u=!0,a[s]=t,--c||i(a))},h)}),--c||i(a)});return c.error&&h(c.value),r.promise}})},343:(t,e,r)=>{"use strict";var i=r(6532).navigator,n=i&&i.userAgent;t.exports=n?String(n):""},700:(t,e,r)=>{var i=r(21154).default,n=r(31062);t.exports=function(t){var e=n(t,"string");return"symbol"==i(e)?e:e+""},t.exports.__esModule=!0,t.exports.default=t.exports},1504:(t,e,r)=>{"use strict";var i=r(80387)("iterator"),n=!1;try{var s=0,a={next:function(){return{done:!!s++}},return:function(){n=!0}};a[i]=function(){return this},Array.from(a,function(){throw 2})}catch(t){}t.exports=function(t,e){try{if(!e&&!n)return!1}catch(t){return!1}var r=!1;try{var s={};s[i]=function(){return{next:function(){return{done:r=!0}}}},t(s)}catch(t){}return r}},2129:(t,e,r)=>{"use strict";var i,n,s,a=r(38246),o=r(6532),u=r(76514),h=r(94323),c=r(77525),l=r(72649),f=r(46815),g=r(91001),p="Object already initialized",d=o.TypeError,v=o.WeakMap;if(a||l.state){var y=l.state||(l.state=new v);y.get=y.get,y.has=y.has,y.set=y.set,i=function(t,e){if(y.has(t))throw new d(p);return e.facade=t,y.set(t,e),e},n=function(t){return y.get(t)||{}},s=function(t){return y.has(t)}}else{var m=f("state");g[m]=!0,i=function(t,e){if(c(t,m))throw new d(p);return e.facade=t,h(t,m,e),e},n=function(t){return c(t,m)?t[m]:{}},s=function(t){return c(t,m)}}t.exports={set:i,get:n,has:s,enforce:function(t){return s(t)?n(t):i(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=n(e)).type!==t)throw new d("Incompatible receiver, "+t+" required");return r}}}},2870:(t,e,r)=>{"use strict";var i=r(5112),n=r(97039);t.exports=i&&n(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},3638:(t,e,r)=>{"use strict";var i=r(68298),n=r(60561);t.exports=function(t,e){var r=t[e];return n(r)?void 0:i(r)}},4074:(t,e,r)=>{"use strict";var i=r(50969),n=r(8991),s=r(17425),a=r(67420),o=r(85587),u=TypeError;t.exports=function(t,e){var r=t.exec;if(s(r)){var h=i(r,t,e);return null!==h&&n(h),h}if("RegExp"===a(t))return i(o,t,e);throw new u("RegExp#exec called on incompatible receiver")}},5112:(t,e,r)=>{"use strict";t.exports=!r(97039)(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},5502:(t,e,r)=>{"use strict";var i=r(97039),n=r(6532).RegExp;t.exports=i(function(){var t=n("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})},6532:function(t){"use strict";var e=function(t){return t&&t.Math===Math&&t};t.exports=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof global&&global)||e("object"==typeof this&&this)||function(){return this}()||Function("return this")()},6926:(t,e,r)=>{"use strict";var i=r(8991),n=r(85152),s=r(60561),a=r(80387)("species");t.exports=function(t,e){var r,o=i(t).constructor;return void 0===o||s(r=i(o)[a])?e:n(r)}},7462:(t,e,r)=>{"use strict";var i=r(58627),n=Math.min;t.exports=function(t){var e=i(t);return e>0?n(e,0x1fffffffffffff):0}},7588:(t,e,r)=>{"use strict";var i=r(79892),n=Function.prototype,s=n.call,a=i&&n.bind.bind(s,s);t.exports=i?a:function(t){return function(){return s.apply(t,arguments)}}},7615:(t,e,r)=>{"use strict";var i=r(10397).f,n=r(77525),s=r(80387)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!n(t,s)&&i(t,s,{configurable:!0,value:e})}},8991:(t,e,r)=>{"use strict";var i=r(76514),n=String,s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not an object")}},9108:(t,e,r)=>{"use strict";var i=r(15390),n=r(69416),s=r(86883).f,a=r(7462),o=r(83407),u=r(18023),h=r(10054),c=r(48072),l=r(60827),f=n("".slice),g=Math.min,p=c("startsWith");i({target:"String",proto:!0,forced:!(!l&&!p&&function(){var t=s(String.prototype,"startsWith");return t&&!t.writable}())&&!p},{startsWith:function(t){var e=o(h(this));u(t);var r=a(g(arguments.length>1?arguments[1]:void 0,e.length)),i=o(t);return f(e,r,r+i.length)===i}})},9145:t=>{"use strict";t.exports=function(t,e){try{1==arguments.length?console.error(t):console.error(t,e)}catch(t){}}},9285:(t,e,r)=>{"use strict";var i=r(80387),n=r(97601),s=i("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||a[s]===t)}},10054:(t,e,r)=>{"use strict";var i=r(60561),n=TypeError;t.exports=function(t){if(i(t))throw new n("Can't call method on "+t);return t}},10187:(t,e,r)=>{"use strict";var i=r(51067),n=r(3638),s=r(60561),a=r(97601),o=r(80387)("iterator");t.exports=function(t){if(!s(t))return n(t,o)||n(t,"@@iterator")||a[i(t)]}},10397:(t,e,r)=>{"use strict";var i=r(5112),n=r(68689),s=r(2870),a=r(8991),o=r(10421),u=TypeError,h=Object.defineProperty,c=Object.getOwnPropertyDescriptor,l="enumerable",f="configurable",g="writable";e.f=i?s?function(t,e,r){if(a(t),e=o(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&g in r&&!r[g]){var i=c(t,e);i&&i[g]&&(t[e]=r.value,r={configurable:f in r?r[f]:i[f],enumerable:l in r?r[l]:i[l],writable:!1})}return h(t,e,r)}:h:function(t,e,r){if(a(t),e=o(e),a(r),n)try{return h(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},10421:(t,e,r)=>{"use strict";var i=r(15013),n=r(11894);t.exports=function(t){var e=i(t,"string");return n(e)?e:e+""}},11894:(t,e,r)=>{"use strict";var i=r(42727),n=r(17425),s=r(43141),a=r(48324),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=i("Symbol");return n(e)&&s(e.prototype,o(t))}},12005:(t,e,r)=>{"use strict";var i,n,s,a,o=r(6532),u=r(93229),h=r(65436),c=r(17425),l=r(77525),f=r(97039),g=r(45121),p=r(31132),d=r(23359),v=r(59592),y=r(97412),m=r(68773),x=o.setImmediate,b=o.clearImmediate,S=o.process,w=o.Dispatch,T=o.Function,O=o.MessageChannel,A=o.String,E=0,C={},P="onreadystatechange";f(function(){i=o.location});var N=function(t){if(l(C,t)){var e=C[t];delete C[t],e()}},M=function(t){return function(){N(t)}},_=function(t){N(t.data)},R=function(t){o.postMessage(A(t),i.protocol+"//"+i.host)};x&&b||(x=function(t){v(arguments.length,1);var e=c(t)?t:T(t),r=p(arguments,1);return C[++E]=function(){u(e,void 0,r)},n(E),E},b=function(t){delete C[t]},m?n=function(t){S.nextTick(M(t))}:w&&w.now?n=function(t){w.now(M(t))}:O&&!y?(a=(s=new O).port2,s.port1.onmessage=_,n=h(a.postMessage,a)):o.addEventListener&&c(o.postMessage)&&!o.importScripts&&i&&"file:"!==i.protocol&&!f(R)?(n=R,o.addEventListener("message",_,!1)):n=P in d("script")?function(t){g.appendChild(d("script"))[P]=function(){g.removeChild(this),N(t)}}:function(t){setTimeout(M(t),0)}),t.exports={set:x,clear:b}},12549:(t,e,r)=>{"use strict";var i,n,s,a=r(97039),o=r(17425),u=r(76514),h=r(65444),c=r(28907),l=r(59908),f=r(80387),g=r(60827),p=f("iterator"),d=!1;[].keys&&("next"in(s=[].keys())?(n=c(c(s)))!==Object.prototype&&(i=n):d=!0),!u(i)||a(function(){var t={};return i[p].call(t)!==t})?i={}:g&&(i=h(i)),o(i[p])||l(i,p,function(){return this}),t.exports={IteratorPrototype:i,BUGGY_SAFARI_ITERATORS:d}},13044:(t,e,r)=>{"use strict";var i=r(15390),n=r(42727),s=r(60827),a=r(99662),o=r(73040).CONSTRUCTOR,u=r(43086),h=n("Promise"),c=s&&!o;i({target:"Promise",stat:!0,forced:s||o},{resolve:function(t){return u(c&&this===h?a:this,t)}})},13209:(t,e,r)=>{"use strict";var i=r(76514);t.exports=function(t){return i(t)||null===t}},13314:(t,e,r)=>{"use strict";var i=r(74934).PROPER,n=r(97039),s=r(15480),a="​\x85᠎";t.exports=function(t){return n(function(){return!!s[t]()||a[t]()!==a||i&&s[t].name!==t})}},14704:(t,e,r)=>{"use strict";var i,n,s,a,o=r(15390),u=r(60827),h=r(68773),c=r(6532),l=r(50969),f=r(59908),g=r(36751),p=r(7615),d=r(44757),v=r(68298),y=r(17425),m=r(76514),x=r(33127),b=r(6926),S=r(12005).set,w=r(82675),T=r(9145),O=r(91231),A=r(39173),E=r(2129),C=r(99662),P=r(73040),N=r(88875),M="Promise",_=P.CONSTRUCTOR,R=P.REJECTION_EVENT,V=P.SUBCLASSING,k=E.getterFor(M),I=E.set,L=C&&C.prototype,D=C,j=L,B=c.TypeError,F=c.document,U=c.process,z=N.f,H=z,X=!!(F&&F.createEvent&&c.dispatchEvent),Y="unhandledrejection",q=function(t){var e;return!!(m(t)&&y(e=t.then))&&e},W=function(t,e){var r,i,n,s=e.value,a=1===e.state,o=a?t.ok:t.fail,u=t.resolve,h=t.reject,c=t.domain;try{o?(a||(2===e.rejection&&K(e),e.rejection=1),!0===o?r=s:(c&&c.enter(),r=o(s),c&&(c.exit(),n=!0)),r===t.promise?h(new B("Promise-chain cycle")):(i=q(r))?l(i,r,u,h):u(r)):h(s)}catch(t){c&&!n&&c.exit(),h(t)}},G=function(t,e){t.notified||(t.notified=!0,w(function(){for(var r,i=t.reactions;r=i.get();)W(r,t);t.notified=!1,e&&!t.rejection&&$(t)}))},Q=function(t,e,r){var i,n;X?((i=F.createEvent("Event")).promise=e,i.reason=r,i.initEvent(t,!1,!0),c.dispatchEvent(i)):i={promise:e,reason:r},!R&&(n=c["on"+t])?n(i):t===Y&&T("Unhandled promise rejection",r)},$=function(t){l(S,c,function(){var e,r=t.facade,i=t.value;if(Z(t)&&(e=O(function(){h?U.emit("unhandledRejection",i,r):Q(Y,r,i)}),t.rejection=h||Z(t)?2:1,e.error))throw e.value})},Z=function(t){return 1!==t.rejection&&!t.parent},K=function(t){l(S,c,function(){var e=t.facade;h?U.emit("rejectionHandled",e):Q("rejectionhandled",e,t.value)})},J=function(t,e,r){return function(i){t(e,i,r)}},tt=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,G(t,!0))},te=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new B("Promise can't be resolved itself");var i=q(e);i?w(function(){var r={done:!1};try{l(i,e,J(te,r,t),J(tt,r,t))}catch(e){tt(r,e,t)}}):(t.value=e,t.state=1,G(t,!1))}catch(e){tt({done:!1},e,t)}}};if(_&&(j=(D=function(t){x(this,j),v(t),l(i,this);var e=k(this);try{t(J(te,e),J(tt,e))}catch(t){tt(e,t)}}).prototype,(i=function(t){I(this,{type:M,done:!1,notified:!1,parent:!1,reactions:new A,rejection:!1,state:0,value:null})}).prototype=f(j,"then",function(t,e){var r=k(this),i=z(b(this,D));return r.parent=!0,i.ok=!y(t)||t,i.fail=y(e)&&e,i.domain=h?U.domain:void 0,0===r.state?r.reactions.add(i):w(function(){W(i,r)}),i.promise}),n=function(){var t=new i,e=k(t);this.promise=t,this.resolve=J(te,e),this.reject=J(tt,e)},N.f=z=function(t){return t===D||t===s?new n(t):H(t)},!u&&y(C)&&L!==Object.prototype)){a=L.then,V||f(L,"then",function(t,e){var r=this;return new D(function(t,e){l(a,r,t,e)}).then(t,e)},{unsafe:!0});try{delete L.constructor}catch(t){}g&&g(L,j)}o({global:!0,constructor:!0,wrap:!0,forced:_},{Promise:D}),p(D,M,!1,!0),d(M)},14971:(t,e,r)=>{"use strict";t.exports=!r(97039)(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})},15013:(t,e,r)=>{"use strict";var i=r(50969),n=r(76514),s=r(11894),a=r(3638),o=r(79582),u=r(80387),h=TypeError,c=u("toPrimitive");t.exports=function(t,e){if(!n(t)||s(t))return t;var r,u=a(t,c);if(u){if(void 0===e&&(e="default"),!n(r=i(u,t,e))||s(r))return r;throw new h("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},15193:(t,e,r)=>{"use strict";var i=r(97039),n=r(6532).RegExp,s=i(function(){var t=n("a","y");return t.lastIndex=2,null!==t.exec("abcd")}),a=s||i(function(){return!n("a","y").sticky});t.exports={BROKEN_CARET:s||i(function(){var t=n("^r","gy");return t.lastIndex=2,null!==t.exec("str")}),MISSED_STICKY:a,UNSUPPORTED_Y:s}},15199:(t,e,r)=>{"use strict";var i=r(15390),n=r(50969),s=r(68298),a=r(88875),o=r(91231),u=r(16424);i({target:"Promise",stat:!0,forced:r(99501)},{race:function(t){var e=this,r=a.f(e),i=r.reject,h=o(function(){var a=s(e.resolve);u(t,function(t){n(a,e,t).then(r.resolve,i)})});return h.error&&i(h.value),r.promise}})},15390:(t,e,r)=>{"use strict";var i=r(6532),n=r(86883).f,s=r(94323),a=r(59908),o=r(68077),u=r(59936),h=r(67008);t.exports=function(t,e){var r,c,l,f,g,p=t.target,d=t.global,v=t.stat;if(r=d?i:v?i[p]||o(p,{}):i[p]&&i[p].prototype)for(c in e){if(f=e[c],l=t.dontCallGetSet?(g=n(r,c))&&g.value:r[c],!h(d?c:p+(v?".":"#")+c,t.forced)&&void 0!==l){if(typeof f==typeof l)continue;u(f,l)}(t.sham||l&&l.sham)&&s(f,"sham",!0),a(r,c,f,t)}}},15480:t=>{"use strict";t.exports="	\n\v\f\r \xa0              　\u2028\u2029\uFEFF"},16424:(t,e,r)=>{"use strict";var i=r(65436),n=r(50969),s=r(8991),a=r(79815),o=r(9285),u=r(67414),h=r(43141),c=r(76901),l=r(10187),f=r(79219),g=TypeError,p=function(t,e){this.stopped=t,this.result=e},d=p.prototype;t.exports=function(t,e,r){var v,y,m,x,b,S,w,T=r&&r.that,O=!!(r&&r.AS_ENTRIES),A=!!(r&&r.IS_RECORD),E=!!(r&&r.IS_ITERATOR),C=!!(r&&r.INTERRUPTED),P=i(e,T),N=function(t){return v&&f(v,"normal",t),new p(!0,t)},M=function(t){return O?(s(t),C?P(t[0],t[1],N):P(t[0],t[1])):C?P(t,N):P(t)};if(A)v=t.iterator;else if(E)v=t;else{if(!(y=l(t)))throw new g(a(t)+" is not iterable");if(o(y)){for(m=0,x=u(t);x>m;m++)if((b=M(t[m]))&&h(d,b))return b;return new p(!1)}v=c(t,y)}for(S=A?t.next:v.next;!(w=n(S,v)).done;){try{b=M(w.value)}catch(t){f(v,"throw",t)}if("object"==typeof b&&b&&h(d,b))return b}return new p(!1)}},17049:(t,e,r)=>{var i=r(700);t.exports=function(t,e,r){return(e=i(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports.__esModule=!0,t.exports.default=t.exports},17425:t=>{"use strict";var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},18023:(t,e,r)=>{"use strict";var i=r(71472),n=TypeError;t.exports=function(t){if(i(t))throw new n("The method doesn't accept regular expressions");return t}},21651:(t,e,r)=>{"use strict";var i=r(97039),n=r(6532).RegExp;t.exports=i(function(){var t=n(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)})},23315:(t,e,r)=>{"use strict";var i=r(15390),n=r(60827),s=r(73040).CONSTRUCTOR,a=r(99662),o=r(42727),u=r(17425),h=r(59908),c=a&&a.prototype;if(i({target:"Promise",proto:!0,forced:s,real:!0},{catch:function(t){return this.then(void 0,t)}}),!n&&u(a)){var l=o("Promise").prototype.catch;c.catch!==l&&h(c,"catch",l,{unsafe:!0})}},23359:(t,e,r)=>{"use strict";var i=r(6532),n=r(76514),s=i.document,a=n(s)&&n(s.createElement);t.exports=function(t){return a?s.createElement(t):{}}},23864:t=>{"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},24991:(t,e,r)=>{for(var i=r(64798),n="undefined"==typeof window?global:window,s=["moz","webkit"],a="AnimationFrame",o=n["request"+a],u=n["cancel"+a]||n["cancelRequest"+a],h=0;!o&&h<s.length;h++)o=n[s[h]+"Request"+a],u=n[s[h]+"Cancel"+a]||n[s[h]+"CancelRequest"+a];if(!o||!u){var c=0,l=0,f=[],g=1e3/60;o=function(t){if(0===f.length){var e=i(),r=Math.max(0,g-(e-c));c=r+e,setTimeout(function(){var t=f.slice(0);f.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(c)}catch(t){setTimeout(function(){throw t},0)}},Math.round(r))}return f.push({handle:++l,callback:t,cancelled:!1}),l},u=function(t){for(var e=0;e<f.length;e++)f[e].handle===t&&(f[e].cancelled=!0)}}t.exports=function(t){return o.call(n,t)},t.exports.cancel=function(){u.apply(n,arguments)},t.exports.polyfill=function(t){t||(t=n),t.requestAnimationFrame=o,t.cancelAnimationFrame=u}},25225:(t,e,r)=>{"use strict";var i=r(74934).PROPER,n=r(59908),s=r(8991),a=r(83407),o=r(97039),u=r(26602),h="toString",c=RegExp.prototype,l=c[h],f=o(function(){return"/a/b"!==l.call({source:"a",flags:"b"})}),g=i&&l.name!==h;(f||g)&&n(c,h,function(){var t=s(this);return"/"+a(t.source)+"/"+a(u(t))},{unsafe:!0})},26348:(t,e,r)=>{"use strict";var i=r(98064),n=r(67519);t.exports=Object.keys||function(t){return i(t,n)}},26602:(t,e,r)=>{"use strict";var i=r(50969),n=r(77525),s=r(43141),a=r(53907),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0===e&&!("flags"in o)&&!n(t,"flags")&&s(o,t)?i(a,t):e}},27199:(t,e,r)=>{"use strict";var i=r(7588),n=r(58627),s=r(83407),a=r(10054),o=i("".charAt),u=i("".charCodeAt),h=i("".slice),c=function(t){return function(e,r){var i,c,l=s(a(e)),f=n(r),g=l.length;return f<0||f>=g?t?"":void 0:(i=u(l,f))<55296||i>56319||f+1===g||(c=u(l,f+1))<56320||c>57343?t?o(l,f):i:t?h(l,f,f+2):(i-55296<<10)+(c-56320)+65536}};t.exports={codeAt:c(!1),charAt:c(!0)}},28090:(t,e,r)=>{"use strict";var i=r(15390),n=r(52554).trim;i({target:"String",proto:!0,forced:r(13314)("trim")},{trim:function(){return n(this)}})},28907:(t,e,r)=>{"use strict";var i=r(77525),n=r(17425),s=r(40377),a=r(46815),o=r(14971),u=a("IE_PROTO"),h=Object,c=h.prototype;t.exports=o?h.getPrototypeOf:function(t){var e=s(t);if(i(e,u))return e[u];var r=e.constructor;return n(r)&&e instanceof r?r.prototype:e instanceof h?c:null}},30052:(t,e,r)=>{"use strict";var i=r(45465),n=r(39529),s=r(97601),a=r(2129),o=r(10397).f,u=r(96516),h=r(43429),c=r(60827),l=r(5112),f="Array Iterator",g=a.set,p=a.getterFor(f);t.exports=u(Array,"Array",function(t,e){g(this,{type:f,target:i(t),index:0,kind:e})},function(){var t=p(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,h(void 0,!0);switch(t.kind){case"keys":return h(r,!1);case"values":return h(e[r],!1)}return h([r,e[r]],!1)},"values");var d=s.Arguments=s.Array;if(n("keys"),n("values"),n("entries"),!c&&l&&"values"!==d.name)try{o(d,"name",{value:"values"})}catch(t){}},30246:(t,e,r)=>{"use strict";var i=r(7588),n=r(40377),s=Math.floor,a=i("".charAt),o=i("".replace),u=i("".slice),h=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,c=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,i,l,f){var g=r+t.length,p=i.length,d=c;return void 0!==l&&(l=n(l),d=h),o(f,d,function(n,o){var h;switch(a(o,0)){case"$":return"$";case"&":return t;case"`":return u(e,0,r);case"'":return u(e,g);case"<":h=l[u(o,1,-1)];break;default:var c=+o;if(0===c)return n;if(c>p){var f=s(c/10);if(0===f)return n;if(f<=p)return void 0===i[f-1]?a(o,1):i[f-1]+a(o,1);return n}h=i[c-1]}return void 0===h?"":h})}},31062:(t,e,r)=>{var i=r(21154).default;t.exports=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},31132:(t,e,r)=>{"use strict";t.exports=r(7588)([].slice)},31429:(t,e,r)=>{"use strict";var i=r(72649);t.exports=function(t,e){return i[t]||(i[t]=e||{})}},33127:(t,e,r)=>{"use strict";var i=r(43141),n=TypeError;t.exports=function(t,e){if(i(e,t))return t;throw new n("Incorrect invocation")}},33748:(t,e,r)=>{"use strict";var i=r(50969),n=r(7588),s=r(57224),a=r(8991),o=r(60561),u=r(10054),h=r(6926),c=r(97361),l=r(7462),f=r(83407),g=r(3638),p=r(4074),d=r(15193),v=r(97039),y=d.UNSUPPORTED_Y,m=Math.min,x=n([].push),b=n("".slice),S=!v(function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]}),w="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;s("split",function(t,e,r){var n="0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:i(e,this,t,r)}:e;return[function(e,r){var s=u(this),a=o(e)?void 0:g(e,t);return a?i(a,e,s,r):i(n,f(s),e,r)},function(t,i){var s=a(this),o=f(t);if(!w){var u=r(n,s,o,i,n!==e);if(u.done)return u.value}var g=h(s,RegExp),d=s.unicode,v=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(y?"g":"y"),S=new g(y?"^(?:"+s.source+")":s,v),T=void 0===i?0xffffffff:i>>>0;if(0===T)return[];if(0===o.length)return null===p(S,o)?[o]:[];for(var O=0,A=0,E=[];A<o.length;){S.lastIndex=y?0:A;var C,P=p(S,y?b(o,A):o);if(null===P||(C=m(l(S.lastIndex+(y?A:0)),o.length))===O)A=c(o,A,d);else{if(x(E,b(o,O,A)),E.length===T)return E;for(var N=1;N<=P.length-1;N++)if(x(E,P[N]),E.length===T)return E;A=O=C}}return x(E,b(o,O)),E}]},w||!S,y)},33989:(t,e,r)=>{"use strict";var i=r(343);t.exports=/ipad|iphone|ipod/i.test(i)&&"undefined"!=typeof Pebble},34357:(t,e,r)=>{"use strict";var i=r(50969),n=r(57224),s=r(8991),a=r(60561),o=r(7462),u=r(83407),h=r(10054),c=r(3638),l=r(97361),f=r(4074);n("match",function(t,e,r){return[function(e){var r=h(this),n=a(e)?void 0:c(e,t);return n?i(n,e,r):new RegExp(e)[t](u(r))},function(t){var i,n=s(this),a=u(t),h=r(e,n,a);if(h.done)return h.value;if(!n.global)return f(n,a);var c=n.unicode;n.lastIndex=0;for(var g=[],p=0;null!==(i=f(n,a));){var d=u(i[0]);g[p]=d,""===d&&(n.lastIndex=l(a,o(n.lastIndex),c)),p++}return 0===p?null:g}]})},36691:(t,e,r)=>{"use strict";var i=r(7588),n=r(97039),s=r(17425),a=r(77525),o=r(5112),u=r(74934).CONFIGURABLE,h=r(93930),c=r(2129),l=c.enforce,f=c.get,g=String,p=Object.defineProperty,d=i("".slice),v=i("".replace),y=i([].join),m=o&&!n(function(){return 8!==p(function(){},"length",{value:8}).length}),x=String(String).split("String"),b=t.exports=function(t,e,r){"Symbol("===d(g(e),0,7)&&(e="["+v(g(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||u&&t.name!==e)&&(o?p(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&a(r,"arity")&&t.length!==r.arity&&p(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?o&&p(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var i=l(t);return a(i,"source")||(i.source=y(x,"string"==typeof e?e:"")),t};Function.prototype.toString=b(function(){return s(this)&&f(this).source||h(this)},"toString")},36751:(t,e,r)=>{"use strict";var i=r(96482),n=r(76514),s=r(10054),a=r(99466);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=i(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,i){return s(r),a(i),n(r)&&(e?t(r,i):r.__proto__=i),r}}():void 0)},37812:(t,e,r)=>{"use strict";var i=r(23359)("span").classList,n=i&&i.constructor&&i.constructor.prototype;t.exports=n===Object.prototype?void 0:n},38246:(t,e,r)=>{"use strict";var i=r(6532),n=r(17425),s=i.WeakMap;t.exports=n(s)&&/native code/.test(String(s))},39173:t=>{"use strict";var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=e},39529:(t,e,r)=>{"use strict";var i=r(80387),n=r(65444),s=r(10397).f,a=i("unscopables"),o=Array.prototype;void 0===o[a]&&s(o,a,{configurable:!0,value:n(null)}),t.exports=function(t){o[a][t]=!0}},40377:(t,e,r)=>{"use strict";var i=r(10054),n=Object;t.exports=function(t){return n(i(t))}},42727:(t,e,r)=>{"use strict";var i=r(6532),n=r(17425);t.exports=function(t,e){var r;return arguments.length<2?n(r=i[t])?r:void 0:i[t]&&i[t][e]}},43086:(t,e,r)=>{"use strict";var i=r(8991),n=r(76514),s=r(88875);t.exports=function(t,e){if(i(t),n(e)&&e.constructor===t)return e;var r=s.f(t);return(0,r.resolve)(e),r.promise}},43141:(t,e,r)=>{"use strict";t.exports=r(7588)({}.isPrototypeOf)},43429:t=>{"use strict";t.exports=function(t,e){return{value:t,done:e}}},44615:(t,e,r)=>{"use strict";var i=r(52359),n=r(97039),s=r(6532).String;t.exports=!!Object.getOwnPropertySymbols&&!n(function(){var t=Symbol("symbol detection");return!s(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&i&&i<41})},44757:(t,e,r)=>{"use strict";var i=r(42727),n=r(51170),s=r(80387),a=r(5112),o=s("species");t.exports=function(t){var e=i(t);a&&e&&!e[o]&&n(e,o,{configurable:!0,get:function(){return this}})}},45121:(t,e,r)=>{"use strict";t.exports=r(42727)("document","documentElement")},45452:(t,e,r)=>{"use strict";var i=r(15390),n=r(51206).left,s=r(57862),a=r(52359);i({target:"Array",proto:!0,forced:!r(68773)&&a>79&&a<83||!s("reduce")},{reduce:function(t){var e=arguments.length;return n(this,t,e,e>1?arguments[1]:void 0)}})},45465:(t,e,r)=>{"use strict";var i=r(76607),n=r(10054);t.exports=function(t){return i(n(t))}},46815:(t,e,r)=>{"use strict";var i=r(31429),n=r(88532),s=i("keys");t.exports=function(t){return s[t]||(s[t]=n(t))}},47908:(t,e,r)=>{"use strict";var i=r(93229),n=r(50969),s=r(7588),a=r(57224),o=r(97039),u=r(8991),h=r(17425),c=r(60561),l=r(58627),f=r(7462),g=r(83407),p=r(10054),d=r(97361),v=r(3638),y=r(30246),m=r(4074),x=r(80387)("replace"),b=Math.max,S=Math.min,w=s([].concat),T=s([].push),O=s("".indexOf),A=s("".slice),E="$0"==="a".replace(/./,"$0"),C=!!/./[x]&&""===/./[x]("a","$0");a("replace",function(t,e,r){var s=C?"$":"$0";return[function(t,r){var i=p(this),s=c(t)?void 0:v(t,x);return s?n(s,t,i,r):n(e,g(i),t,r)},function(t,n){var a=u(this),o=g(t);if("string"==typeof n&&-1===O(n,s)&&-1===O(n,"$<")){var c=r(e,a,o,n);if(c.done)return c.value}var p=h(n);p||(n=g(n));var v=a.global;v&&(M=a.unicode,a.lastIndex=0);for(var x=[];null!==(R=m(a,o))&&(T(x,R),v);){""===g(R[0])&&(a.lastIndex=d(o,f(a.lastIndex),M))}for(var E="",C=0,P=0;P<x.length;P++){for(var N,M,_,R=x[P],V=g(R[0]),k=b(S(l(R.index),o.length),0),I=[],L=1;L<R.length;L++)T(I,void 0===(N=R[L])?N:String(N));var D=R.groups;if(p){var j=w([V],I,k,o);void 0!==D&&T(j,D),_=g(i(n,void 0,j))}else _=y(V,o,k,I,D,n);k>=C&&(E+=A(o,C,k)+_,C=k+V.length)}return E+A(o,C)}]},!!o(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})||!E||C)},48072:(t,e,r)=>{"use strict";var i=r(80387)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[i]=!1,"/./"[t](e)}catch(t){}}return!1}},48324:(t,e,r)=>{"use strict";t.exports=r(44615)&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},50065:(t,e,r)=>{"use strict";var i=r(7588),n=r(97039),s=r(17425),a=r(51067),o=r(42727),u=r(93930),h=function(){},c=o("Reflect","construct"),l=/^\s*(?:class|function)\b/,f=i(l.exec),g=!l.test(h),p=function(t){if(!s(t))return!1;try{return c(h,[],t),!0}catch(t){return!1}},d=function(t){if(!s(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return g||!!f(l,u(t))}catch(t){return!0}};d.sham=!0,t.exports=!c||n(function(){var t;return p(p.call)||!p(Object)||!p(function(){t=!0})||t})?d:p},50969:(t,e,r)=>{"use strict";var i=r(79892),n=Function.prototype.call;t.exports=i?n.bind(n):function(){return n.apply(n,arguments)}},51067:(t,e,r)=>{"use strict";var i=r(55744),n=r(17425),s=r(67420),a=r(80387)("toStringTag"),o=Object,u="Arguments"===s(function(){return arguments}()),h=function(t,e){try{return t[e]}catch(t){}};t.exports=i?s:function(t){var e,r,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=h(e=o(t),a))?r:u?s(e):"Object"===(i=s(e))&&n(e.callee)?"Arguments":i}},51170:(t,e,r)=>{"use strict";var i=r(36691),n=r(10397);t.exports=function(t,e,r){return r.get&&i(r.get,e,{getter:!0}),r.set&&i(r.set,e,{setter:!0}),n.f(t,e,r)}},51206:(t,e,r)=>{"use strict";var i=r(68298),n=r(40377),s=r(76607),a=r(67414),o=TypeError,u="Reduce of empty array with no initial value",h=function(t){return function(e,r,h,c){var l=n(e),f=s(l),g=a(l);if(i(r),0===g&&h<2)throw new o(u);var p=t?g-1:0,d=t?-1:1;if(h<2)for(;;){if(p in f){c=f[p],p+=d;break}if(p+=d,t?p<0:g<=p)throw new o(u)}for(;t?p>=0:g>p;p+=d)p in f&&(c=r(c,f[p],p,l));return c}};t.exports={left:h(!1),right:h(!0)}},52359:(t,e,r)=>{"use strict";var i,n,s=r(6532),a=r(343),o=s.process,u=s.Deno,h=o&&o.versions||u&&u.version,c=h&&h.v8;c&&(n=(i=c.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!n&&a&&(!(i=a.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=a.match(/Chrome\/(\d+)/))&&(n=+i[1]),t.exports=n},52431:(t,e,r)=>{"use strict";var i=r(6532),n=r(343),s=r(67420),a=function(t){return n.slice(0,t.length)===t};t.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":i.Bun&&"string"==typeof Bun.version?"BUN":i.Deno&&"object"==typeof Deno.version?"DENO":"process"===s(i.process)?"NODE":i.window&&i.document?"BROWSER":"REST"},52554:(t,e,r)=>{"use strict";var i=r(7588),n=r(10054),s=r(83407),a=r(15480),o=i("".replace),u=RegExp("^["+a+"]+"),h=RegExp("(^|[^"+a+"])["+a+"]+$"),c=function(t){return function(e){var r=s(n(e));return 1&t&&(r=o(r,u,"")),2&t&&(r=o(r,h,"$1")),r}};t.exports={start:c(1),end:c(2),trim:c(3)}},53436:(t,e,r)=>{"use strict";var i=r(67420);t.exports=Array.isArray||function(t){return"Array"===i(t)}},53538:(t,e,r)=>{"use strict";var i=r(12549).IteratorPrototype,n=r(65444),s=r(23864),a=r(7615),o=r(97601),u=function(){return this};t.exports=function(t,e,r,h){var c=e+" Iterator";return t.prototype=n(i,{next:s(+!h,r)}),a(t,c,!1,!0),o[c]=u,t}},53907:(t,e,r)=>{"use strict";var i=r(8991);t.exports=function(){var t=i(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},55744:(t,e,r)=>{"use strict";var i=r(80387)("toStringTag"),n={};n[i]="z",t.exports="[object z]"===String(n)},57224:(t,e,r)=>{"use strict";r(79287);var i=r(50969),n=r(59908),s=r(85587),a=r(97039),o=r(80387),u=r(94323),h=o("species"),c=RegExp.prototype;t.exports=function(t,e,r,l){var f=o(t),g=!a(function(){var e={};return e[f]=function(){return 7},7!==""[t](e)}),p=g&&!a(function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[h]=function(){return r},r.flags="",r[f]=/./[f]),r.exec=function(){return e=!0,null},r[f](""),!e});if(!g||!p||r){var d=/./[f],v=e(f,""[t],function(t,e,r,n,a){var o=e.exec;return o===s||o===c.exec?g&&!a?{done:!0,value:i(d,e,r,n)}:{done:!0,value:i(t,r,e,n)}:{done:!1}});n(String.prototype,t,v[0]),n(c,f,v[1])}l&&u(c[f],"sham",!0)}},57862:(t,e,r)=>{"use strict";var i=r(97039);t.exports=function(t,e){var r=[][t];return!!r&&i(function(){r.call(null,e||function(){return 1},1)})}},58397:(t,e,r)=>{"use strict";var i=r(6532),n=r(59164),s=r(37812),a=r(30052),o=r(94323),u=r(7615),h=r(80387)("iterator"),c=a.values,l=function(t,e){if(t){if(t[h]!==c)try{o(t,h,c)}catch(e){t[h]=c}if(u(t,e,!0),n[e]){for(var r in a)if(t[r]!==a[r])try{o(t,r,a[r])}catch(e){t[r]=a[r]}}}};for(var f in n)l(i[f]&&i[f].prototype,f);l(s,"DOMTokenList")},58627:(t,e,r)=>{"use strict";var i=r(63585);t.exports=function(t){var e=+t;return e!=e||0===e?0:i(e)}},59164:t=>{"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},59592:t=>{"use strict";var e=TypeError;t.exports=function(t,r){if(t<r)throw new e("Not enough arguments");return t}},59908:(t,e,r)=>{"use strict";var i=r(17425),n=r(10397),s=r(36691),a=r(68077);t.exports=function(t,e,r,o){o||(o={});var u=o.enumerable,h=void 0!==o.name?o.name:e;if(i(r)&&s(r,h,o),o.global)u?t[e]=r:a(e,r);else{try{o.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:n.f(t,e,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},59936:(t,e,r)=>{"use strict";var i=r(77525),n=r(75351),s=r(86883),a=r(10397);t.exports=function(t,e,r){for(var o=n(e),u=a.f,h=s.f,c=0;c<o.length;c++){var l=o[c];i(t,l)||r&&i(r,l)||u(t,l,h(e,l))}}},60419:(t,e,r)=>{"use strict";var i=r(343);t.exports=/web0s(?!.*chrome)/i.test(i)},60561:t=>{"use strict";t.exports=function(t){return null==t}},60827:t=>{"use strict";t.exports=!1},63345:(t,e)=>{"use strict";e.f=Object.getOwnPropertySymbols},63585:t=>{"use strict";var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var i=+t;return(i>0?r:e)(i)}},64798:function(t){(function(){var e,r,i,n;"undefined"!=typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:"undefined"!=typeof process&&null!==process&&process.hrtime?(t.exports=function(){return(e()-n)/1e6},r=process.hrtime,n=(e=function(){var t;return 1e9*(t=r())[0]+t[1]})()-1e9*process.uptime()):Date.now?(t.exports=function(){return Date.now()-i},i=Date.now()):(t.exports=function(){return new Date().getTime()-i},i=new Date().getTime())}).call(this)},65436:(t,e,r)=>{"use strict";var i=r(69416),n=r(68298),s=r(79892),a=i(i.bind);t.exports=function(t,e){return n(t),void 0===e?t:s?a(t,e):function(){return t.apply(e,arguments)}}},65444:(t,e,r)=>{"use strict";var i,n=r(8991),s=r(97269),a=r(67519),o=r(91001),u=r(45121),h=r(23359),c=r(46815),l="prototype",f="script",g=c("IE_PROTO"),p=function(){},d=function(t){return"<"+f+">"+t+"</"+f+">"},v=function(t){t.write(d("")),t.close();var e=t.parentWindow.Object;return t=null,e},y=function(){var t,e=h("iframe");return e.style.display="none",u.appendChild(e),e.src=String("java"+f+":"),(t=e.contentWindow.document).open(),t.write(d("document.F=Object")),t.close(),t.F},m=function(){try{i=new ActiveXObject("htmlfile")}catch(t){}m="undefined"!=typeof document?document.domain&&i?v(i):y():v(i);for(var t=a.length;t--;)delete m[l][a[t]];return m()};o[g]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(p[l]=n(t),r=new p,p[l]=null,r[g]=t):r=m(),void 0===e?r:s.f(r,e)}},65876:(t,e,r)=>{"use strict";r.r(e),r.d(e,{AElement:()=>t2,AnimateColorElement:()=>t$,AnimateElement:()=>tQ,AnimateTransformElement:()=>tZ,BoundingBox:()=>tE,CB1:()=>Z,CB2:()=>K,CB3:()=>J,CB4:()=>tt,Canvg:()=>ex,CircleElement:()=>tL,ClipPathElement:()=>ea,DefsElement:()=>tH,DescElement:()=>eg,Document:()=>ev,Element:()=>tw,EllipseElement:()=>tD,FeColorMatrixElement:()=>ei,FeCompositeElement:()=>ec,FeDropShadowElement:()=>eu,FeGaussianBlurElement:()=>el,FeMorphologyElement:()=>eh,FilterElement:()=>eo,Font:()=>tA,FontElement:()=>tK,FontFaceElement:()=>tJ,GElement:()=>tX,GlyphElement:()=>tM,GradientElement:()=>tY,ImageElement:()=>t9,LineElement:()=>tj,LinearGradientElement:()=>tq,MarkerElement:()=>tz,MaskElement:()=>en,Matrix:()=>ty,MissingGlyphElement:()=>t0,Mouse:()=>to,PSEUDO_ZERO:()=>W,Parser:()=>tg,PathElement:()=>tN,PathParser:()=>tC,PatternElement:()=>tU,Point:()=>ta,PolygonElement:()=>tF,PolylineElement:()=>tB,Property:()=>tn,QB1:()=>te,QB2:()=>tr,QB3:()=>ti,RadialGradientElement:()=>tW,RectElement:()=>tI,RenderedElement:()=>tP,Rotate:()=>td,SVGElement:()=>tk,SVGFontLoader:()=>t6,Scale:()=>tv,Screen:()=>tc,Skew:()=>tm,SkewX:()=>tx,SkewY:()=>tb,StopElement:()=>tG,StyleElement:()=>et,SymbolElement:()=>t8,TRefElement:()=>t1,TSpanElement:()=>tR,TextElement:()=>t_,TextPathElement:()=>t5,TitleElement:()=>ef,Transform:()=>tS,Translate:()=>tp,UnknownElement:()=>tT,UseElement:()=>ee,ViewPort:()=>ts,compressSpaces:()=>M,default:()=>ex,getSelectorSpecificity:()=>q,normalizeAttributeName:()=>I,normalizeColor:()=>D,parseExternalUrl:()=>L,presets:()=>N,toNumbers:()=>V,trimLeft:()=>_,trimRight:()=>R,vectorMagnitude:()=>G,vectorsAngle:()=>$,vectorsRatio:()=>Q}),r(98074);var i=r(69377);r(34357),r(47908),r(9108),r(30052),r(58397);var n=r(17049);r(45452),r(90845),r(33748);var s=r(24991);r(28090);var a=r(79064);r(68328),r(96595),r(73522);var o=function(t,e){return(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function h(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function c(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var l=Math.PI;function f(t,e,r){t.lArcFlag=+(0!==t.lArcFlag),t.sweepFlag=+(0!==t.sweepFlag);var i=t.rX,n=t.rY,s=t.x,a=t.y;i=Math.abs(t.rX),n=Math.abs(t.rY);var o=h([(e-s)/2,(r-a)/2],-t.xRot/180*l),u=o[0],c=o[1],f=Math.pow(u,2)/Math.pow(i,2)+Math.pow(c,2)/Math.pow(n,2);1<f&&(i*=Math.sqrt(f),n*=Math.sqrt(f)),t.rX=i,t.rY=n;var g=Math.pow(i,2)*Math.pow(c,2)+Math.pow(n,2)*Math.pow(u,2),p=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(n,2)-g)/g)),d=i*c/n*p,v=-n*u/i*p,y=h([d,v],t.xRot/180*l);t.cX=y[0]+(e+s)/2,t.cY=y[1]+(r+a)/2,t.phi1=Math.atan2((c-v)/n,(u-d)/i),t.phi2=Math.atan2((-c-v)/n,(-u-d)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*l),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*l),t.phi1*=180/l,t.phi2*=180/l}function g(t,e,r){c(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var n=Math.sqrt(i);return[[(t*r+e*n)/(t*t+e*e),(e*r-t*n)/(t*t+e*e)],[(t*r-e*n)/(t*t+e*e),(e*r+t*n)/(t*t+e*e)]]}var p,d=Math.PI/180;function v(t,e,r,i){return t+Math.cos(i/180*l)*e+Math.sin(i/180*l)*r}function y(t,e,r,i){var n=e-t,s=r-e,a=3*n+3*(i-r)-6*s,o=6*(s-n),u=3*n;return 1e-6>Math.abs(a)?[-u/o]:function(t,e,r){void 0===r&&(r=1e-6);var i=t*t/4-e;if(i<-r)return[];if(i<=r)return[-t/2];var n=Math.sqrt(i);return[-t/2-n,-t/2+n]}(o/a,u/a,1e-6)}function m(t,e,r,i,n){var s=1-n;return s*s*s*t+3*s*s*n*e+3*s*n*n*r+n*n*n*i}!function(t){function e(){return n(function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t})}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return n(function(n,s,a){return n.type&T.SMOOTH_CURVE_TO&&(n.type=T.CURVE_TO,t=isNaN(t)?s:t,e=isNaN(e)?a:e,n.x1=n.relative?s-t:2*s-t,n.y1=n.relative?a-e:2*a-e),n.type&T.CURVE_TO?(t=n.relative?s+n.x2:n.x2,e=n.relative?a+n.y2:n.y2):(t=NaN,e=NaN),n.type&T.SMOOTH_QUAD_TO&&(n.type=T.QUAD_TO,r=isNaN(r)?s:r,i=isNaN(i)?a:i,n.x1=n.relative?s-r:2*s-r,n.y1=n.relative?a-i:2*a-i),n.type&T.QUAD_TO?(r=n.relative?s+n.x1:n.x1,i=n.relative?a+n.y1:n.y1):(r=NaN,i=NaN),n})}function i(){var t=NaN,e=NaN;return n(function(r,i,n){if(r.type&T.SMOOTH_QUAD_TO&&(r.type=T.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?n:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?n-e:2*n-e),r.type&T.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?n+r.y1:r.y1;var s=r.x1,a=r.y1;r.type=T.CURVE_TO,r.x1=((r.relative?0:i)+2*s)/3,r.y1=((r.relative?0:n)+2*a)/3,r.x2=(r.x+2*s)/3,r.y2=(r.y+2*a)/3}else t=NaN,e=NaN;return r})}function n(t){var e=0,r=0,i=NaN,n=NaN;return function(s){if(isNaN(i)&&!(s.type&T.MOVE_TO))throw Error("path must start with moveto");var a=t(s,e,r,i,n);return s.type&T.CLOSE_PATH&&(e=i,r=n),void 0!==s.x&&(e=s.relative?e+s.x:s.x),void 0!==s.y&&(r=s.relative?r+s.y:s.y),s.type&T.MOVE_TO&&(i=e,n=r),a}}function s(t,e,r,i,s,a){return c(t,e,r,i,s,a),n(function(n,o,u,h){var c=n.x1,l=n.x2,f=n.relative&&!isNaN(h),g=void 0!==n.x?n.x:f?0:o,p=void 0!==n.y?n.y:f?0:u;n.type&T.HORIZ_LINE_TO&&0!==e&&(n.type=T.LINE_TO,n.y=n.relative?0:u),n.type&T.VERT_LINE_TO&&0!==r&&(n.type=T.LINE_TO,n.x=n.relative?0:o),void 0!==n.x&&(n.x=n.x*t+p*r+(f?0:s)),void 0!==n.y&&(n.y=g*e+n.y*i+(f?0:a)),void 0!==n.x1&&(n.x1=n.x1*t+n.y1*r+(f?0:s)),void 0!==n.y1&&(n.y1=c*e+n.y1*i+(f?0:a)),void 0!==n.x2&&(n.x2=n.x2*t+n.y2*r+(f?0:s)),void 0!==n.y2&&(n.y2=l*e+n.y2*i+(f?0:a));var d=t*i-e*r;if(void 0!==n.xRot&&(1!==t||0!==e||0!==r||1!==i)){if(0===d)delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag,n.type=T.LINE_TO;else{var v,y,m=n.xRot*Math.PI/180,x=Math.sin(m),b=Math.cos(m),S=1/((v=n.rX)*v),w=1/((y=n.rY)*y),O=b*b*S+x*x*w,A=2*x*b*(S-w),E=x*x*S+b*b*w,C=O*i*i-A*e*i+E*e*e,P=A*(t*i+e*r)-2*(O*r*i+E*t*e),N=O*r*r-A*t*r+E*t*t,M=(Math.atan2(P,C-N)+Math.PI)%Math.PI/2,_=Math.sin(M),R=Math.cos(M);n.rX=Math.abs(d)/Math.sqrt(R*R*C+P*_*R+_*_*N),n.rY=Math.abs(d)/Math.sqrt(_*_*C-P*_*R+R*R*N),n.xRot=180*M/Math.PI}}return void 0!==n.sweepFlag&&0>d&&(n.sweepFlag=+!n.sweepFlag),n})}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),c(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return n(function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t})},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),n(function(i,n,s,a,o){if(isNaN(a)&&!(i.type&T.MOVE_TO))throw Error("path must start with moveto");return e&&i.type&T.HORIZ_LINE_TO&&(i.type=T.LINE_TO,i.y=i.relative?0:s),r&&i.type&T.VERT_LINE_TO&&(i.type=T.LINE_TO,i.x=i.relative?0:n),t&&i.type&T.CLOSE_PATH&&(i.type=T.LINE_TO,i.x=i.relative?a-n:a,i.y=i.relative?o-s:o),i.type&T.ARC&&(0===i.rX||0===i.rY)&&(i.type=T.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i})},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=n,t.SANITIZE=function(t){void 0===t&&(t=0),c(t);var e=NaN,r=NaN,i=NaN,s=NaN;return n(function(n,a,o,u,h){var c=Math.abs,l=!1,f=0,g=0;if(n.type&T.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:a-e,g=isNaN(r)?0:o-r),n.type&(T.CURVE_TO|T.SMOOTH_CURVE_TO)?(e=n.relative?a+n.x2:n.x2,r=n.relative?o+n.y2:n.y2):(e=NaN,r=NaN),n.type&T.SMOOTH_QUAD_TO?(i=isNaN(i)?a:2*a-i,s=isNaN(s)?o:2*o-s):n.type&T.QUAD_TO?(i=n.relative?a+n.x1:n.x1,s=n.relative?o+n.y1:n.y2):(i=NaN,s=NaN),n.type&T.LINE_COMMANDS||n.type&T.ARC&&(0===n.rX||0===n.rY||!n.lArcFlag)||n.type&T.CURVE_TO||n.type&T.SMOOTH_CURVE_TO||n.type&T.QUAD_TO||n.type&T.SMOOTH_QUAD_TO){var p=void 0===n.x?0:n.relative?n.x:n.x-a,d=void 0===n.y?0:n.relative?n.y:n.y-o;f=isNaN(i)?void 0===n.x1?f:n.relative?n.x:n.x1-a:i-a,g=isNaN(s)?void 0===n.y1?g:n.relative?n.y:n.y1-o:s-o;var v=void 0===n.x2?0:n.relative?n.x:n.x2-a,y=void 0===n.y2?0:n.relative?n.y:n.y2-o;c(p)<=t&&c(d)<=t&&c(f)<=t&&c(g)<=t&&c(v)<=t&&c(y)<=t&&(l=!0)}return n.type&T.CLOSE_PATH&&c(a-u)<=t&&c(o-h)<=t&&(l=!0),l?[]:n})},t.MATRIX=s,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),c(t,e,r);var i=Math.sin(t),n=Math.cos(t);return s(n,i,-i,n,e-e*n+r*i,r-e*i-r*n)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),c(t,e),s(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),c(t,e),s(t,0,0,e,0,0)},t.SKEW_X=function(t){return c(t),s(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return c(t),s(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),c(t),s(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),c(t),s(1,0,0,-1,0,t)},t.A_TO_C=function(){return n(function(t,e,r){return T.ARC===t.type?function(t,e,r){var i,n,s,a;t.cX||f(t,e,r);for(var o=Math.min(t.phi1,t.phi2),u=Math.max(t.phi1,t.phi2)-o,c=Math.ceil(u/90),l=Array(c),g=e,p=r,v=0;v<c;v++){var y,m,x,b,S,w,O=(y=t.phi1,m=t.phi2,(1-(x=v/c))*y+x*m),A=(b=t.phi1,S=t.phi2,(1-(w=(v+1)/c))*b+w*S),E=4/3*Math.tan((A-O)*d/4),C=[Math.cos(O*d)-E*Math.sin(O*d),Math.sin(O*d)+E*Math.cos(O*d)],P=C[0],N=C[1],M=[Math.cos(A*d),Math.sin(A*d)],_=M[0],R=M[1],V=[_+E*Math.sin(A*d),R-E*Math.cos(A*d)],k=V[0],I=V[1];l[v]={relative:t.relative,type:T.CURVE_TO};var L=function(e,r){var i=h([e*t.rX,r*t.rY],t.xRot),n=i[0],s=i[1];return[t.cX+n,t.cY+s]};i=L(P,N),l[v].x1=i[0],l[v].y1=i[1],n=L(k,I),l[v].x2=n[0],l[v].y2=n[1],s=L(_,R),l[v].x=s[0],l[v].y=s[1],t.relative&&(l[v].x1-=g,l[v].y1-=p,l[v].x2-=g,l[v].y2-=p,l[v].x-=g,l[v].y-=p),g=(a=[l[v].x,l[v].y])[0],p=a[1]}return l}(t,t.relative?0:e,t.relative?0:r):t})},t.ANNOTATE_ARCS=function(){return n(function(t,e,r){return t.relative&&(e=0,r=0),T.ARC===t.type&&f(t,e,r),t})},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=function(t){var e={};for(var r in t)e[r]=t[r];return e},s=e(),a=i(),o=r(),u=n(function(e,r,i){var n=o(a(s(t(e))));function h(t){t>u.maxX&&(u.maxX=t),t<u.minX&&(u.minX=t)}function c(t){t>u.maxY&&(u.maxY=t),t<u.minY&&(u.minY=t)}if(n.type&T.DRAWING_COMMANDS&&(h(r),c(i)),n.type&T.HORIZ_LINE_TO&&h(n.x),n.type&T.VERT_LINE_TO&&c(n.y),n.type&T.LINE_TO&&(h(n.x),c(n.y)),n.type&T.CURVE_TO){h(n.x),c(n.y);for(var l=0,p=y(r,n.x1,n.x2,n.x);l<p.length;l++)0<(b=p[l])&&1>b&&h(m(r,n.x1,n.x2,n.x,b));for(var d=0,x=y(i,n.y1,n.y2,n.y);d<x.length;d++)0<(b=x[d])&&1>b&&c(m(i,n.y1,n.y2,n.y,b))}if(n.type&T.ARC){h(n.x),c(n.y),f(n,r,i);for(var b,S=n.xRot/180*Math.PI,w=Math.cos(S)*n.rX,O=Math.sin(S)*n.rX,A=-Math.sin(S)*n.rY,E=Math.cos(S)*n.rY,C=n.phi1<n.phi2?[n.phi1,n.phi2]:-180>n.phi2?[n.phi2+360,n.phi1+360]:[n.phi2,n.phi1],P=C[0],N=C[1],M=function(t){var e=t[0],r=180*Math.atan2(t[1],e)/Math.PI;return r<P?r+360:r},_=0,R=g(A,-w,0).map(M);_<R.length;_++)(b=R[_])>P&&b<N&&h(v(n.cX,w,A,b));for(var V=0,k=g(E,-O,0).map(M);V<k.length;V++)(b=k[V])>P&&b<N&&c(v(n.cY,O,E,b))}return e});return u.minX=1/0,u.maxX=-1/0,u.minY=1/0,u.maxY=-1/0,u}}(p||(p={}));var x,b=function(){function t(){}return t.prototype.round=function(t){return this.transform(p.ROUND(t))},t.prototype.toAbs=function(){return this.transform(p.TO_ABS())},t.prototype.toRel=function(){return this.transform(p.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(p.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(p.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(p.QT_TO_C())},t.prototype.aToC=function(){return this.transform(p.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(p.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(p.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(p.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(p.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,n,s){return this.transform(p.MATRIX(t,e,r,i,n,s))},t.prototype.skewX=function(t){return this.transform(p.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(p.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(p.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(p.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(p.ANNOTATE_ARCS())},t}(),S=function(t){return 48<=t.charCodeAt(0)&&57>=t.charCodeAt(0)},w=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return u(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},n=0;n<t.length;n++){var s=t[n],a=this.curCommandType===T.ARC&&(3===this.curArgs.length||4===this.curArgs.length)&&1===this.curNumber.length&&("0"===this.curNumber||"1"===this.curNumber),o=S(s)&&("0"===this.curNumber&&"0"===s||a);if(!S(s)||o){if("e"!==s&&"E"!==s){if("-"!==s&&"+"!==s||!this.curNumberHasExp||this.curNumberHasExpDigits){if("."!==s||this.curNumberHasExp||this.curNumberHasDecimal||a){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw SyntaxError("Invalid number ending at "+n);if(this.curCommandType===T.ARC){if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw SyntaxError('Expected positive number, got "'+u+'" at index "'+n+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+n+'"')}this.curArgs.push(u),this.curArgs.length===O[this.curCommandType]&&(T.HORIZ_LINE_TO===this.curCommandType?i({type:T.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):T.VERT_LINE_TO===this.curCommandType?i({type:T.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===T.MOVE_TO||this.curCommandType===T.LINE_TO||this.curCommandType===T.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),T.MOVE_TO===this.curCommandType&&(this.curCommandType=T.LINE_TO)):this.curCommandType===T.CURVE_TO?i({type:T.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===T.SMOOTH_CURVE_TO?i({type:T.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===T.QUAD_TO?i({type:T.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===T.ARC&&i({type:T.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(" "!==s&&"	"!==s&&"\r"!==s&&"\n"!==s){if(","===s&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==s&&"-"!==s&&"."!==s){if(o)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw SyntaxError("Unterminated command at index "+n+".");if(!this.canParseCommandOrComma)throw SyntaxError('Unexpected character "'+s+'" at index '+n+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==s&&"Z"!==s){if("h"===s||"H"===s)this.curCommandType=T.HORIZ_LINE_TO,this.curCommandRelative="h"===s;else if("v"===s||"V"===s)this.curCommandType=T.VERT_LINE_TO,this.curCommandRelative="v"===s;else if("m"===s||"M"===s)this.curCommandType=T.MOVE_TO,this.curCommandRelative="m"===s;else if("l"===s||"L"===s)this.curCommandType=T.LINE_TO,this.curCommandRelative="l"===s;else if("c"===s||"C"===s)this.curCommandType=T.CURVE_TO,this.curCommandRelative="c"===s;else if("s"===s||"S"===s)this.curCommandType=T.SMOOTH_CURVE_TO,this.curCommandRelative="s"===s;else if("q"===s||"Q"===s)this.curCommandType=T.QUAD_TO,this.curCommandRelative="q"===s;else if("t"===s||"T"===s)this.curCommandType=T.SMOOTH_QUAD_TO,this.curCommandRelative="t"===s;else{if("a"!==s&&"A"!==s)throw SyntaxError('Unexpected character "'+s+'" at index '+n+".");this.curCommandType=T.ARC,this.curCommandRelative="a"===s}}else e.push({type:T.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}}else this.curNumber=s,this.curNumberHasDecimal="."===s}}else this.curNumber+=s,this.curNumberHasDecimal=!0}else this.curNumber+=s}else this.curNumber+=s,this.curNumberHasExp=!0}else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,n=Object.getPrototypeOf(this).parse.call(this,e);i<n.length;i++){var s=t(n[i]);Array.isArray(s)?r.push.apply(r,s):r.push(s)}return r}}})},e}(b),T=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return u(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=p.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var n=t(i[r]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===T.CLOSE_PATH)e+="z";else if(i.type===T.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===T.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===T.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===T.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===T.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===T.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===T.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===T.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==T.ARC)throw Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}(t)},e.parse=function(t){var e=new w,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(b),O=((x={})[T.MOVE_TO]=2,x[T.LINE_TO]=2,x[T.HORIZ_LINE_TO]=1,x[T.VERT_LINE_TO]=1,x[T.CLOSE_PATH]=0,x[T.QUAD_TO]=4,x[T.SMOOTH_QUAD_TO]=2,x[T.CURVE_TO]=6,x[T.SMOOTH_CURVE_TO]=4,x[T.ARC]=7,x);function A(t){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r(25225);var E=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],C=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24],P=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null},N=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>i(function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)})()};return("undefined"!=typeof DOMParser||void 0===t)&&Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function M(t){return t.replace(/(?!\u3000)\s+/gm," ")}function _(t){return t.replace(/^[\n \t]+/,"")}function R(t){return t.replace(/[\n \t]+$/,"")}function V(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var k=/^[A-Z-]+$/;function I(t){return k.test(t)?t.toLowerCase():t}function L(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function D(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(t,r)=>e--&&r?String(Math.round(parseFloat(t))):t)}var j=/(\[[^\]]+\])/g,B=/(#[^\s+>~.[:]+)/g,F=/(\.[^\s+>~.[:]+)/g,U=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,z=/(:[\w-]+\([^)]*\))/gi,H=/(:[^\s+>~.[:]+)/g,X=/([^\s+>~.[:]+)/g;function Y(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function q(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=Y(r,j),e[1]+=i,[r,i]=Y(r,B),e[0]+=i,[r,i]=Y(r,F),e[1]+=i,[r,i]=Y(r,U),e[2]+=i,[r,i]=Y(r,z),e[1]+=i,[r,i]=Y(r,H),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=Y(r,X),e[2]+=i,e.join("")}var W=1e-8;function G(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function Q(t,e){return(t[0]*e[0]+t[1]*e[1])/(G(t)*G(e))}function $(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Q(t,e))}function Z(t){return t*t*t}function K(t){return 3*t*t*(1-t)}function J(t){return 3*t*(1-t)*(1-t)}function tt(t){return(1-t)*(1-t)*(1-t)}function te(t){return t*t}function tr(t){return 2*t*(1-t)}function ti(t){return(1-t)*(1-t)}class tn{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new tn(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return M(this.getString()).trim().split(t).map(t=>new tn(e,r,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=D(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"==typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var s=this.getNumber();if(e&&s<1)return s*n.computeSize(r);return s}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?tn.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r&&(","===e[n]&&i++,3!==i);n++);if(t.hasValue()&&this.isString()&&3!==i){var s=new a(e);s.ok&&(s.alpha=t.getNumber(),e=s.toRGBA())}return new tn(this.document,this.name,e)}}tn.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class ts{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class ta{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=V(t);return new ta(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=V(t);return new ta(r,i)}static parsePath(t){for(var e=V(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new ta(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class to{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach((t,e)=>{for(var{run:i}=t,n=r[e];n;)i(n),n=n.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(s,a)&&(i[n]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInBox(s,a)&&(i[n]=t)})}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,n=new ta(t,e),s=i.canvas;s;)n.x-=s.offsetLeft,n.y-=s.offsetTop,s=s.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var tu="undefined"!=typeof window?window:null,th="undefined"!=typeof fetch?fetch.bind(void 0):null;class tc{constructor(t){var{fetch:e=th,window:r=tu}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new ts,this.mouse=new to(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:s,height:a,desiredHeight:o,minX:u=0,minY:h=0,refX:c,refY:l,clip:f=!1,clipX:g=0,clipY:p=0}=t,[d,v]=M(i).replace(/^defer\s/,"").split(" "),y=d||"xMidYMid",m=v||"meet",x=n/s,b=a/o,S=Math.min(x,b),w=Math.max(x,b),T=s,O=o;"meet"===m&&(T*=S,O*=S),"slice"===m&&(T*=w,O*=w);var A=new tn(e,"refX",c),E=new tn(e,"refY",l),C=A.hasValue()&&E.hasValue();if(C&&r.translate(-S*A.getPixels("x"),-S*E.getPixels("y")),f){var P=S*g,N=S*p;r.beginPath(),r.moveTo(P,N),r.lineTo(n,N),r.lineTo(n,a),r.lineTo(P,a),r.closePath(),r.clip()}if(!C){var _="meet"===m&&S===b,R="slice"===m&&w===b,V="meet"===m&&S===x,k="slice"===m&&w===x;y.startsWith("xMid")&&(_||R)&&r.translate(n/2-T/2,0),y.endsWith("YMid")&&(V||k)&&r.translate(0,a/2-O/2),y.startsWith("xMax")&&(_||R)&&r.translate(n-T,0),y.endsWith("YMax")&&(V||k)&&r.translate(0,a-O)}switch(!0){case"none"===y:r.scale(x,b);break;case"meet"===m:r.scale(S,S);break;case"slice"===m:r.scale(w,w)}r.translate(-u,-h)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:a=!1,forceRedraw:o,scaleWidth:u,scaleHeight:h,offsetX:c,offsetY:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:f,mouse:g}=this,p=1e3/f;if(this.frameDuration=p,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,n,a,u,h,c,l),e){var d=Date.now(),v=d,y=0,m=()=>{(y=(d=Date.now())-v)>=p&&(v=d-y%p,this.shouldUpdate(i,o)&&(this.render(t,n,a,u,h,c,l),g.runEvents())),this.intervalId=s(m)};r||g.start(),this.intervalId=s(m)}}stop(){this.intervalId&&(s.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce((t,e)=>e.update(r)||t,!1))return!0}return!!("function"==typeof e&&e()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(t,e,r,i,n,s,a){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:h,ctx:c,isFirstRender:l}=this,f=c.canvas;h.clear(),f.width&&f.height?h.setCurrent(f.width,f.height):h.setCurrent(o,u);var g=t.getStyle("width"),p=t.getStyle("height");!e&&(l||"number"!=typeof i&&"number"!=typeof n)&&(g.hasValue()&&(f.width=g.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),p.hasValue()&&(f.height=p.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&g.hasValue()&&p.hasValue()&&(d=g.getPixels("x"),v=p.getPixels("y")),h.setCurrent(d,v),"number"==typeof s&&t.getAttribute("x",!0).setValue(s),"number"==typeof a&&t.getAttribute("y",!0).setValue(a),"number"==typeof i||"number"==typeof n){var y=V(t.getAttribute("viewBox").getString()),m=0,x=0;if("number"==typeof i){var b=t.getStyle("width");b.hasValue()?m=b.getPixels("x")/i:isNaN(y[2])||(m=y[2]/i)}if("number"==typeof n){var S=t.getStyle("height");S.hasValue()?x=S.getPixels("y")/n:isNaN(y[3])||(x=y[3]/n)}m||(m=x),x||(x=m),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var w=t.getStyle("transform",!0,!0);w.setValue("".concat(w.getString()," scale(").concat(1/m,", ").concat(1/x,")"))}r||c.clearRect(0,0,d,v),t.render(c),l&&(this.isFirstRender=!1)}}tc.defaultWindow=tu,tc.defaultFetch=th;var{defaultFetch:tl}=tc,tf="undefined"!=typeof DOMParser?DOMParser:null;class tg{constructor(){var{fetch:t=tl,DOMParser:e=tf}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return i(function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)})()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw Error(e.textContent);return t}load(t){var e=this;return i(function*(){var r=yield e.fetch(t),i=yield r.text();return e.parseFromString(i)})()}}class tp{constructor(t,e){this.type="translate",this.point=null,this.point=ta.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class td{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=V(e);this.angle=new tn(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(s.getRadians()),t.translate(-a,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(-1*s.getRadians()),t.translate(-a,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class tv{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=ta.parseScale(e);(0===i.x||0===i.y)&&(i.x=W,i.y=W),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(e,r||e),t.translate(-s,-a)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(1/e,1/r||e),t.translate(-s,-a)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class ty{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=V(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),s=r.getPixels("y");t.translate(n,s),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-s)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],s=i[2],a=i[4],o=i[1],u=i[3],h=i[5],c=1/(n*(+u-0*h)-s*(+o-0*h)+a*(0*o-0*u)),l=e.getPixels("x"),f=r.getPixels("y");t.translate(l,f),t.transform(c*(+u-0*h),c*(0*h-+o),c*(0*a-+s),c*(+n-0*a),c*(s*h-a*u),c*(a*o-n*h)),t.translate(-l,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}class tm extends ty{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new tn(t,"angle",e)}}class tx extends tm{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class tb extends tm{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class tS{constructor(t,e,r){this.document=t,this.transforms=[],M(e).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/).forEach(t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),n=tS.transformTypes[e];void 0!==n&&this.transforms.push(new n(this.document,i,r))}})}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split();return r.hasValue()?new tS(t,r.getString(),[i,n]):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length,i=r-1;i>=0;i--)e[i].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}tS.transformTypes={translate:tp,rotate:td,scale:tv,matrix:ty,skewX:tx,skewY:tb};class tw{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!e||1!==e.nodeType)return;Array.from(e.attributes).forEach(e=>{var r=I(e.nodeName);this.attributes[r]=new tn(t,r,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()&&this.getAttribute("style").getString().split(";").map(t=>t.trim()).forEach(e=>{if(e){var[r,i]=e.split(":").map(t=>t.trim());this.styles[r]=new tn(t,r,i)}});var{definitions:i}=t,n=this.getAttribute("id");n.hasValue()&&!i[n.getString()]&&(i[n.getString()]=this),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}})}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new tn(this.document,t,"");return this.attributes[t]=i,i}return r||tn.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return tn.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!=n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:s}=this;if(s){var a=s.getStyle(t);if(null!=a&&a.hasValue())return a}}if(e){var o=new tn(this.document,t,"");return this.styles[t]=o,o}return i||tn.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=tS.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof tw?t:this.document.createElement(t);e.parent=this,tw.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!!i&&""!==i&&i.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var s in i){var a=this.stylesSpecificity[s];void 0===a&&(a="000"),n>=a&&(this.styles[s]=i[s],this.stylesSpecificity[s]=n)}}}removeStyles(t,e){return e.reduce((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]},[])}restoreStyles(t,e){e.forEach(e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)})}isFirstChild(){var t;return(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))===0}}tw.ignoreChildTypes=["title"];class tT extends tw{constructor(t,e,r){super(t,e,r)}}function tO(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}class tA{constructor(t,e,r,i,n,s){var a=s?"string"==typeof s?tA.parse(s):s:{};this.fontFamily=n||a.fontFamily,this.fontSize=i||a.fontSize,this.fontStyle=t||a.fontStyle,this.fontWeight=r||a.fontWeight,this.fontVariant=e||a.fontVariant}static parse(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0,r="",i="",n="",s="",a="",o=M(t).trim().split(" "),u={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return o.forEach(t=>{switch(!0){case!u.fontStyle&&tA.styles.includes(t):"inherit"!==t&&(r=t),u.fontStyle=!0;break;case!u.fontVariant&&tA.variants.includes(t):"inherit"!==t&&(i=t),u.fontStyle=!0,u.fontVariant=!0;break;case!u.fontWeight&&tA.weights.includes(t):"inherit"!==t&&(n=t),u.fontStyle=!0,u.fontVariant=!0,u.fontWeight=!0;break;case!u.fontSize:"inherit"!==t&&([s]=t.split("/")),u.fontStyle=!0,u.fontVariant=!0,u.fontWeight=!0,u.fontSize=!0;break;default:"inherit"!==t&&(a+=t)}}),new tA(r,i,n,s,a,e)}toString(){var t;return[function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:if(/^oblique\s+(-|)\d+deg$/.test(e))return e;return""}}(this.fontStyle),this.fontVariant,function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:if(/^[\d.]+$/.test(e))return e;return""}}(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"==typeof process?t:t.trim().split(",").map(tO).join(","))].join(" ").trim()}}tA.styles="normal|italic|oblique|inherit",tA.variants="normal|small-caps|inherit",tA.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class tE{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var s=6*e-12*r+6*i,a=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0===a){if(0===s)return;var u=-o/s;0<u&&u<1&&(t?this.addX(this.sumCubic(u,e,r,i,n)):this.addY(this.sumCubic(u,e,r,i,n)));return}var h=Math.pow(s,2)-4*o*a;if(!(h<0)){var c=(-s+Math.sqrt(h))/(2*a);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)));var l=(-s-Math.sqrt(h))/(2*a);0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,s,a,o){this.addPoint(t,e),this.addPoint(a,o),this.bezierCurveAdd(!0,t,r,n,a),this.bezierCurveAdd(!1,e,i,s,o)}addQuadraticCurve(t,e,r,i,n,s){var a=t+2/3*(r-t),o=e+2/3*(i-e);this.addBezierCurve(t,e,a,a+1/3*(n-t),o,o+1/3*(s-e),n,s)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:s}=this;return r<=t&&t<=n&&i<=e&&e<=s}}class tC extends T{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new ta(0,0),this.control=new ta(0,0),this.current=new ta(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new ta(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==T.CURVE_TO&&t!==T.SMOOTH_CURVE_TO&&t!==T.QUAD_TO&&t!==T.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this;return new ta(2*e-i,2*r-n)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r]){for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}}return t}}class tP extends tw{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),s=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var a=r.getFillStyleDefinition(this,i);a&&(t.fillStyle=a)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var u=new tn(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=u}if(n.isUrlDefinition()){var h=n.getFillStyleDefinition(this,s);h&&(t.strokeStyle=h)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var c=n.getString();"inherit"!==c&&(t.strokeStyle="none"===c?"rgba(0,0,0,0)":c)}if(s.hasValue()){var l=new tn(this.document,"stroke",t.strokeStyle).addOpacity(s).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");f.hasValue()&&(t.lineWidth=f.getPixels()||W);var g=this.getStyle("stroke-linecap"),p=this.getStyle("stroke-linejoin"),d=this.getStyle("stroke-miterlimit"),v=this.getStyle("stroke-dasharray"),y=this.getStyle("stroke-dashoffset");if(g.hasValue()&&(t.lineCap=g.getString()),p.hasValue()&&(t.lineJoin=p.getString()),d.hasValue()&&(t.miterLimit=d.getNumber()),v.hasValue()&&"none"!==v.getString()){var m=V(v.getString());void 0!==t.setLineDash?t.setLineDash(m):void 0!==t.webkitLineDash?t.webkitLineDash=m:void 0!==t.mozDash&&(1!==m.length||0!==m[0])&&(t.mozDash=m);var x=y.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=x:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=x:void 0!==t.mozDashOffset&&(t.mozDashOffset=x)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var b=this.getStyle("font"),S=this.getStyle("font-style"),w=this.getStyle("font-variant"),T=this.getStyle("font-weight"),O=this.getStyle("font-size"),A=this.getStyle("font-family"),E=new tA(S.getString(),w.getString(),T.getString(),O.hasValue()?"".concat(O.getPixels(!0),"px"):"",A.getString(),tA.parse(b.getString(),t.font));S.setValue(E.fontStyle),w.setValue(E.fontVariant),T.setValue(E.fontWeight),O.setValue(E.fontSize),A.setValue(E.fontFamily),t.font=E.toString(),O.isPixels()&&(this.document.emSize=O.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class tN extends tP{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new tC(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new tE;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case tC.MOVE_TO:this.pathM(t,r);break;case tC.LINE_TO:this.pathL(t,r);break;case tC.HORIZ_LINE_TO:this.pathH(t,r);break;case tC.VERT_LINE_TO:this.pathV(t,r);break;case tC.CURVE_TO:this.pathC(t,r);break;case tC.SMOOTH_CURVE_TO:this.pathS(t,r);break;case tC.QUAD_TO:this.pathQ(t,r);break;case tC.SMOOTH_QUAD_TO:this.pathT(t,r);break;case tC.ARC:this.pathA(t,r);break;case tC.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((t,e)=>[t,r[e]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),s=this.getStyle("marker-mid"),a=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[u,h]=r[0];o.render(t,u,h)}if(s.isUrlDefinition())for(var c=s.getDefinition(),l=1;l<i;l++){var[f,g]=r[l];c.render(t,f,g)}if(a.isUrlDefinition()){var p=a.getDefinition(),[d,v]=r[i];p.render(t,d,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=tN.pathM(r),{x:n,y:s}=i;r.addMarker(i),e.addPoint(n,s),t&&t.moveTo(n,s)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=tN.pathL(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathH(t){var{current:e,command:r}=t,i=new ta((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=tN.pathH(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathV(t){var{current:e,command:r}=t,i=new ta(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=tN.pathV(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathC(t){var{current:e}=t,r=t.getPoint("x1","y1");return{current:e,point:r,controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=tN.pathC(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathS(t){var{current:e}=t,r=t.getReflectedControlPoint();return{current:e,point:r,controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=tN.pathS(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=tN.pathQ(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=tN.pathT(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:s,lArcFlag:a,sweepFlag:o}=r,u=Math.PI/180*s,h=t.getAsCurrentPoint(),c=new ta(Math.cos(u)*(e.x-h.x)/2+Math.sin(u)*(e.y-h.y)/2,-Math.sin(u)*(e.x-h.x)/2+Math.cos(u)*(e.y-h.y)/2),l=Math.pow(c.x,2)/Math.pow(i,2)+Math.pow(c.y,2)/Math.pow(n,2);l>1&&(i*=Math.sqrt(l),n*=Math.sqrt(l));var f=(a===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(c.y,2)-Math.pow(n,2)*Math.pow(c.x,2))/(Math.pow(i,2)*Math.pow(c.y,2)+Math.pow(n,2)*Math.pow(c.x,2)));isNaN(f)&&(f=0);var g=new ta(f*i*c.y/n,-(f*n)*c.x/i),p=new ta((e.x+h.x)/2+Math.cos(u)*g.x-Math.sin(u)*g.y,(e.y+h.y)/2+Math.sin(u)*g.x+Math.cos(u)*g.y),d=$([1,0],[(c.x-g.x)/i,(c.y-g.y)/n]),v=[(c.x-g.x)/i,(c.y-g.y)/n],y=[(-c.x-g.x)/i,(-c.y-g.y)/n],m=$(v,y);return -1>=Q(v,y)&&(m=Math.PI),Q(v,y)>=1&&(m=0),{currentPoint:h,rX:i,rY:n,sweepFlag:o,xAxisRotation:u,centp:p,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:s,sweepFlag:a,xAxisRotation:o,centp:u,a1:h,ad:c}=tN.pathA(r),l=1-a?1:-1,f=h+c/2*l,g=new ta(u.x+n*Math.cos(f),u.y+s*Math.sin(f));if(r.addMarkerAngle(g,f-l*Math.PI/2),r.addMarkerAngle(i,f-l*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(h)&&!isNaN(c)){var p=n>s?1:n/s,d=n>s?s/n:1;t.translate(u.x,u.y),t.rotate(o),t.scale(p,d),t.arc(0,0,n>s?n:s,h,h+c,!!(1-a)),t.scale(1/p,1/d),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){tN.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class tM extends tN{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class t_ extends tP{constructor(t,e,r){super(t,e,new.target===t_||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n}),e}getFontSize(){var{document:t,parent:e}=this,r=tA.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new tE(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var s=e.length,a=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===a)&&r<s-1&&" "!==o&&(u="terminal"),r>0&&" "!==a&&r<s-1&&" "!==o&&(u="medial"),r>0&&" "!==a&&(r===s-1||" "===o)&&(u="initial"),void 0!==t.glyphs[i]){var h=t.glyphs[i];n=h instanceof tM?h:h[u]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,s=M(e.textContent||"");return 0===i&&(s=_(s)),i===n&&(s=R(s)),s}renderChildren(t){if("text"!==this.type){this.renderTElementChildren(t);return}this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,r)=>{this.renderChild(t,this,this,r)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n){for(var{unitsPerEm:s}=n.fontFace,a=tA.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(a.fontSize),u=r.getStyle("font-style").getString(a.fontStyle),h=o/s,c=n.isRTL?i.split("").reverse().join(""):i,l=V(r.getAttribute("dx").getString()),f=c.length,g=0;g<f;g++){var p=this.getGlyph(n,c,g);t.translate(this.x,this.y),t.scale(h,-h);var d=t.lineWidth;t.lineWidth=t.lineWidth*s/o,"italic"===u&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/h,-1/h),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||n.horizAdvX)/s,void 0===l[g]||isNaN(l[g])||(this.x+=l[g])}return}var{x:v,y}=this;t.fillStyle&&t.fillText(i,v,y),t.strokeStyle&&t.strokeText(i,v,y)}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)}):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!=typeof n.measureText)return n;t.save(),n.setContext(t,!0);var s=n.getAttribute("x"),a=n.getAttribute("y"),o=n.getAttribute("dx"),u=n.getAttribute("dy"),h=n.getStyle("font-family").getDefinition(),c=!!h&&h.isRTL;0!==i||(s.hasValue()||s.setValue(n.getInheritedAttribute("x")),a.hasValue()||a.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),u.hasValue()||u.setValue(n.getInheritedAttribute("dy")));var l=n.measureText(t);return c&&(e.x-=l),s.hasValue()?(e.applyAnchoring(),n.x=s.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,c||(e.x+=l),a.hasValue()?(n.y=a.getPixels("y"),u.hasValue()&&(n.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+l),e.maxX=Math.max(e.maxX,n.x,n.x+l),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!=typeof n.getBoundingBox)return null;var s=n.getBoundingBox(t);return s?(n.children.forEach((r,i)=>{var a=e.getChildBoundingBox(t,e,n,i);s.addBoundingBox(a)}),s):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach((r,i)=>{e.renderChild(t,e,n,i)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),s=i.isRTL?e.split("").reverse().join(""):e,a=V(r.getAttribute("dx").getString()),o=s.length,u=0,h=0;h<o;h++)u+=(this.getGlyph(i,s,h).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,void 0===a[h]||isNaN(a[h])||(u+=a[h]);return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:c}=t.measureText(e);return this.clearContext(t),t.restore(),c}getInheritedAttribute(t){for(var e=this;e instanceof t_&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class tR extends t_{constructor(t,e,r){super(t,e,new.target===tR||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class tV extends tR{constructor(){super(...arguments),this.type="textNode"}}class tk extends tP{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,s=t.canvas;if(i.setDefaults(t),s.style&&void 0!==t.font&&n&&void 0!==n.getComputedStyle){t.font=n.getComputedStyle(s).getPropertyValue("font");var a=new tn(r,"fontSize",tA.parse(t.font).fontSize);a.hasValue()&&(r.rootEmSize=a.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),c=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?V(l.getString()):null,g=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,d=0,v=0,y=0;f&&(p=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"!==this.type||(v=p,y=d,p=0,d=0)),i.viewPort.setCurrent(o,u),this.node&&(!this.parent||(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)==="foreignObject")&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:u,minX:p,minY:d,refX:h.getValue(),refY:c.getValue(),clip:g,clipX:v,clipY:y}),f&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),s=this.getAttribute("viewBox"),a=this.getAttribute("style"),o=i.getNumber(0),u=n.getNumber(0);if(r){if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}}if(i.setValue(t),n.setValue(e),s.hasValue()||s.setValue("0 0 ".concat(o||t," ").concat(u||e)),a.hasValue()){var c=this.getStyle("width"),l=this.getStyle("height");c.hasValue()&&c.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}class tI extends tN{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),s=this.getAttribute("rx"),a=this.getAttribute("ry"),o=s.getPixels("x"),u=a.getPixels("y");if(s.hasValue()&&!a.hasValue()&&(u=o),a.hasValue()&&!s.hasValue()&&(o=u),o=Math.min(o,i/2),u=Math.min(u,n/2),t){var h=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+h*o,r,e+i,r+u-h*u,e+i,r+u),t.lineTo(e+i,r+n-u),t.bezierCurveTo(e+i,r+n-u+h*u,e+i-o+h*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-h*o,r+n,e,r+n-u+h*u,e,r+n-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-h*u,e+o-h*o,r,e+o,r),t.closePath())}return new tE(e,r,e+i,r+n)}getMarkers(){return null}}class tL extends tN{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new tE(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class tD extends tN{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),s=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,s),t.bezierCurveTo(n+r,s+e*i,n+e*r,s+i,n,s+i),t.bezierCurveTo(n-e*r,s+i,n-r,s+e*i,n-r,s),t.bezierCurveTo(n-r,s-e*i,n-e*r,s-i,n,s-i),t.bezierCurveTo(n+e*r,s-i,n+r,s-e*i,n+r,s),t.closePath()),new tE(n-r,s-i,n+r,s+i)}getMarkers(){return null}}class tj extends tN{constructor(){super(...arguments),this.type="line"}getPoints(){return[new ta(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new ta(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new tE(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class tB extends tN{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=ta.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new tE(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach(e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)}),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class tF extends tB{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class tU extends tw{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),s=new tk(this.document,null);s.attributes.viewBox=new tn(this.document,"viewBox",this.getAttribute("viewBox").getValue()),s.attributes.width=new tn(this.document,"width","".concat(i,"px")),s.attributes.height=new tn(this.document,"height","".concat(n,"px")),s.attributes.transform=new tn(this.document,"transform",this.getAttribute("patternTransform").getValue()),s.children=this.children;var a=this.document.createCanvas(i,n),o=a.getContext("2d"),u=this.getAttribute("x"),h=this.getAttribute("y");u.hasValue()&&h.hasValue()&&o.translate(u.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var c=-1;c<=1;c++)for(var l=-1;l<=1;l++)o.save(),s.attributes.x=new tn(this.document,"x",c*a.width),s.attributes.y=new tn(this.document,"y",l*a.height),s.render(o),o.restore();return t.createPattern(a,"repeat")}}class tz extends tw{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,s=this.getAttribute("orient").getString("auto"),a=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===s&&t.rotate(r),"strokeWidth"===a&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new tk(this.document,null);o.type=this.type,o.attributes.viewBox=new tn(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new tn(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new tn(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new tn(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new tn(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new tn(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new tn(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new tn(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===a&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===s&&t.rotate(-r),t.translate(-i,-n)}}}class tH extends tw{constructor(){super(...arguments),this.type="defs"}render(){}}class tX extends tP{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new tE;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class tY extends tw{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach(t=>{"stop"===t.type&&i.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,s=this.getGradient(t,e);if(!s)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach(t=>{s.addColorStop(t.offset,this.addParentOpacity(r,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:a}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=a.screen,[h]=u.viewPorts,c=new tI(a,null);c.attributes.x=new tn(a,"x",-o/3),c.attributes.y=new tn(a,"y",-o/3),c.attributes.width=new tn(a,"width",o),c.attributes.height=new tn(a,"height",o);var l=new tX(a,null);l.attributes.transform=new tn(a,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[c];var f=new tk(a,null);f.attributes.x=new tn(a,"x",0),f.attributes.y=new tn(a,"y",0),f.attributes.width=new tn(a,"width",h.width),f.attributes.height=new tn(a,"height",h.height),f.children=[l];var g=a.createCanvas(h.width,h.height),p=g.getContext("2d");return p.fillStyle=s,f.render(p),p.createPattern(g,"no-repeat")}return s}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new tn(this.document,"color",e).addOpacity(t).getColor():e}}class tq extends tY{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),s=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),a=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===a&&s===o?null:t.createLinearGradient(n,s,a,o)}}class tW extends tY{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),s=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),a=n,o=s;this.getAttribute("fx").hasValue()&&(a=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return t.createRadialGradient(a,o,h,n,s,u)}}class tG extends tw{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),s=this.getStyle("stop-color",!0);""===s.getString()&&s.setValue("#000"),n.hasValue()&&(s=s.addOpacity(n)),this.offset=i,this.color=s.getColor()}}class tQ extends tw{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new tn(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var s=this.calcValue(),a=this.getAttribute("type");if(a.hasValue()){var o=a.getString();s="".concat(o,"(").concat(s,")")}r.setValue(s),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),s=Math.ceil(i);r.from=new tn(t,"from",parseFloat(e.getValue()[n])),r.to=new tn(t,"to",parseFloat(e.getValue()[s])),r.progress=(i-n)/(s-n)}else r.from=this.from,r.to=this.to;return r}}class t$ extends tQ{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new a(e.getColor()),n=new a(r.getColor());if(i.ok&&n.ok){var s=i.r+(n.r-i.r)*t,o=i.g+(n.g-i.g)*t,u=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(s),", ").concat(Math.floor(o),", ").concat(Math.floor(u),")")}return this.getAttribute("from").getColor()}}class tZ extends tQ{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=V(e.getString()),n=V(r.getString());return i.map((e,r)=>e+(n[r]-e)*t).join(" ")}}class tK extends tw{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var s of n)switch(s.type){case"font-face":this.fontFace=s;var a=s.getStyle("font-family");a.hasValue()&&(i[a.getString()]=this);break;case"missing-glyph":this.missingGlyph=s;break;case"glyph":s.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[s.unicode]&&(this.glyphs[s.unicode]=Object.create(null)),this.glyphs[s.unicode][s.arabicForm]=s):this.glyphs[s.unicode]=s}}render(){}}class tJ extends tw{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class t0 extends tN{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class t1 extends t_{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class t2 extends t_{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],s=i.length>0&&Array.from(i).every(t=>3===t.nodeType);this.hasText=s,this.text=s?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,s=new tn(e,"fontSize",tA.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new tE(r,i-s.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var a=new tX(this.document,null);a.children=this.children,a.parent=this,a.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function t3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function t4(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t3(Object(r),!0).forEach(function(e){n(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}class t5 extends t_{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:r,points:i}=e;switch(r){case tC.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case tC.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case tC.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case tC.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case tC.ARC:var[n,s,a,o,u,h,c,l]=i,f=a>o?1:a/o,g=a>o?o/a:1;t&&(t.translate(n,s),t.rotate(c),t.scale(f,g),t.arc(0,0,a>o?a:o,u,u+h,!!(1-l)),t.scale(1/f,1/g),t.rotate(-c),t.translate(-n,-s));break;case tC.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach((i,n)=>{var{p0:s,p1:a,rotation:o,text:u}=i;t.save(),t.translate(s.x,s.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(s.x,s.y+r/8),t.lineTo(a.x,a.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,s,a,o,u){var h=s,c=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(c+=(i-r)/n),u>-1&&(h+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(h,l,0),g=this.getEquidistantPointOnPath(h+c,l,0),p={p0:f,p1:g},d=f&&g?Math.atan2(g.y-f.y,g.x-f.x):0;if(a){var v=Math.cos(Math.PI/2+d)*a,y=Math.cos(-d)*a;p.p0=t4(t4({},f),{},{x:f.x+v,y:f.y+y}),p.p1=t4(t4({},g),{},{x:g.x+v,y:g.y+y})}return{offset:h+=c,segment:p,rotation:d}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),s=this.parent.getAttribute("dy").getPixels("y"),a=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),h=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(h=o.getPixels()):h=u.getPixels();var c=[],l=e.length;this.letterSpacingCache=c;for(var f=0;f<l;f++)c.push(void 0!==n[f]?n[f]:h);var g=c.reduce((t,e,r)=>0===r?0:t+e||0,0),p=this.measureText(t),d=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;("middle"===a||"center"===a)&&(m=-d/2),("end"===a||"right"===a)&&(m=-d),m+=y,r.forEach((e,n)=>{var{offset:o,segment:u,rotation:h}=this.findSegmentToFitChar(t,a,d,v,i,m,s,e,n);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[n],p0:u.p0,p1:u.p1,rotation:h})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,s=i?i.y:0,a=r.next(),o=a.type,u=[];switch(a.type){case tC.MOVE_TO:this.pathM(r,u);break;case tC.LINE_TO:o=this.pathL(r,u);break;case tC.HORIZ_LINE_TO:o=this.pathH(r,u);break;case tC.VERT_LINE_TO:o=this.pathV(r,u);break;case tC.CURVE_TO:this.pathC(r,u);break;case tC.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case tC.QUAD_TO:this.pathQ(r,u);break;case tC.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case tC.ARC:u=this.pathA(r);break;case tC.CLOSE_PATH:tN.pathZ(r)}a.type!==tC.CLOSE_PATH?e.push({type:o,points:u,start:{x:n,y:s},pathLength:this.calcLength(n,s,o,u)}):e.push({type:tC.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=tN.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=tN.pathL(t).point;return e.push(r,i),tC.LINE_TO}pathH(t,e){var{x:r,y:i}=tN.pathH(t).point;return e.push(r,i),tC.LINE_TO}pathV(t,e){var{x:r,y:i}=tN.pathV(t).point;return e.push(r,i),tC.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=tN.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=tN.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),tC.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=tN.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=tN.pathT(t);return e.push(r.x,r.y,i.x,i.y),tC.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:s,a1:a,ad:o}=tN.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[s.x,s.y,e,r,a,o,n,i]}calcLength(t,e,r,i){var n=0,s=null,a=null,o=0;switch(r){case tC.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case tC.CURVE_TO:for(o=.01,n=0,s=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]);o<=1;o+=.01)a=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case tC.QUAD_TO:for(o=.01,n=0,s=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]);o<=1;o+=.01)a=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case tC.ARC:n=0;var u=i[4],h=i[5],c=i[4]+h,l=Math.PI/180;if(Math.abs(u-c)<l&&(l=Math.abs(u-c)),s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),h<0)for(o=u-l;o>c;o-=l)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;else for(o=u+l;o<c;o+=l)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],c,0),n+=this.getLineLength(s.x,s.y,a.x,a.y)}return 0}getPointOnLine(t,e,r,i,n){var s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+W),u=Math.sqrt(t*t/(1+o*o));i<e&&(u*=-1);var h=o*u,c=null;if(i===e)c={x:s,y:a+h};else if((a-r)/(s-e+W)===o)c={x:s+u,y:a+h};else{var l=0,f=0,g=this.getLineLength(e,r,i,n);if(g<W)return null;var p=(s-e)*(i-e)+(a-r)*(n-r);p/=g*g,l=e+p*(i-e),f=r+p*(n-r);var d=this.getLineLength(s,a,l,f),v=Math.sqrt(t*t-d*d);u=Math.sqrt(v*v/(1+o*o)),i<e&&(u*=-1),h=o*u,c={x:l+u,y:f+h}}return c}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var s of n){if(s&&(s.pathLength<5e-5||r+s.pathLength+5e-5<t)){r+=s.pathLength;continue}var a=t-r,o=0;switch(s.type){case tC.LINE_TO:i=this.getPointOnLine(a,s.start.x,s.start.y,s.points[0],s.points[1],s.start.x,s.start.y);break;case tC.ARC:var u=s.points[4],h=s.points[5],c=s.points[4]+h;if(o=u+a/s.pathLength*h,h<0&&o<c||h>=0&&o>c)break;i=this.getPointOnEllipticalArc(s.points[0],s.points[1],s.points[2],s.points[3],o,s.points[6]);break;case tC.CURVE_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3],s.points[4],s.points[5]);break;case tC.QUAD_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3])}if(i)return i;break}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return -1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,s,a,o,u){return{x:o*Z(t)+s*K(t)+i*J(t)+e*tt(t),y:u*Z(t)+a*K(t)+n*J(t)+r*tt(t)}}getPointOnQuadraticBezier(t,e,r,i,n,s,a){return{x:t*t*s+i*tr(t)+e*ti(t),y:t*t*a+n*tr(t)+r*ti(t)}}getPointOnEllipticalArc(t,e,r,i,n,s){var a=Math.cos(s),o=Math.sin(s),u={x:r*Math.cos(n),y:i*Math.sin(n)};return{x:t+(u.x*a-u.y*o),y:e+(u.x*o+u.y*a)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var s=0,a=0;a<=r;a+=i){var o=this.getPointOnPath(a),u=this.getPointOnPath(a+i);o&&u&&(s+=this.getLineLength(o.x,o.y,u.x,u.y))>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:a}),s-=n)}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var t7=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class t9 extends tP{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(!i)return;var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}loadImage(t){var e=this;return i(function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0})()}loadSvg(t){var e=this;return i(function*(){var r=t7.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield e.document.fetch(t);e.image=yield n.text()}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0})()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&a&&o){if(t.save(),t.translate(n,s),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:a,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var h=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a,desiredWidth:h.width,height:o,desiredHeight:h.height}),this.loaded&&(void 0===h.complete||h.complete)&&t.drawImage(h,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y");return new tE(t,e,t+this.getStyle("width").getPixels("x"),e+this.getStyle("height").getPixels("y"))}}class t8 extends tP{constructor(){super(...arguments),this.type="symbol"}render(t){}}class t6{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return i(function*(){try{var{document:i}=r,n=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(n).forEach(e=>{var r=i.createElement(e);i.definitions[t]=r})}catch(t){console.error('Error while loading font "'.concat(e,'":'),t)}r.loaded=!0})()}}class et extends tw{constructor(t,e,r){super(t,e,r),this.type="style",M(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),s=i[1].split(";");n.forEach(e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(s.forEach(e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),s=e.substr(r+1,e.length-r).trim();n&&s&&(i[n]=new tn(t,n,s))}),t.styles[r]=i,t.stylesSpecificity[r]=q(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach(e=>{if(e.indexOf('format("svg")')>0){var r=L(e);r&&new t6(t).load(n,r)}})}}})}})}}et.parseExternalUrl=L;class ee extends tP{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new tk(e,null)).attributes.viewBox=new tn(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new tn(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new tn(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new tn(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),s=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new tn(e,"width",n.getString())),s.hasValue()&&(i.attributes.height=new tn(e,"height",s.getString()))}var a=i.parent;i.parent=this,i.render(t),i.parent=a}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return tS.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function er(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class ei extends tw{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=V(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var s=i[0]*Math.PI/180;i=[er(s,.213,.787,-.213),er(s,.715,-.715,-.715),er(s,.072,-.072,.928),0,0,er(s,.213,-.213,.143),er(s,.715,.285,.14),er(s,.072,-.072,-.283),0,0,er(s,.213,-.213,-.787),er(s,.715,-.715,.715),er(s,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:s,matrix:a}=this,o=t.getImageData(0,0,i,n),u=0;u<n;u++)for(var h=0;h<i;h++){var c,l,f,g,p,d,v,y,m,x,b,S,w,T,O,A,E,C,P,N,M=(c=o.data)[u*i*4+4*h+0],_=(l=o.data)[u*i*4+4*h+1],R=(f=o.data)[u*i*4+4*h+2],V=(g=o.data)[u*i*4+4*h+3],k=a[0]*M+a[1]*_+a[2]*R+a[3]*V+ +a[4],I=a[5]*M+a[6]*_+a[7]*R+a[8]*V+ +a[9],L=a[10]*M+a[11]*_+a[12]*R+a[13]*V+ +a[14],D=a[15]*M+a[16]*_+a[17]*R+a[18]*V+ +a[19];s&&(k=0,I=0,L=0,D*=V/255),p=o.data,d=h,v=u,y=k,p[v*i*4+4*d+0]=y,m=o.data,x=h,b=u,S=I,m[b*i*4+4*x+1]=S,w=o.data,T=h,O=u,A=L,w[O*i*4+4*T+2]=A,E=o.data,C=h,P=u,N=D,E[P*i*4+4*C+3]=N}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class en extends tw{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");if(!s&&!a){var o=new tE;this.children.forEach(e=>{o.addBoundingBox(e.getBoundingBox(t))}),i=Math.floor(o.x1),n=Math.floor(o.y1),s=Math.floor(o.width),a=Math.floor(o.height)}var u=this.removeStyles(e,en.ignoreStyles),h=r.createCanvas(i+s,n+a),c=h.getContext("2d");r.screen.setDefaults(c),this.renderChildren(c),new ei(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(c,0,0,i+s,n+a);var l=r.createCanvas(i+s,n+a),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=c.createPattern(h,"no-repeat"),f.fillRect(0,0,i+s,n+a),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,i+s,n+a),this.restoreStyles(e,u)}render(t){}}en.ignoreStyles=["mask","transform","clip-path"];var es=()=>{};class ea extends tw{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=es,r.closePath=es),Reflect.apply(i,t,[]),this.children.forEach(i=>{if(void 0!==i.path){var s=void 0!==i.elementTransform?i.elementTransform():null;s||(s=tS.fromElement(e,i)),s&&s.apply(t),i.path(t),r&&(r.closePath=n),s&&s.unapply(t)}}),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class eo extends tw{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var s=0,a=0;i.forEach(t=>{var e=t.extraFilterDistance||0;s=Math.max(s,e),a=Math.max(a,e)});var o=Math.floor(n.width),u=Math.floor(n.height),h=o+2*s,c=u+2*a;if(!(h<1)&&!(c<1)){var l=Math.floor(n.x),f=Math.floor(n.y),g=this.removeStyles(e,eo.ignoreStyles),p=r.createCanvas(h,c),d=p.getContext("2d");r.screen.setDefaults(d),d.translate(-l+s,-f+a),e.render(d),i.forEach(t=>{"function"==typeof t.apply&&t.apply(d,0,0,h,c)}),t.drawImage(p,0,0,h,c,l-s,f-a,h,c),this.restoreStyles(e,g)}}}render(t){}}eo.ignoreStyles=["filter","transform","clip-path"];class eu extends tw{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class eh extends tw{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class ec extends tw{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class el extends tw{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,n){var{document:s,blurRadius:a}=this,o=s.window?s.window.document.body:null,u=t.canvas;u.id=s.getUniqueId(),o&&(u.style.display="none",o.appendChild(u)),function(t,e,r,i,n,s){if(!isNaN(s)&&!(s<1)){s|=0;var a=function(t,e,r,i,n){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==A(t)||!("getContext"in t))throw TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var s=t.getContext("2d");try{return s.getImageData(e,r,i,n)}catch(t){throw Error("unable to access image data: "+t)}}(t,e,r,i,n);a=function(t,e,r,i,n,s){for(var a,o=t.data,u=2*s+1,h=i-1,c=n-1,l=s+1,f=l*(l+1)/2,g=new P,p=g,d=1;d<u;d++)p=p.next=new P,d===l&&(a=p);p.next=g;for(var v=null,y=null,m=0,x=0,b=E[s],S=C[s],w=0;w<n;w++){p=g;for(var T=o[x],O=o[x+1],A=o[x+2],N=o[x+3],M=0;M<l;M++)p.r=T,p.g=O,p.b=A,p.a=N,p=p.next;for(var _=0,R=0,V=0,k=0,I=l*T,L=l*O,D=l*A,j=l*N,B=f*T,F=f*O,U=f*A,z=f*N,H=1;H<l;H++){var X=x+((h<H?h:H)<<2),Y=o[X],q=o[X+1],W=o[X+2],G=o[X+3],Q=l-H;B+=(p.r=Y)*Q,F+=(p.g=q)*Q,U+=(p.b=W)*Q,z+=(p.a=G)*Q,_+=Y,R+=q,V+=W,k+=G,p=p.next}v=g,y=a;for(var $=0;$<i;$++){var Z=z*b>>>S;if(o[x+3]=Z,0!==Z){var K=255/Z;o[x]=(B*b>>>S)*K,o[x+1]=(F*b>>>S)*K,o[x+2]=(U*b>>>S)*K}else o[x]=o[x+1]=o[x+2]=0;B-=I,F-=L,U-=D,z-=j,I-=v.r,L-=v.g,D-=v.b,j-=v.a;var J=$+s+1;J=m+(J<h?J:h)<<2,_+=v.r=o[J],R+=v.g=o[J+1],V+=v.b=o[J+2],k+=v.a=o[J+3],B+=_,F+=R,U+=V,z+=k,v=v.next;var tt=y,te=tt.r,tr=tt.g,ti=tt.b,tn=tt.a;I+=te,L+=tr,D+=ti,j+=tn,_-=te,R-=tr,V-=ti,k-=tn,y=y.next,x+=4}m+=i}for(var ts=0;ts<i;ts++){var ta=o[x=ts<<2],to=o[x+1],tu=o[x+2],th=o[x+3],tc=l*ta,tl=l*to,tf=l*tu,tg=l*th,tp=f*ta,td=f*to,tv=f*tu,ty=f*th;p=g;for(var tm=0;tm<l;tm++)p.r=ta,p.g=to,p.b=tu,p.a=th,p=p.next;for(var tx=i,tb=0,tS=0,tw=0,tT=0,tO=1;tO<=s;tO++){x=tx+ts<<2;var tA=l-tO;tp+=(p.r=ta=o[x])*tA,td+=(p.g=to=o[x+1])*tA,tv+=(p.b=tu=o[x+2])*tA,ty+=(p.a=th=o[x+3])*tA,tT+=ta,tb+=to,tS+=tu,tw+=th,p=p.next,tO<c&&(tx+=i)}x=ts,v=g,y=a;for(var tE=0;tE<n;tE++){var tC=x<<2;o[tC+3]=th=ty*b>>>S,th>0?(th=255/th,o[tC]=(tp*b>>>S)*th,o[tC+1]=(td*b>>>S)*th,o[tC+2]=(tv*b>>>S)*th):o[tC]=o[tC+1]=o[tC+2]=0,tp-=tc,td-=tl,tv-=tf,ty-=tg,tc-=v.r,tl-=v.g,tf-=v.b,tg-=v.a,tC=ts+((tC=tE+l)<c?tC:c)*i<<2,tp+=tT+=v.r=o[tC],td+=tb+=v.g=o[tC+1],tv+=tS+=v.b=o[tC+2],ty+=tw+=v.a=o[tC+3],v=v.next,tc+=ta=y.r,tl+=to=y.g,tf+=tu=y.b,tg+=th=y.a,tT-=ta,tb-=to,tS-=tu,tw-=th,y=y.next,x+=i}}return t}(a,0,0,i,n,s),t.getContext("2d").putImageData(a,e,r)}}(u,e,r,i,n,a),o&&o.removeChild(u)}}class ef extends tw{constructor(){super(...arguments),this.type="title"}}class eg extends tw{constructor(){super(...arguments),this.type="desc"}}function ep(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function ed(){return(ed=i(function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((e,i)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,n,s)=>{i(s)},r.src=t})})).apply(this,arguments)}class ev{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=ev.createCanvas,createImage:n=ev.createImage,anonymousCrossOrigin:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,i)=>t(r,"boolean"==typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=ev.elementTypes[e];return void 0!==r?new r(this,t):new tT(this,t)}createTextNode(t){return new tV(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ep(Object(r),!0).forEach(function(e){n(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ep(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({document:this},t))}}function ey(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function em(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ey(Object(r),!0).forEach(function(e){n(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ey(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}ev.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},ev.createImage=function(t){return ed.apply(this,arguments)},ev.elementTypes={svg:tk,rect:tI,circle:tL,ellipse:tD,line:tj,polyline:tB,polygon:tF,path:tN,pattern:tU,marker:tz,defs:tH,linearGradient:tq,radialGradient:tW,stop:tG,animate:tQ,animateColor:t$,animateTransform:tZ,font:tK,"font-face":tJ,"missing-glyph":t0,glyph:tM,text:t_,tspan:tR,tref:t1,a:t2,textPath:t5,image:t9,g:tX,symbol:t8,style:et,use:ee,mask:en,clipPath:ea,filter:eo,feDropShadow:eu,feMorphology:eh,feComposite:ec,feColorMatrix:ei,feGaussianBlur:el,title:ef,desc:eg};class ex{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new tg(r),this.screen=new tc(t,r),this.options=r;var i=new ev(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return i(function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=new tg(i);return new ex(t,(yield n.parse(e)),i)})()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new ex(t,new tg(r).parseFromString(e),r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return ex.from(t,e,em(em({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return ex.fromString(t,e,em(em({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return i(function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(em({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()})()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,em(em({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}},67008:(t,e,r)=>{"use strict";var i=r(97039),n=r(17425),s=/#|\.prototype\./,a=function(t,e){var r=u[o(t)];return r===c||r!==h&&(n(e)?i(e):!!e)},o=a.normalize=function(t){return String(t).replace(s,".").toLowerCase()},u=a.data={},h=a.NATIVE="N",c=a.POLYFILL="P";t.exports=a},67414:(t,e,r)=>{"use strict";var i=r(7462);t.exports=function(t){return i(t.length)}},67420:(t,e,r)=>{"use strict";var i=r(7588),n=i({}.toString),s=i("".slice);t.exports=function(t){return s(n(t),8,-1)}},67519:t=>{"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},68077:(t,e,r)=>{"use strict";var i=r(6532),n=Object.defineProperty;t.exports=function(t,e){try{n(i,t,{value:e,configurable:!0,writable:!0})}catch(r){i[t]=e}return e}},68298:(t,e,r)=>{"use strict";var i=r(17425),n=r(79815),s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not a function")}},68328:(t,e,r)=>{"use strict";var i=r(15390),n=r(69416),s=r(71381).indexOf,a=r(57862),o=n([].indexOf),u=!!o&&1/o([1],1,-0)<0;i({target:"Array",proto:!0,forced:u||!a("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return u?o(this,t,e)||0:s(this,t,e)}})},68689:(t,e,r)=>{"use strict";var i=r(5112),n=r(97039),s=r(23359);t.exports=!i&&!n(function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a})},68773:(t,e,r)=>{"use strict";t.exports="NODE"===r(52431)},69377:t=>{function e(t,e,r,i,n,s,a){try{var o=t[s](a),u=o.value}catch(t){return void r(t)}o.done?e(u):Promise.resolve(u).then(i,n)}t.exports=function(t){return function(){var r=this,i=arguments;return new Promise(function(n,s){var a=t.apply(r,i);function o(t){e(a,n,s,o,u,"next",t)}function u(t){e(a,n,s,o,u,"throw",t)}o(void 0)})}},t.exports.__esModule=!0,t.exports.default=t.exports},69416:(t,e,r)=>{"use strict";var i=r(67420),n=r(7588);t.exports=function(t){if("Function"===i(t))return n(t)}},71381:(t,e,r)=>{"use strict";var i=r(45465),n=r(84890),s=r(67414),a=function(t){return function(e,r,a){var o,u=i(e),h=s(u);if(0===h)return!t&&-1;var c=n(a,h);if(t&&r!=r){for(;h>c;)if((o=u[c++])!=o)return!0}else for(;h>c;c++)if((t||c in u)&&u[c]===r)return t||c||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},71472:(t,e,r)=>{"use strict";var i=r(76514),n=r(67420),s=r(80387)("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[s])?!!e:"RegExp"===n(t))}},72649:(t,e,r)=>{"use strict";var i=r(60827),n=r(6532),s=r(68077),a="__core-js_shared__",o=t.exports=n[a]||s(a,{});(o.versions||(o.versions=[])).push({version:"3.41.0",mode:i?"pure":"global",copyright:"\xa9 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.41.0/LICENSE",source:"https://github.com/zloirock/core-js"})},73040:(t,e,r)=>{"use strict";var i=r(6532),n=r(99662),s=r(17425),a=r(67008),o=r(93930),u=r(80387),h=r(52431),c=r(60827),l=r(52359),f=n&&n.prototype,g=u("species"),p=!1,d=s(i.PromiseRejectionEvent);t.exports={CONSTRUCTOR:a("Promise",function(){var t=o(n),e=t!==String(n);if(!e&&66===l||c&&!(f.catch&&f.finally))return!0;if(!l||l<51||!/native code/.test(t)){var r=new n(function(t){t(1)}),i=function(t){t(function(){},function(){})};if((r.constructor={})[g]=i,!(p=r.then(function(){})instanceof i))return!0}return!e&&("BROWSER"===h||"DENO"===h)&&!d}),REJECTION_EVENT:d,SUBCLASSING:p}},73522:(t,e,r)=>{"use strict";var i=r(15390),n=r(7588),s=r(53436),a=n([].reverse),o=[1,2];i({target:"Array",proto:!0,forced:String(o)===String(o.reverse())},{reverse:function(){return s(this)&&(this.length=this.length),a(this)}})},74934:(t,e,r)=>{"use strict";var i=r(5112),n=r(77525),s=Function.prototype,a=i&&Object.getOwnPropertyDescriptor,o=n(s,"name"),u=o&&(!i||i&&a(s,"name").configurable);t.exports={EXISTS:o,PROPER:o&&"something"===(function(){}).name,CONFIGURABLE:u}},75351:(t,e,r)=>{"use strict";var i=r(42727),n=r(7588),s=r(77660),a=r(63345),o=r(8991),u=n([].concat);t.exports=i("Reflect","ownKeys")||function(t){var e=s.f(o(t)),r=a.f;return r?u(e,r(t)):e}},76514:(t,e,r)=>{"use strict";var i=r(17425);t.exports=function(t){return"object"==typeof t?null!==t:i(t)}},76607:(t,e,r)=>{"use strict";var i=r(7588),n=r(97039),s=r(67420),a=Object,o=i("".split);t.exports=n(function(){return!a("z").propertyIsEnumerable(0)})?function(t){return"String"===s(t)?o(t,""):a(t)}:a},76901:(t,e,r)=>{"use strict";var i=r(50969),n=r(68298),s=r(8991),a=r(79815),o=r(10187),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?o(t):e;if(n(r))return s(i(r,t));throw new u(a(t)+" is not iterable")}},77525:(t,e,r)=>{"use strict";var i=r(7588),n=r(40377),s=i({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return s(n(t),e)}},77660:(t,e,r)=>{"use strict";var i=r(98064),n=r(67519).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,n)}},79064:t=>{t.exports=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6));var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var n=r[i].re,s=r[i].process,a=n.exec(t);if(a){var o=s(a);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=[],i=0;i<r.length;i++)for(var n=r[i].example,s=0;s<n.length;s++)t[t.length]=n[s];for(var a in e)t[t.length]=a;var o=document.createElement("ul");o.setAttribute("id","rgbcolor-examples");for(var i=0;i<t.length;i++)try{var u=document.createElement("li"),h=new RGBColor(t[i]),c=document.createElement("div");c.style.cssText="margin: 3px; border: 1px solid black; background:"+h.toHex()+"; color:"+h.toHex(),c.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[i]+" -> "+h.toRGB()+" -> "+h.toHex());u.appendChild(c),u.appendChild(l),o.appendChild(u)}catch(t){}return o}}},79219:(t,e,r)=>{"use strict";var i=r(50969),n=r(8991),s=r(3638);t.exports=function(t,e,r){var a,o;n(t);try{if(!(a=s(t,"return"))){if("throw"===e)throw r;return r}a=i(a,t)}catch(t){o=!0,a=t}if("throw"===e)throw r;if(o)throw a;return n(a),r}},79287:(t,e,r)=>{"use strict";var i=r(15390),n=r(85587);i({target:"RegExp",proto:!0,forced:/./.exec!==n},{exec:n})},79582:(t,e,r)=>{"use strict";var i=r(50969),n=r(17425),s=r(76514),a=TypeError;t.exports=function(t,e){var r,o;if("string"===e&&n(r=t.toString)&&!s(o=i(r,t))||n(r=t.valueOf)&&!s(o=i(r,t))||"string"!==e&&n(r=t.toString)&&!s(o=i(r,t)))return o;throw new a("Can't convert object to primitive value")}},79815:t=>{"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},79892:(t,e,r)=>{"use strict";t.exports=!r(97039)(function(){var t=(function(){}).bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})},80387:(t,e,r)=>{"use strict";var i=r(6532),n=r(31429),s=r(77525),a=r(88532),o=r(44615),u=r(48324),h=i.Symbol,c=n("wks"),l=u?h.for||h:h&&h.withoutSetter||a;t.exports=function(t){return s(c,t)||(c[t]=o&&s(h,t)?h[t]:l("Symbol."+t)),c[t]}},82675:(t,e,r)=>{"use strict";var i,n,s,a,o,u=r(6532),h=r(84849),c=r(65436),l=r(12005).set,f=r(39173),g=r(97412),p=r(33989),d=r(60419),v=r(68773),y=u.MutationObserver||u.WebKitMutationObserver,m=u.document,x=u.process,b=u.Promise,S=h("queueMicrotask");if(!S){var w=new f,T=function(){var t,e;for(v&&(t=x.domain)&&t.exit();e=w.get();)try{e()}catch(t){throw w.head&&i(),t}t&&t.enter()};g||v||d||!y||!m?!p&&b&&b.resolve?((a=b.resolve(void 0)).constructor=b,o=c(a.then,a),i=function(){o(T)}):v?i=function(){x.nextTick(T)}:(l=c(l,u),i=function(){l(T)}):(n=!0,s=m.createTextNode(""),new y(T).observe(s,{characterData:!0}),i=function(){s.data=n=!n}),S=function(t){w.head||i(),w.add(t)}}t.exports=S},83407:(t,e,r)=>{"use strict";var i=r(51067),n=String;t.exports=function(t){if("Symbol"===i(t))throw TypeError("Cannot convert a Symbol value to a string");return n(t)}},84849:(t,e,r)=>{"use strict";var i=r(6532),n=r(5112),s=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!n)return i[t];var e=s(i,t);return e&&e.value}},84890:(t,e,r)=>{"use strict";var i=r(58627),n=Math.max,s=Math.min;t.exports=function(t,e){var r=i(t);return r<0?n(r+e,0):s(r,e)}},85152:(t,e,r)=>{"use strict";var i=r(50065),n=r(79815),s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not a constructor")}},85587:(t,e,r)=>{"use strict";var i=r(50969),n=r(7588),s=r(83407),a=r(53907),o=r(15193),u=r(31429),h=r(65444),c=r(2129).get,l=r(21651),f=r(5502),g=u("native-string-replace",String.prototype.replace),p=RegExp.prototype.exec,d=p,v=n("".charAt),y=n("".indexOf),m=n("".replace),x=n("".slice),b=function(){var t=/a/,e=/b*/g;return i(p,t,"a"),i(p,e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),S=o.BROKEN_CARET,w=void 0!==/()??/.exec("")[1];(b||w||S||l||f)&&(d=function(t){var e,r,n,o,u,l,f,T=c(this),O=s(t),A=T.raw;if(A)return A.lastIndex=this.lastIndex,e=i(d,A,O),this.lastIndex=A.lastIndex,e;var E=T.groups,C=S&&this.sticky,P=i(a,this),N=this.source,M=0,_=O;if(C&&(-1===y(P=m(P,"y",""),"g")&&(P+="g"),_=x(O,this.lastIndex),this.lastIndex>0&&(!this.multiline||this.multiline&&"\n"!==v(O,this.lastIndex-1))&&(N="(?: "+N+")",_=" "+_,M++),r=RegExp("^(?:"+N+")",P)),w&&(r=RegExp("^"+N+"$(?!\\s)",P)),b&&(n=this.lastIndex),o=i(p,C?r:this,_),C?o?(o.input=x(o.input,M),o[0]=x(o[0],M),o.index=this.lastIndex,this.lastIndex+=o[0].length):this.lastIndex=0:b&&o&&(this.lastIndex=this.global?o.index+o[0].length:n),w&&o&&o.length>1&&i(g,o[0],r,function(){for(u=1;u<arguments.length-2;u++)void 0===arguments[u]&&(o[u]=void 0)}),o&&E)for(u=0,o.groups=l=h(null);u<E.length;u++)l[(f=E[u])[0]]=o[f[1]];return o}),t.exports=d},86883:(t,e,r)=>{"use strict";var i=r(5112),n=r(50969),s=r(89273),a=r(23864),o=r(45465),u=r(10421),h=r(77525),c=r(68689),l=Object.getOwnPropertyDescriptor;e.f=i?l:function(t,e){if(t=o(t),e=u(e),c)try{return l(t,e)}catch(t){}if(h(t,e))return a(!n(s.f,t,e),t[e])}},88532:(t,e,r)=>{"use strict";var i=r(7588),n=0,s=Math.random(),a=i(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++n+s,36)}},88875:(t,e,r)=>{"use strict";var i=r(68298),n=TypeError,s=function(t){var e,r;this.promise=new t(function(t,i){if(void 0!==e||void 0!==r)throw new n("Bad Promise constructor");e=t,r=i}),this.resolve=i(e),this.reject=i(r)};t.exports.f=function(t){return new s(t)}},89273:(t,e)=>{"use strict";var r={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor;e.f=i&&!r.call({1:2},1)?function(t){var e=i(this,t);return!!e&&e.enumerable}:r},90845:(t,e,r)=>{"use strict";var i=r(15390),n=r(69416),s=r(86883).f,a=r(7462),o=r(83407),u=r(18023),h=r(10054),c=r(48072),l=r(60827),f=n("".slice),g=Math.min,p=c("endsWith");i({target:"String",proto:!0,forced:!(!l&&!p&&function(){var t=s(String.prototype,"endsWith");return t&&!t.writable}())&&!p},{endsWith:function(t){var e=o(h(this));u(t);var r=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===r?i:g(a(r),i),s=o(t);return f(e,n-s.length,n)===s}})},91001:t=>{"use strict";t.exports={}},91231:t=>{"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},93229:(t,e,r)=>{"use strict";var i=r(79892),n=Function.prototype,s=n.apply,a=n.call;t.exports="object"==typeof Reflect&&Reflect.apply||(i?a.bind(s):function(){return a.apply(s,arguments)})},93930:(t,e,r)=>{"use strict";var i=r(7588),n=r(17425),s=r(72649),a=i(Function.toString);n(s.inspectSource)||(s.inspectSource=function(t){return a(t)}),t.exports=s.inspectSource},94323:(t,e,r)=>{"use strict";var i=r(5112),n=r(10397),s=r(23864);t.exports=i?function(t,e,r){return n.f(t,e,s(1,r))}:function(t,e,r){return t[e]=r,t}},95941:(t,e,r)=>{"use strict";var i=r(15390),n=r(88875);i({target:"Promise",stat:!0,forced:r(73040).CONSTRUCTOR},{reject:function(t){var e=n.f(this);return(0,e.reject)(t),e.promise}})},96482:(t,e,r)=>{"use strict";var i=r(7588),n=r(68298);t.exports=function(t,e,r){try{return i(n(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},96516:(t,e,r)=>{"use strict";var i=r(15390),n=r(50969),s=r(60827),a=r(74934),o=r(17425),u=r(53538),h=r(28907),c=r(36751),l=r(7615),f=r(94323),g=r(59908),p=r(80387),d=r(97601),v=r(12549),y=a.PROPER,m=a.CONFIGURABLE,x=v.IteratorPrototype,b=v.BUGGY_SAFARI_ITERATORS,S=p("iterator"),w="keys",T="values",O="entries",A=function(){return this};t.exports=function(t,e,r,a,p,v,E){u(r,e,a);var C,P,N,M=function(t){if(t===p&&I)return I;if(!b&&t&&t in V)return V[t];switch(t){case w:case T:case O:return function(){return new r(this,t)}}return function(){return new r(this)}},_=e+" Iterator",R=!1,V=t.prototype,k=V[S]||V["@@iterator"]||p&&V[p],I=!b&&k||M(p),L="Array"===e&&V.entries||k;if(L&&(C=h(L.call(new t)))!==Object.prototype&&C.next&&(s||h(C)===x||(c?c(C,x):o(C[S])||g(C,S,A)),l(C,_,!0,!0),s&&(d[_]=A)),y&&p===T&&k&&k.name!==T&&(!s&&m?f(V,"name",T):(R=!0,I=function(){return n(k,this)})),p){if(P={values:M(T),keys:v?I:M(w),entries:M(O)},E)for(N in P)!b&&!R&&N in V||g(V,N,P[N]);else i({target:e,proto:!0,forced:b||R},P)}return(!s||E)&&V[S]!==I&&g(V,S,I,{name:p}),d[e]=I,P}},96595:(t,e,r)=>{"use strict";var i=r(15390),n=r(7588),s=r(18023),a=r(10054),o=r(83407),u=r(48072),h=n("".indexOf);i({target:"String",proto:!0,forced:!u("includes")},{includes:function(t){return!!~h(o(a(this)),o(s(t)),arguments.length>1?arguments[1]:void 0)}})},97039:t=>{"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},97269:(t,e,r)=>{"use strict";var i=r(5112),n=r(2870),s=r(10397),a=r(8991),o=r(45465),u=r(26348);e.f=i&&!n?Object.defineProperties:function(t,e){a(t);for(var r,i=o(e),n=u(e),h=n.length,c=0;h>c;)s.f(t,r=n[c++],i[r]);return t}},97361:(t,e,r)=>{"use strict";var i=r(27199).charAt;t.exports=function(t,e,r){return e+(r?i(t,e).length:1)}},97412:(t,e,r)=>{"use strict";var i=r(343);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(i)},97601:t=>{"use strict";t.exports={}},98064:(t,e,r)=>{"use strict";var i=r(7588),n=r(77525),s=r(45465),a=r(71381).indexOf,o=r(91001),u=i([].push);t.exports=function(t,e){var r,i=s(t),h=0,c=[];for(r in i)!n(o,r)&&n(i,r)&&u(c,r);for(;e.length>h;)n(i,r=e[h++])&&(~a(c,r)||u(c,r));return c}},98074:(t,e,r)=>{"use strict";r(14704),r(11),r(23315),r(15199),r(95941),r(13044)},99466:(t,e,r)=>{"use strict";var i=r(13209),n=String,s=TypeError;t.exports=function(t){if(i(t))return t;throw new s("Can't set "+n(t)+" as a prototype")}},99501:(t,e,r)=>{"use strict";var i=r(99662),n=r(1504);t.exports=r(73040).CONSTRUCTOR||!n(function(t){i.all(t).then(void 0,function(){})})},99662:(t,e,r)=>{"use strict";t.exports=r(6532).Promise}};