(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2155],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(95155);s(12115);var r=s(6874),l=s.n(r),i=s(66766),o=s(29911);let n=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:o.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:o.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:o.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:o.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:o.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:o.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:o.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:r}=e;return(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:r,children:(0,a.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},r)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},18097:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var a=s(95155),r=s(70347),l=s(7583),i=s(12115),o=s(30285),n=s(55594),c=s(62177),d=s(90221),m=s(10351),f=s(62523),u=s(55365),x=s(85339),h=s(51154),g=s(75937),p=s(56671),N=s(21751),b=s(35695),j=s(60760),v=s(19320);let y=(e,t)=>n.z.object({contactNo:n.z.string().optional().refine(e=>!e||/^\d{10}$/.test(e),"Please enter a valid 10-digit mobile number"),email:n.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}).refine(s=>"mobile"===e||t?!!s.contactNo:!!s.email,{message:"Either mobile number or email is required",path:["contactNo","email"]}),w=n.z.object({firstName:n.z.string().min(2,"First name is required").regex(/^[a-zA-Z]+$/,"Invalid first name"),lastName:n.z.string().min(2,"Last name is required").regex(/^[a-zA-Z]+$/,"Invalid last name"),contactNo:n.z.string().regex(/^\d{10}$/,"Please enter a valid 10-digit mobile number"),referralCode:n.z.string().optional(),email:n.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}),C=e=>{let{message:t}=e;return t?(0,a.jsxs)(u.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-red-500"}),(0,a.jsx)(u.TN,{className:"text-red-500",children:t})]}):null};function S(){let e=(0,b.useRouter)(),t=(0,b.useSearchParams)(),[s,n]=(0,i.useState)(!0),[u,x]=(0,i.useState)(!1),[S,k]=(0,i.useState)(""),[I,P]=(0,i.useState)(null),[F,R]=(0,i.useState)("mobile"),[z,A]=(0,i.useState)(!1),[T,E]=(0,i.useState)(null),U=(0,c.mN)({resolver:(0,d.u)(y(F,z)),defaultValues:{contactNo:"",email:""},mode:"onChange"}),L=(0,c.mN)({resolver:(0,d.u)(w),defaultValues:{firstName:"",lastName:"",contactNo:"",referralCode:t.get("ref")||localStorage.getItem("referralCode")||"",email:""},mode:"onChange"});(0,i.useEffect)(()=>{localStorage.getItem("user")&&(e.push("/"),setTimeout(()=>{p.toast.error("You are already logged in as a tutor. Please logout first to login as a class.")},500))},[e]),(0,i.useEffect)(()=>{localStorage.getItem("clientToken")&&(e.push("/"),setTimeout(()=>{p.toast.error("You are already logged in as a class.")},500))},[e]),(0,i.useEffect)(()=>{let e=t.get("ref");e&&(P(e),localStorage.setItem("referralCode",e),L.setValue("referralCode",e))},[t,L]),(0,i.useEffect)(()=>{s?(U.reset({contactNo:"mobile"===F?U.getValues().contactNo:"",email:"email"===F?U.getValues().email:""}),U.setFocus("mobile"===F?"contactNo":"email"),U.trigger()):(L.reset({firstName:"",lastName:"",contactNo:"",referralCode:I||"",email:""}),L.setFocus("firstName"),L.trigger())},[s,F,z,I,U,L]);let O=async(s,a)=>{x(!0),k("");try{if(a){let r=await (0,N.Lx)({contactNo:a,email:s});if(!1===r.success){k(r.message||"Authentication failed"),p.toast.error(r.message||"Authentication failed");return}let l=t.get("redirect")||"",i=l?"&redirect=".concat(encodeURIComponent(l)):"";e.push("/verify-otp?contactNo=".concat(a,"&flow=login&email=").concat(encodeURIComponent(s)).concat(i)),p.toast.success("OTP sent successfully. Please check your phone."),U.reset({contactNo:"",email:""}),A(!1),E(null)}else{let a=await (0,N.iM)({email:s});if(!1===a.success){k(a.message||"Email check failed"),p.toast.error(a.message||"Email check failed");return}if(E(a.data),A(a.data.isOldUser),U.setValue("contactNo",a.data.contactNo||""),U.setValue("email",s),U.trigger(),!a.data.isOldUser&&a.data.otpSent){let r=t.get("redirect")||"",l=r?"&redirect=".concat(encodeURIComponent(r)):"";e.push("/verify-otp?contactNo=".concat(a.data.contactNo,"&flow=login&email=").concat(encodeURIComponent(s)).concat(l)),p.toast.success("OTP sent successfully. Please check your phone."),U.reset({contactNo:"",email:""}),A(!1),E(null)}else a.data.isOldUser&&p.toast.info("This email is not linked to a mobile number. Please enter a mobile number to proceed.")}}catch(t){var r,l;let e=(null==t?void 0:null===(l=t.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message)||"Something went wrong";k(e),p.toast.error(e)}finally{x(!1)}},M=async s=>{x(!0),k("");try{if("email"===F)await O(s.email,z?s.contactNo:void 0);else if("mobile"===F){let a=await (0,N.Lx)({contactNo:s.contactNo});if(!1===a.success){k(a.message||"Authentication failed"),p.toast.error(a.message||"Authentication failed");return}let r=t.get("redirect")||"",l=r?"&redirect=".concat(encodeURIComponent(r)):"";e.push("/verify-otp?contactNo=".concat(s.contactNo,"&flow=login").concat(l)),p.toast.success("OTP sent successfully. Please check your phone."),U.reset({contactNo:"",email:""}),A(!1),E(null)}}catch(t){var a,r;let e=(null==t?void 0:null===(r=t.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||"Something went wrong";k(e),p.toast.error(e)}finally{x(!1)}},V=async s=>{x(!0),k("");try{let a={...s,...I?{referralCode:I}:{}},r=await (0,N.DY)(a);if(!1===r.success){k(r.message||"Authentication failed"),p.toast.error(r.message||"Authentication failed");return}let l=t.get("redirect")||"",i=l?"&redirect=".concat(encodeURIComponent(l)):"";e.push("/verify-otp?contactNo=".concat(s.contactNo,"&flow=register&firstName=").concat(encodeURIComponent(s.firstName),"&lastName=").concat(encodeURIComponent(s.lastName)).concat(s.referralCode?"&referralCode=".concat(encodeURIComponent(s.referralCode)):"").concat(s.email?"&email=".concat(encodeURIComponent(s.email)):"").concat(i)),p.toast.success("OTP sent successfully. Please check your phone."),L.reset({firstName:"",lastName:"",contactNo:"",referralCode:"",email:""}),localStorage.removeItem("referralCode"),P(null),A(!1),E(null)}catch(t){var a,r;let e=(null==t?void 0:null===(r=t.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||"Something went wrong";k(e),p.toast.error(e)}finally{x(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4",children:(0,a.jsx)("p",{className:"text-center text-orange-700 font-medium",children:s?"Coaching Classes Login Portal":"Coaching Classes Registration Portal"})}),(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,a.jsx)(o.$,{variant:s?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat(s?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{n(!0),k(""),R("mobile"),A(!1),E(null),U.reset({contactNo:"",email:""}),U.trigger()},children:"Class Login"}),(0,a.jsx)(o.$,{variant:s?"ghost":"default",className:"px-4 py-2 rounded-lg ".concat(s?"text-gray-600 hover:text-[#ff914d]":"bg-[#ff914d] text-white hover:bg-[#ff914d]/90"),onClick:()=>{n(!1),k(""),R("mobile"),A(!1),E(null),L.reset({firstName:"",lastName:"",contactNo:"",referralCode:I||"",email:""}),L.trigger()},children:"Class Sign Up"})]})}),s&&(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,a.jsx)(o.$,{variant:"mobile"===F?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat("mobile"===F?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{R("mobile"),k(""),A(!1),E(null),U.reset({contactNo:"",email:""}),U.trigger()},children:"Mobile"}),(0,a.jsx)(o.$,{variant:"email"===F?"default":"ghost",className:"px-4 py-2 rounded-lg ".concat("email"===F?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"),onClick:()=>{R("email"),k(""),A(!1),E(null),U.reset({contactNo:"",email:""}),U.trigger()},children:"Email"})]})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:s?"Welcome Back to Your Class Portal":"Register Your Coaching Class"}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 mr-3"}),(0,a.jsx)("span",{className:"text-[#ff914d] font-medium",children:"COACHING CLASS PORTAL"}),(0,a.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 ml-3"})]}),I&&!s&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700 text-center",children:["\uD83C\uDF89 You're joining via referral code: ",(0,a.jsx)("span",{className:"font-semibold",children:I})]})})]}),(0,a.jsx)("div",{children:s?(0,a.jsx)(g.lV,{...U,children:(0,a.jsxs)("form",{onSubmit:U.handleSubmit(M),className:"space-y-6",children:[S&&(0,a.jsx)(C,{message:S}),"email"===F&&(0,a.jsx)(g.zB,{control:U.control,name:"email",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Email"}),(0,a.jsx)(g.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.pHD,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,a.jsx)(f.p,{type:"email",placeholder:"Enter email address",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),U.formState.touchedFields.email&&(0,a.jsx)(g.C5,{className:"text-red-500"})]})}}),(0,a.jsx)(j.N,{children:("mobile"===F||z)&&(0,a.jsx)(v.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,a.jsx)(g.zB,{control:U.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,a.jsx)(g.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,a.jsx)(f.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...t,disabled:!!(null==T?void 0:T.contactNo)&&!z&&"email"===F,autoFocus:z})]})}),U.formState.touchedFields.contactNo&&(0,a.jsx)(g.C5,{className:"text-red-500"})]})}})})}),(0,a.jsx)(o.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:u||!U.formState.isValid,children:u?(0,a.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):"email"!==F||z?"Send OTP to Login":"Check Email"}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,a.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,a.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})}):(0,a.jsx)(g.lV,{...L,children:(0,a.jsxs)("form",{onSubmit:L.handleSubmit(V),className:"space-y-6",children:[S&&(0,a.jsx)(C,{message:S}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,a.jsx)(g.zB,{control:L.control,name:"firstName",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"First Name"}),(0,a.jsx)(g.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,a.jsx)(f.p,{placeholder:"First Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),L.formState.touchedFields.firstName&&(0,a.jsx)(g.C5,{className:"text-red-500"})]})}}),(0,a.jsx)(g.zB,{control:L.control,name:"lastName",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Last Name"}),(0,a.jsx)(g.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,a.jsx)(f.p,{placeholder:"Last Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t})]})}),L.formState.touchedFields.lastName&&(0,a.jsx)(g.C5,{className:"text-red-500"})]})}})]}),(0,a.jsx)(g.zB,{control:L.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,a.jsx)(g.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,a.jsx)(f.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...t})]})}),L.formState.touchedFields.contactNo&&(0,a.jsx)(g.C5,{className:"text-red-500"})]})}}),I&&(0,a.jsx)(g.zB,{control:L.control,name:"referralCode",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Referral Code"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(f.p,{placeholder:"Referral Code",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...t,disabled:!0})})}),L.formState.touchedFields.referralCode&&(0,a.jsx)(g.C5,{className:"text-red-500"})]})}}),(0,a.jsx)(o.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:u||!L.formState.isValid,children:u?(0,a.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):"Send OTP to Register"}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,a.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,a.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})})})]})}),(0,a.jsx)(l.default,{})]})}function k(){return(0,a.jsx)(i.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})}),children:(0,a.jsx)(S,{})})}},32358:(e,t,s)=>{Promise.resolve().then(s.bind(s,18097))},55365:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>n,TN:()=>c});var a=s(95155),r=s(12115),l=s(74466),i=s(59434);let o=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),n=r.forwardRef((e,t)=>{let{className:s,variant:r,...l}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(o({variant:r}),s),...l})});n.displayName="Alert",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})}).displayName="AlertTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",s),...r})});c.displayName="AlertDescription"},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var a=s(95155);s(12115);var r=s(59434);function l(e){let{className:t,type:s,...l}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},75937:(e,t,s)=>{"use strict";s.d(t,{lV:()=>d,MJ:()=>p,Rr:()=>N,zB:()=>f,eI:()=>h,lR:()=>g,C5:()=>b});var a=s(95155),r=s(12115),l=s(66634),i=s(62177),o=s(59434),n=s(24265);function c(e){let{className:t,...s}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}let d=i.Op,m=r.createContext({}),f=e=>{let{...t}=e;return(0,a.jsx)(m.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},u=()=>{let e=r.useContext(m),t=r.useContext(x),{getFieldState:s}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),l=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...l}},x=r.createContext({});function h(e){let{className:t,...s}=e,l=r.useId();return(0,a.jsx)(x.Provider,{value:{id:l},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...s})})}function g(e){let{className:t,...s}=e,{error:r,formItemId:l}=u();return(0,a.jsx)(c,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...s})}function p(e){let{...t}=e,{error:s,formItemId:r,formDescriptionId:i,formMessageId:o}=u();return(0,a.jsx)(l.DX,{"data-slot":"form-control",id:r,"aria-describedby":s?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!s,...t})}function N(e){let{className:t,...s}=e,{formDescriptionId:r}=u();return(0,a.jsx)("p",{"data-slot":"form-description",id:r,className:(0,o.cn)("text-muted-foreground text-sm",t),...s})}function b(e){var t;let{className:s,...r}=e,{error:l,formMessageId:i}=u(),n=l?String(null!==(t=null==l?void 0:l.message)&&void 0!==t?t:""):r.children;return n?(0,a.jsx)("p",{"data-slot":"form-message",id:i,className:(0,o.cn)("text-destructive text-sm",s),...r,children:n}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,844,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,1342,2265,8093,347,8441,1684,7358],()=>t(32358)),_N_E=e.O()}]);