"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2265],{6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},19320:(t,e,i)=>{function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function r(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function o(t,e,i){let n=t.getProps();return r(n,e,void 0!==i?i:n.custom,t)}function a(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>rf});var l,h,u=i(69515);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],c=new Set(d),p=new Set(["width","height","top","left","right","bottom",...d]);var m=i(60098);let f=t=>Array.isArray(t);var y=i(23387);let g=t=>!!(t&&t.getVelocity);function v(t,e){let i=t.getValue("willChange");if(g(i)&&i.add)return i.add(e);if(!i&&y.W.WillChange){let i=new y.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let x=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),w="data-"+x("framerAppearId"),T=(t,e)=>i=>e(t(i)),b=(...t)=>t.reduce(T),P=(t,e,i)=>i>e?e:i<t?t:i,S=t=>1e3*t,A=t=>t/1e3;var M=i(74261);let E={layout:0,mainThread:0,waapi:0},V=()=>{},D=()=>{},k=t=>e=>"string"==typeof e&&e.startsWith(t),C=k("--"),R=k("var(--"),j=t=>!!R(t)&&L.test(t.split("/*")[0].trim()),L=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,O={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},B={...O,transform:t=>P(0,1,t)},F={...O,default:1},I=t=>Math.round(1e5*t)/1e5,U=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,W=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,N=(t,e)=>i=>!!("string"==typeof i&&W.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),$=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,o,a]=n.match(U);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},G=t=>P(0,255,t),Y={...O,transform:t=>Math.round(G(t))},z={test:N("rgb","red"),parse:$("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+Y.transform(t)+", "+Y.transform(e)+", "+Y.transform(i)+", "+I(B.transform(n))+")"},X={test:N("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:z.transform},H=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),K=H("deg"),q=H("%"),Q=H("px"),_=H("vh"),Z=H("vw"),J={...q,parse:t=>q.parse(t)/100,transform:t=>q.transform(100*t)},tt={test:N("hsl","hue"),parse:$("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+q.transform(I(e))+", "+q.transform(I(i))+", "+I(B.transform(n))+")"},te={test:t=>z.test(t)||X.test(t)||tt.test(t),parse:t=>z.test(t)?z.parse(t):tt.test(t)?tt.parse(t):X.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?z.transform(t):tt.transform(t),getAnimatableNone:t=>{let e=te.parse(t);return e.alpha=0,te.transform(e)}},ti=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tn="number",ts="color",tr=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function to(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,o=e.replace(tr,t=>(te.test(t)?(n.color.push(r),s.push(ts),i.push(te.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(tn),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:n,types:s}}function ta(t){return to(t).values}function tl(t){let{split:e,types:i}=to(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===tn?s+=I(t[r]):e===ts?s+=te.transform(t[r]):s+=t[r]}return s}}let th=t=>"number"==typeof t?0:te.test(t)?te.getAnimatableNone(t):t,tu={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(U)?.length||0)+(t.match(ti)?.length||0)>0},parse:ta,createTransformer:tl,getAnimatableNone:function(t){let e=ta(t);return tl(t)(e.map(th))}};function td(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tc(t,e){return i=>i>0?e:t}let tp=(t,e,i)=>t+(e-t)*i,tm=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},tf=[X,z,tt],ty=t=>tf.find(e=>e.test(t));function tg(t){let e=ty(t);if(V(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tt&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;s=td(a,n,t+1/3),r=td(a,n,t),o=td(a,n,t-1/3)}else s=r=o=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*o),alpha:n}}(i)),i}let tv=(t,e)=>{let i=tg(t),n=tg(e);if(!i||!n)return tc(t,e);let s={...i};return t=>(s.red=tm(i.red,n.red,t),s.green=tm(i.green,n.green,t),s.blue=tm(i.blue,n.blue,t),s.alpha=tp(i.alpha,n.alpha,t),z.transform(s))},tx=new Set(["none","hidden"]);function tw(t,e){return i=>tp(t,e,i)}function tT(t){return"number"==typeof t?tw:"string"==typeof t?j(t)?tc:te.test(t)?tv:tS:Array.isArray(t)?tb:"object"==typeof t?te.test(t)?tv:tP:tc}function tb(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>tT(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function tP(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=tT(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tS=(t,e)=>{let i=tu.createTransformer(e),n=to(t),s=to(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?tx.has(t)&&!s.values.length||tx.has(e)&&!n.values.length?function(t,e){return tx.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):b(tb(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],o=t.indexes[r][n[r]],a=t.values[o]??0;i[s]=a,n[r]++}return i}(n,s),s.values),i):(V(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),tc(t,e))};function tA(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tp(t,e,i):tT(t)(t,e)}let tM=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>u.Gt.update(e,t),stop:()=>(0,u.WG)(e),now:()=>u.uv.isProcessing?u.uv.timestamp:M.k.now()}},tE=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tV(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}var tD=i(62923);function tk(t,e,i){let n=Math.max(e-5,0);return(0,tD.f)(i-t(n),e-n)}let tC={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tR(t,e){return t*Math.sqrt(1-e*e)}let tj=["duration","bounce"],tL=["stiffness","damping","mass"];function tO(t,e){return e.some(e=>void 0!==t[e])}function tB(t=tC.visualDuration,e=tC.bounce){let i;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:r}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tC.velocity,stiffness:tC.stiffness,damping:tC.damping,mass:tC.mass,isResolvedFromDuration:!1,...t};if(!tO(t,tL)&&tO(t,tj)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*P(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tC.mass,stiffness:n,damping:s}}else{let i=function({duration:t=tC.duration,bounce:e=tC.bounce,velocity:i=tC.velocity,mass:n=tC.mass}){let s,r;V(t<=S(tC.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=P(tC.minDamping,tC.maxDamping,o),t=P(tC.minDuration,tC.maxDuration,A(t)),o<1?(s=e=>{let n=e*o,s=n*t;return .001-(n-i)/tR(e,o)*Math.exp(-s)},r=e=>{let n=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tR(Math.pow(e,2),o);return(n*i+i-r)*a*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,r,5/t);if(t=S(t),isNaN(a))return{stiffness:tC.stiffness,damping:tC.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tC.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-A(n.velocity||0)}),f=p||0,y=u/(2*Math.sqrt(h*d)),g=a-o,v=A(Math.sqrt(h/d)),x=5>Math.abs(g);if(s||(s=x?tC.restSpeed.granular:tC.restSpeed.default),r||(r=x?tC.restDelta.granular:tC.restDelta.default),y<1){let t=tR(v,y);i=e=>a-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),n=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;y<1&&(n=0===t?S(f):tk(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(n)<=s&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tV(w),2e4),e=tE(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tF({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:h=.5,restSpeed:u}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,v=p+g,x=void 0===o?v:o(v);x!==v&&(g=x-p);let w=t=>-g*Math.exp(-t/n),T=t=>x+w(t),b=t=>{let e=w(t),i=T(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=tB({keyframes:[m.value,y(m.value)],velocity:tk(T,t,m.value),damping:s,stiffness:r,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,b(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||b(t),m)}}}tB.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min(tV(n),2e4);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:A(s)}}(t,100,tB);return t.ease=e.ease,t.duration=S(e.duration),t.type="keyframes",t};var tI=i(19827);let tU=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tW(t,e,i,n){if(t===e&&i===n)return tI.l;let s=e=>(function(t,e,i,n,s){let r,o;let a=0;do(r=tU(o=e+(i-e)/2,n,s)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tU(s(t),e,n)}let tN=tW(.42,0,1,1),t$=tW(0,0,.58,1),tG=tW(.42,0,.58,1),tY=t=>Array.isArray(t)&&"number"!=typeof t[0],tz=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tX=t=>e=>1-t(1-e),tH=tW(.33,1.53,.69,.99),tK=tX(tH),tq=tz(tK),tQ=t=>(t*=2)<1?.5*tK(t):.5*(2-Math.pow(2,-10*(t-1))),t_=t=>1-Math.sin(Math.acos(t)),tZ=tX(t_),tJ=tz(t_),t0=t=>Array.isArray(t)&&"number"==typeof t[0],t1={linear:tI.l,easeIn:tN,easeInOut:tG,easeOut:t$,circIn:t_,circInOut:tJ,circOut:tZ,backIn:tK,backInOut:tq,backOut:tH,anticipate:tQ},t5=t=>"string"==typeof t,t2=t=>{if(t0(t)){D(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,s]=t;return tW(e,i,n,s)}return t5(t)?(D(void 0!==t1[t],`Invalid easing type '${t}'`,"invalid-easing-type"),t1[t]):t},t3=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function t9({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let s=tY(n)?n.map(t2):t2(n),r={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:s}={}){let r=t.length;if(D(r===e.length,"Both input and output ranges must be the same length","range-length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],s=i||y.W.mix||tA,r=t.length-1;for(let i=0;i<r;i++){let r=s(t[i],t[i+1]);e&&(r=b(Array.isArray(e)?e[i]||tI.l:e,r)),n.push(r)}return n}(e,n,s),l=a.length,h=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=t3(t[n],t[n+1],i);return a[n](s)};return i?e=>h(P(t[0],t[r-1],e)):h}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=t3(0,e,n);t.push(tp(i,1,s))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(s)?s:e.map(()=>s||tG).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}let t4=t=>null!==t;function t6(t,{repeat:e,repeatType:i="loop"},n,s=1){let r=t.filter(t4),o=s<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return o&&void 0!==n?n:r[o]}let t8={decay:tF,inertia:tF,tween:t9,keyframes:t9,spring:tB};function t7(t){"string"==typeof t.type&&(t.type=t8[t.type])}class et{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ee=t=>t/100;class ei extends et{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==M.k.now()&&this.tick(M.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},E.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;t7(t);let{type:e=t9,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:r=0}=t,{keyframes:o}=t,a=e||t9;a!==t9&&"number"!=typeof o[0]&&(this.mixKeyframes=b(ee,tA(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=tV(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=r)),v=P(0,1,i)*o}let w=g?{done:!1,value:h[0]}:x.next(v);s&&(w.value=s(w.value));let{done:T}=w;g||null===a||(T=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return b&&p!==tF&&(w.value=t6(h,this.options,f,this.speed)),m&&m(w.value),b&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return A(this.calculatedDuration)}get time(){return A(this.currentTime)}set time(t){t=S(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(M.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=A(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tM,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(M.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,E.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let en=t=>180*t/Math.PI,es=t=>eo(en(Math.atan2(t[1],t[0]))),er={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:es,rotateZ:es,skewX:t=>en(Math.atan(t[1])),skewY:t=>en(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eo=t=>((t%=360)<0&&(t+=360),t),ea=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),el=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eh={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ea,scaleY:el,scale:t=>(ea(t)+el(t))/2,rotateX:t=>eo(en(Math.atan2(t[6],t[5]))),rotateY:t=>eo(en(Math.atan2(-t[2],t[0]))),rotateZ:es,rotate:es,skewX:t=>en(Math.atan(t[4])),skewY:t=>en(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eu(t){return+!!t.includes("scale")}function ed(t,e){let i,n;if(!t||"none"===t)return eu(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=eh,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=er,n=e}if(!n)return eu(e);let r=i[e],o=n[1].split(",").map(ep);return"function"==typeof r?r(o):o[r]}let ec=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ed(i,e)};function ep(t){return parseFloat(t.trim())}let em=t=>t===O||t===Q,ef=new Set(["x","y","z"]),ey=d.filter(t=>!ef.has(t)),eg={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ed(e,"x"),y:(t,{transform:e})=>ed(e,"y")};eg.translateX=eg.x,eg.translateY=eg.y;let ev=new Set,ex=!1,ew=!1,eT=!1;function eb(){if(ew){let t=Array.from(ev).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ey.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ew=!1,ex=!1,ev.forEach(t=>t.complete(eT)),ev.clear()}function eP(){ev.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ew=!0)})}class eS{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(ev.add(this),ex||(ex=!0,u.Gt.read(eP),u.Gt.resolveKeyframes(eb))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),ev.delete(this)}cancel(){"scheduled"===this.state&&(ev.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eA=t=>t.startsWith("--");function eM(t){let e;return()=>(void 0===e&&(e=t()),e)}let eE=eM(()=>void 0!==window.ScrollTimeline);var eV=i(24744);let eD={},ek=function(t,e){let i=eM(t);return()=>eD[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eC=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eR={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eC([0,.65,.55,1]),circOut:eC([.55,0,1,.45]),backIn:eC([.31,.01,.66,-.59]),backOut:eC([.33,1.53,.69,.99])};function ej(t){return"function"==typeof t&&"applyToOptions"in t}class eL extends et{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,D("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return ej(t)&&ek()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?ek()?tE(e,i):"ease-out":t0(e)?eC(e):Array.isArray(e)?e.map(e=>t(e,i)||eR.easeOut):eR[e]}(a,s);Array.isArray(d)&&(u.easing=d),eV.Q.value&&E.waapi++;let c={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return eV.Q.value&&p.finished.finally(()=>{E.waapi--}),p}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=t6(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eA(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return A(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return A(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=S(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eE())?(this.animation.timeline=t,tI.l):e(this)}}let eO={anticipate:tQ,backInOut:tq,circInOut:tJ};class eB extends eL{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eO&&(t.ease=eO[t.ease])}(t),t7(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...r}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new ei({...r,autoplay:!1}),a=S(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eF=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tu.test(t)||"0"===t)&&!t.startsWith("url("));var eI=i(27351);let eU=new Set(["opacity","clipPath","filter","transform"]),eW=eM(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eN extends et{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=M.k.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:a,motionValue:l,element:h,...u},c=h?.KeyframeResolver||eS;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:r,velocity:o,delay:a,isHandoff:l,onUpdate:h}=i;this.resolvedAt=M.k.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eF(s,e),a=eF(r,e);return V(o===a,`You are trying to animate ${e} from "${s}" to "${r}". "${o?r:s}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||ej(i))&&n)}(t,s,r,o)&&((y.W.instantAnimations||!a)&&h?.(t6(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:o}=t;if(!(0,eI.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eW()&&i&&eU.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==s&&0!==r&&"inertia"!==o}(u)?new eB({...u,element:u.motionValue.owner.current}):new ei(u);d.finished.then(()=>this.notifyFinished()).catch(tI.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eT=!0,eP(),eb(),eT=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e$=t=>null!==t,eG={type:"spring",stiffness:500,damping:25,restSpeed:10},eY=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ez={type:"keyframes",duration:.8},eX={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eH=(t,{keyframes:e})=>e.length>2?ez:c.has(t)?t.startsWith("scale")?eY(e[1]):eG:eX,eK=(t,e,i,n={},s,r)=>o=>{let l=a(n,t)||{},h=l.delay||n.delay||0,{elapsed:d=0}=n;d-=S(h);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-d,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:r?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(l)&&Object.assign(c,eH(t,c)),c.duration&&(c.duration=S(c.duration)),c.repeatDelay&&(c.repeatDelay=S(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(p=!0)),(y.W.instantAnimations||y.W.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!l.type&&!l.ease,p&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(e$),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(c.keyframes,l);if(void 0!==t){u.Gt.update(()=>{c.onUpdate(t),c.onComplete()});return}}return l.isSync?new ei(c):new eN(c)};function eq(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:l,...h}=e;n&&(r=n);let d=[],c=s&&t.animationState&&t.animationState.getState()[s];for(let e in h){let n=t.getValue(e,t.latestValues[e]??null),s=h[e];if(void 0===s||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let o={delay:i,...a(r||{},e)},l=n.get();if(void 0!==l&&!n.isAnimating&&!Array.isArray(s)&&s===l&&!o.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){let i=t.props[w];if(i){let t=window.MotionHandoffAnimation(i,e,u.Gt);null!==t&&(o.startTime=t,m=!0)}}v(t,e),n.start(eK(e,n,s,t.shouldReduceMotion&&p.has(e)?{type:!1}:o,t,m));let f=n.animation;f&&d.push(f)}return l&&Promise.all(d).then(()=>{u.Gt.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=o(t,e)||{};for(let e in s={...s,...i}){var r;let i=f(r=s[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,m.OQ)(i))}}(t,l)})}),d}function eQ(t,e,i={}){let n=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all(eq(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,n=0,s=0,r=1,o){let a=[],l=t.variantChildren.size,h=(l-1)*s,u="function"==typeof n,d=u?t=>n(t,l):1===r?(t=0)=>t*s:(t=0)=>h-t*s;return Array.from(t.variantChildren).sort(e_).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(eQ(t,e,{...o,delay:i+(u?0:n)+d(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,n,r,o,a,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}}function e_(t,e){return t.sortNodePosition(e)}function eZ(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function eJ(t){return"string"==typeof t||Array.isArray(t)}let e0=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],e1=["initial",...e0],e5=e1.length,e2=[...e0].reverse(),e3=e0.length;function e9(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function e4(){return{animate:e9(!0),whileInView:e9(),whileHover:e9(),whileTap:e9(),whileDrag:e9(),whileFocus:e9(),exit:e9()}}class e6{constructor(t){this.isMounted=!1,this.node=t}update(){}}class e8 extends e6{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>eQ(t,e,i)));else if("string"==typeof e)n=eQ(t,e,i);else{let s="function"==typeof e?o(t,e,i.custom):e;n=Promise.all(eq(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=e4(),s=!0,r=e=>(i,n)=>{let s=o(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function a(a){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<e5;t++){let n=e1[t],s=e.props[n];(eJ(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},u=[],d=new Set,c={},p=1/0;for(let e=0;e<e3;e++){var m,y;let o=e2[e],g=i[o],v=void 0!==l[o]?l[o]:h[o],x=eJ(v),w=o===a?g.isActive:null;!1===w&&(p=e);let T=v===h[o]&&v!==l[o]&&x;if(T&&s&&t.manuallyAnimateOnMount&&(T=!1),g.protectedKeys={...c},!g.isActive&&null===w||!v&&!g.prevProp||n(v)||"boolean"==typeof v)continue;let b=(m=g.prevProp,"string"==typeof(y=v)?y!==m:!!Array.isArray(y)&&!eZ(y,m)),P=b||o===a&&g.isActive&&!T&&x||e>p&&x,S=!1,A=Array.isArray(v)?v:[v],M=A.reduce(r(o),{});!1===w&&(M={});let{prevResolvedValues:E={}}=g,V={...E,...M},D=e=>{P=!0,d.has(e)&&(S=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in V){let e=M[t],i=E[t];if(c.hasOwnProperty(t))continue;let n=!1;(f(e)&&f(i)?eZ(e,i):e===i)?void 0!==e&&d.has(t)?D(t):g.protectedKeys[t]=!0:null!=e?D(t):d.add(t)}g.prevProp=v,g.prevResolvedValues=M,g.isActive&&(c={...c,...M}),s&&t.blockInitialAnimation&&(P=!1);let k=!(T&&b)||S;P&&k&&u.push(...A.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),u.push({animation:e})}let g=!!u.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(u):Promise.resolve()}return{animateChanges:a,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=a(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=e4(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let e7=0;class it extends e6{constructor(){super(...arguments),this.id=e7++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ie={x:!1,y:!1};function ii(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let is=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ir(t){return{point:{x:t.pageX,y:t.pageY}}}let io=t=>e=>is(e)&&t(e,ir(e));function ia(t,e,i,n){return ii(t,e,io(i),n)}function il({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function ih(t){return t.max-t.min}function iu(t,e,i,n=.5){t.origin=n,t.originPoint=tp(e.min,e.max,t.origin),t.scale=ih(i)/ih(e),t.translate=tp(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function id(t,e,i,n){iu(t.x,e.x,i.x,n?n.originX:void 0),iu(t.y,e.y,i.y,n?n.originY:void 0)}function ic(t,e,i){t.min=i.min+e.min,t.max=t.min+ih(e)}function ip(t,e,i){t.min=e.min-i.min,t.max=t.min+ih(e)}function im(t,e,i){ip(t.x,e.x,i.x),ip(t.y,e.y,i.y)}let iy=()=>({translate:0,scale:1,origin:0,originPoint:0}),ig=()=>({x:iy(),y:iy()}),iv=()=>({min:0,max:0}),ix=()=>({x:iv(),y:iv()});function iw(t){return[t("x"),t("y")]}function iT(t){return void 0===t||1===t}function ib({scale:t,scaleX:e,scaleY:i}){return!iT(t)||!iT(e)||!iT(i)}function iP(t){return ib(t)||iS(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iS(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iA(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function iM(t,e=0,i=1,n,s){t.min=iA(t.min,e,i,n,s),t.max=iA(t.max,e,i,n,s)}function iE(t,{x:e,y:i}){iM(t.x,e.translate,e.scale,e.originPoint),iM(t.y,i.translate,i.scale,i.originPoint)}function iV(t,e){t.min=t.min+e,t.max=t.max+e}function iD(t,e,i,n,s=.5){let r=tp(t.min,t.max,s);iM(t,e,i,r,n)}function ik(t,e){iD(t.x,e.x,e.scaleX,e.scale,e.originX),iD(t.y,e.y,e.scaleY,e.scale,e.originY)}function iC(t,e){return il(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iR=({current:t})=>t?t.ownerDocument.defaultView:null;function ij(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iL=(t,e)=>Math.abs(t-e);class iO{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:s=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iI(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iL(t.x,e.x)**2+iL(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=u.uv;this.history.push({...n,timestamp:s});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iB(e,this.transformPagePoint),u.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iI("pointercancel"===t.type?this.lastMoveEventInfo:iB(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!is(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=r,this.contextWindow=n||window;let o=iB(ir(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=u.uv;this.history=[{...a,timestamp:l}];let{onSessionStart:h}=e;h&&h(t,iI(o,this.history)),this.removeListeners=b(ia(this.contextWindow,"pointermove",this.handlePointerMove),ia(this.contextWindow,"pointerup",this.handlePointerUp),ia(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,u.WG)(this.updatePoint)}}function iB(t,e){return e?{point:e(t.point)}:t}function iF(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iI({point:t},e){return{point:t,delta:iF(t,iU(e)),offset:iF(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=iU(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>S(.1)));)i--;if(!n)return{x:0,y:0};let r=A(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let o={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iU(t){return t[t.length-1]}function iW(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iN(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i$(t,e,i){return{min:iG(t,e),max:iG(t,i)}}function iG(t,e){return"number"==typeof t?t:t[e]||0}let iY=new WeakMap;class iz{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ix(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iO(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ir(t).point)},onStart:(t,e)=>{var i;let{drag:n,dragPropagation:s,onDragStart:r}=this.getProps();if(n&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=n)||"y"===i?ie[i]?null:(ie[i]=!0,()=>{ie[i]=!1}):ie.x||ie.y?null:(ie.x=ie.y=!0,()=>{ie.x=ie.y=!1}),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iw(t=>{let e=this.getAxisMotionValue(t).get()||0;if(q.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=ih(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&u.Gt.postRender(()=>r(t,e)),v(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>iw(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:i,contextWindow:iR(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!n||!i)return;let{velocity:r}=n;this.startAnimation(r);let{onDragEnd:o}=this.getProps();o&&u.Gt.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!iX(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tp(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tp(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&ij(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:iW(t.x,i,s),y:iW(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i$(t,"left","right"),y:i$(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iw(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!ij(e))return!1;let n=e.current;D(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=iC(t,i),{scroll:s}=e;return s&&(iV(n.x,s.offset.x),iV(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),o={x:iN((t=s.layout.layoutBox).x,r.x),y:iN(t.y,r.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=il(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iw(o=>{if(!iX(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return v(this.visualElement,t),i.start(eK(t,i,0,e,this.visualElement,!1))}stopAnimation(){iw(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iw(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iw(e=>{let{drag:i}=this.getProps();if(!iX(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-tp(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ij(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iw(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=ih(t),s=ih(e);return s>n?i=t3(e.min,e.max-n,t.min):n>s&&(i=t3(t.min,t.max-s,e.min)),P(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iw(e=>{if(!iX(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set(tp(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;iY.set(this.visualElement,this);let t=ia(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();ij(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),u.Gt.read(e);let s=ii(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iw(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:o}}}function iX(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class iH extends e6{constructor(t){super(t),this.removeGroupControls=tI.l,this.removeListeners=tI.l,this.controls=new iz(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tI.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let iK=t=>(e,i)=>{t&&u.Gt.postRender(()=>t(e,i))};class iq extends e6{constructor(){super(...arguments),this.removePointerDownListener=tI.l}onPointerDown(t){this.session=new iO(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iR(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:iK(t),onStart:iK(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&u.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ia(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var iQ=i(95155);let{schedule:i_}=(0,i(58437).I)(queueMicrotask,!1);var iZ=i(12115),iJ=i(32082),i0=i(90869);let i1=(0,iZ.createContext)({}),i5={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function i2(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let i3={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Q.test(t))return t;t=parseFloat(t)}let i=i2(t,e.target.x),n=i2(t,e.target.y);return`${i}% ${n}%`}},i9={},i4=!1;class i6 extends iZ.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;!function(t){for(let e in t)i9[e]=t[e],C(e)&&(i9[e].isCSSVariable=!0)}(i7),s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),i4&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),i5.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:r}=i;return r&&(r.isPresent=s,i4=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent===s||(s?r.promote():r.relegate()||u.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i_.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function i8(t){let[e,i]=(0,iJ.xQ)(),n=(0,iZ.useContext)(i0.L);return(0,iQ.jsx)(i6,{...t,layoutGroup:n,switchLayoutGroup:(0,iZ.useContext)(i1),isPresent:e,safeToRemove:i})}let i7={borderRadius:{...i3,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:i3,borderTopRightRadius:i3,borderBottomLeftRadius:i3,borderBottomRightRadius:i3,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tu.parse(t);if(n.length>5)return t;let s=tu.createTransformer(t),r=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+r]/=o,n[1+r]/=a;let l=tp(o,a,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}};var nt=i(6983);function ne(t){return(0,nt.G)(t)&&"ownerSVGElement"in t}var ni=i(75626),nn=i(56668);let ns=(t,e)=>t.depth-e.depth;class nr{constructor(){this.children=[],this.isDirty=!1}add(t){(0,nn.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,nn.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ns),this.isDirty=!1,this.children.forEach(t)}}function no(t){return g(t)?t.get():t}let na=["TopLeft","TopRight","BottomLeft","BottomRight"],nl=na.length,nh=t=>"string"==typeof t?parseFloat(t):t,nu=t=>"number"==typeof t||Q.test(t);function nd(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nc=nm(0,.5,tZ),np=nm(.5,.95,tI.l);function nm(t,e,i){return n=>n<t?0:n>e?1:i(t3(t,e,n))}function nf(t,e){t.min=e.min,t.max=e.max}function ny(t,e){nf(t.x,e.x),nf(t.y,e.y)}function ng(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nv(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function nx(t,e,[i,n,s],r,o){!function(t,e=0,i=1,n=.5,s,r=t,o=t){if(q.test(e)&&(e=parseFloat(e),e=tp(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tp(r.min,r.max,n);t===r&&(a-=e),t.min=nv(t.min,e,i,a,s),t.max=nv(t.max,e,i,a,s)}(t,e[i],e[n],e[s],e.scale,r,o)}let nw=["x","scaleX","originX"],nT=["y","scaleY","originY"];function nb(t,e,i,n){nx(t.x,e,nw,i?i.x:void 0,n?n.x:void 0),nx(t.y,e,nT,i?i.y:void 0,n?n.y:void 0)}function nP(t){return 0===t.translate&&1===t.scale}function nS(t){return nP(t.x)&&nP(t.y)}function nA(t,e){return t.min===e.min&&t.max===e.max}function nM(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nE(t,e){return nM(t.x,e.x)&&nM(t.y,e.y)}function nV(t){return ih(t.x)/ih(t.y)}function nD(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nk{constructor(){this.members=[]}add(t){(0,nn.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,nn.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nC={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nR=["","X","Y","Z"],nj=0;function nL(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nO({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=nj++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,eV.Q.value&&(nC.nodes=nC.calculatedTargetDeltas=nC.calculatedProjections=0),this.nodes.forEach(nI),this.nodes.forEach(nz),this.nodes.forEach(nX),this.nodes.forEach(nU),eV.Q.addProjectionMetrics&&eV.Q.addProjectionMetrics(nC)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nr)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ni.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=ne(e)&&!(ne(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i;let n=0,s=()=>this.root.updateBlockedByResize=!1;u.Gt.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=M.k.now(),n=({timestamp:s})=>{let r=s-i;r>=250&&((0,u.WG)(n),t(r-e))};return u.Gt.setup(n,!0),()=>(0,u.WG)(n)}(s,250),i5.hasAnimatedSinceResize&&(i5.hasAnimatedSinceResize=!1,this.nodes.forEach(nY)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||nZ,{onLayoutAnimationStart:o,onLayoutAnimationComplete:l}=s.getProps(),h=!this.targetLayout||!nE(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...a(r,"layout"),onPlay:o,onComplete:l};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||nY(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,u.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nH),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[w];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",u.Gt,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nN);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(n$);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nG),this.nodes.forEach(nB),this.nodes.forEach(nF)):this.nodes.forEach(n$),this.clearAllSnapshots();let t=M.k.now();u.uv.delta=P(0,1e3/60,t-u.uv.timestamp),u.uv.timestamp=t,u.uv.isProcessing=!0,u.PP.update.process(u.uv),u.PP.preRender.process(u.uv),u.PP.render.process(u.uv),u.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i_.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nW),this.sharedNodes.forEach(nK)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,u.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){u.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||ih(this.snapshot.measuredBox.x)||ih(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ix(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nS(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iP(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),n1((e=n).x),n1(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ix();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(n2))){let{scroll:t}=this.root;t&&(iV(e.x,t.offset.x),iV(e.y,t.offset.y))}return e}removeElementScroll(t){let e=ix();if(ny(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&ny(e,t),iV(e.x,s.offset.x),iV(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=ix();ny(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ik(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iP(n.latestValues)&&ik(i,n.latestValues)}return iP(this.latestValues)&&ik(i,this.latestValues),i}removeTransform(t){let e=ix();ny(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iP(i.latestValues))continue;ib(i.latestValues)&&i.updateSnapshot();let n=ix();ny(n,i.measurePageBox()),nb(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iP(this.latestValues)&&nb(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==u.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=u.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ix(),this.relativeTargetOrigin=ix(),im(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),ny(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ix(),this.targetWithTransforms=ix()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,o,a;this.forceRelativeParentToResolveTarget(),r=this.target,o=this.relativeTarget,a=this.relativeParent.target,ic(r.x,o.x,a.x),ic(r.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ny(this.target,this.layout.layoutBox),iE(this.target,this.targetDelta)):ny(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ix(),this.relativeTargetOrigin=ix(),im(this.relativeTargetOrigin,this.target,t.target),ny(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}eV.Q.value&&nC.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||ib(this.parent.latestValues)||iS(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===u.uv.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;ny(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let s,r;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(s=i[a]).projectionDelta;let{visualElement:o}=s.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&ik(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iE(t,r)),n&&iP(s.latestValues)&&ik(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=ix());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ng(this.prevProjectionDelta.x,this.projectionDelta.x),ng(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),id(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===r&&this.treeScale.y===o&&nD(this.projectionDelta.x,this.prevProjectionDelta.x)&&nD(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),eV.Q.value&&nC.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ig(),this.projectionDelta=ig(),this.projectionDeltaWithTransform=ig()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},o=ig();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=ix(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(n_));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(nq(o.x,t.x,n),nq(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,y;if(im(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,y=n,nQ(p.x,m.x,f.x,y),nQ(p.y,m.y,f.y,y),i&&(h=this.relativeTarget,c=i,nA(h.x,c.x)&&nA(h.y,c.y)))this.isProjectionDirty=!1;i||(i=ix()),ny(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=tp(0,i.opacity??1,nc(n)),t.opacityExit=tp(e.opacity??1,0,np(n))):r&&(t.opacity=tp(e.opacity??1,i.opacity??1,n));for(let s=0;s<nl;s++){let r=`border${na[s]}Radius`,o=nd(e,r),a=nd(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nu(o)===nu(a)?(t[r]=Math.max(tp(nh(o),nh(a),n),0),(q.test(a)||q.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=tp(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,u.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=u.Gt.update(()=>{i5.hasAnimatedSinceResize=!0,E.layout++,this.motionValue||(this.motionValue=(0,m.OQ)(0)),this.currentAnimation=function(t,e,i){let n=g(t)?t:(0,m.OQ)(t);return n.start(eK("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{E.layout--},onComplete:()=>{E.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&n5(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||ix();let e=ih(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=ih(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}ny(e,i),ik(e,s),id(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nk),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nL("z",t,n,this.animationValues);for(let e=0;e<nR.length;e++)nL(`rotate${nR[e]}`,t,n,this.animationValues),nL(`skew${nR[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=no(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=no(e?.pointerEvents)||""),this.hasProjected&&!iP(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let s=n.animationValues||n.latestValues;this.applyTransformsToTarget();let r=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,o=i?.z||0;if((s||r||o)&&(n=`translate3d(${s}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(r=i(s,r)),t.transform=r;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,i9){if(void 0===s[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=i9[e],l="none"===r?s[e]:i(s[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?no(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nN),this.root.sharedNodes.clear()}}}function nB(t){t.updateLayout()}function nF(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?iw(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=ih(n);n.min=i[t].min,n.max=n.min+s}):n5(s,e.layoutBox,i)&&iw(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],o=ih(i[n]);s.max=s.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=ig();id(o,i,e.layoutBox);let a=ig();r?id(a,t.applyTransform(n,!0),e.measuredBox):id(a,i,e.layoutBox);let l=!nS(o),h=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let o=ix();im(o,e.layoutBox,s.layoutBox);let a=ix();im(a,i,r.layoutBox),nE(o,a)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nI(t){eV.Q.value&&nC.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nU(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nW(t){t.clearSnapshot()}function nN(t){t.clearMeasurements()}function n$(t){t.isLayoutDirty=!1}function nG(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nY(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nz(t){t.resolveTargetDelta()}function nX(t){t.calcProjection()}function nH(t){t.resetSkewAndRotation()}function nK(t){t.removeLeadSnapshot()}function nq(t,e,i){t.translate=tp(e.translate,0,i),t.scale=tp(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nQ(t,e,i,n){t.min=tp(e.min,i.min,n),t.max=tp(e.max,i.max,n)}function n_(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nZ={duration:.45,ease:[.4,0,.1,1]},nJ=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),n0=nJ("applewebkit/")&&!nJ("chrome/")?Math.round:tI.l;function n1(t){t.min=n0(t.min),t.max=n0(t.max)}function n5(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nV(e)-nV(i)))}function n2(t){return t!==t.root&&t.scroll?.wasRoot}let n3=nO({attachResizeListener:(t,e)=>ii(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),n9={current:void 0},n4=nO({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!n9.current){let t=new n3({});t.mount(window),t.setOptions({layoutScroll:!0}),n9.current=t}return n9.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function n6(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function n8(t){return!("touch"===t.pointerType||ie.x||ie.y)}function n7(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&u.Gt.postRender(()=>s(e,ir(e)))}class st extends e6{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=n6(t,i),o=t=>{if(!n8(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{n8(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",o,s)}),r}(t,(t,e)=>(n7(this.node,e,"Start"),t=>n7(this.node,t,"End"))))}unmount(){}}class se extends e6{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=b(ii(this.node.current,"focus",()=>this.onFocus()),ii(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let si=(t,e)=>!!e&&(t===e||si(t,e.parentElement)),sn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ss=new WeakSet;function sr(t){return e=>{"Enter"===e.key&&t(e)}}function so(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let sa=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=sr(()=>{if(ss.has(i))return;so(i,"down");let t=sr(()=>{so(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>so(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function sl(t){return is(t)&&!(ie.x||ie.y)}function sh(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&u.Gt.postRender(()=>s(e,ir(e)))}class su extends e6{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=n6(t,i),o=t=>{let n=t.currentTarget;if(!sl(t))return;ss.add(n);let r=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ss.has(n)&&ss.delete(n),sl(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||si(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{if((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,s),(0,eI.s)(t))t.addEventListener("focus",t=>sa(t,s)),!sn.has(t.tagName)&&-1===t.tabIndex&&!t.hasAttribute("tabindex")&&(t.tabIndex=0)}),r}(t,(t,e)=>(sh(this.node,e,"Start"),(t,{success:e})=>sh(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sd=new WeakMap,sc=new WeakMap,sp=t=>{let e=sd.get(t.target);e&&e(t)},sm=t=>{t.forEach(sp)},sf={some:0,all:1};class sy extends e6{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sf[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;sc.has(i)||sc.set(i,{});let n=sc.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(sm,{root:t,...e})),n[s]}(e);return sd.set(t,i),n.observe(t),()=>{sd.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sg=(0,iZ.createContext)({strict:!1});var sv=i(51508);let sx=(0,iZ.createContext)({});function sw(t){return n(t.animate)||e1.some(e=>eJ(t[e]))}function sT(t){return!!(sw(t)||t.variants)}function sb(t){return Array.isArray(t)?t.join(" "):t}var sP=i(68972);let sS={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sA={};for(let t in sS)sA[t]={isEnabled:e=>sS[t].some(t=>!!e[t])};let sM=Symbol.for("motionComponentSymbol");var sE=i(80845),sV=i(97494);function sD(t,{layout:e,layoutId:i}){return c.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!i9[t]||"opacity"===t)}let sk=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sC={...O,transform:Math.round},sR={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,backgroundPositionX:Q,backgroundPositionY:Q,rotate:K,rotateX:K,rotateY:K,rotateZ:K,scale:F,scaleX:F,scaleY:F,scaleZ:F,skew:K,skewX:K,skewY:K,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:B,originX:J,originY:J,originZ:Q,zIndex:sC,fillOpacity:B,strokeOpacity:B,numOctaves:sC},sj={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sL=d.length;function sO(t,e,i){let{style:n,vars:s,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(c.has(t)){o=!0;continue}if(C(t)){s[t]=i;continue}{let e=sk(i,sR[t]);t.startsWith("origin")?(a=!0,r[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",s=!0;for(let r=0;r<sL;r++){let o=d[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=sk(a,sR[o]);if(!l){s=!1;let e=sj[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;n.transformOrigin=`${t} ${e} ${i}`}}let sB=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function sF(t,e,i){for(let n in e)g(e[n])||sD(n,i)||(t[n]=e[n])}let sI={offset:"stroke-dashoffset",array:"stroke-dasharray"},sU={offset:"strokeDashoffset",array:"strokeDasharray"};function sW(t,{attrX:e,attrY:i,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:o=0,...a},l,h,u){if(sO(t,a,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==s&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?sI:sU;t[r.offset]=Q.transform(-n);let o=Q.transform(e),a=Q.transform(i);t[r.array]=`${o} ${a}`}(d,s,r,o,!1)}let sN=()=>({...sB(),attrs:{}}),s$=t=>"string"==typeof t&&"svg"===t.toLowerCase();var sG=i(95500);let sY=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function sz(t){if("string"!=typeof t||t.includes("-"));else if(sY.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var sX=i(82885);let sH=t=>(e,i)=>{let s=(0,iZ.useContext)(sx),o=(0,iZ.useContext)(sE.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,o){return{latestValues:function(t,e,i,s){let o={},a=s(t,{});for(let t in a)o[t]=no(a[t]);let{initial:l,animate:h}=t,u=sw(t),d=sT(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=r(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(i,s,o,t),renderState:e()}})(t,e,s,o);return i?a():(0,sX.M)(a)};function sK(t,e,i){let{style:n}=t,s={};for(let r in n)(g(n[r])||e.style&&g(e.style[r])||sD(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(s[r]=n[r]);return s}let sq={useVisualState:sH({scrapeMotionValuesFromProps:sK,createRenderState:sB})};function sQ(t,e,i){let n=sK(t,e,i);for(let i in t)(g(t[i])||g(e[i]))&&(n[-1!==d.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let s_={useVisualState:sH({scrapeMotionValuesFromProps:sQ,createRenderState:sN})},sZ=t=>e=>e.test(t),sJ=[O,Q,q,K,Z,_,{test:t=>"auto"===t,parse:t=>t}],s0=t=>sJ.find(sZ(t)),s1=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),s5=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,s2=t=>/^0[^.\s]+$/u.test(t),s3=new Set(["brightness","contrast","saturate","opacity"]);function s9(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(U)||[];if(!n)return t;let s=i.replace(n,""),r=+!!s3.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let s4=/\b([a-z-]*)\(.*?\)/gu,s6={...tu,getAnimatableNone:t=>{let e=t.match(s4);return e?e.map(s9).join(" "):t}},s8={...sR,color:te,backgroundColor:te,outlineColor:te,fill:te,stroke:te,borderColor:te,borderTopColor:te,borderRightColor:te,borderBottomColor:te,borderLeftColor:te,filter:s6,WebkitFilter:s6},s7=t=>s8[t];function rt(t,e){let i=s7(t);return i!==s6&&(i=tu),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let re=new Set(["auto","none","0"]);class ri extends eS{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&j(n=n.trim())){let s=function t(e,i,n=1){D(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[s,r]=function(t){let e=s5.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let o=window.getComputedStyle(i).getPropertyValue(s);if(o){let t=o.trim();return s1(t)?parseFloat(t):t}return j(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!p.has(i)||2!==t.length)return;let[n,s]=t,r=s0(n),o=s0(s);if(r!==o){if(em(r)&&em(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eg[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||s2(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!re.has(e)&&to(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=rt(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eg[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=eg[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let rn=[...sJ,te,tu],rs=t=>rn.find(sZ(t)),rr={current:null},ro={current:!1},ra=new WeakMap,rl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rh{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eS,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=M.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,u.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!s,this.isControllingVariants=sw(e),this.isVariantNode=sT(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&g(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ra.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ro.current||function(){if(ro.current=!0,sP.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rr.current=t.matches;t.addEventListener("change",e),e()}else rr.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,u.WG)(this.notifyUpdate),(0,u.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=c.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&u.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sA){let e=sA[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ix()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rl.length;e++){let i=rl[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if(g(s))t.addValue(n,s);else if(g(r))t.addValue(n,(0,m.OQ)(s,{owner:t}));else if(r!==s){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,(0,m.OQ)(void 0!==e?e:s,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,m.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(s1(i)||s2(i))?i=parseFloat(i):!rs(i)&&tu.test(e)&&(i=rt(t,e)),this.setBaseTarget(t,g(i)?i.get():i)),g(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=r(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||g(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new ni.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class ru extends rh{constructor(){super(...arguments),this.KeyframeResolver=ri}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;g(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rd(t,{style:e,vars:i},n,s){let r;let o=t.style;for(r in e)o[r]=e[r];for(r in s?.applyProjectionStyles(o,n),i)o.setProperty(r,i[r])}class rc extends ru{constructor(){super(...arguments),this.type="html",this.renderInstance=rd}readValueFromInstance(t,e){if(c.has(e))return this.projection?.isProjecting?eu(e):ec(t,e);{let i=window.getComputedStyle(t),n=(C(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iC(t,e)}build(t,e,i){sO(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return sK(t,e,i)}}let rp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class rm extends ru{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ix}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(c.has(e)){let t=s7(e);return t&&t.default||0}return e=rp.has(e)?e:x(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return sQ(t,e,i)}build(t,e,i){sW(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){!function(t,e,i,n){for(let i in rd(t,e,void 0,n),e.attrs)t.setAttribute(rp.has(i)?i:x(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=s$(t.tagName),super.mount(t)}}let rf=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((l={animation:{Feature:e8},exit:{Feature:it},inView:{Feature:sy},tap:{Feature:su},focus:{Feature:se},hover:{Feature:st},pan:{Feature:iq},drag:{Feature:iH,ProjectionNode:n4,MeasureLayout:i8},layout:{ProjectionNode:n4,MeasureLayout:i8}},h=(t,e)=>sz(t)?new rm(e):new rc(e,{allowProjection:t!==iZ.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:s,useRender:r,useVisualState:o,Component:a}=t;function l(t,e){var i,n,l;let h;let u={...(0,iZ.useContext)(sv.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,iZ.useContext)(i0.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(sw(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eJ(e)?e:void 0,animate:eJ(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,iZ.useContext)(sx));return(0,iZ.useMemo)(()=>({initial:e,animate:i}),[sb(e),sb(i)])}(t),p=o(t,d);if(!d&&sP.B){n=0,l=0,(0,iZ.useContext)(sg).strict;let t=function(t){let{drag:e,layout:i}=sA;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,n,s){let{visualElement:r}=(0,iZ.useContext)(sx),o=(0,iZ.useContext)(sg),a=(0,iZ.useContext)(sE.t),l=(0,iZ.useContext)(sv.Q).reducedMotion,h=(0,iZ.useRef)(null);n=n||o.renderer,!h.current&&n&&(h.current=n(t,{visualState:e,parent:r,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let u=h.current,d=(0,iZ.useContext)(i1);u&&!u.projection&&s&&("html"===u.type||"svg"===u.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!o||a&&ij(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,s,d);let c=(0,iZ.useRef)(!1);(0,iZ.useInsertionEffect)(()=>{u&&c.current&&u.update(i,a)});let p=i[w],m=(0,iZ.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,sV.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),i_.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,iZ.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(a,p,u,s,t.ProjectionNode)}return(0,iQ.jsxs)(sx.Provider,{value:c,children:[h&&c.visualElement?(0,iQ.jsx)(h,{visualElement:c.visualElement,...u}):null,r(a,t,(i=c.visualElement,(0,iZ.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):ij(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}n&&function(t){for(let e in t)sA[e]={...sA[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!==(i=null!==(e=a.displayName)&&void 0!==e?e:a.name)&&void 0!==i?i:"",")"));let h=(0,iZ.forwardRef)(l);return h[sM]=a,h}({...sz(t)?s_:sq,preloadedFeatures:l,useRender:function(t=!1){return(e,i,n,{latestValues:s},r)=>{let o=(sz(e)?function(t,e,i,n){let s=(0,iZ.useMemo)(()=>{let i=sN();return sW(i,e,s$(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};sF(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return sF(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,iZ.useMemo)(()=>{let i=sB();return sO(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,s,r,e),a=(0,sG.J)(i,"string"==typeof e,t),l=e!==iZ.Fragment?{...a,...o,ref:n}:{},{children:h}=i,u=(0,iZ.useMemo)(()=>g(h)?h.get():h,[h]);return(0,iZ.createElement)(e,{...l,children:u})}}(e),createVisualElement:h,Component:t})}))},27351:(t,e,i)=>{i.d(e,{s:()=>s});var n=i(6983);function s(t){return(0,n.G)(t)&&"offsetHeight"in t}},32082:(t,e,i)=>{i.d(e,{xQ:()=>r});var n=i(12115),s=i(80845);function r(t=!0){let e=(0,n.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let h=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,h]:[!0]}},68972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},74436:(t,e,i)=>{i.d(e,{k5:()=>u});var n=i(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=n.createContext&&n.createContext(s),o=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function h(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach(function(e){var n,s,r;n=t,s=e,r=i[e],(s=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function u(t){return e=>n.createElement(d,a({attr:h({},t.attr)},e),function t(e){return e&&e.map((e,i)=>n.createElement(e.tag,h({key:i},e.attr),t(e.child)))}(t.child))}function d(t){var e=e=>{var i,{attr:s,size:r,title:l}=t,u=function(t,e){if(null==t)return{};var i,n,s=function(t,e){if(null==t)return{};var i={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;i[n]=t[n]}return i}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],!(e.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(t,i)&&(s[i]=t[i])}return s}(t,o),d=r||e.size||"1em";return e.className&&(i=e.className),t.className&&(i=(i?i+" ":"")+t.className),n.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},e.attr,s,u,{className:i,style:h(h({color:t.color||e.color},e.style),t.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&n.createElement("title",null,l),t.children)};return void 0!==r?n.createElement(r.Consumer,null,t=>e(t)):e(s)}},80845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(12115).createContext)(null)},90869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(12115).createContext)({})},95500:(t,e,i)=>{i.d(e,{J:()=>a,D:()=>o});let n=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function s(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||n.has(t)}let r=t=>!s(t);function o(t){"function"==typeof t&&(r=e=>e.startsWith("on")?!s(e):t(e))}try{o(require("@emotion/is-prop-valid").default)}catch{}function a(t,e,i){let n={};for(let o in t)("values"!==o||"object"!=typeof t.values)&&(r(o)||!0===i&&s(o)||!e&&!s(o)||t.draggable&&o.startsWith("onDrag"))&&(n[o]=t[o]);return n}},97494:(t,e,i)=>{i.d(e,{E:()=>s});var n=i(12115);let s=i(68972).B?n.useLayoutEffect:n.useEffect}}]);