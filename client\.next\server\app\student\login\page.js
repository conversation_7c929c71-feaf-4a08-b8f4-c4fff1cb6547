(()=>{var e={};e.id=8504,e.ids=[8504],e.modules={1030:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\student\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9985:(e,t,r)=>{Promise.resolve().then(r.bind(r,1030))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23137:(e,t,r)=>{Promise.resolve().then(r.bind(r,78772))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},53161:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["student",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1030)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/student/login/page",pathname:"/student/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78772:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S});var s=r(60687),a=r(90269),i=r(46303),l=r(43210),n=r(29523),o=r(45880),d=r(27605),c=r(63442),m=r(17019),u=r(89667),f=r(91821),x=r(93613),p=r(41862),g=r(80942),h=r(52581),b=r(41831),N=r(16189),v=r(88920),j=r(92449);let y=o.z.object({contactNo:o.z.string().optional().refine(e=>!e||/^\d{10}$/.test(e),"Please enter a valid 10-digit mobile number"),email:o.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}).refine(e=>!e.contactNo&&!e.email||!!e.contactNo||!!e.email,{message:"Either mobile number or email is required",path:["contactNo","email"]}),w=o.z.object({firstName:o.z.string().min(2,"First name is required").regex(/^[a-zA-Z]+$/,"Invalid first name").transform(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()),lastName:o.z.string().min(2,"Last name is required").regex(/^[a-zA-Z]+$/,"Invalid last name").transform(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()),contactNo:o.z.string().regex(/^\d{10}$/,"Please enter a valid 10-digit mobile number"),referralCode:o.z.string().optional(),email:o.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address")}),C=({message:e})=>e?(0,s.jsxs)(f.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-500"}),(0,s.jsx)(f.TN,{className:"text-red-500",children:e})]}):null;function P(){let e=(0,N.useRouter)(),t=(0,N.useSearchParams)(),[r,o]=(0,l.useState)(!0),[f,x]=(0,l.useState)(!1),[P,S]=(0,l.useState)(""),[$,R]=(0,l.useState)(null),[I,k]=(0,l.useState)("mobile"),[z,q]=(0,l.useState)(!1),[U,A]=(0,l.useState)(null),E=(0,d.mN)({resolver:(0,c.u)(y),defaultValues:{contactNo:"",email:""},mode:"onChange"}),F=(0,d.mN)({resolver:(0,c.u)(w),defaultValues:{firstName:"",lastName:"",contactNo:"",referralCode:t.get("ref")||localStorage.getItem("referralCode")||"",email:""},mode:"onChange"}),T=async(r,s)=>{x(!0),S("");try{if(s){let a=await (0,b.bZ)({contactNo:s,email:r});if(!1===a.success){S(a.message||"Authentication failed"),h.toast.error(a.message||"Authentication failed");return}let i=t.get("redirect")||"",l=i?`&redirect=${encodeURIComponent(i)}`:"";e.push(`/student-verify-otp?contactNo=${s}&flow=login&email=${encodeURIComponent(r)}${l}`),h.toast.success("OTP sent successfully. Please check your phone."),E.reset({contactNo:"",email:""}),q(!1),A(null)}else{let s=await (0,b.iM)({email:r});if(!1===s.success){S(s.message||"Email check failed"),h.toast.error(s.message||"Email check failed");return}if(A(s.data),q(s.data.isOldUser),E.setValue("contactNo",s.data.contactNo||""),E.setValue("email",r),E.trigger(),!s.data.isOldUser&&s.data.otpSent){let a=t.get("redirect")||"",i=a?`&redirect=${encodeURIComponent(a)}`:"";e.push(`/student-verify-otp?contactNo=${s.data.contactNo}&flow=login&email=${encodeURIComponent(r)}${i}`),h.toast.success("OTP sent successfully. Please check your phone."),E.reset({contactNo:"",email:""}),q(!1),A(null)}else s.data.isOldUser&&h.toast.info("This email is not linked to a mobile number. Please enter a mobile number to proceed.")}}catch(t){let e=t?.response?.data?.message||"Something went wrong";S(e),h.toast.error(e)}finally{x(!1)}},_=async r=>{x(!0),S("");try{if("email"===I)await T(r.email,z?r.contactNo:void 0);else if("mobile"===I){let s=await (0,b.bZ)({contactNo:r.contactNo});if(!1===s.success){S(s.message||"Authentication failed"),h.toast.error(s.message||"Authentication failed");return}let a=t.get("redirect")||"",i=a?`&redirect=${encodeURIComponent(a)}`:"";e.push(`/student-verify-otp?contactNo=${r.contactNo}&flow=login${i}`),h.toast.success("OTP sent successfully. Please check your phone."),E.reset({contactNo:"",email:""}),q(!1),A(null)}}catch(t){let e=t?.response?.data?.message||"Something went wrong";S(e),h.toast.error(e)}finally{x(!1)}},M=async r=>{x(!0),S("");try{let s={...r,...$?{referralCode:$}:{}},a=await (0,b.zy)(s);if(!1===a.success){S(a.message||"Authentication failed"),h.toast.error(a.message||"Authentication failed");return}let i=t.get("redirect")||"",l=i?`&redirect=${encodeURIComponent(i)}`:"";e.push(`/student-verify-otp?contactNo=${r.contactNo}&flow=register&firstName=${encodeURIComponent(r.firstName)}&lastName=${encodeURIComponent(r.lastName)}${r.referralCode?`&referralCode=${encodeURIComponent(r.referralCode)}`:""}${r.email?`&email=${encodeURIComponent(r.email)}`:""}${l}`),h.toast.success("OTP sent successfully. Please check your phone."),F.reset({firstName:"",lastName:"",contactNo:"",referralCode:"",email:""}),localStorage.removeItem("referralCode"),R(null),q(!1),A(null)}catch(t){let e=t?.response?.data?.message||"Something went wrong";S(e),h.toast.error(e)}finally{x(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{}),(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("div",{className:"bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4",children:(0,s.jsx)("p",{className:"text-center text-orange-700 font-medium",children:r?"Student Login Portal":"Student Registration Portal"})}),(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,s.jsx)(n.$,{variant:r?"default":"ghost",className:`px-4 py-2 rounded-lg ${r?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"}`,onClick:()=>{o(!0),S(""),k("mobile"),q(!1),A(null),E.reset({contactNo:"",email:""})},children:"Student Login"}),(0,s.jsx)(n.$,{variant:r?"ghost":"default",className:`px-4 py-2 rounded-lg ${r?"text-gray-600 hover:text-[#ff914d]":"bg-[#ff914d] text-white hover:bg-[#ff914d]/90"}`,onClick:()=>{o(!1),S(""),k("mobile"),q(!1),A(null),F.reset({firstName:"",lastName:"",contactNo:"",referralCode:$||"",email:""})},children:"Student Sign Up"})]})}),r&&(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,s.jsx)(n.$,{variant:"mobile"===I?"default":"ghost",className:`px-4 py-2 rounded-lg ${"mobile"===I?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"}`,onClick:()=>{k("mobile"),S(""),q(!1),A(null),E.reset({contactNo:"",email:""})},children:"Mobile"}),(0,s.jsx)(n.$,{variant:"email"===I?"default":"ghost",className:`px-4 py-2 rounded-lg ${"email"===I?"bg-[#ff914d] text-white hover:bg-[#ff914d]/90":"text-gray-600 hover:text-[#ff914d]"}`,onClick:()=>{k("email"),S(""),q(!1),A(null),E.reset({contactNo:"",email:""})},children:"Email"})]})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:r?"Welcome Back to Your Student Portal":"Register as a Student"}),(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 mr-3"}),(0,s.jsx)("span",{className:"text-[#ff914d] font-medium",children:"STUDENT PORTAL"}),(0,s.jsx)("div",{className:"h-0.5 w-16 bg-orange-300 ml-3"})]}),$&&!r&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-green-700 text-center",children:["\uD83C\uDF89 You' joining via referral code: ",(0,s.jsx)("span",{className:"font-semibold",children:$})]})})]}),(0,s.jsx)("div",{children:r?(0,s.jsx)(g.lV,{...E,children:(0,s.jsxs)("form",{onSubmit:E.handleSubmit(_),className:"space-y-6",children:[P&&(0,s.jsx)(C,{message:P}),"email"===I&&(0,s.jsx)(g.zB,{control:E.control,name:"email",render:({field:e})=>(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Email"}),(0,s.jsx)(g.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.pHD,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{type:"email",placeholder:"Enter email address",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...e})]})}),E.formState.touchedFields.email&&(0,s.jsx)(g.C5,{className:"text-red-500"})]})}),(0,s.jsx)(v.N,{children:("mobile"===I||z)&&(0,s.jsx)(j.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,s.jsx)(g.zB,{control:E.control,name:"contactNo",render:({field:e})=>(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,s.jsx)(g.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...e,disabled:!!U?.contactNo&&!z&&"email"===I,autoFocus:z})]})}),E.formState.touchedFields.contactNo&&(0,s.jsx)(g.C5,{className:"text-red-500"})]})})})}),(0,s.jsx)(n.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:f,children:f?(0,s.jsx)(p.A,{className:"h-5 w-5 animate-spin"}):"email"!==I||z?"Send OTP to Login":"Check Email"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,s.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,s.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})}):(0,s.jsx)(g.lV,{...F,children:(0,s.jsxs)("form",{onSubmit:F.handleSubmit(M),className:"space-y-6",children:[P&&(0,s.jsx)(C,{message:P}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsx)(g.zB,{control:F.control,name:"firstName",render:({field:e})=>(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"First Name"}),(0,s.jsx)(g.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{placeholder:"First Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...e})]})}),F.formState.touchedFields.firstName&&(0,s.jsx)(g.C5,{className:"text-red-500"})]})}),(0,s.jsx)(g.zB,{control:F.control,name:"lastName",render:({field:e})=>(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Last Name"}),(0,s.jsx)(g.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.JXP,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{placeholder:"Last Name",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...e})]})}),F.formState.touchedFields.lastName&&(0,s.jsx)(g.C5,{className:"text-red-500"})]})})]}),(0,s.jsx)(g.zB,{control:F.control,name:"contactNo",render:({field:e})=>(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Mobile Number"}),(0,s.jsx)(g.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m.QFc,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]",size:20}),(0,s.jsx)(u.p,{type:"tel",placeholder:"Enter 10-digit mobile number",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",maxLength:10,...e})]})}),F.formState.touchedFields.contactNo&&(0,s.jsx)(g.C5,{className:"text-red-500"})]})}),$&&(0,s.jsx)(g.zB,{control:F.control,name:"referralCode",render:({field:e})=>(0,s.jsxs)(g.eI,{children:[(0,s.jsx)(g.lR,{className:"text-gray-700 font-medium",children:"Referral Code"}),(0,s.jsx)(g.MJ,{children:(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(u.p,{placeholder:"Referral Code",className:"pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20",...e,disabled:!0})})}),F.formState.touchedFields.referralCode&&(0,s.jsx)(g.C5,{className:"text-red-500"})]})}),(0,s.jsx)(n.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:f,children:f?(0,s.jsx)(p.A,{className:"h-5 w-5 animate-spin"}):"Send OTP to Register"}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["By continuing, you agree to our"," ",(0,s.jsx)("a",{href:"https://www.uest.in/terms-and-conditions",className:"text-[#ff914d] hover:underline",children:"Terms & Conditions"})," ","and"," ",(0,s.jsx)("a",{href:"https://www.uest.in/privacy-policy",className:"text-[#ff914d] hover:underline",children:"Privacy Policy"})]})})]})})})]})}),(0,s.jsx)(i.default,{})]})}function S(){return(0,s.jsx)(l.Suspense,{fallback:(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 mx-auto mb-4 rounded"}),(0,s.jsx)("div",{className:"bg-gray-200 h-8 w-full mb-4 rounded"}),(0,s.jsx)("div",{className:"bg-gray-200 h-12 w-full rounded"})]})})})}),children:(0,s.jsx)(P,{})})}},79551:e=>{"use strict";e.exports=require("url")},80942:(e,t,r)=>{"use strict";r.d(t,{lV:()=>c,MJ:()=>h,Rr:()=>b,zB:()=>u,eI:()=>p,lR:()=>g,C5:()=>N});var s=r(60687),a=r(43210),i=r(11329),l=r(27605),n=r(4780),o=r(61170);function d({className:e,...t}){return(0,s.jsx)(o.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}let c=l.Op,m=a.createContext({}),u=({...e})=>(0,s.jsx)(m.Provider,{value:{name:e.name},children:(0,s.jsx)(l.xI,{...e})}),f=()=>{let e=a.useContext(m),t=a.useContext(x),{getFieldState:r}=(0,l.xW)(),s=(0,l.lN)({name:e.name}),i=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...i}},x=a.createContext({});function p({className:e,...t}){let r=a.useId();return(0,s.jsx)(x.Provider,{value:{id:r},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",e),...t})})}function g({className:e,...t}){let{error:r,formItemId:a}=f();return(0,s.jsx)(d,{"data-slot":"form-label","data-error":!!r,className:(0,n.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...t})}function h({...e}){let{error:t,formItemId:r,formDescriptionId:a,formMessageId:l}=f();return(0,s.jsx)(i.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${a} ${l}`:`${a}`,"aria-invalid":!!t,...e})}function b({className:e,...t}){let{formDescriptionId:r}=f();return(0,s.jsx)("p",{"data-slot":"form-description",id:r,className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function N({className:e,...t}){let{error:r,formMessageId:a}=f(),i=r?String(r?.message??""):t.children;return i?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,n.cn)("text-destructive text-sm",e),...t,children:i}):null}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,TN:()=>d});var s=r(60687),a=r(43210),i=r(24224),l=r(4780);let n=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:t,...r},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,l.cn)(n({variant:t}),e),...r}));o.displayName="Alert",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7013,2105,2449,288,2800],()=>r(53161));module.exports=s})();