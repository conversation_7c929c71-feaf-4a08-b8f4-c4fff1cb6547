{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/classes-details/%5Bid%5D/InnerPage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/classes-details/[id]/InnerPage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/classes-details/[id]/InnerPage.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/classes-details/%5Bid%5D/InnerPage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/classes-details/[id]/InnerPage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/classes-details/[id]/InnerPage.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/axios.ts"], "sourcesContent": ["import axios from 'axios';\r\nimport { toast } from 'sonner';\r\n\r\nconst baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';\r\nconst baseURL2 = process.env.NEXT_PUBLIC_UWHIZ_API_URL || 'http://localhost:4006';\r\n\r\nconsole.log('Axios baseURL:', baseURL);\r\n\r\nexport const axiosInstance = axios.create({\r\n  baseURL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {    \r\n    const serverSelect = config.headers['Server-Select'];\r\n    config.baseURL = serverSelect === 'uwhizServer' ? baseURL2 : baseURL;\r\n\r\n    // Only access localStorage in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      const studentToken = localStorage.getItem('studentToken');\r\n      if (studentToken) {\r\n        config.headers.Authorization = `Bearer ${studentToken}`;\r\n      }\r\n    }\r\n\r\n    return config;\r\n  },\r\n  (error) => Promise.reject(error)\r\n);\r\n\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response && error.response.status === 401) {\r\n      toast.error(error.response.data.message || 'Unauthorized');\r\n      if (typeof window !== 'undefined') {\r\n        localStorage.removeItem('user');\r\n        localStorage.removeItem('studentToken');\r\n        localStorage.removeItem('student_data');\r\n        window.location.replace('/?authError=1');\r\n      }\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,UAAU,oEAAmC;AACnD,MAAM,WAAW,6DAAyC;AAE1D,QAAQ,GAAG,CAAC,kBAAkB;AAEvB,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxC;IACA,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,MAAM,eAAe,OAAO,OAAO,CAAC,gBAAgB;IACpD,OAAO,OAAO,GAAG,iBAAiB,gBAAgB,WAAW;IAE7D,0CAA0C;IAC1C,uCAAmC;;IAKnC;IAEA,OAAO;AACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;AAG5B,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;QACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAC3C,uCAAmC;;QAKnC;IACF;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/slices/formProgressSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\n\r\nexport enum FormId {\r\n  PROFILE = 'about',\r\n  DESCRIPTION = 'description',\r\n  PHOTO_LOGO = 'photo_logo',\r\n  EDUCATION = 'education',\r\n  EXPERIENCE = 'experience',\r\n  CERTIFICATES = 'certificates',\r\n  TUTIONCLASS = 'tution_class',\r\n  ADDRESS = 'address',\r\n}\r\n\r\ninterface FormProgressState {\r\n  completedSteps: number;\r\n  totalSteps: number;\r\n  currentStep: number;\r\n  completedForms: Record<string, boolean>;\r\n}\r\n\r\nconst initialState: FormProgressState = {\r\n  completedSteps: 0,\r\n  totalSteps: 8,\r\n  currentStep: 1,\r\n  completedForms: {\r\n    [FormId.PROFILE]: false,\r\n    [FormId.DESCRIPTION]: false,\r\n    [FormId.PHOTO_LOGO]: false,\r\n    [FormId.EDUCATION]: false,\r\n    [FormId.CERTIFICATES]: false,\r\n    [FormId.EXPERIENCE]: false,\r\n    [FormId.TUTIONCLASS]: false,\r\n    [FormId.ADDRESS]: false,\r\n  },\r\n};\r\n\r\nconst formProgressSlice = createSlice({\r\n  name: 'formProgress',\r\n  initialState,\r\n  reducers: {\r\n    completeForm: (state, action: PayloadAction<FormId>) => {\r\n      const formId = action.payload;\r\n      if (!state.completedForms[formId]) {\r\n        state.completedForms[formId] = true;\r\n        state.completedSteps = Math.min(state.completedSteps + 1, state.totalSteps);\r\n      }\r\n    },\r\n    setCurrentStep: (state, action: PayloadAction<number>) => {\r\n      state.currentStep = action.payload;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { completeForm, setCurrentStep } = formProgressSlice.actions;\r\nexport default formProgressSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,IAAA,AAAK,gCAAA;;;;;;;;;WAAA;;AAkBZ,MAAM,eAAkC;IACtC,gBAAgB;IAChB,YAAY;IACZ,aAAa;IACb,gBAAgB;QACd,SAAgB,EAAE;QAClB,eAAoB,EAAE;QACtB,cAAmB,EAAE;QACrB,aAAkB,EAAE;QACpB,gBAAqB,EAAE;QACvB,cAAmB,EAAE;QACrB,gBAAoB,EAAE;QACtB,WAAgB,EAAE;IACpB;AACF;AAEA,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACpC,MAAM;IACN;IACA,UAAU;QACR,cAAc,CAAC,OAAO;YACpB,MAAM,SAAS,OAAO,OAAO;YAC7B,IAAI,CAAC,MAAM,cAAc,CAAC,OAAO,EAAE;gBACjC,MAAM,cAAc,CAAC,OAAO,GAAG;gBAC/B,MAAM,cAAc,GAAG,KAAK,GAAG,CAAC,MAAM,cAAc,GAAG,GAAG,MAAM,UAAU;YAC5E;QACF;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;IACF;AACF;AAEO,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,kBAAkB,OAAO;uCAC1D,kBAAkB,OAAO", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/helper.ts"], "sourcesContent": ["import { FormId, completeForm } from '@/store/slices/formProgressSlice';\r\nimport { AppDispatch } from '@/store';\r\nimport { jwtVerify } from 'jose';\r\n\r\n/* eslint-disable */\r\nexport const evaluateCompletedForms = (classData: any, dispatch: AppDispatch) => {\r\n  if (classData.contactNo) {\r\n    dispatch(completeForm(FormId.PROFILE));\r\n  }\r\n\r\n  if (classData.ClassAbout?.tutorBio?.length > 50) {\r\n    dispatch(completeForm(FormId.DESCRIPTION));\r\n  }\r\n\r\n  if (classData.ClassAbout?.profilePhoto && classData.ClassAbout?.classesLogo) {\r\n    dispatch(completeForm(FormId.PHOTO_LOGO));\r\n  }\r\n\r\n  if (classData.education?.length > 0) {\r\n    dispatch(completeForm(FormId.EDUCATION));\r\n  }\r\n\r\n  if (classData.certificates?.length > 0) {\r\n    dispatch(completeForm(FormId.CERTIFICATES));\r\n  }\r\n\r\n  if (classData.experience?.length > 0) {\r\n    dispatch(completeForm(FormId.EXPERIENCE));\r\n  }\r\n\r\n  if (classData.tuitionClasses?.length > 0) {\r\n    dispatch(completeForm(FormId.TUTIONCLASS));\r\n  }\r\n\r\n  if (classData.address) {\r\n    dispatch(completeForm(FormId.ADDRESS));\r\n  }\r\n};\r\n\r\nexport function convertTo24HourFormat(time12h: string): string {\r\n  if (!time12h) return '';\r\n  const [time, modifier] = time12h.split(' ');\r\n\r\n  let [hours, minutes] = time.split(':');\r\n\r\n  if (hours === '12') hours = '00';\r\n  if (modifier === 'PM') hours = String(parseInt(hours) + 12);\r\n\r\n  return `${hours.padStart(2, '0')}:${minutes}`;\r\n}\r\n\r\nexport const safeParseArray = (value: any): string[] => {\r\n  if (!value) return [];\r\n  try {\r\n    const parsed = typeof value === 'string' ? JSON.parse(value) : value;\r\n    return Array.isArray(parsed) ? parsed : [parsed];\r\n  } catch {\r\n    return [value]; // fallback to treating as plain string\r\n  }\r\n};\r\n\r\nexport const parseAndJoinArray = (value: any): string => {\r\n  try {\r\n    const parsed = typeof value === 'string' ? JSON.parse(value) : value;\r\n    return Array.isArray(parsed) ? parsed.join(', ') : parsed || 'N/A';\r\n  } catch {\r\n    return value || 'N/A';\r\n  }\r\n};\r\n\r\n\r\nconst secret = new TextEncoder().encode('secret123');\r\n\r\nexport async function verifyJWT(token: string): Promise<any | null> {\r\n  try {\r\n    const { payload } = await jwtVerify(token, secret);\r\n    return payload;\r\n  } catch (e) {\r\n    console.error('Invalid token:', e);\r\n    return null;\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAGO,MAAM,yBAAyB,CAAC,WAAgB;IACrD,IAAI,UAAU,SAAS,EAAE;QACvB,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,OAAO;IACtC;IAEA,IAAI,UAAU,UAAU,EAAE,UAAU,SAAS,IAAI;QAC/C,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,WAAW;IAC1C;IAEA,IAAI,UAAU,UAAU,EAAE,gBAAgB,UAAU,UAAU,EAAE,aAAa;QAC3E,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,UAAU;IACzC;IAEA,IAAI,UAAU,SAAS,EAAE,SAAS,GAAG;QACnC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,SAAS;IACxC;IAEA,IAAI,UAAU,YAAY,EAAE,SAAS,GAAG;QACtC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,YAAY;IAC3C;IAEA,IAAI,UAAU,UAAU,EAAE,SAAS,GAAG;QACpC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,UAAU;IACzC;IAEA,IAAI,UAAU,cAAc,EAAE,SAAS,GAAG;QACxC,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,WAAW;IAC1C;IAEA,IAAI,UAAU,OAAO,EAAE;QACrB,SAAS,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,2IAAA,CAAA,SAAM,CAAC,OAAO;IACtC;AACF;AAEO,SAAS,sBAAsB,OAAe;IACnD,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,CAAC,MAAM,SAAS,GAAG,QAAQ,KAAK,CAAC;IAEvC,IAAI,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IAElC,IAAI,UAAU,MAAM,QAAQ;IAC5B,IAAI,aAAa,MAAM,QAAQ,OAAO,SAAS,SAAS;IAExD,OAAO,GAAG,MAAM,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,SAAS;AAC/C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,OAAO,OAAO,EAAE;IACrB,IAAI;QACF,MAAM,SAAS,OAAO,UAAU,WAAW,KAAK,KAAK,CAAC,SAAS;QAC/D,OAAO,MAAM,OAAO,CAAC,UAAU,SAAS;YAAC;SAAO;IAClD,EAAE,OAAM;QACN,OAAO;YAAC;SAAM,EAAE,uCAAuC;IACzD;AACF;AAEO,MAAM,oBAAoB,CAAC;IAChC,IAAI;QACF,MAAM,SAAS,OAAO,UAAU,WAAW,KAAK,KAAK,CAAC,SAAS;QAC/D,OAAO,MAAM,OAAO,CAAC,UAAU,OAAO,IAAI,CAAC,QAAQ,UAAU;IAC/D,EAAE,OAAM;QACN,OAAO,SAAS;IAClB;AACF;AAGA,MAAM,SAAS,IAAI,cAAc,MAAM,CAAC;AAEjC,eAAe,UAAU,KAAa;IAC3C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAC3C,OAAO;IACT,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/classes-details/%5Bid%5D/page.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\r\nimport InnerPage from \"./InnerPage\"; \r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { parseAndJoinArray } from \"@/lib/helper\";\r\n\r\ninterface TeacherDetailsSEOProps {\r\n  params: { id: string };\r\n}\r\n\r\nasync function fetchTeacherData(id: string) {\r\n  try {\r\n    const res = await axiosInstance.get(`classes/details/${id}`);\r\n    return res.data;\r\n  } catch (err) {\r\n    console.error(\"Failed to fetch teacher data for SEO\", err);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport async function generateMetadata({ params }: TeacherDetailsSEOProps): Promise<Metadata> {\r\n  const { id } = params;\r\n  const data = await fetchTeacherData(id);\r\n\r\n  const { firstName, lastName, ClassAbout, tuitionClasses } = data;\r\n  const fullName = `${firstName} ${lastName}`;\r\n  const profileImg = ClassAbout?.profilePhoto\r\n    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.profilePhoto}`\r\n    : \"/default-teacher-profile.jpg\";\r\n  const catchyHeadline = ClassAbout?.catchyHeadline || \"Professional Educator\";\r\n  const tutorBio = ClassAbout?.tutorBio || \"Explore the expertise of our educators.\";\r\n  const tuitionDetails = tuitionClasses?.length > 0\r\n    ? parseAndJoinArray(tuitionClasses[0].subject || tuitionClasses[0].details || [])\r\n    : \"Educational Services\";\r\n\r\n  return {\r\n    title: `${fullName} - ${catchyHeadline} | Uest`,\r\n    description: `${tutorBio} Specializing in ${tuitionDetails}. Book your classes with ${fullName} on Uest today!`,\r\n    keywords: [\r\n      fullName,\r\n      catchyHeadline,\r\n      tuitionDetails,\r\n      \"online education\",\r\n      \"tuition classes\",\r\n      \"Uest\",\r\n      \"teacher profile\",\r\n    ],\r\n    openGraph: {\r\n      title: `${fullName} - ${catchyHeadline} | Uest`,\r\n      description: `${tutorBio} Specializing in ${tuitionDetails}.`,\r\n      url: `https://www.uest.in/classes-details/${id}`,\r\n      type: \"website\",\r\n      images: [\r\n        {\r\n          url: profileImg,\r\n          width: 800,\r\n          height: 600,\r\n          alt: `${fullName} Profile`,\r\n        },\r\n      ],\r\n    },\r\n    robots: {\r\n      index: true,\r\n      follow: true,\r\n    },\r\n  };\r\n}\r\n\r\nexport default function TeacherDetailsPage({ params }: TeacherDetailsSEOProps) {\r\n  return <InnerPage />;\r\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAMA,eAAe,iBAAiB,EAAU;IACxC,IAAI;QACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,IAAI;QAC3D,OAAO,IAAI,IAAI;IACjB,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAA0B;IACvE,MAAM,EAAE,EAAE,EAAE,GAAG;IACf,MAAM,OAAO,MAAM,iBAAiB;IAEpC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG;IAC5D,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,UAAU;IAC3C,MAAM,aAAa,YAAY,eAC3B,8DAA0C,WAAW,YAAY,EAAE,GACnE;IACJ,MAAM,iBAAiB,YAAY,kBAAkB;IACrD,MAAM,WAAW,YAAY,YAAY;IACzC,MAAM,iBAAiB,gBAAgB,SAAS,IAC5C,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO,IAAI,cAAc,CAAC,EAAE,CAAC,OAAO,IAAI,EAAE,IAC9E;IAEJ,OAAO;QACL,OAAO,GAAG,SAAS,GAAG,EAAE,eAAe,OAAO,CAAC;QAC/C,aAAa,GAAG,SAAS,iBAAiB,EAAE,eAAe,yBAAyB,EAAE,SAAS,eAAe,CAAC;QAC/G,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YACT,OAAO,GAAG,SAAS,GAAG,EAAE,eAAe,OAAO,CAAC;YAC/C,aAAa,GAAG,SAAS,iBAAiB,EAAE,eAAe,CAAC,CAAC;YAC7D,KAAK,CAAC,oCAAoC,EAAE,IAAI;YAChD,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK,GAAG,SAAS,QAAQ,CAAC;gBAC5B;aACD;QACH;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAEe,SAAS,mBAAmB,EAAE,MAAM,EAA0B;IAC3E,qBAAO,8OAAC,wJAAA,CAAA,UAAS;;;;;AACnB", "debugId": null}}]}