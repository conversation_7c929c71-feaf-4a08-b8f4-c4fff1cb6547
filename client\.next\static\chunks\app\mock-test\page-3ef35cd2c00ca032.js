(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2328],{30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,r:()=>o});var s=a(95155);a(12115);var r=a(66634),n=a(74466),l=a(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:n,asChild:i=!1,...c}=e,d=i?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,l.cn)(o({variant:a,size:n,className:t})),...c})}},38239:(e,t,a)=>{"use strict";a.d(t,{default:()=>E});var s=a(95155),r=a(30285),n=a(12115),l=a(35695),o=a(55077);let i=async e=>{try{return(await o.S.get("/uwhizStudentData/".concat(e))).data}catch(e){var t,a;return{success:!1,error:"Failed To Get Student Detail: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}};var c=a(86214);let d=async e=>{try{return(await o.S.post("/mock-exam-terminate",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var t,a;return{success:!1,error:"Failed to save termination log: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}},u=async e=>{try{return(await o.S.get("mock-exam-terminate/count?studentId=".concat(e),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var t,a;return{success:!1,error:"Failed To Get Count of termination: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}};var m=a(94631),x=a(14186),h=a(56671),g=a(66766),f=a(88927);let p=async(e,t,a)=>{try{return(await o.S.get("mock-exam/".concat(e,"/").concat(t),{params:{isWeekly:a.toString()},headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var s,r;throw Error("Failed To Get Question: ".concat((null===(r=e.response)||void 0===r?void 0:null===(s=r.data)||void 0===s?void 0:s.message)||e.message))}},w=e=>{let t=Date.now(),a=localStorage.getItem("examAttempts"),s=a?JSON.parse(a):{};s[e]=t,localStorage.setItem("examAttempts",JSON.stringify(s))},y=async e=>{try{let t=await o.S.post("/uwhizCoinTransction/add",e);return{success:!0,data:t.data}}catch(e){var t,a;return{success:!1,error:"Failed to log transction of coins in mock exam: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}},v=async e=>{try{let t=await o.S.post("/uwhizCoinTransction/update",e);return{success:!0,data:t.data}}catch(e){var t,a;return{success:!1,error:"Failed to update coins: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}};var b=a(76079);let k=()=>{let e=(0,l.useSearchParams)(),t=(0,l.useRouter)(),[a,s]=(0,n.useState)(null);return(0,n.useEffect)(()=>{(async()=>{let a=e.get("token");if(a)try{let e=(await o.S.get("/student/login-with-jwt",{params:{token:a},withCredentials:!0})).data.data;if(null==e?void 0:e.userId){let t={id:e.userId,contactNo:e.contactNo,firstName:e.firstName,lastName:e.lastName};localStorage.setItem("student_data",JSON.stringify(t)),localStorage.setItem("studentToken",a),localStorage.setItem("mobile_request","true"),s(e.userId)}let r=window.location.pathname;t.replace(r)}catch(e){console.error("JWT login failed:",e)}else{let e=localStorage.getItem("student_data"),t=e?JSON.parse(e):null;s((null==t?void 0:t.id)||null)}})()},[e,t]),a};var j=a(45320),S=a(78064);let N=n.memo(()=>(0,s.jsx)("header",{className:"fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(g.default,{height:60,width:60,src:f.A.src,alt:"Uwhiz Logo",quality:100,className:"object-contain sm:h-20 sm:w-20"}),(0,s.jsx)("h1",{className:"text-lg sm:text-2xl font-bold tracking-tight",children:"Uest Daily Quiz"})]})}));function E(){let e=(0,l.useRouter)(),t=(0,l.useSearchParams)(),o=k(),[f,E]=(0,n.useState)(!1),[A,F]=(0,n.useState)(!1),[z,D]=(0,n.useState)(!1),[C,T]=(0,n.useState)(!1),[O,I]=(0,n.useState)(!1),[q,P]=(0,n.useState)(!1),[Q,R]=(0,n.useState)(!1),[L,U]=(0,n.useState)([]),[W,_]=(0,n.useState)(0),[$,G]=(0,n.useState)(0),[J,K]=(0,n.useState)([]),[Y,B]=(0,n.useState)(!1),[M,H]=(0,n.useState)(null),[V,X]=(0,n.useState)(!1),Z=(0,n.useRef)(null),[ee,et]=(0,n.useState)(""),[ea,es]=(0,n.useState)(0),[er,en]=(0,n.useState)(!1),[el,eo]=(0,n.useState)(null),[ei,ec]=(0,n.useState)(!1),[ed,eu]=(0,n.useState)(null),em=(0,n.useRef)(null),[ex,eh]=(0,n.useState)(!1),[eg,ef]=(0,n.useState)(null);(0,n.useEffect)(()=>{E("true"===t.get("isWeekly"))},[t]);let ep=async()=>{if(!o)return 0;try{let e=await u(o);return"number"==typeof e?e:0}catch(e){return console.error("Failed to fetch violation count:",e),0}};(0,n.useEffect)(()=>{(async()=>{es(await ep())})()},[o]),(0,n.useEffect)(()=>{ea>=3&&(R(!0),et("Quiz terminated due to multiple cheating attempts."),o&&w(o))},[ea,o]),(0,n.useEffect)(()=>{o&&(async()=>{try{let e=await (0,c.S)(o,1,1,{isWeekly:f});if(e.success&&e.data.data.mockExamResults.length>0){let t=e.data.data.mockExamResults[0],a=new Date(t.createdAt).toISOString().split("T")[0],s=new Date().toISOString().split("T")[0];if(a!==s||f){if(f){let e=new Date;e.setDate(e.getDate()-(0===e.getDay()?6:e.getDay()-1)),e.setHours(0,0,0,0),new Date(t.createdAt)>=e&&I(!0)}}else I(!0)}}catch(e){h.toast.error("Failed to verify exam eligibility.",e)}})()},[o,f]),(0,n.useEffect)(()=>(Z.current=new Audio("/clock-ticking-sound-effect.mp3"),Z.current.loop=!0,()=>{Z.current&&(Z.current.pause(),Z.current=null)}),[]),(0,n.useEffect)(()=>{!(L.length>0)||!($<=5)||!($>0)||C||Q||Y||A||z||O||!Z.current?Z.current&&Z.current.pause():Z.current.play().catch(e=>{console.error("Failed to play tick sound:",e)})},[$,L,C,Q,Y,A,z,O]);let ew=(0,n.useCallback)(async()=>{let e=L[W];if(M){let t=M===e.correctAnswer;K(a=>[...a,{questionId:e.id,selectedAnswer:M,isCorrect:t}]),en(!0),setTimeout(()=>{en(!1),H(null),W<L.length-1?_(e=>e+1):B(!0)},1e3)}else K(t=>[...t,{questionId:e.id,selectedAnswer:"skipped",isCorrect:!1}]),h.toast.warning("Question skipped."),W<L.length-1?_(e=>e+1):B(!0)},[M,L,W]),ey=(0,n.useMemo)(()=>J.reduce((e,t)=>e+ +!!t.isCorrect,0),[J]),ev=(0,n.useMemo)(()=>{let e;let t=ey/L.length*100;return e=t>=100?5:t>=90?4:t>=80?3:t>=70?2:+(t>=60),f?5*e:e},[ey,L.length,f]);(0,n.useEffect)(()=>{Y&&o&&(async()=>{try{let e=Date.now(),t=f?await (0,b.If)(o):await (0,b.$m)(o);if(!t.success){h.toast.error(t.error),eu(t.error);return}ec(!0);let a=f?await (0,b.xT)(o):await (0,b.Gk)(o);ec(!1),a.success?(eo(a.data),h.toast.success("Streak updated successfully! Current streak: ".concat(a.data.streak," ").concat(f?"Weeks":"Days"))):(eu(a.error),h.toast.error(a.error));let s=ev+1,r=await (0,c.q)({studentId:o,score:ey,coinEarnings:s,isWeekly:f,duration:eg?e-eg:0});r.success?h.toast.success("Result saved successfully!"):h.toast.error(r.error);let n=await v({modelId:o,modelType:"STUDENT",coins:s});n.success?h.toast.success("Coins updated successfully! Added ".concat(s," coins (Score: ").concat(ev,", Streak: ").concat(1,").")):h.toast.error(n.error);let l={modelId:o,modelType:"STUDENT",amount:s,type:"CREDIT",reason:"".concat(f?"Weekly":"Daily"," Quiz Exam (Score + Streak: ").concat(1,")")},i=await y(l);i.success?h.toast.success("Transaction logged successfully!"):h.toast.error(i.error),await w(o),await ek()}catch(e){ec(!1),eu("Failed to fetch streak: ".concat(e.message)),h.toast.error("Failed to save result, update coins, or update streak: ".concat(e.message))}})()},[Y,o,ey,ev,f,eg]),(0,n.useEffect)(()=>{if(L.length>0&&$>0&&!C&&!Q&&!A&&!z&&!O){let e=setInterval(()=>{G(t=>{let a=t-1;return a<=0?(clearInterval(e),ew(),0):a})},1e3);return()=>clearInterval(e)}},[$,L,W,C,Q,A,z,O,ew]),(0,n.useEffect)(()=>{!(L.length>0)||C||Q||A||z||O||G(45)},[W,L,C,Q,A,z,O]),(0,n.useEffect)(()=>{C&&L.length>0&&ef(Date.now())},[C,L]),(0,n.useEffect)(()=>{Y&&a.e(5585).then(a.bind(a,5585)).then(e=>{(0,e.default)({particleCount:100,spread:80,startVelocity:30,ticks:200,origin:{x:.5,y:.2},colors:["#FF4500","#FFD700","#FF69B4","#00FF00","#1E90FF"],shapes:["square"],gravity:.3,scalar:1.2})})},[Y]);let eb=(0,n.useCallback)(async()=>{if(!o){F(!0);return}try{let e=await i(o);if(!e.success){h.toast.error(e.error),D(!0);return}if("APPROVED"!=e.data.status){D(!0),eh(!0),h.toast.error("Student Profile is not approved yet. Please check your profile.");return}let t=e.data.medium.toUpperCase(),a=await p(o,t,f);a&&Array.isArray(a)?(U(a),G(45),T(!0)):h.toast.error("No questions found or invalid response.")}catch(e){h.toast.error(e)}},[o,f]);(0,n.useEffect)(()=>{o&&!O&&eb()},[o,eb,O]);let ek=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;try{if(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement){if(document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen&&await document.mozCancelFullScreen(),await new Promise(e=>setTimeout(e,100)),!document.fullscreenElement&&!document.webkitFullscreenElement&&!document.mozFullScreenElement)return!0;if(t<e)return await ek(e,t+1);throw Error("Max attempts reached")}return!0}catch(a){if(console.error("Failed to exit full-screen mode (attempt ".concat(t,"):"),a),t<e)return await new Promise(e=>setTimeout(e,500)),await ek(e,t+1);return h.toast.error("Failed to exit full-screen mode. Please press Esc to exit manually."),!1}};(0,n.useEffect)(()=>{Y&&o&&Q&&(w(o),ek())},[Y,o]),(0,n.useEffect)(()=>{Q&&o&&ek().then(e=>{console.log("Quiz terminated and full-screen exited.",e)})},[Q,o]);let ej=()=>{let e=document.documentElement;e.requestFullscreen&&e.requestFullscreen().catch(e=>console.error("Failed to enter fullscreen:",e))},eS=(0,n.useCallback)(async e=>{if(C||A||z||q||V||O)return;let t=["F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"],a=e.ctrlKey&&e.shiftKey&&("I"===e.key||"J"===e.key||"C"===e.key)||e.metaKey&&e.altKey&&"I"===e.key||"F12"===e.key,s=(e.ctrlKey||e.metaKey)&&("c"===e.key||"C"===e.key);if(["Alt","Control","Tab","Shift","Enter"].includes(e.key)||t.includes(e.key)||a||s){if(e.preventDefault(),s){h.toast.warning("Copying is disabled during the quiz.");return}if(!o){es(0);return}X(!0);try{let s=a?"DevTools shortcut":t.includes(e.key)?'Function key "'.concat(e.key,'"'):'Restricted key "'.concat(e.key,'"');await d({studentId:o,reason:s});let r=await u(o);es(r),1===r?(P(!0),h.toast.warning("".concat(s," detected."))):2===r?(P(!0),h.toast.warning("".concat(s," detected. One more violation will terminate the quiz."))):r>=3&&(R(!0),et("Quiz terminated due to multiple cheating attempts."),o&&w(o),h.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{X(!1)}}},[o,C,A,z,q,V,O]),eN=(0,n.useCallback)(async()=>{if(!C&&!A&&!z&&!q&&!V&&!O&&document.hidden){X(!0);try{if(await d({studentId:o,reason:"Tab switch"}),!o){es(0);return}let e=await u(o);es(e),1===e?(P(!0),h.toast.warning("Tab switch detected.")):2===e?(P(!0),h.toast.warning("Again tab switch detected. One more violation will terminate the quiz.")):e>=3&&(R(!0),et("Quiz terminated due to multiple cheating attempts."),o&&w(o),h.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{X(!1)}}},[o,C,A,z,q,V,O]),eE=(0,n.useCallback)(async e=>{C||A||z||q||V||O||(e.preventDefault(),h.toast.warning("Right-click is disabled during the quiz."))},[o,C,A,z,q,V,O]),eA=(0,n.useCallback)(async()=>{if(!C&&!A&&!z&&!q&&!V&&!O){X(!0);try{await d({studentId:o,reason:"Window blur"});let e=await u(o);es(e),1===e?(P(!0),h.toast.warning("Window focus lost.")):2==e?(P(!0),h.toast.warning("Window focus lost again. One more violation will terminate the quiz.")):e>=3&&(R(!0),et("Quiz terminated due to multiple cheating attempts."),o&&w(o),h.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{X(!1)}}},[o,C,A,z,q,V,O]),eF=(0,n.useCallback)(async()=>{if(!C&&!A&&!z&&!q&&!V&&!O&&!document.fullscreenElement){X(!0);try{if(await d({studentId:o,reason:"Full-screen exit"}),!o){es(0);return}let e=await u(o);es(e),1===e?(P(!0),h.toast.warning("You have exited full-screen mode.")):2===e?(P(!0),h.toast.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.")):e>=3&&(R(!0),et("Quiz terminated due to multiple cheating attempts."),o&&w(o),h.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){h.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{X(!1)}}},[o,C,A,z,q,V,O]),ez=async()=>{R(!1),"true"===localStorage.getItem("mobile_request")&&(localStorage.removeItem("mobile_request"),window.location.href="UEST://DailyQuiz"),(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement)&&(await ek()||h.toast.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.")),e.push("/mock-exam-card"),setTimeout(()=>{e.push("/mock-exam-card")},1e3)};(0,n.useEffect)(()=>(C||A||z||Q||O||(document.addEventListener("visibilitychange",eN),document.addEventListener("keydown",eS),window.addEventListener("blur",eA),document.addEventListener("contextmenu",eE),document.addEventListener("fullscreenchange",eF)),()=>{document.removeEventListener("visibilitychange",eN),document.removeEventListener("keydown",eS),window.removeEventListener("blur",eA),document.removeEventListener("contextmenu",eE),document.removeEventListener("fullscreenchange",eF)}),[eN,eS,eA,eE,eF,C,A,z,Q,O]);let eD=(0,n.useCallback)(e=>{let t=Math.floor(e/60);return"".concat(t.toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0"))},[]),eC=(0,n.useMemo)(()=>L.length>0?(W+1)/L.length*100:0,[W,L]),eT=e=>{let t="w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white";return M===e?"".concat(t," bg-orange-100 border-orange-500"):t},eO=e=>{er||H(e)};if(A)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Login Required"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Please log in as a student to access the quiz."}),(0,s.jsx)(r.$,{onClick:()=>e.push("/student/login?redirect=/mock-test".concat(f?"?isWeekly=true":"")),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Login to Continue"})]})});if(z)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:ex?"Profile Not Approved":"Complete Your Profile"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:ex?"Your profile has not been approved yet. Please wait for approval and check notifications.":"Your profile is incomplete. Please complete your profile to proceed."}),(0,s.jsx)(r.$,{onClick:()=>{e.push("/student/profile")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:ex?"Update Profile":"Complete Profile"})]})});if(O)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-black",children:"Exam Already Taken"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:f?"You have already attempted the weekly exam this week. Please try again next Sunday.":"You have already attempted the daily exam today. Please try again tomorrow."}),(0,s.jsx)(r.$,{onClick:()=>{I(!1),e.push("/mock-exam-card")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Go to Home"})]})});if(0===L.length)return(0,s.jsxs)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:[(0,s.jsx)("p",{className:"text-base sm:text-xl font-medium mr-4",children:"Loading questions..."}),(0,s.jsx)(m.A,{className:"w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange"})]});let eI=async()=>{if(em.current)try{let{width:e}=em.current.getBoundingClientRect(),t=em.current.scrollHeight,a=await S.gO(em.current,{quality:1,pixelRatio:2,backgroundColor:"#ffffff",canvasWidth:e,canvasHeight:t,style:{margin:"0",padding:"35px 0px 0px 20px  "}}),s=document.createElement("a");s.href=a,s.download=f?"uest-weekly-quiz-result.jpg":"uest-daily-quiz-result.jpg",s.click()}catch(e){console.error("Failed to download card:",e),h.toast.error("Failed to download the quiz result card. Please try again.")}};if(Y){let e="I scored ".concat(ey,"/").concat(L.length," with a streak of ").concat((null==el?void 0:el.streak)||0," ").concat(f?"weeks":"days"," on U-whiz ").concat(f?"Weekly":"Daily"," Daily Exam! \uD83C\uDF89 Try it out!"),t=localStorage.getItem("student_data"),a=t?JSON.parse(t):null,r=a?"".concat(a.firstName," ").concat(a.lastName):"Unknown Student";return(0,s.jsx)("div",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200",children:(0,s.jsxs)("div",{className:"relative z-10 w-full max-w-md text-center",children:[(0,s.jsxs)("div",{ref:em,className:"bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-md",children:[(0,s.jsxs)("div",{className:"mb-6 text-center text-sm text-gray-600 dark:text-gray-400 flex flex-col items-center",children:[(0,s.jsx)("p",{children:"Powered by"}),(0,s.jsx)(g.default,{src:"/logo.png",alt:"Preply Logo",width:120,height:40})]}),(0,s.jsx)("div",{className:"absolute inset-0 z-0 pointer-events-none",children:(0,s.jsx)("div",{className:"animate-pulse opacity-20 bg-[radial-gradient(#facc15_1px,transparent_1px)] bg-[length:20px_20px] w-full h-full"})}),(0,s.jsxs)("h1",{className:"text-3xl font-extrabold text-customOrange dark:text-orange-400 mb-4 text-center",children:[f?"Weekly":"Daily"," Quiz Completed \uD83C\uDF89"]}),(0,s.jsxs)("p",{className:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 text-center",children:["Congratulations, ",(0,s.jsx)("span",{className:"text-customOrange font-bold",children:r})]}),(0,s.jsx)("p",{className:"text-base text-gray-600 dark:text-gray-400 mb-4 text-center",children:"Keep up the momentum. \uD83D\uDCAA"}),(0,s.jsx)("div",{className:"w-1/3 mx-auto h-2 bg-gray-200 dark:bg-gray-600 rounded-full mb-4",children:(0,s.jsx)("div",{className:"h-full bg-customOrange dark:bg-orange-400 rounded-full transition-all duration-500",style:{width:"".concat(L.length>0?ey/L.length*100:0,"%")}})}),(0,s.jsxs)("p",{className:"text-base text-gray-800 dark:text-gray-200 mb-4 text-center",children:["Final Score: ",(0,s.jsx)("span",{className:"font-bold",children:ey})," / ",L.length]}),(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,s.jsx)("div",{className:"absolute w-24 h-24 rounded-full bg-orange-500 opacity-30 blur-xl animate-ping"}),(0,s.jsx)("div",{className:"absolute w-16 h-16 rounded-full bg-red-500 opacity-20 blur-md animate-pulse"}),(0,s.jsx)("div",{className:"absolute w-12 h-12 rounded-full bg-yellow-300 opacity-40 blur-sm animate-bounce"}),(0,s.jsx)("div",{className:"relative w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center shadow-lg animate-burning",children:(0,s.jsx)("span",{className:"text-4xl",children:"\uD83D\uDD25"})}),(0,s.jsx)("span",{className:"mt-2 text-sm font-semibold text-gray-600 dark:text-gray-400 text-center",children:ei?"Loading Streak...":ed?"Error Loading Streak":"\uD83D\uDD25 Streak: ".concat((null==el?void 0:el.streak)||0," ").concat(f?"Weeks":"Days")})]})}),(0,s.jsxs)("div",{className:"text-sm text-gray-800 dark:text-gray-300 space-y-1 mb-6 text-center",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Coins Earned:"})," ",ev+((null==el?void 0:el.streak)||0)]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Bonus:"})," +",(null==el?void 0:el.streak)||0," for Streak!"]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col mt-6 bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-xl",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center justify-between gap-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-800 dark:text-gray-300 flex-1",children:"Ask your friends to join the quiz and get a chance to win some coins!"}),(0,s.jsx)("div",{className:"social-media-buttons flex items-center gap-4",children:(0,s.jsx)(j.Kz,{url:"https://uest.in/mock-exam-card",title:e,children:(0,s.jsx)(j.Y4,{size:32,round:!0})})})]}),(0,s.jsxs)("div",{className:"flex mt-4 gap-4",children:[(0,s.jsx)("button",{onClick:eI,className:"download-button bg-orange-500 text-white px-6 py-3 rounded-lg shadow-md hover:bg-orange-600 min-w-[150px] whitespace-nowrap",children:"\uD83D\uDCF7 Download Result"}),(0,s.jsx)("button",{onClick:ez,className:"bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold px-6 py-3 rounded-lg shadow-md min-w-[150px] whitespace-nowrap",children:"\uD83D\uDE80 Continue Learning"})]})]})]})})}let eq=L[W];return(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-100 text-gray-900",children:[C&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto",children:[(0,s.jsxs)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:["Start ",f?"Weekly":"Daily"," Quiz"]}),(0,s.jsx)("p",{className:"font-semibold mb-4 text-sm sm:text-base text-gray-600",children:"Note: This is a mock exam for testing purposes only."}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base",children:[(0,s.jsx)("p",{className:"font-semibold mb-2",children:"Instructions (English):"}),(0,s.jsxs)("ul",{className:"list-disc list-inside mb-4 text-gray-600",children:[(0,s.jsx)("li",{children:"Do not switch tabs during the quiz."}),(0,s.jsx)("li",{children:"Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys)."}),(0,s.jsx)("li",{children:"Do not open Developer Tools."}),(0,s.jsx)("li",{children:"Do not exit full-screen mode."}),(0,s.jsx)("li",{children:"Do not interact with other windows or applications."}),(0,s.jsx)("li",{children:"Do not change the screen or minimize the quiz window."}),(0,s.jsx)("li",{children:"Do not receive or make calls during the quiz."}),(0,s.jsx)("li",{children:"Do not use split screen or floating windows on your device."})]}),(0,s.jsx)("p",{className:"font-semibold mb-2",children:"સૂચનાઓ (ગુજરાતી):"}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,s.jsx)("li",{children:"ક્વિઝ દરમિયાન ટેબ બદલશો નહીં."}),(0,s.jsx)("li",{children:"પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં."}),(0,s.jsx)("li",{children:"ડેવલપર ટૂલ્સ ખોલશો નહીં."}),(0,s.jsx)("li",{children:"ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં."}),(0,s.jsx)("li",{children:"ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં."}),(0,s.jsx)("li",{children:"અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં."}),(0,s.jsx)("li",{children:"સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં."}),(0,s.jsx)("li",{children:"ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં."}),(0,s.jsx)("li",{children:"તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં."})]})]}),(0,s.jsxs)(r.$,{onClick:()=>{T(!1),ej(),L.length>0&&(G(45),ef(Date.now()))},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:["Start ",f?"Weekly":"Daily"," Quiz"]})]})}),q&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-customOrange",children:"Warning"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have performed a restricted action. Repeating this will terminate the quiz."}),(0,s.jsx)(r.$,{onClick:()=>P(!1),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"OK"})]})}),Q&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-red-500",children:"Quiz Terminated"}),(0,s.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:ee||"Your quiz has been terminated due to multiple cheating attempts."}),(0,s.jsx)(r.$,{onClick:ez,className:"bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all",children:"Go to Home"})]})}),!C&&!A&&!z&&!O&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(N,{}),(0,s.jsx)("div",{className:"fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200",children:(0,s.jsx)("div",{className:"h-3.5 bg-customOrange rounded-r-full transition-all duration-300",style:{width:"".concat(eC,"%")}})}),(0,s.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center w-full max-w-3xl",children:[(0,s.jsxs)("div",{className:"mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg",children:[(0,s.jsx)(x.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse"}),(0,s.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-customOrange",children:eD($)})]}),(0,s.jsxs)("div",{className:"w-full text-center flex flex-col items-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-3 sm:mb-4",children:(0,s.jsxs)("span",{className:"text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm",children:["Question ",W+1," of ",L.length]})}),(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6",children:eq.question}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full",children:[(0,s.jsxs)(r.$,{variant:"outline",className:eT("optionOne"),onClick:()=>eO("optionOne"),disabled:Q||er,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"A"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eq.optionOne})]}),(0,s.jsxs)(r.$,{variant:"outline",className:eT("optionTwo"),onClick:()=>eO("optionTwo"),disabled:Q||er,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"B"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eq.optionTwo})]}),(0,s.jsxs)(r.$,{variant:"outline",className:eT("optionThree"),onClick:()=>eO("optionThree"),disabled:Q||er,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"C"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eq.optionThree})]}),(0,s.jsxs)(r.$,{variant:"outline",className:eT("optionFour"),onClick:()=>eO("optionFour"),disabled:Q||er,children:[(0,s.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"D"}),(0,s.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eq.optionFour})]})]})]}),(0,s.jsx)(r.$,{className:"bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed",onClick:()=>ew(),disabled:Q||er,children:W===L.length-1?"Finish":"Next Question"})]}),(0,s.jsxs)("footer",{className:"fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm",children:[(0,s.jsx)("span",{children:"Powered by"}),(0,s.jsx)("span",{className:"font-semibold",children:"UEST EdTech"})]})]})})]})]})}N.displayName="QuizHeader"},54177:(e,t,a)=>{Promise.resolve().then(a.bind(a,38239))},55077:(e,t,a)=>{"use strict";a.d(t,{S:()=>l});var s=a(23464),r=a(56671);let n="http://localhost:4005/api/v1";console.log("Axios baseURL:",n);let l=s.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!0});l.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":n;{let t=localStorage.getItem("studentToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(r.toast.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,a)=>{"use strict";a.d(t,{MB:()=>o,ZO:()=>l,cn:()=>n,wR:()=>c,xh:()=>i});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}let l=()=>localStorage.getItem("studentToken"),o=()=>{localStorage.removeItem("studentToken")},i=()=>!!l(),c=()=>{if(l())return{isAuth:!0,userType:"STUDENT"};{let e=localStorage.getItem("user");if(e)try{let t=JSON.parse(e);if(t&&t.id)return{isAuth:!0,userType:"CLASS"}}catch(e){console.error("Error parsing user data:",e)}}return{isAuth:!1,userType:null}}},76079:(e,t,a)=>{"use strict";a.d(t,{$m:()=>r,Gk:()=>n,If:()=>l,xT:()=>o});var s=a(55077);let r=async e=>{try{let t=await s.S.put("/mock-exam-streak/".concat(e),{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}},n=async e=>{try{let t=await s.S.get("/mock-exam-streak/".concat(e),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to get mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}},l=async e=>{try{let t=await s.S.put("/mock-exam-weekly-streak/".concat(e),{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}},o=async e=>{try{let t=await s.S.get("/mock-exam-weekly-streak/".concat(e),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to get mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}}},86214:(e,t,a)=>{"use strict";a.d(t,{S:()=>n,q:()=>r});var s=a(55077);let r=async e=>{try{let t=await s.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam result: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}},n=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};try{let n=new URLSearchParams({page:t.toString(),limit:a.toString(),...void 0!==r.isWeekly&&{isWeekly:r.isWeekly.toString()}}).toString(),l=await s.S.get("/mock-exam-result/".concat(e,"?").concat(n),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:l.data}}catch(e){var n,l;return{success:!1,error:"Failed to get mock exam result: ".concat((null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message)||e.message)}}}},88927:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s={src:"/_next/static/media/uwhizExam.5364baa3.png",height:626,width:798,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAElBMVEURCwcCAgEcEQogIB49Pj0lJSX5PC0XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nCXKuREAMAgEsb2H/lv2GDIFQtg2YsGHBIfkwGRP2k7KAwYIAEmvy1CUAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6}}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4212,3242,8441,1684,7358],()=>t(54177)),_N_E=e.O()}]);