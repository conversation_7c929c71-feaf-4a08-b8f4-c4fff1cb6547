(()=>{var e={};e.id=1554,e.ids=[1554],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35817:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>i,Wz:()=>r,sA:()=>l});var a=s(50346);let r=(e,t)=>{e.contactNo&&t((0,a.ac)(a._3.PROFILE)),e.ClassAbout?.tutorBio?.length>50&&t((0,a.ac)(a._3.DESCRIPTION)),e.ClassAbout?.profilePhoto&&e.ClassAbout?.classesLogo&&t((0,a.ac)(a._3.PHOTO_LOGO)),e.education?.length>0&&t((0,a.ac)(a._3.EDUCATION)),e.certificates?.length>0&&t((0,a.ac)(a._3.CERTIFICATES)),e.experience?.length>0&&t((0,a.ac)(a._3.EXPERIENCE)),e.tuitionClasses?.length>0&&t((0,a.ac)(a._3.TUTIONCLASS)),e.address&&t((0,a.ac)(a._3.ADDRESS))},i=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch{return[e]}},l=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch{return e||"N/A"}};new TextEncoder().encode("secret123")},40841:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c={children:["",{children:["verified-classes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68062)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/verified-classes/page",pathname:"/verified-classes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},47033:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},66874:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>l,wL:()=>d});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},68062:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\verified-classes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},76242:(e,t,s)=>{"use strict";s.d(t,{Bc:()=>l,ZI:()=>c,k$:()=>o,m_:()=>n});var a=s(60687);s(43210);var r=s(99191),i=s(4780);function l({delayDuration:e=0,...t}){return(0,a.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function n({...e}){return(0,a.jsx)(l,{children:(0,a.jsx)(r.bL,{"data-slot":"tooltip",...e})})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:s,...l}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,i.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...l,children:[s,(0,a.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},79551:e=>{"use strict";e.exports=require("url")},80111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var a=s(60687),r=s(43210),i=s.n(r),l=s(16189),n=s(92449),o=s(66874),c=s(29523),d=s(80462),u=s(11860),m=s(64398),p=s(47033),x=s(14952),g=s(28527),h=s(90269),f=s(46303),v=s(52581),j=s(30474),b=s(85726);let N=({label:e,value:t,onChange:s})=>{let r=i().useRef(null);return(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-muted-foreground mb-1",children:e}),(0,a.jsx)("input",{ref:r,type:"text",placeholder:`Enter ${e}`,className:"border bg-white dark:bg-black rounded-lg px-3 py-2 text-sm text-black dark:text-white   focus:border-customOrange focus:ring focus:ring-customOrange/20 focus:outline-none   transition-all duration-200",value:t,onChange:e=>{let t=e.target.value.replace(/\s+/g," ").trimStart();e.target.value=t,s(e)},maxLength:50})]})};var y=s(35817),w=s(63503),C=s(76242),A=s(90471),k=s(69587);let P=()=>{let e=(0,l.useRouter)(),[t,s]=(0,r.useState)([]),[i,h]=(0,r.useState)(null),[f,P]=(0,r.useState)(!0),[E,S]=(0,r.useState)(1),[T,_]=(0,r.useState)(1),[O,q]=(0,r.useState)(!1),[R]=(0,r.useState)(!1),[$,F]=(0,r.useState)(!1),G=(0,l.useSearchParams)(),[L,D]=(0,r.useState)(!1),[I,z]=(0,r.useState)(null),[U,B]=(0,r.useState)(1e3),[M,V]=(0,r.useState)(null),[Z,H]=(0,r.useState)({education:G.get("education")||"",details:G.get("details")||"",boardType:G.get("boardType")||"",medium:G.get("medium")||"",section:G.get("section")||"",coachingType:G.get("coachingType")||"",subject:G.get("subject")||"",firstName:G.get("firstName")||"",lastName:G.get("lastName")||"",className:G.get("className")||"",sortByRating:!0,sortByReviewCount:!0}),W=e=>{if(!i)return[];let t=i.details.find(e=>"Education"===e.name);if(!t)return[];let s=t.subDetails.find(t=>t.name===e);return s?s.values.map(e=>({id:e.id,value:e.name})):[]},J=async()=>{try{let e=await g.S.get("/constant/TuitionClasses");h(e.data)}catch{v.toast.error("Failed to fetch tuition class categories")}},K=()=>{if(!navigator.geolocation){V("Geolocation is not supported by your browser.");return}navigator.geolocation.getCurrentPosition(e=>{z({lat:e.coords.latitude,lng:e.coords.longitude}),V(null)},()=>{V("Unable to retrieve your location.")})},X=async()=>{if(I){P(!0);try{let e=await g.S.get("/classes/nearby",{params:{lat:I.lat,lng:I.lng,radius:U}});s(e.data.data||[]),_(1)}catch{v.toast.error("Failed to fetch nearby classes")}finally{P(!1)}}},Y=async e=>{P(!0);try{let t=e||Z,a=await g.S.get("/classes/approved-tutors",{params:{page:E,limit:9,...t}}),r=a.data.data;s(r),_(a.data.totalPages)}catch{v.toast.error("Failed to fetch tutors")}finally{P(!1)}};(0,r.useEffect)(()=>{J()},[]),(0,r.useEffect)(()=>{L&&I?X():Y()},[E,L,I,U]);let Q={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},ee={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:100}}},et=({label:e,value:t,onChange:s,options:r})=>(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-muted-foreground mb-1",children:e}),(0,a.jsxs)("select",{className:"border rounded-lg px-3 py-2 text-sm   dark:bg-black   text-gray-900 dark:text-white",value:t,onChange:s,children:[(0,a.jsxs)("option",{value:"",className:"bg-white dark:bg-zinc-900",children:["All ",e]}),r.map(e=>(0,a.jsx)("option",{value:e.value,className:"bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100",children:e.value},e.id))]})]});return(0,r.useEffect)(()=>{L&&!I&&K()},[L,I]),(0,a.jsxs)(n.P.section,{initial:"hidden",animate:"visible",variants:Q,className:"container mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold",children:"Find Your Perfect Tutor"}),(0,a.jsx)("p",{className:"mt-4 text-lg font-medium bg-gradient-to-r from-gray-700 to-gray-500 dark:from-gray-300 dark:to-gray-400 bg-clip-text text-transparent",children:"Discover experienced tutors who can help you achieve your learning goals"})]}),(0,a.jsxs)(n.P.div,{className:"mb-8 bg-white/30 dark:bg-black/30 backdrop-blur-lg rounded-xl p-6",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-customOrange"}),(0,a.jsx)("h3",{className:"text-xl font-semibold",children:"Filters"})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsx)(c.$,{variant:"outline",className:"hover:bg-customOrange/10",onClick:()=>q(!O),children:O?(0,a.jsx)(u.A,{className:"w-4 h-4"}):(0,a.jsx)(d.A,{className:"w-4 h-4"})})})]}),(0,a.jsxs)("div",{className:`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 transition-all duration-300 ${O?"block":"hidden"}`,children:[(0,a.jsx)(et,{label:"Category",value:Z.education,onChange:e=>{H(t=>({...t,education:e.target.value,details:"",boardType:"Education"!==e.target.value?"":t.boardType,medium:"Education"!==e.target.value?"":t.medium,section:"Education"!==e.target.value?"":t.section,subject:"Education"!==e.target.value?"":t.subject}))},options:i?i.details.map(e=>({id:e.id,value:e.name})):[]}),"Education"===Z.education&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(et,{label:"Board Type",value:Z.boardType,onChange:e=>H(t=>({...t,boardType:e.target.value})),options:W("Board Type")}),(0,a.jsx)(et,{label:"Medium",value:Z.medium,onChange:e=>H(t=>({...t,medium:e.target.value})),options:W("Medium")}),(0,a.jsx)(et,{label:"Section",value:Z.section,onChange:e=>H(t=>({...t,section:e.target.value})),options:W("Section")}),(0,a.jsx)(et,{label:"Subject",value:Z.subject,onChange:e=>H(t=>({...t,subject:e.target.value})),options:W("Subject")})]}),Z.education&&"Education"!==Z.education&&(0,a.jsx)(et,{label:"Details",value:Z.details,onChange:e=>H(t=>({...t,details:e.target.value})),options:(e=>{if(!i)return[];let t=i.details.find(t=>t.name===e);return t?"Education"===e?[]:t.subDetails.map(e=>({id:e.id,value:e.name})):[]})(Z.education)}),(0,a.jsx)(et,{label:"Coaching Type",value:Z.coachingType,onChange:e=>H(t=>({...t,coachingType:e.target.value})),options:[{id:"personal",value:"Personal"},{id:"group",value:"Group"},{id:"online",value:"Online"},{id:"hybrid",value:"Hybrid"}]}),(0,a.jsx)(N,{label:"First Name",value:Z.firstName,onChange:e=>H(t=>({...t,firstName:e.target.value}))}),(0,a.jsx)(N,{label:"Class Name",value:Z.className,onChange:e=>H(t=>({...t,className:e.target.value}))})]}),(0,a.jsxs)("div",{className:`flex gap-4 mt-4 ${O?"block":"hidden"}`,children:[(0,a.jsx)(c.$,{className:"w-[200px] bg-customOrange hover:bg-customOrange/90",onClick:()=>{S(1),Y()},children:"Apply Filters"}),(0,a.jsx)(c.$,{variant:"default",className:"w-[200px]",onClick:()=>{let e={education:"",details:"",boardType:"",medium:"",section:"",coachingType:"",subject:"",firstName:"",lastName:"",className:"",sortByRating:!0,sortByReviewCount:!0};H(e),S(1),Y(e)},children:"Reset Filters"})]})]}),(0,a.jsxs)("div",{className:"flex flex-nowrap items-center gap-6 w-full mb-5",children:[(0,a.jsxs)("label",{className:"flex items-center gap-2 mb-0 whitespace-nowrap flex-shrink-0",children:[(0,a.jsx)("input",{type:"checkbox",checked:L,onChange:()=>D(e=>!e),className:"accent-customOrange"}),(0,a.jsx)("span",{children:"Show Nearby Classes"})]}),L&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,a.jsxs)("select",{className:"border rounded px-2 py-1 min-w-[90px]",value:U,onChange:e=>B(Number(e.target.value)),children:[(0,a.jsx)("option",{value:100,children:"100m"}),(0,a.jsx)("option",{value:500,children:"500m"}),(0,a.jsx)("option",{value:1e3,children:"1km"}),(0,a.jsx)("option",{value:5e3,children:"5km"}),(0,a.jsx)("option",{value:1e4,children:"10km"}),(0,a.jsx)("option",{value:1e4,children:"60km"})]}),(0,a.jsx)("span",{className:"whitespace-nowrap",children:U>=1e3?`${U/1e3}km Radius`:`${U}m Radius`})]}),M&&(0,a.jsx)("span",{className:"text-red-500 ml-2 flex-shrink-0",children:M})]})]}),f?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,a.jsx)(b.E,{className:"h-96 w-full rounded-xl"},t))}):0===t.length?(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-10",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No tutors found. Try adjusting your filters."})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.P.div,{variants:Q,initial:"hidden",animate:"visible",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map((t,s)=>(0,a.jsx)(n.P.div,{variants:ee,whileHover:{y:-5},className:"h-full",children:(0,a.jsxs)(o.Zp,{className:"h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center gap-4",children:[(0,a.jsx)(n.P.div,{className:"relative w-30 h-30 rounded-full overflow-hidden ring-2 ring-customOrange/20",whileHover:{scale:1.05},children:(0,a.jsx)(j.default,{src:t.ClassAbout&&t.ClassAbout.classesLogo?`http://localhost:4005/${t.ClassAbout.classesLogo}`:"/default-profile.jpg",alt:t.firstName,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold hover:text-customOrange transition-colors",children:[t.firstName," ",t.lastName]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:t.className})]}),(0,a.jsxs)(C.m_,{children:[(0,a.jsx)(C.k$,{asChild:!0,children:(0,a.jsx)("div",{className:"relative group cursor-pointer",children:(0,a.jsx)(A.VqV,{className:"text-green-500"})})}),(0,a.jsx)(C.ZI,{className:"text-xs",children:"Verified by Uest"})]})]}),(0,a.jsxs)(o.Wu,{className:"flex-1 space-y-4",children:[(0,a.jsxs)(C.m_,{children:[(0,a.jsx)(C.k$,{asChild:!0,children:t.education?.filter(e=>e.isDegree&&"APPROVED"===e.status).length>0&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground mt-1 flex items-center gap-1 flex-wrap",children:[(0,a.jsx)(k.YNd,{className:"text-customOrange text-lg"}),(0,a.jsxs)("span",{className:"text-md",children:["Degrees:"," ",t.education.filter(e=>e.isDegree&&"APPROVED"===e.status).map(e=>e.degree).join(", ")]})]})}),(0,a.jsx)(C.ZI,{className:"text-xs",children:"Verified by Uest"})]}),(0,a.jsx)("p",{className:"line-clamp-2 text-sm text-muted-foreground",children:t.ClassAbout&&t.ClassAbout.tutorBio||"No bio available."}),Array.isArray(t.tuitionClasses)&&t.tuitionClasses.length>0&&(0,a.jsx)("div",{className:"space-y-2 p-4 rounded-lg bg-black/5 dark:bg-white/5",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Category"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,y.sA)(t.tuitionClasses[0].education)||"N/A"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Coaching Type"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,y.sA)(t.tuitionClasses[0].coachingType)||"N/A"})]}),"Education"===t.tuitionClasses[0].education&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Board"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,y.sA)(t.tuitionClasses[0].boardType)||"N/A"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Medium"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,y.sA)(t.tuitionClasses[0].medium)||"N/A"})]})]})]})}),void 0!==t.distance&&(0,a.jsxs)("div",{className:"text-sm text-customOrange font-semibold",children:["Distance: ",(t.distance/1e3).toFixed(2)," km"]})]}),(0,a.jsxs)(o.wL,{className:"flex flex-col items-start gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 pt-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,a.jsx)("span",{className:"font-semibold text-foreground",children:t.averageRating?t.averageRating.toFixed(1):"0"}),(0,a.jsxs)("span",{children:["(",t.reviewCount||0," reviews)"]})]}),(0,a.jsxs)("div",{className:"flex gap-2 w-full",children:[(0,a.jsx)(c.$,{className:"flex-1 bg-customOrange hover:bg-[#E88143]",onClick:()=>e.push(`/classes-details/${t.id}`),children:"View Profile"}),(0,a.jsx)(c.$,{variant:"outline",className:"flex-1 hover:bg-orange-50",onClick:()=>{if(!R){F(!0);return}let s=`${t.firstName} ${t.lastName}`;e.push(`/student/chat?userId=${t.id}&userName=${encodeURIComponent(s)}`)},children:"Message"})]})]})]})},s))}),(0,a.jsxs)("div",{className:"flex justify-center items-center mt-8 gap-4",children:[(0,a.jsxs)(c.$,{variant:"outline",disabled:1===E,onClick:()=>S(E-1),className:"flex gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"})," Previous"]}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",E," of ",T]}),(0,a.jsxs)(c.$,{variant:"outline",disabled:E===T,onClick:()=>S(E+1),className:"flex gap-2",children:["Next ",(0,a.jsx)(x.A,{className:"h-4 w-4"})]})]}),(0,a.jsx)(w.lG,{open:$,onOpenChange:F,children:(0,a.jsxs)(w.Cf,{className:"sm:max-w-md",children:[(0,a.jsx)(w.c7,{children:(0,a.jsx)(w.L3,{className:"text-center",children:"Login Required"})}),(0,a.jsx)("div",{className:"space-y-4 py-4",children:(0,a.jsx)("p",{className:"text-center text-muted-foreground",children:"Please login as a student to add this class to send a message."})})]})})]})]})},E=()=>(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(h.default,{}),(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,a.jsx)(b.E,{className:"h-96 w-full rounded-xl"},t))})}),children:(0,a.jsx)(P,{})}),(0,a.jsx)(f.default,{})]})},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(60687),r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,r.cn)("bg-accent animate-pulse rounded-md",e),...t})}},94735:e=>{"use strict";e.exports=require("events")},94772:(e,t,s)=>{Promise.resolve().then(s.bind(s,68062))},96980:(e,t,s)=>{Promise.resolve().then(s.bind(s,80111))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,7013,9191,2449,471,2800],()=>s(40841));module.exports=a})();