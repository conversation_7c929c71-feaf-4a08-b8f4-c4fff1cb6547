(()=>{var e={};e.id=6976,e.ids=[6976],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39746:(e,t,r)=>{Promise.resolve().then(r.bind(r,58248))},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},49801:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["student-verify-otp",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58248)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/student-verify-otp/page",pathname:"/student-verify-otp",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57898:(e,t,r)=>{Promise.resolve().then(r.bind(r,82138))},58248:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\student-verify-otp\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx","default")},61170:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var s=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var n=r(60687),i=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var i;let e,l;let o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}(t,o):o),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...i}=e,o=s.Children.toArray(a),d=o.find(l);if(d){let e=d.props.children,a=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...i,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),d=s.forwardRef((e,t)=>(0,n.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80942:(e,t,r)=>{"use strict";r.d(t,{lV:()=>c,MJ:()=>v,Rr:()=>h,zB:()=>p,eI:()=>x,lR:()=>g,C5:()=>b});var s=r(60687),a=r(43210),n=r(11329),i=r(27605),l=r(4780),o=r(61170);function d({className:e,...t}){return(0,s.jsx)(o.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}let c=i.Op,u=a.createContext({}),p=({...e})=>(0,s.jsx)(u.Provider,{value:{name:e.name},children:(0,s.jsx)(i.xI,{...e})}),m=()=>{let e=a.useContext(u),t=a.useContext(f),{getFieldState:r}=(0,i.xW)(),s=(0,i.lN)({name:e.name}),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},f=a.createContext({});function x({className:e,...t}){let r=a.useId();return(0,s.jsx)(f.Provider,{value:{id:r},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...t})})}function g({className:e,...t}){let{error:r,formItemId:a}=m();return(0,s.jsx)(d,{"data-slot":"form-label","data-error":!!r,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...t})}function v({...e}){let{error:t,formItemId:r,formDescriptionId:a,formMessageId:i}=m();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${a} ${i}`:`${a}`,"aria-invalid":!!t,...e})}function h({className:e,...t}){let{formDescriptionId:r}=m();return(0,s.jsx)("p",{"data-slot":"form-description",id:r,className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}function b({className:e,...t}){let{error:r,formMessageId:a}=m(),n=r?String(r?.message??""):t.children;return n?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,l.cn)("text-destructive text-sm",e),...t,children:n}):null}},81630:e=>{"use strict";e.exports=require("http")},82138:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var s=r(60687),a=r(90269),n=r(46303),i=r(43210),l=r(29523),o=r(45880),d=r(27605),c=r(63442),u=r(89667),p=r(91821),m=r(93613),f=r(41862),x=r(80942),g=r(52581),v=r(41831),h=r(16189);let b=o.z.object({otp:o.z.string().regex(/^\d{6}$/,"OTP must be a 6-digit number"),contactNo:o.z.string().regex(/^\d{10}$/,"Invalid mobile number"),email:o.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address"),firstName:o.z.string().optional().refine(e=>!e||/^[a-zA-Z]+$/.test(e),"Invalid first name"),lastName:o.z.string().optional().refine(e=>!e||/^[a-zA-Z]+$/.test(e),"Invalid last name"),referralCode:o.z.string().optional()}),y=({message:e})=>e?(0,s.jsxs)(p.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 text-red-500"}),(0,s.jsx)(p.TN,{className:"text-red-500",children:e})]}):null;function j(){let e=(0,h.useRouter)(),t=(0,h.useSearchParams)(),[r,o]=(0,i.useState)(!1),[p,m]=(0,i.useState)(""),[j,N]=(0,i.useState)(!1),[w,P]=(0,i.useState)(120),T=t.get("contactNo")||"",_=t.get("email")||"",C=t.get("flow")||"login",I=t.get("firstName")||"",S=t.get("lastName")||"",k=t.get("referralCode")||"",q="register"===C,$=`otpTimer_${T}_${C}`,R=(0,d.mN)({resolver:(0,c.u)(b),defaultValues:{otp:"",contactNo:T,email:_,firstName:I,lastName:S,referralCode:k},mode:"onChange"}),{formState:E,trigger:O,setValue:z}=R,A=async r=>{o(!0),m("");try{let s=await (0,v.RY)({contactNo:r.contactNo,otp:r.otp,...r.email&&{email:r.email},...q&&{firstName:r.firstName,lastName:r.lastName,referralCode:r.referralCode}});if(!1===s.success){m(s.message||"OTP verification failed"),g.toast.error(s.message||"OTP verification failed");return}if(s.data){let{userId:a,contactNo:n,firstName:i,lastName:l,token:o}=s.data;localStorage.setItem("studentToken",o),localStorage.setItem("student_data",JSON.stringify({id:a,contactNo:n,firstName:i,lastName:l})),localStorage.removeItem($),g.toast.success(r.email&&!q?"Contact number updated and login successful":q?"Registration successful":"Login successful");let d=t.get("redirect")||"/";e.push(d)}}catch(t){let e=t?.response?.data?.message||"Something went wrong";m(e),g.toast.error(e)}finally{o(!1)}},M=async()=>{N(!0),m("");try{let e=await (0,v.Ty)({contactNo:T,firstName:I||"User"});if(!1===e.success){m(e.message||"Failed to resend OTP"),g.toast.error(e.message||"Failed to resend OTP");return}P(120),localStorage.setItem($,"120"),g.toast.success("New OTP sent successfully")}catch(t){let e=t?.response?.data?.message||"Something went wrong";e.includes("Too many OTP requests")?g.toast.error("Too many OTP requests. Please wait before trying again."):g.toast.error(e),m(e)}finally{N(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{}),(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Verify OTP"}),(0,s.jsxs)("p",{className:"text-[#ff914d]",children:["Enter the 6-digit OTP sent to ",T]}),_&&(0,s.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["Linked to email: ",_]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(x.lV,{...R,children:(0,s.jsxs)("form",{onSubmit:R.handleSubmit(A),className:"space-y-6",children:[p&&(0,s.jsx)(y,{message:p}),(0,s.jsx)(x.zB,{control:R.control,name:"otp",render:({field:e})=>(0,s.jsxs)(x.eI,{children:[(0,s.jsx)(x.lR,{className:"text-gray-700 font-medium",children:"OTP"}),(0,s.jsx)(x.MJ,{children:(0,s.jsx)("div",{className:"flex space-x-2 justify-center",children:[...Array(6)].map((t,r)=>(0,s.jsx)(u.p,{type:"text",maxLength:1,className:"w-12 h-12 text-center text-lg font-medium border-gray-200 focus:border-[#ff914d] focus:ring-[#ff914d]/20 rounded-lg",value:e.value[r]||"",onChange:t=>{let s=(e.value||"").split("");s[r]=t.target.value.replace(/\D/g,""),e.onChange(s.join("")),O("otp"),t.target.value&&r<5&&document.getElementById(`otp-${r+1}`)?.focus()},onKeyDown:t=>{"Backspace"===t.key&&!e.value[r]&&r>0&&document.getElementById(`otp-${r-1}`)?.focus()},id:`otp-${r}`},r))})}),(0,s.jsx)(x.C5,{className:"text-red-500"})]})}),(0,s.jsx)(x.zB,{control:R.control,name:"contactNo",render:({field:e})=>(0,s.jsx)(x.eI,{hidden:!0,children:(0,s.jsx)(x.MJ,{children:(0,s.jsx)(u.p,{...e})})})}),(0,s.jsx)(x.zB,{control:R.control,name:"email",render:({field:e})=>(0,s.jsx)(x.eI,{hidden:!0,children:(0,s.jsx)(x.MJ,{children:(0,s.jsx)(u.p,{...e,value:e.value||""})})})}),(0,s.jsx)(x.zB,{control:R.control,name:"firstName",render:({field:e})=>(0,s.jsx)(x.eI,{hidden:!0,children:(0,s.jsx)(x.MJ,{children:(0,s.jsx)(u.p,{...e,value:e.value||""})})})}),(0,s.jsx)(x.zB,{control:R.control,name:"lastName",render:({field:e})=>(0,s.jsx)(x.eI,{hidden:!0,children:(0,s.jsx)(x.MJ,{children:(0,s.jsx)(u.p,{...e,value:e.value||""})})})}),(0,s.jsx)(x.zB,{control:R.control,name:"referralCode",render:({field:e})=>(0,s.jsx)(x.eI,{hidden:!0,children:(0,s.jsx)(x.MJ,{children:(0,s.jsx)(u.p,{...e,value:e.value||""})})})}),(0,s.jsx)(l.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:r||!E.isValid,children:r?(0,s.jsx)(f.A,{className:"h-5 w-5 animate-spin"}):"Verify OTP"}),(0,s.jsx)(l.$,{type:"button",variant:"ghost",className:"w-full text-[#ff914d] hover:text-[#ff914d]/90",disabled:j||w>0,onClick:M,children:j?(0,s.jsx)(f.A,{className:"h-5 w-5 animate-spin"}):w>0?`Resend OTP in ${w}s`:"Resend OTP"})]})})})]})}),(0,s.jsx)(n.default,{})]})}function N(){return(0,s.jsx)(i.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})}),children:(0,s.jsx)(j,{})})}},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,TN:()=>d});var s=r(60687),a=r(43210),n=r(24224),i=r(4780);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:t,...r},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,i.cn)(l({variant:t}),e),...r}));o.displayName="Alert",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7013,2105,2800],()=>r(49801));module.exports=s})();