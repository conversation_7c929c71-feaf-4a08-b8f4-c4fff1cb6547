"use client";

import React, { Suspense, useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Target,
  Clock,
  Rocket,
  Music,
  Brush,
  Film,
  Dumbbell,
  Languages,
  Laptop,
  School,
  Star,
  ArrowRight,
  Sparkles,
  Monitor,
  CookingPot,
  PartyPopper,
  Sigma,
  Activity,
  StretchHorizontal,
  Plane,
  PenTool,
  ArrowLeft,
} from "lucide-react";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { isAuthenticated } from "@/lib/utils";
import { getAllClasses } from "@/services/classesApi";

interface Category {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
}

interface ClassData {
  id: string;
  firstName: string;
  lastName: string;
  className: string;
  ClassAbout?: {
    profilePhoto?: string;
    catchyHeadline?: string;
    tutorBio?: string;
    classesLogo?: string;
  };
  tuitionClasses?: Array<{
    subject?: string[];
    details?: string[];
  }>;
  averageRating?: number;
  reviewCount?: number;
  coins?: number;
  status?: string;
}

const categories: Category[] = [
  { id: "1", name: "Academic", icon: School, color: "text-blue-600", bgColor: "bg-blue-50" },
  { id: "2", name: "Music", icon: Music, color: "text-purple-600", bgColor: "bg-purple-50" },
  { id: "3", name: "Art", icon: Brush, color: "text-pink-600", bgColor: "bg-pink-50" },
  { id: "4", name: "Sports", icon: Dumbbell, color: "text-green-600", bgColor: "bg-green-50" },
  { id: "5", name: "Language", icon: Languages, color: "text-orange-600", bgColor: "bg-orange-50" },
  { id: "6", name: "Technology", icon: Laptop, color: "text-indigo-600", bgColor: "bg-indigo-50" },
  { id: "7", name: "Film", icon: Film, color: "text-red-600", bgColor: "bg-red-50" },
  { id: "8", name: "Cooking", icon: CookingPot, color: "text-yellow-600", bgColor: "bg-yellow-50" },
];

const HomeInnerPage = () => {
  const router = useRouter();
  const [classes, setClasses] = useState<ClassData[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>("1");

  const heroSlides = [
    {
      title: "Your Gateway to Educational Excellence",
      subtitle: "Discover personalized learning experiences with expert tutors",
      image: "/hero-1.jpg",
      cta: "Start Learning",
      link: "/verified-classes"
    },
    {
      title: "Master Skills with Expert Guidance",
      subtitle: "Join thousands of students achieving their academic goals",
      image: "/hero-2.jpg", 
      cta: "Find Tutors",
      link: "/verified-classes"
    },
    {
      title: "Unlock Your Potential Today",
      subtitle: "Comprehensive courses designed for your success",
      image: "/hero-3.jpg",
      cta: "Explore Courses",
      link: "/verified-classes"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [heroSlides.length]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await getAllClasses(1, 8, "", "approved");
      if (response.success && response.data) {
        setClasses(response.data.classes || []);
      }
    } catch (error) {
      console.error("Error fetching classes:", error);
      toast.error("Failed to load classes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleCategoryClick = (categoryId: string) => {
    setSelectedCategory(categoryId);
    router.push(`/verified-classes?category=${categoryId}`);
  };

  const handleGetStarted = () => {
    const authStatus = isAuthenticated();
    if (authStatus.isAuth) {
      if (authStatus.userType === 'student') {
        router.push('/student/profile');
      } else if (authStatus.userType === 'class') {
        router.push('/classes/dashboard');
      }
    } else {
      router.push('/student/login');
    }
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <Header />
      
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90 z-10"></div>
        
        {/* Background Slider */}
        <div className="absolute inset-0">
          {heroSlides.map((slide, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentSlide ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <Image
                src={slide.image}
                alt={slide.title}
                fill
                className="object-cover"
                priority={index === 0}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/logo.png";
                }}
              />
            </div>
          ))}
        </div>

        {/* Content */}
        <div className="relative z-20 text-center text-white px-4 max-w-4xl mx-auto">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              {heroSlides[currentSlide].title}
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200">
              {heroSlides[currentSlide].subtitle}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 text-lg"
                onClick={handleGetStarted}
              >
                <Rocket className="mr-2 h-5 w-5" />
                Get Started
              </Button>
              <Link href={heroSlides[currentSlide].link}>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4 text-lg"
                >
                  {heroSlides[currentSlide].cta}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>

        {/* Navigation Arrows */}
        <button
          onClick={prevSlide}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-30 bg-white/20 hover:bg-white/30 rounded-full p-3 transition-all"
        >
          <ArrowLeft className="h-6 w-6 text-white" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-30 bg-white/20 hover:bg-white/30 rounded-full p-3 transition-all"
        >
          <ArrowRight className="h-6 w-6 text-white" />
        </button>

        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-2">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all ${
                index === currentSlide ? 'bg-white' : 'bg-white/50'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Explore Learning Categories
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Discover a wide range of subjects and skills with our expert tutors
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {categories.map((category) => {
              const IconComponent = category.icon;
              return (
                <motion.div
                  key={category.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="cursor-pointer"
                  onClick={() => handleCategoryClick(category.id)}
                >
                  <Card className="h-32 hover:shadow-lg transition-all duration-300 border-2 hover:border-blue-200">
                    <CardContent className="flex flex-col items-center justify-center h-full p-4">
                      <div className={`${category.bgColor} p-3 rounded-full mb-3`}>
                        <IconComponent className={`h-8 w-8 ${category.color}`} />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-center">
                        {category.name}
                      </h3>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Classes Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Meet Our Top Tutors
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Connect with our top verified tutors and start your learning journey today
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="h-96 w-full rounded-xl bg-gray-200 dark:bg-gray-700 animate-pulse"
                />
              ))}
            </div>
          ) : classes.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-gray-600 dark:text-gray-300">
                No tutors found at the moment.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {classes.slice(0, 4).map((tutor, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.1 }}
                  whileHover={{ y: -5 }}
                  className="h-full"
                >
                  <Card className="h-full bg-white dark:bg-gray-800 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="relative w-16 h-16 rounded-full overflow-hidden">
                          <Image
                            src={
                              tutor.ClassAbout?.classesLogo
                                ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${tutor.ClassAbout.classesLogo}`
                                : "/default-profile.jpg"
                            }
                            alt={tutor.firstName}
                            fill
                            className="object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "/default-profile.jpg";
                            }}
                          />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {tutor.firstName} {tutor.lastName}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            {tutor.ClassAbout?.catchyHeadline || "Expert Tutor"}
                          </p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-300">Rating</span>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm font-medium">
                              {tutor.averageRating?.toFixed(1) || "5.0"}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-300">Reviews</span>
                          <span className="text-sm font-medium">
                            {tutor.reviewCount || 0}
                          </span>
                        </div>

                        {tutor.coins && (
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600 dark:text-gray-300">Coins</span>
                            <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                              {tutor.coins}
                            </Badge>
                          </div>
                        )}
                      </div>

                      <Link href={`/classes-details/${tutor.id}`}>
                        <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700">
                          View Profile
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link href="/verified-classes">
              <Button size="lg" variant="outline" className="px-8">
                View All Tutors
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Why Choose UEST?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Experience the future of online learning with our comprehensive platform
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Target,
                title: "Personalized Learning",
                description: "Tailored learning experiences designed for your unique goals and pace"
              },
              {
                icon: Clock,
                title: "Flexible Scheduling",
                description: "Learn at your convenience with flexible timing and 24/7 access"
              },
              {
                icon: Star,
                title: "Expert Tutors",
                description: "Learn from verified experts with proven track records of success"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
                className="text-center"
              >
                <div className="bg-blue-100 dark:bg-blue-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Start Your Learning Journey?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of students who have transformed their academic success with UEST
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4"
                onClick={handleGetStarted}
              >
                <Sparkles className="mr-2 h-5 w-5" />
                Get Started Today
              </Button>
              <Link href="/verified-classes">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4"
                >
                  Browse Tutors
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default HomeInnerPage;
