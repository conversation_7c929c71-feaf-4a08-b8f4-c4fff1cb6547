(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5344],{14050:(e,t,a)=>{"use strict";a.d(t,{b:()=>c});var s=a(12115);a(47650);var n=a(66634),r=a(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,n.TL)(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(n?a:t,{...i,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),o="horizontal",l=["horizontal","vertical"],d=s.forwardRef((e,t)=>{var a;let{decorative:s,orientation:n=o,...d}=e,c=(a=n,l.includes(a))?n:o;return(0,r.jsx)(i.div,{"data-orientation":c,...s?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:t})});d.displayName="Separator";var c=d},20185:(e,t,a)=>{"use strict";a.d(t,{Ow:()=>r,Wz:()=>n,sA:()=>i});var s=a(94314);let n=(e,t)=>{var a,n,r,i,o,l,d,c;e.contactNo&&t((0,s.ac)(s._3.PROFILE)),(null===(n=e.ClassAbout)||void 0===n?void 0:null===(a=n.tutorBio)||void 0===a?void 0:a.length)>50&&t((0,s.ac)(s._3.DESCRIPTION)),(null===(r=e.ClassAbout)||void 0===r?void 0:r.profilePhoto)&&(null===(i=e.ClassAbout)||void 0===i?void 0:i.classesLogo)&&t((0,s.ac)(s._3.PHOTO_LOGO)),(null===(o=e.education)||void 0===o?void 0:o.length)>0&&t((0,s.ac)(s._3.EDUCATION)),(null===(l=e.certificates)||void 0===l?void 0:l.length)>0&&t((0,s.ac)(s._3.CERTIFICATES)),(null===(d=e.experience)||void 0===d?void 0:d.length)>0&&t((0,s.ac)(s._3.EXPERIENCE)),(null===(c=e.tuitionClasses)||void 0===c?void 0:c.length)>0&&t((0,s.ac)(s._3.TUTIONCLASS)),e.address&&t((0,s.ac)(s._3.ADDRESS))},r=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch(t){return[e]}},i=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch(t){return e||"N/A"}};new TextEncoder().encode("secret123")},22346:(e,t,a)=>{"use strict";a.d(t,{Separator:()=>i});var s=a(95155);a(12115);var n=a(14050),r=a(59434);function i(e){let{className:t,orientation:a="horizontal",decorative:i=!0,...o}=e;return(0,s.jsx)(n.b,{"data-slot":"separator-root",decorative:i,orientation:a,className:(0,r.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},24265:(e,t,a)=>{"use strict";a.d(t,{b:()=>c});var s=a(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}a(47650);var r=a(95155),i=Symbol("radix.slottable");function o(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let a=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:a,...r}=e;if(s.isValidElement(a)){var i;let e,o;let l=(i=a,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let a={...t};for(let s in t){let n=e[s],r=t[s];/^on[A-Z]/.test(s)?n&&r?a[s]=(...e)=>{r(...e),n(...e)}:n&&(a[s]=n):"style"===s?a[s]={...n,...r}:"className"===s&&(a[s]=[n,r].filter(Boolean).join(" "))}return{...e,...a}}(r,a.props);return a.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let a=!1,s=e.map(e=>{let s=n(e,t);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let t=0;t<s.length;t++){let a=s[t];"function"==typeof a?a():n(e[t],null)}}}}(t,l):l),s.cloneElement(a,d)}return s.Children.count(a)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=s.forwardRef((e,a)=>{let{children:n,...i}=e,l=s.Children.toArray(n),d=l.find(o);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,r.jsx)(t,{...i,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,r.jsx)(t,{...i,ref:a,children:n})});return a.displayName=`${e}.Slot`,a}(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(n?a:t,{...i,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),d=s.forwardRef((e,t)=>(0,r.jsx)(l.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>o});var s=a(95155);a(12115);var n=a(66634),r=a(74466),i=a(59434);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:r,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:r,className:t})),...d})}},35279:()=>{},45436:(e,t,a)=>{"use strict";a.d(t,{V:()=>n});var s=a(55077);let n=(0,a(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:a}=t;try{return(await s.S.get("/classes/details/".concat(e))).data}catch(e){var n;return a((null===(n=e.response)||void 0===n?void 0:n.data)||"Fetch failed")}})},47339:(e,t,a)=>{"use strict";a.d(t,{TuitionClassForm:()=>D});var s=a(95155),n=a(62177),r=a(90221),i=a(55594),o=a(56671),l=a(30285),d=a(75937),c=a(59409);a(35279);var u=a(12115),m=a(54416),p=a(19946);let h=(0,p.A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);var x=a(5196),v=a(59434);function f(e){let{options:t,value:a,onChange:n,placeholder:r="Select options...",className:i}=e,[o,l]=u.useState(!1),[d,c]=u.useState(""),p=u.useRef(null),f=t.filter(e=>e.label.toLowerCase().includes(d.toLowerCase()));return u.useEffect(()=>{let e=e=>{p.current&&!p.current.contains(e.target)&&(l(!1),c(""))};return o&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[o]),(0,s.jsxs)("div",{className:"relative",ref:p,children:[(0,s.jsxs)("div",{className:(0,v.cn)("flex min-h-[40px] w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",i),onClick:()=>l(!o),children:[(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:a.length>0?a.map(e=>{let r=t.find(t=>t.value===e);return(0,s.jsxs)("div",{className:"flex items-center gap-1 rounded-md bg-secondary px-2 py-1 text-sm",children:[null==r?void 0:r.label,(0,s.jsx)(m.A,{className:"h-3 w-3 cursor-pointer",onClick:t=>{t.stopPropagation(),n(a.filter(t=>t!==e))}})]},e)}):(0,s.jsx)("span",{className:"text-muted-foreground",children:r})}),(0,s.jsx)(h,{className:"h-4 w-4 opacity-50"})]}),o&&(0,s.jsxs)("div",{className:"absolute z-50 mt-1 w-full rounded-md border bg-popover p-1 shadow-md",children:[(0,s.jsx)("div",{className:"flex items-center border-b px-2",children:(0,s.jsx)("input",{type:"text",placeholder:"Search...",className:"h-8 w-full bg-transparent px-2 py-1 text-sm outline-none",value:d,onChange:e=>c(e.target.value)})}),(0,s.jsx)("div",{className:"max-h-[200px] overflow-y-auto",children:0===f.length?(0,s.jsx)("div",{className:"px-2 py-1.5 text-sm text-muted-foreground",children:"No options found."}):f.map(e=>(0,s.jsxs)("div",{className:(0,v.cn)("flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent",a.includes(e.value)&&"bg-accent"),onClick:()=>{n(a.includes(e.value)?a.filter(t=>t!==e.value):[...a,e.value])},children:[(0,s.jsx)(x.A,{className:(0,v.cn)("h-4 w-4",a.includes(e.value)?"opacity-100":"opacity-0")}),e.label]},e.value))})]})]})}var g=a(55077),b=a(34540),j=a(94314),y=a(45436),w=a(62525);let N=(0,p.A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var C=a(66695),S=a(54165),T=a(20185);let z=i.z.object({tuitionDetails:i.z.array(i.z.object({education:i.z.string().min(1,"Category is required"),coachingType:i.z.array(i.z.string()).optional(),boardType:i.z.array(i.z.string()).optional(),subject:i.z.array(i.z.string()).optional(),medium:i.z.array(i.z.string()).optional(),section:i.z.array(i.z.string()).optional(),details:i.z.array(i.z.string()).optional()})).refine(e=>e.every(e=>{var t,a,s,n,r,i,o,l;return"Education"===e.education?(null===(t=e.boardType)||void 0===t?void 0:t.length)&&(null===(a=e.subject)||void 0===a?void 0:a.length)&&(null===(s=e.medium)||void 0===s?void 0:s.length)&&(null===(n=e.section)||void 0===n?void 0:n.length):!(null===(r=e.boardType)||void 0===r?void 0:r.length)&&!(null===(i=e.subject)||void 0===i?void 0:i.length)&&!(null===(o=e.medium)||void 0===o?void 0:o.length)&&!(null===(l=e.section)||void 0===l?void 0:l.length)}),{message:"For 'Education', boardType, subject, medium, and section are required. For other categories, these fields must be empty.",path:["tuitionDetails"]})});function k(e){let{form:t,index:a,removeTuition:n,constants:r,tuitionFieldsLength:i}=e,o=(e,t)=>{let a=r.find(t=>t.name===e),s=null==a?void 0:a.subDetails.find(e=>e.name===t);return(null==s?void 0:s.values)||[]},u=e=>o("Education",e),m=e=>{let t=r.find(t=>t.name===e);return(null==t?void 0:t.subDetails.map(e=>({id:e.id,name:e.name,isActive:!0,subDetailId:e.id})))||[]},p=t.watch("tuitionDetails.".concat(a,".education"));return(0,s.jsxs)("div",{className:"relative rounded-2xl border p-6 bg-muted/40 shadow-sm space-y-6",children:[i>1&&(0,s.jsx)(l.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-4 right-4 text-destructive hover:bg-destructive/10",onClick:()=>n(a),children:(0,s.jsx)(w.A,{size:20})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(d.zB,{control:t.control,name:"tuitionDetails.".concat(a,".education"),render:e=>{let{field:n}=e;return(0,s.jsxs)(d.eI,{className:"w-full",children:[(0,s.jsx)(d.lR,{children:"Category"}),(0,s.jsx)(d.MJ,{children:(0,s.jsxs)(c.l6,{onValueChange:e=>{n.onChange(e),t.setValue("tuitionDetails.".concat(a,".boardType"),[]),t.setValue("tuitionDetails.".concat(a,".subject"),[]),t.setValue("tuitionDetails.".concat(a,".medium"),[]),t.setValue("tuitionDetails.".concat(a,".section"),[]),t.setValue("tuitionDetails.".concat(a,".details"),[])},value:n.value,children:[(0,s.jsx)(c.bq,{className:"w-full",children:(0,s.jsx)(c.yv,{placeholder:"Select Category"})}),(0,s.jsx)(c.gC,{children:r.map(e=>(0,s.jsx)(c.eb,{value:e.name,children:e.name},e.id))})]})}),(0,s.jsx)(d.C5,{})]})}}),(0,s.jsx)(d.zB,{control:t.control,name:"tuitionDetails.".concat(a,".coachingType"),render:e=>{let{field:t}=e;return(0,s.jsxs)(d.eI,{className:"w-full",children:[(0,s.jsx)(d.lR,{children:"Coaching Type"}),(0,s.jsx)(d.MJ,{children:(0,s.jsx)(f,{options:[{label:"Personal",value:"Personal"},{label:"Group",value:"Group"},{label:"Online",value:"Online"},{label:"Hybrid",value:"Hybrid"}],value:t.value||[],onChange:e=>t.onChange(e),placeholder:"Select Coaching Type"})}),(0,s.jsx)(d.C5,{})]})}}),"Education"===p&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.zB,{control:t.control,name:"tuitionDetails.".concat(a,".boardType"),render:e=>{let{field:t}=e;return(0,s.jsxs)(d.eI,{className:"w-full",children:[(0,s.jsx)(d.lR,{children:"Board Type"}),(0,s.jsx)(d.MJ,{children:(0,s.jsx)(f,{options:u("Board Type").map(e=>({label:e.name,value:e.name})),value:t.value||[],onChange:e=>t.onChange(e),placeholder:"Select Board Type"})}),(0,s.jsx)(d.C5,{})]})}}),(0,s.jsx)(d.zB,{control:t.control,name:"tuitionDetails.".concat(a,".medium"),render:e=>{let{field:t}=e;return(0,s.jsxs)(d.eI,{className:"w-full",children:[(0,s.jsx)(d.lR,{children:"Medium"}),(0,s.jsx)(d.MJ,{children:(0,s.jsx)(f,{options:u("Medium").map(e=>({label:e.name,value:e.name})),value:t.value||[],onChange:e=>t.onChange(e),placeholder:"Select Medium"})}),(0,s.jsx)(d.C5,{})]})}}),(0,s.jsx)(d.zB,{control:t.control,name:"tuitionDetails.".concat(a,".section"),render:e=>{let{field:t}=e;return(0,s.jsxs)(d.eI,{className:"w-full",children:[(0,s.jsx)(d.lR,{children:"Section"}),(0,s.jsx)(d.MJ,{children:(0,s.jsx)(f,{options:u("Section").map(e=>({label:e.name,value:e.name})),value:t.value||[],onChange:e=>t.onChange(e),placeholder:"Select Section"})}),(0,s.jsx)(d.C5,{})]})}}),(0,s.jsx)(d.zB,{control:t.control,name:"tuitionDetails.".concat(a,".subject"),render:e=>{let{field:t}=e;return(0,s.jsxs)(d.eI,{className:"w-full",children:[(0,s.jsx)(d.lR,{children:"Subject"}),(0,s.jsx)(d.MJ,{children:(0,s.jsx)(f,{options:u("Subject").map(e=>({label:e.name,value:e.name})),value:t.value||[],onChange:e=>t.onChange(e),placeholder:"Select Subject"})}),(0,s.jsx)(d.C5,{})]})}})]}),p&&"Education"!==p&&(0,s.jsx)(d.zB,{control:t.control,name:"tuitionDetails.".concat(a,".details"),render:e=>{let{field:t}=e;return(0,s.jsxs)(d.eI,{className:"w-full",children:[(0,s.jsxs)(d.lR,{children:[p," Details"]}),(0,s.jsx)(d.MJ,{children:(0,s.jsx)(f,{options:m(p).map(e=>({label:e.name,value:e.name})),value:t.value||[],onChange:e=>t.onChange(e),placeholder:"Select ".concat(p," Details")})}),(0,s.jsx)(d.C5,{})]})}})]})]})}function D(){var e;let t=(0,b.wA)(),[a,i]=(0,u.useState)([]),c=(0,b.d4)(e=>e.class.classData),m=(0,n.mN)({resolver:(0,r.u)(z),defaultValues:{tuitionDetails:[{education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}]}}),{fields:p,append:h,remove:x}=(0,n.jz)({control:m.control,name:"tuitionDetails"}),v=async()=>{try{let e=await g.S.get("/constant/TuitionClasses");e.data&&e.data.details&&i(e.data.details)}catch(e){o.toast.error("Failed to fetch constants")}},f=async e=>{try{let a={tuitionDetails:e.tuitionDetails.map(e=>({...e,boardType:e.boardType?JSON.stringify(e.boardType):null,subject:e.subject?JSON.stringify(e.subject):null,medium:e.medium?JSON.stringify(e.medium):null,section:e.section?JSON.stringify(e.section):null,details:e.details?JSON.stringify(e.details):null}))};await g.S.post("/classes-profile/tuition-classes",a),await t((0,y.V)(c.id)),o.toast.success("Tuition class details uploaded successfully"),t((0,j.ac)(j._3.TUTIONCLASS)),m.reset({tuitionDetails:[{education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}]}),o.toast.success("Now you can send for review your profile")}catch(e){o.toast.error("Something went wrong")}},D=async(e,a)=>{try{await g.S.delete("/classes-profile/tuition-class/".concat(e),{data:{classId:a}}),o.toast.success("Tuition class deleted successfully"),await t((0,y.V)(a)),m.reset({tuitionDetails:[{education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}]})}catch(e){o.toast.error("Failed to delete tuition class")}};return(0,u.useEffect)(()=>{v()},[]),(0,s.jsxs)(s.Fragment,{children:[(null==c?void 0:null===(e=c.tuitionClasses)||void 0===e?void 0:e.length)>0&&(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Tuition Classes"}),c.tuitionClasses.map((e,t)=>(0,s.jsxs)(C.Zp,{className:"bg-muted/30 relative",children:[(0,s.jsxs)(C.aR,{className:"flex flex-row items-start justify-between",children:[(0,s.jsxs)(C.ZB,{className:"text-base font-semibold",children:["Tuition #",t+1]}),(0,s.jsxs)(S.lG,{children:[(0,s.jsx)(S.zM,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"icon",className:"text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(S.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(S.c7,{children:[(0,s.jsx)(S.L3,{children:"Delete Tuition Class"}),(0,s.jsx)(S.rr,{children:"Are you sure you want to delete this tuition class? This action cannot be undone. All associated time slots will also be deleted."})]}),(0,s.jsxs)(S.Es,{className:"gap-2",children:[(0,s.jsx)(l.$,{variant:"outline",onClick:()=>{var e;return null===(e=document.querySelector('button[data-state="open"]'))||void 0===e?void 0:e.click()},children:"Cancel"}),(0,s.jsx)(l.$,{variant:"destructive",onClick:()=>{var t;D(e.id,c.id),null===(t=document.querySelector('button[data-state="open"]'))||void 0===t||t.click()},children:"Delete"})]})]})]})]}),(0,s.jsxs)(C.Wu,{className:"space-y-3 text-sm",children:[e.education&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Category:"})," ",e.education]}),e.coachingType&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Coaching Type:"})," ",(0,T.Ow)(e.coachingType).join(", ")]}),"Education"===e.education&&(0,s.jsxs)(s.Fragment,{children:[e.boardType&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Board:"})," ",(0,T.Ow)(e.boardType).join(", ")]}),e.medium&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Medium:"})," ",(0,T.Ow)(e.medium).join(", ")]}),e.section&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Section:"})," ",(0,T.Ow)(e.section).join(", ")]}),e.subject&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Subject:"})," ",(0,T.Ow)(e.subject).join(", ")]})]}),"Education"!==e.education&&e.details&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Details:"})," ",(0,T.Ow)(e.details).join(", ")]})]})]},t))]}),(0,s.jsx)(d.lV,{...m,children:(0,s.jsxs)("form",{onSubmit:m.handleSubmit(f),className:"space-y-8",children:[p.map((e,t)=>(0,s.jsx)(k,{form:m,index:t,removeTuition:x,constants:a,tuitionFieldsLength:p.length},e.id)),(0,s.jsxs)(l.$,{type:"button",onClick:()=>h({education:"",coachingType:[],boardType:[],subject:[],medium:[],section:[],details:[]}),className:"flex items-center gap-2",children:[(0,s.jsx)(N,{size:18}),"Add Another Tuition"]}),(0,s.jsx)(l.$,{type:"submit",children:"Save Tuition Class Details"})]})})]})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>p,L3:()=>h,c7:()=>m,lG:()=>o,rr:()=>x,zM:()=>l});var s=a(95155);a(12115);var n=a(4033),r=a(54416),i=a(59434);function o(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"dialog",...t})}function l(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,s.jsx)(n.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,...o}=e;return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(c,{}),(0,s.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,(0,s.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(r.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}},55077:(e,t,a)=>{"use strict";a.d(t,{S:()=>i});var s=a(23464),n=a(56671);let r="http://localhost:4005/api/v1";console.log("Axios baseURL:",r);let i=s.A.create({baseURL:r,headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":r;{let t=localStorage.getItem("studentToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(n.toast.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var s=a(95155);a(12115);var n=a(59824),r=a(66474),i=a(5196),o=a(47863),l=a(59434);function d(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(n.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...o}=e;return(0,s.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":a,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[i,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:r="popper",...i}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,s.jsx)(h,{}),(0,s.jsx)(n.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(x,{})]})})}function p(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(n.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(n.p4,{children:a})]})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(o.A,{className:"size-4"})})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(r.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{MB:()=>o,ZO:()=>i,cn:()=>r,wR:()=>d,xh:()=>l});var s=a(52596),n=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,s.$)(t))}let i=()=>localStorage.getItem("studentToken"),o=()=>{localStorage.removeItem("studentToken")},l=()=>!!i(),d=()=>{if(i())return{isAuth:!0,userType:"STUDENT"};{let e=localStorage.getItem("user");if(e)try{let t=JSON.parse(e);if(t&&t.id)return{isAuth:!0,userType:"CLASS"}}catch(e){console.error("Error parsing user data:",e)}}return{isAuth:!1,userType:null}}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>r,aR:()=>i,wL:()=>c});var s=a(95155);a(12115);var n=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},75937:(e,t,a)=>{"use strict";a.d(t,{lV:()=>c,MJ:()=>f,Rr:()=>g,zB:()=>m,eI:()=>x,lR:()=>v,C5:()=>b});var s=a(95155),n=a(12115),r=a(66634),i=a(62177),o=a(59434),l=a(24265);function d(e){let{className:t,...a}=e;return(0,s.jsx)(l.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}let c=i.Op,u=n.createContext({}),m=e=>{let{...t}=e;return(0,s.jsx)(u.Provider,{value:{name:t.name},children:(0,s.jsx)(i.xI,{...t})})},p=()=>{let e=n.useContext(u),t=n.useContext(h),{getFieldState:a}=(0,i.xW)(),s=(0,i.lN)({name:e.name}),r=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...r}},h=n.createContext({});function x(e){let{className:t,...a}=e,r=n.useId();return(0,s.jsx)(h.Provider,{value:{id:r},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...a})})}function v(e){let{className:t,...a}=e,{error:n,formItemId:r}=p();return(0,s.jsx)(d,{"data-slot":"form-label","data-error":!!n,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:r,...a})}function f(e){let{...t}=e,{error:a,formItemId:n,formDescriptionId:i,formMessageId:o}=p();return(0,s.jsx)(r.DX,{"data-slot":"form-control",id:n,"aria-describedby":a?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!a,...t})}function g(e){let{className:t,...a}=e,{formDescriptionId:n}=p();return(0,s.jsx)("p",{"data-slot":"form-description",id:n,className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function b(e){var t;let{className:a,...n}=e,{error:r,formMessageId:i}=p(),l=r?String(null!==(t=null==r?void 0:r.message)&&void 0!==t?t:""):n.children;return l?(0,s.jsx)("p",{"data-slot":"form-message",id:i,className:(0,o.cn)("text-destructive text-sm",a),...n,children:l}):null}},87431:(e,t,a)=>{Promise.resolve().then(a.bind(a,47339)),Promise.resolve().then(a.bind(a,22346))},94314:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>l,_3:()=>n,ac:()=>i});var s=a(51990),n=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let r=(0,s.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let a=t.payload;e.completedForms[a]||(e.completedForms[a]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:o}=r.actions,l=r.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[5302,7040,5186,4540,1990,6046,4945,4632,1342,4520,8441,1684,7358],()=>t(87431)),_N_E=e.O()}]);