(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3736],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(95155);s(12115);var a=s(6874),l=s.n(a),i=s(66766),n=s(29911);let c=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},12054:(e,t,s)=>{"use strict";s.d(t,{Kh:()=>n,Qg:()=>i,U4:()=>l,o3:()=>c});var r=s(59434);let a="http://localhost:4005/api/v1",l=async e=>{try{let t=(0,r.ZO)();if(!t)throw Error("Authentication required");let s=await fetch("".concat(a,"/student-wishlist"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({classId:e}),credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to add to wishlist")}return await s.json()}catch(e){throw console.error("Error adding to wishlist:",e),e}},i=async e=>{try{let t=(0,r.ZO)();if(!t)throw Error("Authentication required");let s=await fetch("".concat(a,"/student-wishlist/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)},credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to remove from wishlist")}return await s.json()}catch(e){throw console.error("Error removing from wishlist:",e),e}},n=async e=>{try{let t=(0,r.ZO)();if(!t)return{inWishlist:!1};let s=await fetch("".concat(a,"/student-wishlist/check/").concat(e),{method:"GET",headers:{Authorization:"Bearer ".concat(t)},credentials:"include"});if(!s.ok)return{inWishlist:!1};return(await s.json()).data}catch(e){return console.error("Error checking wishlist status:",e),{inWishlist:!1}}},c=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{let s=(0,r.ZO)();if(!s)throw Error("Authentication required");let l=await fetch("".concat(a,"/student-wishlist?page=").concat(e,"&limit=").concat(t),{method:"GET",headers:{Authorization:"Bearer ".concat(s)},credentials:"include"});if(!l.ok){let e=await l.json();throw Error(e.message||"Failed to fetch wishlist")}return await l.json()}catch(e){throw console.error("Error fetching wishlist:",e),e}}},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},42239:(e,t,s)=>{Promise.resolve().then(s.bind(s,65410))},42355:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},65410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var r=s(95155),a=s(12115),l=s(35695),i=s(66766),n=s(29911),c=s(51013),o=s(30285),d=s(66695),h=s(68856),m=s(56671),x=s(70347),u=s(7583),f=s(12054),p=s(59434),g=s(42355),j=s(13052);(0,s(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);let w=e=>{let{className:t,...s}=e;return(0,r.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,p.cn)("mx-auto flex w-full justify-center",t),...s})};w.displayName="Pagination";let v=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("ul",{ref:t,className:(0,p.cn)("flex flex-row items-center gap-1",s),...a})});v.displayName="PaginationContent";let y=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("li",{ref:t,className:(0,p.cn)("",s),...a})});y.displayName="PaginationItem";let N=e=>{let{className:t,isActive:s,size:a="icon",...l}=e;return(0,r.jsx)("a",{"aria-current":s?"page":void 0,className:(0,p.cn)((0,o.r)({variant:s?"outline":"ghost",size:a}),t),...l})};N.displayName="PaginationLink";let b=e=>{let{className:t,...s}=e;return(0,r.jsxs)(N,{"aria-label":"Go to previous page",size:"default",className:(0,p.cn)("gap-1 pl-2.5",t),...s,children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Previous"})]})};b.displayName="PaginationPrevious";let k=e=>{let{className:t,...s}=e;return(0,r.jsxs)(N,{"aria-label":"Go to next page",size:"default",className:(0,p.cn)("gap-1 pr-2.5",t),...s,children:[(0,r.jsx)("span",{children:"Next"}),(0,r.jsx)(j.A,{className:"h-4 w-4"})]})};k.displayName="PaginationNext";let O=()=>{let[e,t]=(0,a.useState)(null),[s,g]=(0,a.useState)(!0),[j,O]=(0,a.useState)(1),E=(0,l.useRouter)(),P=(0,a.useCallback)(async()=>{try{g(!0);let e=await (0,f.o3)(j,10);t(e.data)}catch(e){m.toast.error(e.message||"Failed to fetch wishlist")}finally{g(!1)}},[j]);(0,a.useEffect)(()=>{if(!(0,p.xh)()){E.push("/"),m.toast.error("Please login as a student to view your wishlist");return}P()},[j,E,P]);let C=async e=>{try{await (0,f.Qg)(e),m.toast.success("Removed from wishlist"),P()}catch(e){m.toast.error(e.message||"Failed to remove from wishlist")}},A=e=>{O(e)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.default,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"My Wishlist"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Classes you've saved for later"})]}),(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsxs)(o.$,{variant:"outline",size:"sm",className:"text-xs border-orange-200 text-orange-600 hover:bg-orange-50",onClick:()=>E.push("/verified-classes"),children:[(0,r.jsx)(n.YNd,{className:"mr-1 h-3 w-3"}),"Browse More Classes"]})})]}),(0,r.jsx)("div",{className:"h-px bg-gray-200 w-full mb-6"}),s?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[...Array(8)].map((e,t)=>(0,r.jsxs)(d.Zp,{className:"overflow-hidden border border-gray-200 h-full flex flex-col p-0 rounded-md",children:[(0,r.jsx)("div",{className:"relative h-40 bg-gray-100 rounded-t-md overflow-hidden",children:(0,r.jsx)(h.E,{className:"h-full w-full"})}),(0,r.jsxs)(d.Wu,{className:"p-2 py-1.5 flex-grow",children:[(0,r.jsx)(h.E,{className:"h-5 w-3/4"}),(0,r.jsx)(h.E,{className:"h-3 w-1/2 mt-1 mb-0.5"}),(0,r.jsx)(h.E,{className:"h-3 w-2/3"})]}),(0,r.jsx)(d.wL,{className:"p-2 flex justify-between gap-2 mt-0",children:(0,r.jsx)(h.E,{className:"h-7 w-full"})})]},t))}):(null==e?void 0:e.items.length)===0?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(n.YNd,{className:"mx-auto h-16 w-16 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"No items in wishlist"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"You haven't added any classes to your wishlist yet."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(o.$,{onClick:()=>E.push("/verified-classes"),className:"bg-orange-500 hover:bg-orange-600",children:"Browse Classes"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:null==e?void 0:e.items.map(e=>{var t,s,a;let{savedClass:l}=e,h="".concat(l.firstName," ").concat(l.lastName),m=(null===(t=l.ClassAbout)||void 0===t?void 0:t.classesLogo)?"".concat("http://localhost:4005/").concat(l.ClassAbout.classesLogo):"/teacher-profile.jpg";return(0,r.jsxs)(d.Zp,{className:"overflow-hidden hover:shadow-md transition-shadow border border-gray-200 h-full flex flex-col p-0 rounded-md",children:[(0,r.jsxs)("div",{className:"relative h-40 bg-gray-50 rounded-t-md overflow-hidden",children:[(0,r.jsx)(i.default,{src:m,alt:h,fill:!0,className:"object-cover"}),(null===(s=l.status)||void 0===s?void 0:s.status)==="APPROVED"&&(0,r.jsx)("div",{className:"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm",children:(0,r.jsx)(c.VqV,{className:"text-green-500 h-4 w-4"})})]}),(0,r.jsxs)(d.Wu,{className:"p-2 py-1.5 flex-grow",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)(n.x$1,{className:"text-[#ff914d] h-4 w-4 flex-shrink-0"}),(0,r.jsx)("h3",{className:"text-xl font-bold line-clamp-1",children:h}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground line-clamp-1",children:["( ",l.className||""," )"]})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-1 mt-0.5 mb-0.5",children:(null===(a=l.ClassAbout)||void 0===a?void 0:a.catchyHeadline)||"Professional Educator"}),(0,r.jsx)("div",{className:"flex items-center gap-2"})]}),(0,r.jsxs)(d.wL,{className:"p-2 flex justify-between gap-2 mt-0",children:[(0,r.jsx)(o.$,{variant:"default",size:"sm",className:"flex-1 bg-orange-500 hover:bg-orange-600 text-xs h-7",onClick:()=>E.push("/classes-details/".concat(l.id)),children:"View Details"}),(0,r.jsx)(o.$,{variant:"outline",size:"icon",className:"w-7 h-7 p-0 flex items-center justify-center",onClick:()=>C(e.id),children:(0,r.jsx)(n.Mbv,{className:"text-orange-500 h-4 w-4"})})]})]},e.id)})}),e&&e.totalPages>1&&(0,r.jsx)(w,{className:"mt-8",children:(0,r.jsxs)(v,{children:[(0,r.jsx)(y,{children:(0,r.jsx)(b,{size:"default",onClick:()=>A(Math.max(1,j-1)),className:1===j?"pointer-events-none opacity-50":"cursor-pointer"})}),Array.from({length:e.totalPages},(e,t)=>t+1).map(e=>(0,r.jsx)(y,{children:(0,r.jsx)(N,{size:"default",onClick:()=>A(e),isActive:j===e,className:"cursor-pointer",children:e})},e)),(0,r.jsx)(y,{children:(0,r.jsx)(k,{size:"default",onClick:()=>A(Math.min(e.totalPages,j+1)),className:j===e.totalPages?"pointer-events-none opacity-50":"cursor-pointer"})})]})})]})]}),(0,r.jsx)(u.default,{})]})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>d});var r=s(95155);s(12115);var a=s(59434);function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},68856:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(95155),a=s(59434);function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",t),...s})}},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var r=s(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=r.createContext&&r.createContext(a),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(this,arguments)}function c(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,r)}return s}function o(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?c(Object(s),!0).forEach(function(t){var r,a,l;r=e,a=t,l=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in r?Object.defineProperty(r,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):c(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>r.createElement(h,n({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,s)=>r.createElement(t.tag,o({key:s},t.attr),e(t.child)))}(e.child))}function h(e){var t=t=>{var s,{attr:a,size:l,title:c}=e,d=function(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;s[r]=e[r]}return s}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)s=l[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}(e,i),h=l||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),r.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:s,style:o(o({color:e.color||t.color},t.style),e.style),height:h,width:h,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),e.children)};return void 0!==l?r.createElement(l.Consumer,null,e=>t(e)):t(a)}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,512,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,347,8441,1684,7358],()=>t(42239)),_N_E=e.O()}]);