(()=>{var e={};e.id=7742,e.ids=[7742],e.modules={1634:(e,t,s)=>{Promise.resolve().then(s.bind(s,50971))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3976:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(60687),r=s(43210),n=s(16189),i=s(29523),l=s(27324),o=s(54864),d=s(52581);let c=({exam:e,hasApplied:t,isMaxLimitReached:s,hasAttempted:c,onApplyClick:x})=>{let u=(0,n.useRouter)(),m=(0,o.d4)(e=>e.user.isAuthenticated),[h,p]=(0,r.useState)("countdown"),[g,f]=(0,r.useState)(!1),[w,j]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{let t=()=>{let t=new Date(e.start_date).getTime(),s=e.start_registration_date?new Date(e.start_registration_date).getTime():null;if(isNaN(t)){console.error(`Invalid start_date for exam ${e.id}: ${e.start_date}`),p("finished"),f(!1),j(!1);return}let a=(0,l.L_)(new Date,"Asia/Kolkata").getTime(),r=t+6e4*e.duration;s&&a<s?j(!1):j(!0),a<t?(p("countdown"),f(!1)):a>=t&&a<=r?(p("start"),f(!0)):(p("finished"),f(!1))};t();let s=setInterval(t,1e3);return()=>clearInterval(s)},[e.start_date,e.duration,e.id,e.start_registration_date]),c)?(0,a.jsx)("div",{className:"flex flex-col items-center justify-center gap-4 mb-4 mx-5",children:(0,a.jsx)(i.$,{className:"w-full bg-gray-400 text-white font-semibold py-2 rounded-lg cursor-not-allowed",disabled:!0,children:"Attempted"})}):(0,a.jsx)("div",{className:"flex flex-col items-center justify-center gap-4 mb-4 mx-5",children:"countdown"===h?(0,a.jsx)(i.$,{onClick:()=>{if(m){d.toast.error("You are currently logged in as a tutor. Please log out and then log in as a student to apply for UWhiz.");return}x()},className:"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",disabled:s||t||!w,children:t?"Applied":s?"Max Limit Reached":w?"Apply Now":"Registration Will Start Soon"}):"start"===h?(0,a.jsx)(i.$,{onClick:()=>{u.push(`/uwhiz-exam/${e.id}`)},className:"w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",disabled:!g||!t,children:t?"Start Exam Now":"You Have Not Applied"}):(0,a.jsx)(i.$,{disabled:!0,onClick:()=>{u.push(`/uwhiz-details/${e.id}`)},className:"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",children:"Result Will Announce Soon"})})}},5690:(e,t,s)=>{"use strict";s.d(t,{o:()=>r});var a=s(28527);let r=async(e,t)=>{try{return(await a.S.get(`check-attempt?studentId=${e}&examId=${t}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed To Get Student And Exam Detail: ${e.response?.data?.message||e.message}`}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},16145:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23562:(e,t,s)=>{"use strict";s.d(t,{k:()=>i});var a=s(60687);s(43210);var r=s(25177),n=s(4780);function i({className:e,value:t,...s}){return(0,a.jsx)(r.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,a.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29116:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>n});var a=s(37413),r=s(33427);let n={title:"Uwhiz - Online Competitive Exams & Contests | UEST",description:"Join Uwhiz competitive exams and contests on UEST. Test your knowledge, compete with students nationwide, and win exciting prizes. Free and paid exams available with UEST coins.",keywords:["Uwhiz","online exams","competitive exams","student contests","educational competitions","UEST","exam preparation","student assessment","online testing","academic competitions"],openGraph:{title:"Uwhiz - Online Competitive Exams & Contests | UEST",description:"Join Uwhiz competitive exams and contests on UEST. Test your knowledge, compete with students nationwide, and win exciting prizes.",url:"https://www.uest.in/uwhiz",type:"website",images:[{url:"https://www.uest.in/exam-logo.jpg",width:800,height:600,alt:"Uwhiz Competitive Exams"}]},robots:{index:!0,follow:!0},alternates:{canonical:"https://www.uest.in/uwhiz"}};function i(){return(0,a.jsx)(r.default,{})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33427:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\uwhiz\\\\UwhizInnerPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\UwhizInnerPage.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},39171:(e,t,s)=>{"use strict";s.d(t,{Dl:()=>r,LP:()=>n});var a=s(28527);let r=async(e=1,t=10,s)=>{try{return(await a.S.get(`/exams?page=${e}&limit=${t}${s?`&applicantId=${s}`:""}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed to fetch Exam: ${e.response?.data?.message||e.message}`}}},n=async(e,t)=>{try{return(await a.S.get(`/exams/${e}${t?`?applicantId=${t}`:""}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed to fetch Exam: ${e.response?.data?.message||e.message}`}}}},47033:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},50337:(e,t,s)=>{"use strict";s.d(t,{n:()=>r});var a=s(28527);let r=async(e,t)=>{try{return(await a.S.post("/examApplication",{examId:e,applicantId:t},{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){throw Error(e.response?.data?.error||"Failed to apply for exam")}}},50971:(e,t,s)=>{"use strict";s.d(t,{default:()=>M});var a=s(60687),r=s(90269);let n={src:"/_next/static/media/exam-logo.367af320.jpg"};var i=s(66874),l=s(29523),o=s(39171),d=s(28527);let c=async(e,t,s)=>{try{return(await d.S.post("/examApplicantEmail/send-exam-applicant-email",{examId:e,exam_name:t,email:s})).data}catch(e){throw Error(e.response?.data?.message||"Failed to send exam applicant email")}};var x=s(50337),u=s(43210),m=s(33793),h=s(52581),p=s(5690),g=s(41862),f=s(77306),w=s(63826),j=s(47033),b=s(14952),v=s(16145),y=s(46303),N=s(30656),k=s(23562),_=s(44255),S=s(58505),A=s(23711),z=s(67216),C=s(27324);let E=(0,u.memo)(({exam:e})=>{let[t,s]=(0,u.useState)("registration"),[r,n]=(0,u.useState)({days:0,hours:0,minutes:0,seconds:0}),[i,l]=(0,u.useState)({minutes:10,seconds:0,isLate:!1});return(0,u.useEffect)(()=>{let t=()=>{let t=e.start_registration_date?new Date(e.start_registration_date):null,a=new Date(e.start_date);if(isNaN(a.getTime())){console.error(`Invalid start_date for exam ${e.id}: ${e.start_date}`),s("expired"),n({days:0,hours:0,minutes:0,seconds:0}),l({minutes:0,seconds:0,isLate:!0});return}let r=(0,C.L_)(new Date,"Asia/Kolkata"),i=(0,C.L_)(a,"Asia/Kolkata"),o=i.getTime(),d=(function(e,t,s){let a=(0,A.a)(e,void 0);return a.setTime(a.getTime()+t*S.Cg),a})(i,e.duration).getTime(),c=r.getTime();if(t&&!isNaN(t.getTime())&&c<t.getTime()){let e=(0,z.O)(t,r),a=Math.floor(e/86400),i=Math.floor(e%86400/3600),o=Math.floor(e%3600/60);s("registration"),n({days:a,hours:i,minutes:o,seconds:e%60}),l({minutes:10,seconds:0,isLate:!1})}else if(c<o){let e=(0,z.O)(i,r),t=Math.floor(e/86400),a=Math.floor(e%86400/3600),o=Math.floor(e%3600/60);s("application"),n({days:t,hours:a,minutes:o,seconds:e%60}),l({minutes:10,seconds:0,isLate:!1})}else if(c>=o&&c<=d){let e=(0,z.O)(new Date(d),r),t=Math.floor(e/60);s("exam"),n({days:0,hours:0,minutes:0,seconds:0}),l({minutes:t,seconds:e%60,isLate:!1})}else s("late"),n({days:0,hours:0,minutes:0,seconds:0}),l({minutes:0,seconds:0,isLate:!0})};t();let a=setInterval(t,1e3);return()=>clearInterval(a)},[e.start_date,e.start_registration_date,e.id]),(0,a.jsxs)("div",{className:"flex items-center justify-center w-full gap-2 border-2 px-4 bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300",children:[(0,a.jsx)(_.MzU,{className:"text-2xl text-customOrange animate-pulse"}),(0,a.jsx)("span",{className:"font-semibold text-customOrange text-sm",children:"registration"===t?(0,a.jsxs)("span",{children:["Registration Starts in: ",r.days,"d ",r.hours,"h ",r.minutes,"m"," ",r.seconds,"s"]}):"application"===t?(0,a.jsxs)("span",{children:["Exam Starts in: ",r.days,"d ",r.hours,"h ",r.minutes,"m"," ",r.seconds,"s"]}):"exam"===t?(0,a.jsxs)("span",{children:["You May Starts In: ",i.minutes,"m ",i.seconds,"s"]}):"late"===t?(0,a.jsx)("span",{children:"You Are Late"}):(0,a.jsx)("span",{children:"Expired"})})]})});var O=s(3976),P=s(30474),q=s(16189),$=s(58385);function T({open:e,onClose:t}){let s=(0,u.useRef)(null);return(0,a.jsx)("dialog",{ref:s,className:"w-[600px] h-[600px] p-0 border-none shadow-xl rounded-lg backdrop:bg-black/50 fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",onClose:t,children:(0,a.jsxs)("div",{className:"relative w-full h-full",children:[(0,a.jsx)("button",{className:"absolute top-3 right-3 text-gray-600 hover:text-red-500 text-2xl z-10",onClick:t,children:"\xd7"}),(0,a.jsx)(P.default,{src:"/MathsMarvelWinner.png",alt:"Uwhiz Winner",width:400,height:250,className:"w-full h-full object-cover rounded-lg"})]})})}let M=()=>{let[e,t]=(0,u.useState)([]),[s,_]=(0,u.useState)(!1),[S,A]=(0,u.useState)(!1),[z,C]=(0,u.useState)(null),[M,U]=(0,u.useState)(1),[L,D]=(0,u.useState)(1),[R]=(0,u.useState)(9),[F,I]=(0,u.useState)(!1),[G,J]=(0,u.useState)(null),[K,W]=(0,u.useState)(null),[Y,V]=(0,u.useState)(!1),[B,X]=(0,u.useState)(0),[Z,Q]=(0,u.useState)(!1),[H,ee]=(0,u.useState)(!1),[et,es]=(0,u.useState)(null),ea=()=>{try{let e=localStorage.getItem("student_data");return e?JSON.parse(e).id:""}catch{return""}},er=()=>{ee(!0)},en=()=>{ee(!1)},ei=(0,u.useMemo)(()=>e,[e]),el=(0,q.useRouter)(),eo=ea(),ed=ei.filter(e=>{let t=new Date,s=new Date(e.start_date),a=6e4*e.duration;return new Date(s.getTime()+a).getTime()>t.getTime()}),ec=ei.filter(e=>{let t=new Date,s=new Date(e.start_date),a=6e4*e.duration;return new Date(s.getTime()+a).getTime()<t.getTime()});(0,u.useEffect)(()=>{(async()=>{if(eo)try{let e=await (0,$.J)();e.success&&W(e.data)}catch(e){console.error("Error fetching discount info:",e)}})()},[eo]),(0,u.useEffect)(()=>{(async()=>{I(!0);try{let e=await (0,o.Dl)(M,R,eo),s=e.exams;s=eo?await Promise.all(s.map(async e=>{try{let t=await (0,p.o)(eo,e.id);return{...e,hasAttempted:!1!==t.success&&t}}catch(t){return console.error(`Error checking attempt for exam ${e.id}:`,t),{...e,hasAttempted:!1}}})):s.map(e=>({...e,hasAttempted:!1})),t(s),D(e.totalPages||1)}catch(e){console.error("Error fetching exams:",e),h.toast.error(e.message||"Failed to load exams")}finally{I(!1)}})()},[M,R,eo]);let ex=e=>{C(e),J(null),A(!0)},eu=async()=>{try{return(await d.S.get("/coins/get-total-coins/student")).data.coins}catch(e){h.toast.error("Failed to load coin data. Please try again."),console.error("Error fetching data",e)}},em=async()=>{if(!z)return;let e=ea();if(!e){h.toast.error("Please log in as a student to apply for an exam");return}let s=await eu();try{let s=await (0,x.n)(z.id,e);if(s.application){try{let e=localStorage.getItem("student_data");if(e){let t=JSON.parse(e).email;t&&await c(z.id,z.exam_name,t)}}catch(e){console.log("Email sending failed",e)}t(e=>e.map(e=>e.id===z.id?{...e,joinedClassesCount:e.joinedClassesCount+1,totalApplicants:(e.totalApplicants||0)+1,hasApplied:!0}:e)),A(!1),_(!0),h.toast.success(s.message||"Successfully applied for the exam"),J(null)}}catch(t){let e=t?.response?.data?.error||t.message||"Error applying for exam";if(h.toast.error(e),e.includes("Uwhiz Super Kids Exam")){es(e),A(!1);return}if(e.includes("Required Coin for Applying in Exam")){J(e);let t=Number(z.coins_required)??0;K?.hasDiscount&&(t*=1-K.discountPercentage/100),X(Math.floor(Math.floor(t)-s))}else A(!1)}finally{V(!1)}},eh=()=>{_(!1),A(!1),C(null),J(null)},ep=e=>{window.location.href=`/uwhiz-info/${e}`},eg=(e,t)=>0===t?0:Math.min(100,Math.max(0,e/t*100)),ef=async()=>{V(!0);try{let{order:e}=(await d.S.post("/coins/create-order",{amount:100*B})).data,t={key:"rzp_test_Opr6M8CKpK12pF",amount:e.amount,currency:"INR",name:"Uest Coins",description:"Add Uest Coins",order_id:e.id,handler:async function(e){try{Q(!0),await d.S.post("/coins/verify",{razorpay_order_id:e.razorpay_order_id,razorpay_payment_id:e.razorpay_payment_id,razorpay_signature:e.razorpay_signature,amount:100*B}),h.toast.success("Coins added successfully!"),em(),Q(!1)}catch{h.toast.error("Payment verification failed")}finally{Q(!1)}},theme:{color:"#f97316"}};new window.Razorpay(t).open()}catch{h.toast.error("Payment initialization failed")}finally{V(!1)}};return(0,u.useEffect)(()=>{let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,document.body.appendChild(e)},[]),(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("div",{className:"flex justify-center bg-black pb-10",children:(0,a.jsx)(P.default,{height:400,width:400,src:n.src,alt:"Exam Logo",priority:!0,quality:100})}),s&&z&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-xl max-w-md w-full",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-green-600 mb-4",children:"Application Successful!"}),(0,a.jsxs)("p",{className:"text-gray-700 mb-6",children:["You have successfully applied for"," ",(0,a.jsx)("strong",{children:z.exam_name}),"."]}),(0,a.jsx)(l.$,{onClick:eh,className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg",children:"Close"})]})}),S&&z&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100",children:[(0,a.jsx)("h2",{className:"text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2",children:"Are You Sure?"}),(0,a.jsxs)("p",{className:"text-gray-700 text-lg mb-6 leading-relaxed",children:["Do you want to apply for"," ",(0,a.jsx)("strong",{className:"text-customOrange",children:z.exam_name}),"?",null!=z.coins_required&&(0,a.jsxs)("span",{children:[" ","This will cost"," ",K?.hasDiscount?(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"line-through text-gray-500",children:z.coins_required})," ",(0,a.jsx)("strong",{className:"text-green-600",children:(0,$.w)(z.coins_required,K.discountPercentage)})," ",(0,a.jsxs)("span",{className:"text-green-600 text-sm",children:["(",K.discountPercentage,"% discount applied)"]})]}):(0,a.jsx)("strong",{className:"text-customOrange",children:z.coins_required})," ","coins."]})]}),G&&(0,a.jsxs)("div",{className:"flex-col justify-center items-start gap-2 bg-red-50 p-4 rounded-lg mb-6 border border-red-200",children:[(0,a.jsxs)("div",{className:"flex gap-5 items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-red-600 mt-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"text-red-600 text-sm font-medium",children:G})]}),(0,a.jsx)(l.$,{onClick:()=>ef(),className:"mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:Z,children:Z?(0,a.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(g.A,{className:"animate-spin w-5 h-5"}),"Processing..."]}):"Add Coins"})]}),ea()?(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{onClick:em,className:"w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:!!G||Y,children:Y?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Yes, Apply"}),(0,a.jsx)(l.$,{onClick:eh,className:"w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg",children:"Cancel"})]}):(0,a.jsx)(l.$,{onClick:()=>el.push("/student/login?redirect=/uwhiz"),className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",children:"Login to Apply"})]})}),et&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-red-600 dark:text-red-400 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300 mb-6",children:et}),(0,a.jsx)("button",{onClick:()=>es(null),className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg",children:"Close"})]})}),F?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"Loading exams..."}):(0,a.jsxs)(a.Fragment,{children:[ed.length>0&&(0,a.jsxs)(a.Fragment,{children:[ed.length>0&&(0,a.jsx)("div",{className:"flex justify-center items-center mt-10",children:(0,a.jsxs)("h1",{className:"text-3xl font-bold ml-4",children:["Upcoming ",(0,a.jsx)("span",{className:"text-customOrange",children:"Exams "})]})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center items-start gap-6 p-4 sm:p-6 md:p-10 lg:p-20",children:ed&&ed.map(e=>{let t=e.total_student_intake??0,s=e.UwhizPriceRank.find(e=>1===e.rank)?.price??0;return(0,a.jsxs)(i.Zp,{className:"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center px-3 py-2 space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105",children:e.exam_name}),4!==e.id&&6!==e.id&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xl font-bold",children:[(0,a.jsx)(N.qjb,{className:"text-xl text-customOrange"}),(0,a.jsxs)("span",{className:"dark:text-white",children:["1st Prize: ",(0,a.jsx)("span",{className:"text-customOrange",children:s})]})]})]}),(0,a.jsx)(E,{exam:e}),(0,a.jsxs)("div",{className:"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(m.lfF,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Total Questions: ",e.total_questions]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(m.O6N,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Marks: ",e.marks]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(m.w_X,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Duration: ",e.duration]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(f.A,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Coins: ",K?.hasDiscount?(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"line-through text-gray-500 text-xs",children:e.coins_required}),(0,a.jsx)("span",{className:"text-green-600 font-bold",children:(0,$.w)(e.coins_required,K.discountPercentage)}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["(",K.discountPercentage,"% off)"]})]}):0===Number(e.coins_required)?"Free":e.coins_required]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Student Joined"}),(0,a.jsx)(k.k,{value:eg(e.totalApplicants??0,t),className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Limited Seats Available"})})]}),(0,a.jsxs)("div",{className:"flex justify-center px-3",children:[1===e.id&&(0,a.jsx)(l.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>ep(String(e.id)),children:"View details"}),(0,a.jsx)(O.A,{exam:e,hasApplied:e.hasApplied,isMaxLimitReached:e.isMaxLimitReached,hasAttempted:e.hasAttempted,onApplyClick:()=>ex(e)})]}),7===e.id&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-customOrange text-white font-semibold text-sm text-center p-3 flex items-center justify-center gap-2  border-orange-600 shadow-md ",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M12 20.5a8.5 8.5 0 100-17 8.5 8.5 0 000 17z"})}),(0,a.jsx)("span",{children:"This exam is only for those who have participated in the Uwhiz Super Kids Exam."})]})}),1===e.id?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-300 pt-2 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("span",{children:"Sponsored by"}),(0,a.jsx)(P.default,{src:"/nalanda.png",alt:"Nalanda Logo",height:60,width:60,className:"object-contain h-5 w-5"}),(0,a.jsx)("span",{className:"font-semibold",children:"Nalanda Vidhyalay"})]}):null]},e.id)})})]}),ec.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("h1",{className:"text-center text-4xl font-bold dark:text-white mt-10",children:["Past ",(0,a.jsx)("span",{className:"text-customOrange",children:"Exams"})]}),F?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"Loading exams..."}):0===ec.length?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"No past exams found."}):(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-6 p-4 sm:p-6 md:p-10 lg:p-20",children:ec.map(e=>{let t=e.UwhizPriceRank.find(e=>1===e.rank)?.price??0;return(0,a.jsxs)(i.Zp,{className:"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center px-3 py-2 space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105",children:e.exam_name}),4!==e.id&&6!==e.id&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xl font-bold",children:[(0,a.jsx)(N.qjb,{className:"text-xl text-customOrange"}),(0,a.jsxs)("span",{className:"dark:text-white",children:["1st Prize: ",(0,a.jsx)("span",{className:"text-customOrange",children:t})]})]})]}),(0,a.jsx)(E,{exam:e}),(0,a.jsxs)("div",{className:"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(m.lfF,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Total Questions: ",e.total_questions]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(m.O6N,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Marks: ",e.marks]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(m.w_X,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Duration: ",e.duration]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(f.A,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Coins: ",null!=e.coins_required?K?.hasDiscount?(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"line-through text-gray-500 text-xs",children:e.coins_required}),(0,a.jsx)("span",{className:"text-green-600 font-bold",children:(0,$.w)(e.coins_required,K.discountPercentage)}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["(",K.discountPercentage,"% off)"]})]}):e.coins_required:"Free"]})]})]})]}),5===e.id?(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Classes Joined"}),(0,a.jsx)(k.k,{value:100,className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Seats Full"})})]}):(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Student Joined"}),(0,a.jsx)(k.k,{value:100,className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Seats Full"})})]}),(0,a.jsxs)("div",{className:"flex justify-center px-3",children:[1===e.id&&(0,a.jsxs)("div",{className:"flex gap-2 w-full justify-center",children:[(0,a.jsx)(l.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>ep(String(e.id)),children:"View details"}),(0,a.jsx)(l.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>el.push("uwhiz-super-kids-result"),children:"View Result"})]}),3===e.id&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.$,{className:"bg-customOrange mx-5",onClick:er,children:"View Result"}),(0,a.jsx)(T,{open:H,onClose:en})]}),5===e.id?(0,a.jsx)(l.$,{className:"bg-customOrange mx-5",onClick:()=>el.push("/uwhiz-details/5"),children:"View Result"}):3!==e.id&&1!==e.id?(0,a.jsx)(O.A,{exam:e,hasApplied:e.hasApplied,isMaxLimitReached:e.isMaxLimitReached,hasAttempted:e.hasAttempted,onApplyClick:()=>ex(e)}):null]})]},e.id)})})]}),(0,a.jsx)("div",{className:"flex items-center justify-center px-4 py-6 dark:text-white",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>U(1),disabled:1===M,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(w.A,{})}),(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>U(e=>Math.max(e-1,1)),disabled:1===M,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(j.A,{})}),(0,a.jsxs)("span",{className:"text-sm",children:["Page ",M," of ",L]}),(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>U(e=>Math.min(e+1,L)),disabled:M===L,className:"hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(b.A,{})}),(0,a.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>U(L),disabled:M===L,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(v.A,{})})]})})]}),(0,a.jsx)(y.default,{})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58385:(e,t,s)=>{"use strict";s.d(t,{J:()=>r,w:()=>n});var a=s(28527);let r=async()=>{try{return(await a.S.get("/referral/discount/student")).data}catch(e){return{success:!1,error:`Failed to get discount info: ${e.response?.data?.message||e.message}`}}},n=(e,t)=>{let s=Math.round(Number(e)*t/100);return Number(e)-s}},61466:(e,t,s)=>{Promise.resolve().then(s.bind(s,33427))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63826:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},66874:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var a=s(60687);s(43210);var r=s(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},68325:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["uwhiz",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,29116)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/uwhiz/page",pathname:"/uwhiz",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,7013,3793,656,4255,4853,2800],()=>s(68325));module.exports=a})();