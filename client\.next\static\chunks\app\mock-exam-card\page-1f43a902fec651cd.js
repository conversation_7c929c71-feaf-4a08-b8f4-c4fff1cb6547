(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2540],{2070:(e,t,a)=>{Promise.resolve().then(a.bind(a,95838))},7583:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var r=a(95155);a(12115);var s=a(6874),l=a.n(s),i=a(66766),n=a(29911);let o=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:s}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:s,children:(0,r.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},s)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},8034:(e,t,a)=>{"use strict";a.d(t,{FU:()=>i,NL:()=>l,dS:()=>s});var r=a(55077);let s=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{let s=await r.S.get("/mock-exam-leaderboard/leaderboard/".concat(e,"?page=").concat(t,"&limit=").concat(a),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){var s,l;return{success:!1,error:"Failed to get mock exam leaderboard data: ".concat((null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.error)||e.message)}}},l=async()=>{try{let e=await r.S.get("/mock-exam-leaderboard/previous-day",{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:e.data}}catch(a){var e,t;return{success:!1,error:"Failed to get yesterday's top performers: ".concat((null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||a.message)}}},i=async(e,t,a)=>{try{let s=await r.S.post("/reactions",{studentId:e,reactionType:t,reactorId:a},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){var s,l;return{success:!1,error:"Failed to send reaction: ".concat((null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.error)||e.message)}}}},35376:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("book-check",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["path",{d:"m9 9.5 2 2 4-4",key:"1dth82"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>d});var r=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},86214:(e,t,a)=>{"use strict";a.d(t,{S:()=>l,q:()=>s});var r=a(55077);let s=async e=>{try{let t=await r.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam result: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message)}}},l=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};try{let l=new URLSearchParams({page:t.toString(),limit:a.toString(),...void 0!==s.isWeekly&&{isWeekly:s.isWeekly.toString()}}).toString(),i=await r.S.get("/mock-exam-result/".concat(e,"?").concat(l),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:i.data}}catch(e){var l,i;return{success:!1,error:"Failed to get mock exam result: ".concat((null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message)||e.message)}}}},95838:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var r=a(95155),s=a(70347),l=a(7583),i=a(66695),n=a(30285),o=a(66766);let c={src:"/_next/static/media/mockExamImage.b1be346b.svg"};var d=a(64315),x=a(93588),m=a(35695),u=a(19320),h=a(56671),g=a(12115),y=a(86214);function p(){let e=(0,m.useRouter)(),[t,a]=(0,g.useState)(!1),[s,l]=(0,g.useState)(null),[i,o]=(0,g.useState)(null),c=(0,g.useRef)(null),d=(0,g.useRef)(null);(0,g.useEffect)(()=>{try{let e=localStorage.getItem("student_data"),t=e?JSON.parse(e).id:null;o(t)}catch(e){console.error("Error retrieving studentId:",e),o(null)}},[]),(0,g.useEffect)(()=>((async()=>{if(!i){a(!0),l(null);return}try{let e=await (0,y.S)(i,1,1,{isWeekly:!1});if(e.success&&e.data.data.mockExamResults.length>0){let t=e.data.data.mockExamResults[0],r=new Date(t.createdAt).toISOString().split("T")[0],s=new Date().toISOString().split("T")[0];if(r===s){a(!0);let e=new Date,t=new Date;t.setDate(e.getDate()+1),t.setHours(0,0,0,0);let r=t.getTime()-e.getTime(),s=Math.ceil(r/1e3);l(s>0?s:null),c.current=setInterval(()=>{l(e=>null===e||e<=1?(clearInterval(c.current),null):e-1)},1e3),d.current=setTimeout(()=>{a(!1),l(null)},r)}else a(!1),l(null)}else a(!1),l(null)}catch(e){console.error("Error checking exam attempt:",e),h.toast.error("Failed to verify exam eligibility."),a(!0),l(null)}})(),()=>{c.current&&clearInterval(c.current),d.current&&clearTimeout(d.current)}),[i]);let x=()=>{if(null===s)return null;let e=Math.floor(s/3600),t=Math.floor(s%3600/60),a=s%60;return"".concat(e.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"))};return(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(n.$,{className:"w-full mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>{if(!i){h.toast.error("Please log in to attempt the exam."),e.push("/login");return}if(t&&s){h.toast.error("You can attempt the exam again in ".concat(x(),"."));return}e.push("/mock-test")},disabled:t||!i,children:"Try Daily Quiz"}),t&&s&&(0,r.jsxs)("p",{className:"text-center mt-2 text-gray-500 text-sm",children:["Next attempt available in: ",x()]})]})}var f=a(35376);function b(){let e=(0,m.useRouter)(),[t,a]=(0,g.useState)(!0),[s,l]=(0,g.useState)(null),[i,o]=(0,g.useState)(null),c=(0,g.useRef)(null),d=(0,g.useRef)(null);(0,g.useEffect)(()=>{try{let e=localStorage.getItem("student_data"),t=e?JSON.parse(e).id:null;o(t)}catch(e){console.error("Error retrieving studentId:",e),o(null)}},[]),(0,g.useEffect)(()=>((async()=>{let e=new Date,t=0===e.getDay();if(!i){a(!0),l(null);return}try{let r=new Date(e);r.setDate(e.getDate()-(0===e.getDay()?6:e.getDay()-1)),r.setHours(0,0,0,0);let s=new Date(e);s.setDate(e.getDate()+(0===e.getDay()?7:7-e.getDay())),s.setHours(0,0,0,0);let n=s.getTime()-e.getTime(),o=Math.ceil(n/1e3),x=await (0,y.S)(i,1,1,{isWeekly:!0});if(x.success&&x.data.data.mockExamResults.length>0){let e=x.data.data.mockExamResults[0];new Date(e.createdAt)>=r||!t?(a(!0),l(o>0?o:null),c.current=setInterval(()=>{l(e=>null===e||e<=1?(clearInterval(c.current),null):e-1)},1e3),d.current=setTimeout(()=>{a(!t),l(null)},n)):(a(!1),l(null))}else a(!t),t||(l(o>0?o:null),c.current=setInterval(()=>{l(e=>null===e||e<=1?(clearInterval(c.current),null):e-1)},1e3),d.current=setTimeout(()=>{a(!1),l(null)},n))}catch(e){console.error("Error checking exam attempt:",e),h.toast.error("Failed to verify exam eligibility."),a(!0),l(null)}})(),()=>{c.current&&clearInterval(c.current),d.current&&clearTimeout(d.current)}),[i]);let x=()=>{if(null===s)return null;let e=Math.floor(s/86400),t=Math.floor(s%86400/3600),a=Math.floor(s%3600/60),r=s%60;return e>0?"".concat(e,"d ").concat(t.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"),":").concat(r.toString().padStart(2,"0")):"".concat(t.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"),":").concat(r.toString().padStart(2,"0"))};return(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(n.$,{className:"w-full mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>{if(!i){h.toast.error("Please log in to attempt the exam."),e.push("/login");return}if(t&&s){h.toast.error("You can attempt the exam again in ".concat(x(),"."));return}if(0!==new Date().getDay()){h.toast.error("The weekly exam is only available on Sundays.");return}e.push("/mock-test?isWeekly=true")},disabled:t||!i,children:"Try Weekly Exam"}),t&&s&&(0,r.jsxs)("p",{className:"text-center mt-2 text-gray-500 text-sm",children:["You can attempt it after: ",x()]})]})}var v=a(8034);let j=()=>{let e=(0,m.useRouter)(),t=null;try{let e=localStorage.getItem("student_data");t=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),t=null}let[a,h]=(0,g.useState)([]),[y,j]=(0,g.useState)(!0),[w,N]=(0,g.useState)(null);return(0,g.useEffect)(()=>{(async()=>{try{j(!0);let e=await (0,v.NL)();e.success?h(e.data.data):N(e.error)}catch(e){N("Failed to load top performers ".concat(e.message))}finally{j(!1)}})()},[]),(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-800 dark:text-gray-100 transition-colors duration-300",children:[(0,r.jsx)(s.default,{}),(0,r.jsx)("section",{className:"bg-black py-16 flex justify-center items-center",children:(0,r.jsx)(u.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.8,ease:"easeOut"},className:"relative",children:(0,r.jsx)(o.default,{src:c.src,alt:"Current Affairs Quiz Logo",width:250,height:80,priority:!0,quality:100,className:"object-contain rounded-xl shadow-2xl"})})}),(0,r.jsxs)("section",{className:"text-center mt-12 px-4 sm:px-6",children:[(0,r.jsxs)(u.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-4xl sm:text-5xl font-extrabold tracking-tight text-gray-900 dark:text-white",children:["Daily ",(0,r.jsx)("span",{className:"text-amber-400",children:"Quiz"})]}),(0,r.jsx)(u.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"mt-4 text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Stay informed, test your knowledge, and earn exclusive rewards!"}),(0,r.jsxs)(u.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"mt-8 mx-auto w-full max-w-3xl bg-white dark:bg-gray-900/80 border border-amber-200 dark:border-amber-700/50 rounded-xl p-6 shadow-lg flex flex-col sm:flex-row items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDECD️"}),(0,r.jsxs)("p",{className:"text-base sm:text-lg font-semibold text-gray-800 dark:text-gray-100",children:["The ",(0,r.jsx)("span",{className:"text-amber-500",children:"Store"})," is now ",(0,r.jsx)("strong",{children:"LIVE"})," — redeem your coins for exciting rewards!"]})]}),(0,r.jsx)(n.$,{className:"bg-amber-500 hover:bg-amber-600 text-white font-semibold rounded-lg px-6 py-2.5 transition-all duration-300",onClick:()=>e.push("/store"),children:"Visit Store"})]})]}),(0,r.jsxs)("section",{className:"px-4 sm:px-6 md:px-8 lg:px-12 py-12 space-y-12",children:[(0,r.jsx)(u.P.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8,ease:"easeOut"},className:"py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-5xl mx-auto bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-3xl shadow-2xl p-6 sm:p-10 border border-gray-100 dark:border-gray-700/30",children:[(0,r.jsxs)(u.P.div,{className:"flex items-center justify-center gap-4 mb-10",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.2,ease:"easeOut"},children:[(0,r.jsx)(d.gt3,{className:"text-amber-400 text-3xl sm:text-4xl"}),(0,r.jsxs)("h2",{className:"text-2xl sm:text-3xl lg:text-4xl font-extrabold text-gray-900 dark:text-white tracking-tight",children:[(0,r.jsx)("span",{className:"bg-clip-text  text-amber-400",children:"Top Performers"})," ","of Yesterday"]})]}),y?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-8",children:[void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"flex flex-col items-center bg-gray-100 dark:bg-gray-800/50 rounded-2xl p-6 shadow-sm",children:[(0,r.jsx)("div",{className:"w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"}),(0,r.jsx)("div",{className:"mt-4 w-32 h-5 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"}),(0,r.jsxs)("div",{className:"mt-3 flex gap-3",children:[(0,r.jsx)("div",{className:"w-16 h-4 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"}),(0,r.jsx)("div",{className:"w-16 h-4 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"})]})]},t))}):w?(0,r.jsx)("p",{className:"text-center text-rose-500 font-semibold text-lg tracking-wide",children:w}):0===a.length?(0,r.jsx)("p",{className:"text-center text-gray-500 dark:text-gray-400 font-medium text-lg tracking-wide",children:"No data available for yesterday"}):(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-8",children:a.map((e,t)=>{var a;return(0,r.jsx)(u.P.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3*t,duration:.5,ease:"easeOut"},className:"flex flex-col items-center ".concat(0===t?"sm:order-2":1===t?"sm:order-1":"sm:order-3"),children:(0,r.jsxs)("div",{className:"relative flex flex-col items-center gap-3",children:[(0,r.jsxs)("div",{className:"rounded-full border-4 p-1 transition-all duration-500 ease-in-out ".concat(0===t?"border-amber-400 scale-110 shadow-xl":"border-amber-300 shadow-md"),children:[0===t&&(0,r.jsx)(x.GuV,{className:"absolute -top-8 left-1/2 -translate-x-1/2 text-amber-400 w-8 h-8"}),e.profilePhoto?(0,r.jsx)(o.default,{src:"".concat("http://localhost:4005/").concat(e.profilePhoto.replace(/\\/g,"/")),alt:"".concat(e.firstName||""," ").concat(e.lastName||""),width:96,height:96,className:"w-24 h-24 rounded-full object-cover border-2 border-amber-200 dark:border-gray-600"}):(0,r.jsx)("div",{className:"w-24 h-24 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 flex items-center justify-center text-gray-800 dark:text-white font-bold text-xl",children:(null===(a=e.firstName)||void 0===a?void 0:a[0])||"U"})]}),(0,r.jsx)("div",{className:"z-10 rounded-full flex items-center justify-center font-bold text-white mt-[-1.7rem] ".concat(0===t?"w-8 h-8 bg-amber-500 shadow-lg border-4 border-amber-500":1===t?"w-7 h-7 bg-amber-400 shadow border-4 border-amber-400":"w-6 h-6 bg-amber-300 shadow border-4 border-amber-300"),children:e.rank}),(0,r.jsxs)("p",{className:"text-lg font-semibold capitalize text-gray-900 dark:text-white mt-2 tracking-tight",children:[e.firstName," ",e.lastName]}),(0,r.jsxs)("div",{className:"mt-3 flex flex-wrap justify-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1.5 px-4 py-1.5 text-sm font-medium rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-700/50",children:[(0,r.jsx)(d.lHQ,{})," ",e.score]}),(0,r.jsxs)("div",{className:"flex items-center gap-1.5 px-4 py-1.5 text-sm font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700/50",children:["\uD83D\uDD25 ",e.streakCount]})]})]})},e.rank)})})]})}),(0,r.jsx)(u.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7,delay:.4},children:(0,r.jsxs)(i.Zp,{className:"max-w-5xl mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl p-6 sm:p-8",children:[(0,r.jsxs)(u.P.div,{className:"flex justify-center items-center gap-3 mb-6",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.5},children:[(0,r.jsx)(x.xnu,{className:"text-amber-400 text-4xl"}),(0,r.jsxs)("h2",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white",children:["Weekly ",(0,r.jsx)("span",{className:"text-amber-400",children:"Challenge"})]})]}),(0,r.jsx)(u.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.6},className:"text-base sm:text-lg text-gray-600 dark:text-gray-300 text-center mb-6",children:"Test your skills every Sunday for a chance to win big rewards!"}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[{icon:d.w_X,text:"Every Sunday"},{icon:d.O6N,text:"25 Questions"},{icon:d.cEG,content:(0,r.jsxs)("span",{className:"flex flex-col items-start text-base font-semibold",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1 text-gray-500 dark:text-gray-400 text-sm line-through",children:[(0,r.jsx)(u.P.div,{whileHover:{scale:1.1},transition:{duration:.2},children:(0,r.jsx)(o.default,{src:"/uest_coin.png",alt:"Coin",width:14,height:14,sizes:"(max-width: 640px) 14px, 16px",loading:"lazy",className:"inline-block"})}),"9 coins"]}),(0,r.jsx)("strong",{className:"text-amber-500 dark:text-amber-400",children:"Free Entry"})]})},{icon:x.gPQ,text:"Earn Coins"}].map((e,t)=>(0,r.jsxs)(u.P.div,{className:"flex items-center gap-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 shadow-sm transition-all duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.7+.1*t},whileHover:{scale:1.02},children:[(0,r.jsx)(e.icon,{className:"text-amber-400 text-xl flex-shrink-0"}),e.content||(0,r.jsx)("span",{className:"text-base font-medium",children:e.text})]},t))}),(0,r.jsx)(u.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.2},className:"flex justify-center",children:(0,r.jsx)(b,{})})]})}),(0,r.jsx)(u.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7,delay:.5},children:(0,r.jsxs)(i.Zp,{className:"max-w-5xl mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl p-6 sm:p-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"space-y-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5",children:[{icon:d.O6N,text:"Questions: 10"},{icon:x.gPQ,text:"Badges: Streak & Rank-Based"},{icon:d.w_X,text:"Duration: 8 min"},{icon:d.cEG,text:"Earn Up to: 5 Coins"},{icon:f.A,text:"Free Gift: Top 3 students get free classmate books"}].map((e,t)=>(0,r.jsxs)(u.P.div,{className:"flex items-center gap-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-all duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.7+.1*t},whileHover:{scale:1.02},children:[(0,r.jsx)(e.icon,{className:"text-amber-400 text-xl"}),(0,r.jsx)("span",{className:"text-base font-medium",children:e.text})]},t))}),(0,r.jsxs)("div",{className:"flex flex-col justify-between space-y-6",children:[(0,r.jsx)(u.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.2},children:(0,r.jsx)(p,{})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 text-center",children:"Attempt once every 24 hours to earn coins & build your streak!"}),(0,r.jsxs)(u.P.div,{className:"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5 space-y-4 text-gray-800 dark:text-gray-200",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.3},children:[(0,r.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Coin Rewards:"}),(0,r.jsx)("ul",{className:"list-none space-y-2",children:["0 coins for 50% score","1 coin for 60% score","2 coins for 70% score","3 coins for 80% score","4 coins for 90% score","5 coins for 100% score"].map((e,t)=>(0,r.jsxs)(u.P.li,{className:"flex items-center gap-2 hover:text-amber-400 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-400 rounded-full"}),e]},t))}),(0,r.jsxs)("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:[(0,r.jsx)("strong",{children:"Streak Bonus:"})," +1 coin per daily attempt"]})]})]})]}),(0,r.jsx)("div",{className:"mt-8 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4",children:[{src:"/scholer.svg",alt:"100 Coins",text:"100 Coins"},{src:"/Mastermind.svg",alt:"500 Coins",text:"500 Coins"},{src:"/Achiever.svg",alt:"1000 Coins",text:"1000 Coins"},{src:"/Perfect Month.svg",alt:"30 Days Streak",text:"30 Days Streak"},{src:"/Perfect Year.svg",alt:"365 Days Streak",text:"365 Days Streak"},{src:"/Streak.svg",alt:"Daily Streak",text:"Daily Streak"}].map((e,t)=>(0,r.jsxs)(u.P.div,{className:"flex flex-col items-center bg-white dark:bg-gray-800/50 p-3 rounded-lg shadow-sm",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.4+.1*t},whileHover:{scale:1.05},children:[(0,r.jsx)(o.default,{src:e.src,alt:e.alt,width:40,height:40}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 dark:text-gray-200 mt-1 text-center",children:e.text})]},t))}),(0,r.jsxs)("div",{className:"mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,r.jsx)(u.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.8},children:(0,r.jsx)(n.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-amber-500 hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-base py-3",onClick:()=>e.push("/mock-exam-result/".concat(t)),children:"View Quiz Results & Earned Coins"})}),(0,r.jsx)(u.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.9},children:(0,r.jsx)(n.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-amber-500 hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-base py-3",onClick:()=>e.push("/Leader-Board"),children:"View Leaderboards"})})]})]})})]}),(0,r.jsx)(l.default,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6446,7672,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,2265,347,8441,1684,7358],()=>t(2070)),_N_E=e.O()}]);