(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6676],{14050:(e,t,s)=>{"use strict";s.d(t,{b:()=>c});var r=s(12115);s(47650);var a=s(66634),o=s(95155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,a.TL)(`Primitive.${t}`),d=r.forwardRef((e,r)=>{let{asChild:a,...d}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(a?s:t,{...d,ref:r})});return d.displayName=`Primitive.${t}`,{...e,[t]:d}},{}),n="horizontal",i=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var s;let{decorative:r,orientation:a=n,...l}=e,c=(s=a,i.includes(s))?a:n;return(0,o.jsx)(d.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...l,ref:t})});l.displayName="Separator";var c=l},22346:(e,t,s)=>{"use strict";s.d(t,{Separator:()=>d});var r=s(95155);s(12115);var a=s(14050),o=s(59434);function d(e){let{className:t,orientation:s="horizontal",decorative:d=!0,...n}=e;return(0,r.jsx)(a.b,{"data-slot":"separator-root",decorative:d,orientation:s,className:(0,o.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...n})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>i,r:()=>n});var r=s(95155);s(12115);var a=s(66634),o=s(74466),d=s(59434);let n=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:s,size:o,asChild:i=!1,...l}=e,c=i?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,d.cn)(n({variant:s,size:o,className:t})),...l})}},39163:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var r=s(95155),a=s(12115),o=s(55077),d=s(30285),n=s(34540),i=s(45436),l=s(94314),c=s(56671),u=s(66695);let m=()=>{let[e,t]=(0,a.useState)(""),s=(0,a.useRef)(null),[m,p]=(0,a.useState)(null),[h,g]=(0,a.useState)(!1),[v,x]=(0,a.useState)(""),f=(0,n.wA)(),b=JSON.parse(localStorage.getItem("user")||"{}"),y=(0,n.d4)(e=>e.class.classData),j=e=>{let t=e.address_components||[],s=null,r=null,a=null,o=null;return t.forEach(e=>{let t=e.types;(t.includes("locality")||t.includes("sublocality"))&&(s=e.long_name),t.includes("administrative_area_level_1")&&(r=e.long_name),t.includes("postal_code")&&(a=e.long_name),t.includes("country")&&(o=e.long_name)}),{address:e.formatted_address,city:s,state:r,postcode:a,country:o,latitude:e.geometry.location.lat(),longitude:e.geometry.location.lng()}},S=e=>{p(e)},N=async()=>{if(m)try{var e;g(!0),x("");let t=await o.S.post("/classes-profile/address",{fullAddress:m.address,city:m.city,state:m.state,postcode:m.postcode,country:m.country,latitude:m.latitude,longitude:m.longitude,classId:null==y?void 0:y.id});(null===(e=t.data)||void 0===e?void 0:e.success)&&(await f((0,i.V)(b.id)),c.toast.success("Address saved successfully"),f((0,l.ac)(l._3.ADDRESS)),x("Address saved successfully."))}catch(e){console.error("Error saving address:",e),x("Failed to save address.")}finally{g(!1)}};return(0,a.useEffect)(()=>{let e=()=>{if(window.google&&window.google.maps&&s.current){let e=new window.google.maps.places.Autocomplete(s.current,{types:["geocode","establishment"],componentRestrictions:{country:"in"}});e.addListener("place_changed",()=>{let s=e.getPlace();if(s.geometry&&s.formatted_address){let e=j(s);t(s.formatted_address),S(e)}})}};return window.google&&window.google.maps?e():(()=>{let t=document.createElement("script");t.src="https://maps.googleapis.com/maps/api/js?key=".concat("AIzaSyCcuT3cVwtQU32bqhIXpDqy92oV0KDditA","&libraries=places"),t.async=!0,t.defer=!0,document.head.appendChild(t),t.onload=e})(),()=>{document.querySelectorAll('script[src*="maps.googleapis.com"]').forEach(e=>e.remove())}},[]),(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(null==y?void 0:y.address)&&(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Previous Address"}),(0,r.jsxs)(u.Zp,{className:"bg-muted/30",children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{className:"text-base font-semibold",children:"Saved Address"})}),(0,r.jsxs)(u.Wu,{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Address:"})," ",y.address.fullAddress]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"City:"})," ",y.address.city||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"State:"})," ",y.address.state||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Postcode:"})," ",y.address.postcode||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Country:"})," ",y.address.country||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Latitude:"})," ",y.address.latitude]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Longitude:"})," ",y.address.longitude]})]})]})]}),(0,r.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700",children:"Address"}),(0,r.jsx)("input",{id:"address",type:"text",ref:s,value:e,onChange:e=>t(e.target.value),placeholder:"Enter your address",className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"}),m&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Selected Place"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Address:"})," ",m.address]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"City:"})," ",m.city||"N/A"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"State:"})," ",m.state||"N/A"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Postcode:"})," ",m.postcode||"N/A"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Country:"})," ",m.country||"N/A"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Latitude:"})," ",m.latitude]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Longitude:"})," ",m.longitude]}),(0,r.jsx)(d.$,{onClick:N,className:"mt-4 px-4 py-2",disabled:h,children:h?"Saving...":"Save Address"}),v&&(0,r.jsx)("p",{className:"mt-2 text-sm text-green-600",children:v})]})]})}},45436:(e,t,s)=>{"use strict";s.d(t,{V:()=>a});var r=s(55077);let a=(0,s(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:s}=t;try{return(await r.S.get("/classes/details/".concat(e))).data}catch(e){var a;return s((null===(a=e.response)||void 0===a?void 0:a.data)||"Fetch failed")}})},55077:(e,t,s)=>{"use strict";s.d(t,{S:()=>d});var r=s(23464),a=s(56671);let o="http://localhost:4005/api/v1";console.log("Axios baseURL:",o);let d=r.A.create({baseURL:o,headers:{"Content-Type":"application/json"},withCredentials:!0});d.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":o;{let t=localStorage.getItem("studentToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),d.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(a.toast.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,s)=>{"use strict";s.d(t,{MB:()=>n,ZO:()=>d,cn:()=>o,wR:()=>l,xh:()=>i});var r=s(52596),a=s(39688);function o(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}let d=()=>localStorage.getItem("studentToken"),n=()=>{localStorage.removeItem("studentToken")},i=()=>!!d(),l=()=>{if(d())return{isAuth:!0,userType:"STUDENT"};{let e=localStorage.getItem("user");if(e)try{let t=JSON.parse(e);if(t&&t.id)return{isAuth:!0,userType:"CLASS"}}catch(e){console.error("Error parsing user data:",e)}}return{isAuth:!1,userType:null}}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>l,ZB:()=>n,Zp:()=>o,aR:()=>d,wL:()=>c});var r=s(95155);s(12115);var a=s(59434);function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},94314:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>i,_3:()=>a,ac:()=>d});var r=s(51990),a=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let o=(0,r.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let s=t.payload;e.completedForms[s]||(e.completedForms[s]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:d,setCurrentStep:n}=o.actions,i=o.reducer},97482:(e,t,s)=>{Promise.resolve().then(s.bind(s,39163)),Promise.resolve().then(s.bind(s,22346))}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4540,1990,8441,1684,7358],()=>t(97482)),_N_E=e.O()}]);