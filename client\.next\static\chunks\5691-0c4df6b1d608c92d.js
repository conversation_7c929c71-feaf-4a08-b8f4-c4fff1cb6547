"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5691],{7583:(e,t,r)=>{r.d(t,{default:()=>o});var s=r(95155);r(12115);var a=r(6874),i=r.n(a),n=r(66766),l=r(29911);let o=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(i(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(n.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:l.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:l.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:l.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:l.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:l.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:l.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:l.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:r,label:a}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(i(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,s.jsx)(r,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(i(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(n.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(i(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(i(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},26126:(e,t,r)=>{r.d(t,{E:()=>o});var s=r(95155);r(12115);var a=r(66634),i=r(74466),n=r(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:i=!1,...o}=e,c=i?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:r}),t),...o})}},37108:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},66695:(e,t,r)=>{r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>d});var s=r(95155);r(12115);var a=r(59434);function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},68856:(e,t,r)=>{r.d(t,{E:()=>i});var s=r(95155),a=r(59434);function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",t),...r})}},74436:(e,t,r)=>{r.d(t,{k5:()=>d});var s=r(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=s.createContext&&s.createContext(a),n=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var s,a,i;s=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):s[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(h,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function h(e){var t=t=>{var r,{attr:a,size:i,title:o}=e,d=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)r=i[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,n),h=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:h,width:h,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==i?s.createElement(i.Consumer,null,e=>t(e)):t(a)}}}]);