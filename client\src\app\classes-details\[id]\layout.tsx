import { Metadata } from 'next';
import { generateClassesMetadata, generateClassesStructuredData, fetchTeacherData } from './metadata';

interface ClassesLayoutProps {
  children: React.ReactNode;
  params: { id: string };
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  return await generateClassesMetadata(params.id);
}

export default async function ClassesDetailsLayout({ children, params }: ClassesLayoutProps) {
  // Fetch teacher data for structured data
  const teacherData = await fetchTeacherData(params.id);
  
  return (
    <>
      {/* Structured Data for SEO */}
      {teacherData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ 
            __html: JSON.stringify(generateClassesStructuredData(teacherData, params.id)) 
          }}
        />
      )}
      {children}
    </>
  );
}
