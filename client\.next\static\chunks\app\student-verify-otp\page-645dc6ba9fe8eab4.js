(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6976],{4258:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(95155),a=s(70347),l=s(7583),i=s(12115),n=s(30285),o=s(55594),d=s(62177),c=s(90221),m=s(62523),u=s(55365),x=s(85339),h=s(51154),f=s(75937),g=s(56671),v=s(60723),p=s(35695);let j=o.z.object({otp:o.z.string().regex(/^\d{6}$/,"OTP must be a 6-digit number"),contactNo:o.z.string().regex(/^\d{10}$/,"Invalid mobile number"),email:o.z.string().optional().refine(e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),"Invalid email address"),firstName:o.z.string().optional().refine(e=>!e||/^[a-zA-Z]+$/.test(e),"Invalid first name"),lastName:o.z.string().optional().refine(e=>!e||/^[a-zA-Z]+$/.test(e),"Invalid last name"),referralCode:o.z.string().optional()}),b=e=>{let{message:t}=e;return t?(0,r.jsxs)(u.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)(u.TN,{className:"text-red-500",children:t})]}):null};function N(){let e=(0,p.useRouter)(),t=(0,p.useSearchParams)(),[s,o]=(0,i.useState)(!1),[u,x]=(0,i.useState)(""),[N,w]=(0,i.useState)(!1),[y,I]=(0,i.useState)(120),T=t.get("contactNo")||"",k=t.get("email")||"",P=t.get("flow")||"login",S=t.get("firstName")||"",C=t.get("lastName")||"",O=t.get("referralCode")||"",F="register"===P,z="otpTimer_".concat(T,"_").concat(P),A=(0,d.mN)({resolver:(0,c.u)(j),defaultValues:{otp:"",contactNo:T,email:k,firstName:S,lastName:C,referralCode:O},mode:"onChange"}),{formState:_,trigger:R,setValue:E}=A;(0,i.useEffect)(()=>{if(!T||!/^\d{10}$/.test(T)){x("Invalid mobile number. Please try again."),g.toast.error("Invalid mobile number. Redirecting to login."),setTimeout(()=>e.push("/student-login"),2e3);return}E("contactNo",T,{shouldValidate:!0}),E("email",k,{shouldValidate:!0}),E("firstName",S,{shouldValidate:!0}),E("lastName",C,{shouldValidate:!0}),E("referralCode",O,{shouldValidate:!0}),R(),localStorage.removeItem(z),I(120)},[T,k,S,C,O,e,E,R,z]),(0,i.useEffect)(()=>{let e=null;return y>0&&(e=setInterval(()=>{I(t=>{let s=t-1;return s<=0?(localStorage.removeItem(z),clearInterval(e),0):(localStorage.setItem(z,s.toString()),s)})},1e3)),()=>{e&&clearInterval(e)}},[z,y]);let V=async s=>{o(!0),x("");try{let r=await (0,v.RY)({contactNo:s.contactNo,otp:s.otp,...s.email&&{email:s.email},...F&&{firstName:s.firstName,lastName:s.lastName,referralCode:s.referralCode}});if(!1===r.success){x(r.message||"OTP verification failed"),g.toast.error(r.message||"OTP verification failed");return}if(r.data){let{userId:a,contactNo:l,firstName:i,lastName:n,token:o}=r.data;localStorage.setItem("studentToken",o),localStorage.setItem("student_data",JSON.stringify({id:a,contactNo:l,firstName:i,lastName:n})),localStorage.removeItem(z),g.toast.success(s.email&&!F?"Contact number updated and login successful":F?"Registration successful":"Login successful");let d=t.get("redirect")||"/";e.push(d)}}catch(t){var r,a;let e=(null==t?void 0:null===(a=t.response)||void 0===a?void 0:null===(r=a.data)||void 0===r?void 0:r.message)||"Something went wrong";x(e),g.toast.error(e)}finally{o(!1)}},B=async()=>{w(!0),x("");try{let e=await (0,v.Ty)({contactNo:T,firstName:S||"User"});if(!1===e.success){x(e.message||"Failed to resend OTP"),g.toast.error(e.message||"Failed to resend OTP");return}I(120),localStorage.setItem(z,"120"),g.toast.success("New OTP sent successfully")}catch(r){var e,t;let s=(null==r?void 0:null===(t=r.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||"Something went wrong";s.includes("Too many OTP requests")?g.toast.error("Too many OTP requests. Please wait before trying again."):g.toast.error(s),x(s)}finally{w(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"w-full max-w-md bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Verify OTP"}),(0,r.jsxs)("p",{className:"text-[#ff914d]",children:["Enter the 6-digit OTP sent to ",T]}),k&&(0,r.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["Linked to email: ",k]})]}),(0,r.jsx)("div",{children:(0,r.jsx)(f.lV,{...A,children:(0,r.jsxs)("form",{onSubmit:A.handleSubmit(V),className:"space-y-6",children:[u&&(0,r.jsx)(b,{message:u}),(0,r.jsx)(f.zB,{control:A.control,name:"otp",render:e=>{let{field:t}=e;return(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(f.lR,{className:"text-gray-700 font-medium",children:"OTP"}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)("div",{className:"flex space-x-2 justify-center",children:[...Array(6)].map((e,s)=>(0,r.jsx)(m.p,{type:"text",maxLength:1,className:"w-12 h-12 text-center text-lg font-medium border-gray-200 focus:border-[#ff914d] focus:ring-[#ff914d]/20 rounded-lg",value:t.value[s]||"",onChange:e=>{let r=(t.value||"").split("");if(r[s]=e.target.value.replace(/\D/g,""),t.onChange(r.join("")),R("otp"),e.target.value&&s<5){var a;null===(a=document.getElementById("otp-".concat(s+1)))||void 0===a||a.focus()}},onKeyDown:e=>{if("Backspace"===e.key&&!t.value[s]&&s>0){var r;null===(r=document.getElementById("otp-".concat(s-1)))||void 0===r||r.focus()}},id:"otp-".concat(s)},s))})}),(0,r.jsx)(f.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(f.zB,{control:A.control,name:"contactNo",render:e=>{let{field:t}=e;return(0,r.jsx)(f.eI,{hidden:!0,children:(0,r.jsx)(f.MJ,{children:(0,r.jsx)(m.p,{...t})})})}}),(0,r.jsx)(f.zB,{control:A.control,name:"email",render:e=>{let{field:t}=e;return(0,r.jsx)(f.eI,{hidden:!0,children:(0,r.jsx)(f.MJ,{children:(0,r.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,r.jsx)(f.zB,{control:A.control,name:"firstName",render:e=>{let{field:t}=e;return(0,r.jsx)(f.eI,{hidden:!0,children:(0,r.jsx)(f.MJ,{children:(0,r.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,r.jsx)(f.zB,{control:A.control,name:"lastName",render:e=>{let{field:t}=e;return(0,r.jsx)(f.eI,{hidden:!0,children:(0,r.jsx)(f.MJ,{children:(0,r.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,r.jsx)(f.zB,{control:A.control,name:"referralCode",render:e=>{let{field:t}=e;return(0,r.jsx)(f.eI,{hidden:!0,children:(0,r.jsx)(f.MJ,{children:(0,r.jsx)(m.p,{...t,value:t.value||""})})})}}),(0,r.jsx)(n.$,{type:"submit",className:"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors",disabled:s||!_.isValid,children:s?(0,r.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):"Verify OTP"}),(0,r.jsx)(n.$,{type:"button",variant:"ghost",className:"w-full text-[#ff914d] hover:text-[#ff914d]/90",disabled:N||y>0,onClick:B,children:N?(0,r.jsx)(h.A,{className:"h-5 w-5 animate-spin"}):y>0?"Resend OTP in ".concat(y,"s"):"Resend OTP"})]})})})]})}),(0,r.jsx)(l.default,{})]})}function w(){return(0,r.jsx)(i.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"})}),children:(0,r.jsx)(N,{})})}},7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(95155);s(12115);var a=s(6874),l=s.n(a),i=s(66766),n=s(29911);let o=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},42262:(e,t,s)=>{Promise.resolve().then(s.bind(s,4258))},55365:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>d});var r=s(95155),a=s(12115),l=s(74466),i=s(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef((e,t)=>{let{className:s,variant:a,...l}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(n({variant:a}),s),...l})});o.displayName="Alert",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",s),...a})}).displayName="AlertTitle";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",s),...a})});d.displayName="AlertDescription"},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var r=s(95155);s(12115);var a=s(59434);function l(e){let{className:t,type:s,...l}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},75937:(e,t,s)=>{"use strict";s.d(t,{lV:()=>c,MJ:()=>v,Rr:()=>p,zB:()=>u,eI:()=>f,lR:()=>g,C5:()=>j});var r=s(95155),a=s(12115),l=s(66634),i=s(62177),n=s(59434),o=s(24265);function d(e){let{className:t,...s}=e;return(0,r.jsx)(o.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}let c=i.Op,m=a.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(m.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},x=()=>{let e=a.useContext(m),t=a.useContext(h),{getFieldState:s}=(0,i.xW)(),r=(0,i.lN)({name:e.name}),l=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},h=a.createContext({});function f(e){let{className:t,...s}=e,l=a.useId();return(0,r.jsx)(h.Provider,{value:{id:l},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",t),...s})})}function g(e){let{className:t,...s}=e,{error:a,formItemId:l}=x();return(0,r.jsx)(d,{"data-slot":"form-label","data-error":!!a,className:(0,n.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...s})}function v(e){let{...t}=e,{error:s,formItemId:a,formDescriptionId:i,formMessageId:n}=x();return(0,r.jsx)(l.DX,{"data-slot":"form-control",id:a,"aria-describedby":s?"".concat(i," ").concat(n):"".concat(i),"aria-invalid":!!s,...t})}function p(e){let{className:t,...s}=e,{formDescriptionId:a}=x();return(0,r.jsx)("p",{"data-slot":"form-description",id:a,className:(0,n.cn)("text-muted-foreground text-sm",t),...s})}function j(e){var t;let{className:s,...a}=e,{error:l,formMessageId:i}=x(),o=l?String(null!==(t=null==l?void 0:l.message)&&void 0!==t?t:""):a.children;return o?(0,r.jsx)("p",{"data-slot":"form-message",id:i,className:(0,n.cn)("text-destructive text-sm",s),...a,children:o}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,1342,6919,347,8441,1684,7358],()=>t(42262)),_N_E=e.O()}]);