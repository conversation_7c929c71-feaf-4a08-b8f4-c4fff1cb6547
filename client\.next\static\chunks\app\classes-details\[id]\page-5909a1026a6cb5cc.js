(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6968],{7084:(e,t,s)=>{"use strict";s.d(t,{default:()=>M});var a=s(95155),r=s(12115),l=s(66766),i=s(35695),n=s(64315),o=s(12515),c=s(93347),d=s(51013),m=s(30285),u=s(70347),h=s(7583),x=s(55077),g=s(20185),f=s(34540),p=s(38564),v=s(66932),j=s(62525),w=s(59409),N=s(56671),b=s(90010),y=s(55594),S=s(62177),E=s(90221),A=s(75937);let k=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5;try{return(await x.S.get("/reviews/class/".concat(e),{params:{page:t,limit:s}})).data}catch(e){throw Error("Failed to fetch reviews for class: ".concat(e.message))}},C=async e=>{try{return(await x.S.get("/reviews/average/".concat(e))).data.averageRating}catch(e){throw Error("Failed to get average rating: ".concat(e.message))}},F=async e=>{try{return(await x.S.post("/reviews",e)).data}catch(n){var t,s,a,r;let e=(null===(s=n.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||n.message,l=(null===(r=n.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.alreadyReviewed)||!1,i=Error(e);throw i.alreadyReviewed=l,i}},I=async e=>{try{return(await x.S.delete("/reviews/".concat(e))).data}catch(e){throw Error("Failed to delete review: ".concat(e.message))}};var O=s(59434),D=s(91394);let P=y.z.object({message:y.z.string().min(10,"Message must be at least 10 characters").max(500,"Message cannot exceed 500 characters"),rating:y.z.number().min(1,"Please select a rating"),classId:y.z.string().min(1,"Class ID is required")}),L=e=>{let{classId:t,userData:s,onReviewSubmit:l}=e,[i,n]=(0,r.useState)(0),[o,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(!1),[h,x]=(0,r.useState)(null),[g,f]=(0,r.useState)("ALL"),[y,C]=(0,r.useState)([]),[L,T]=(0,r.useState)(1),[R,_]=(0,r.useState)(!1),[z,B]=(0,r.useState)(!1),[M,V]=(0,r.useState)(!1);(0,r.useEffect)(()=>{V((0,O.xh)());let e=()=>{V((0,O.xh)())};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[]);let W=(0,S.mN)({resolver:(0,E.u)(P),defaultValues:{message:"",rating:0,classId:t}}),U=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ALL";try{let r=await k(t,e,5),l="",i=localStorage.getItem("student_data");if(i)try{let e=JSON.parse(i);e.firstName&&e.lastName&&(l="".concat(e.firstName," ").concat(e.lastName))}catch(e){console.error("Error parsing student data:",e)}!l&&s&&s.firstName&&s.lastName&&(l="".concat(s.firstName," ").concat(s.lastName));let n=r.reviews.map(e=>e.studentName&&!e.message.startsWith(e.studentName)?{...e,message:"".concat(e.studentName,": ").concat(e.message)}:e);if(1===e){if("ALL"===a)C(n);else if("ME"===a&&l){let e=n.filter(e=>{if(!e.studentId)return e.message.split(":")[0]===l;{let t=JSON.parse(i||"{}");return e.studentId===t.id}});C(e)}}else if("ALL"===a)C(e=>[...e,...n]);else if("ME"===a&&l){let e=n.filter(e=>{if(!e.studentId)return e.message.split(":")[0]===l;{let t=JSON.parse(i||"{}");return e.studentId===t.id}});C(t=>[...t,...e])}return _(r.hasMore),T(r.currentPage),r}catch(e){return console.error("Error fetching reviews:",e),N.toast.error("Failed to fetch reviews"),null}},[t,s]),q=async()=>{B(!0);try{await U(L+1,g)}finally{B(!1)}};(0,r.useEffect)(()=>{T(1),U(1,g)},[g,t,U]);let J=e=>{x(e),u(!0)},Y=async()=>{if(!h){console.error("No review ID to delete");return}if(!(0,O.xh)()){N.toast.error("You must be logged in to delete a review"),u(!1),x(null);return}try{await I(h),N.toast.success("Review deleted successfully!"),await U(1,g),l&&l()}catch(e){console.error("Error deleting review:",e),N.toast.error(e.message||"Failed to delete review")}finally{u(!1),x(null)}},Z=e=>{n(e),W.setValue("rating",e)},H=async e=>{c(!0);try{let a=localStorage.getItem("student_data"),r="",i="";if(a)try{let e=JSON.parse(a);e.firstName&&e.lastName&&(r="".concat(e.firstName," ").concat(e.lastName),i=e.id)}catch(e){console.error("Error parsing student data:",e)}if(!r&&s&&s.firstName&&s.lastName&&(r="".concat(s.firstName," ").concat(s.lastName)),!(0,O.xh)()){N.toast.error("Only students can submit reviews");return}let o={classId:e.classId,rating:e.rating,message:e.message,studentName:r,studentId:i};await F(o),N.toast.success("Review submitted successfully! The class owner will be notified."),W.reset({message:"",rating:0,classId:t}),n(0),await U(1,g),l&&l()}catch(e){console.error("Error submitting review:",e),N.toast.error(e.message||"Failed to submit review")}finally{c(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.Lt,{open:d,onOpenChange:u,children:(0,a.jsxs)(b.EO,{className:"dark:bg-siderbar",children:[(0,a.jsxs)(b.wd,{children:[(0,a.jsx)(b.r7,{children:"Are you sure?"}),(0,a.jsx)(b.$v,{children:"This action cannot be undone. This will permanently delete your testimonial."})]}),(0,a.jsxs)(b.ck,{children:[(0,a.jsx)(b.Zr,{className:"dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",children:"Cancel"}),(0,a.jsx)(b.Rx,{onClick:Y,className:"bg-red-500 text-white hover:bg-red-600",children:"Delete"})]})]})}),(0,a.jsxs)("div",{className:"w-full max-w-9xl mx-auto",children:[M?(0,a.jsxs)("div",{className:"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Write a Review"}),(0,a.jsx)(A.lV,{...W,children:(0,a.jsxs)("form",{onSubmit:W.handleSubmit(H),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Your Rating"}),(0,a.jsx)("div",{className:"flex gap-1",children:[1,2,3,4,5].map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>Z(e),className:"focus:outline-none",children:(0,a.jsx)(p.A,{className:"w-8 h-8 ".concat(e<=i?"fill-[#FD904B] text-[#FD904B]":"text-gray-300")})},e))}),W.formState.errors.rating&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:W.formState.errors.rating.message})]}),(0,a.jsx)(A.zB,{control:W.control,name:"message",render:e=>{let{field:t}=e;return(0,a.jsxs)(A.eI,{className:"mb-6",children:[(0,a.jsx)(A.lR,{className:"block text-sm font-medium mb-2",children:"Your Message"}),(0,a.jsx)(A.MJ,{children:(0,a.jsx)("textarea",{...t,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FD904B] focus:border-transparent",rows:4,placeholder:"Share your experience (10-500 characters)..."})}),(0,a.jsx)(A.C5,{})]})}}),(0,a.jsx)("button",{type:"submit",disabled:o,className:"w-full bg-[#FD904B] text-white py-2 px-4 rounded-lg hover:bg-[#FD904B]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:o?"Submitting...":"Submit Review"})]})})]}):(0,a.jsx)("div",{className:"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8 text-center",children:(0,a.jsx)("p",{className:"text-lg mb-4",children:"Please log in as a student to write a review"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Class Reviews"}),M&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)(w.l6,{value:g,onValueChange:e=>f(e),children:[(0,a.jsx)(w.bq,{className:"w-[120px]",children:(0,a.jsx)(w.yv,{placeholder:"Filter"})}),(0,a.jsxs)(w.gC,{children:[(0,a.jsx)(w.eb,{value:"ALL",children:"All Reviews"}),(0,a.jsx)(w.eb,{value:"ME",children:"My Reviews"})]})]})]})]}),y.length>0?(0,a.jsxs)("div",{className:"space-y-4",children:[y.map(e=>{var t,s;return(0,a.jsxs)("div",{className:"dark:bg-slidebar border border-gray-400 rounded-lg shadow-sm p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)("div",{className:"relative w-12 h-12 rounded-full overflow-hidden border-2 border-[#FD904B]/80 flex justify-center items-center",children:(0,a.jsx)(D.eu,{children:(0,a.jsx)(D.q5,{className:"bg-white text-black",children:(null==e?void 0:null===(t=e.student)||void 0===t?void 0:t.firstName)&&(null==e?void 0:null===(s=e.student)||void 0===s?void 0:s.lastName)?"".concat(e.student.firstName[0]).concat(e.student.lastName[0]).toUpperCase():"ST"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-600 dark:text-white",children:e.studentName||e.message.split(":")[0]}),M&&(()=>{let t="",s="";try{let e=localStorage.getItem("student_data");if(e){let a=JSON.parse(e);a.firstName&&a.lastName&&(t="".concat(a.firstName," ").concat(a.lastName),s=a.id)}}catch(e){console.error("Error parsing student data:",e)}return e.studentId&&s&&e.studentId===s||t&&(e.studentName&&e.studentName===t||e.message.split(":")[0]===t)?(0,a.jsx)("button",{onClick:()=>J(e.id),className:"text-red-500 hover:text-red-700",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})}):null})()]}),(0,a.jsx)("div",{className:"flex items-center gap-1 mt-1",children:[1,2,3,4,5].map((t,s)=>(0,a.jsx)(p.A,{className:"w-4 h-4 ".concat(s<e.rating?"fill-[#FD904B] text-[#FD904B]":"text-gray-300")},s))})]})]}),(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300 break-words mb-3",children:e.message.includes(":")?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("span",{children:e.message.split(":").slice(1).join(":").trim()})}):e.message}),(0,a.jsx)("div",{className:"flex justify-between items-center text-sm text-gray-500",children:(0,a.jsxs)("span",{children:["Posted on ",new Date(e.createdAt).toLocaleDateString()]})})]},e.id)}),R&&(0,a.jsx)("div",{className:"flex justify-center mt-6",children:(0,a.jsx)(m.$,{onClick:q,variant:"outline",className:"px-6 py-2 border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10",disabled:z,children:z?"Loading...":"Load More"})})]}):(0,a.jsx)("p",{className:"text-muted-foreground",children:"ALL"===g?"No reviews yet.":"You haven't written any reviews yet."})]})]})]})};var T=s(12054);let R=async(e,t)=>{try{return(await x.S.post("/class-view-log/log-view",{classId:e,studentId:t})).data}catch(e){var s,a;return console.error("Error logging class view:",e),{success:!1,message:(null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to log class view"}}};var _=s(54165),z=s(51154);let B=[{key:"education",label:"Education",icon:(0,a.jsx)(n.VHr,{})},{key:"work",label:"Work Experience",icon:(0,a.jsx)(o.C12,{})},{key:"certifications",label:"Certifications",icon:(0,a.jsx)(d.VqV,{})},{key:"tuition",label:"Tuition Classes",icon:(0,a.jsx)(c.VQk,{})}],M=()=>{let[e,t]=(0,r.useState)("education"),[s,o]=(0,r.useState)(null),[p,v]=(0,r.useState)(0),[j,w]=(0,r.useState)(0),[b,y]=(0,r.useState)(!1),[S,E]=(0,r.useState)(null),[A,F]=(0,r.useState)(!1),[I,D]=(0,r.useState)(!1),{id:P}=(0,i.useParams)(),M=(0,i.useRouter)(),V=(0,r.useRef)(!1);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await C(P);v(e)}catch(e){console.error("Failed to fetch average rating",e)}},t=async()=>{try{let e=await k(P,1,1);w(e.total)}catch(e){console.error("Failed to fetch review count",e)}},s=async()=>{let e=(0,O.xh)();if(F(e),e&&P&&!V.current){V.current=!0;try{let e=localStorage.getItem("student_data");if(e){let t=JSON.parse(e);await R(P,t.id),console.log("Class view logged successfully")}}catch(e){console.error("Error logging class view:",e)}}};(async()=>{try{let e=await x.S.get("classes/details/".concat(P));o(e.data)}catch(e){console.error("Failed to fetch teacher data",e)}})(),e(),t(),s()},[P]),(0,r.useEffect)(()=>{(async()=>{if(A&&P)try{let e=await (0,T.Kh)(P);y(e.inWishlist),e.wishlistItem&&E(e.wishlistItem.id)}catch(e){console.error("Error checking wishlist status:",e)}})()},[A,P]);let W=(0,f.d4)(e=>e.user.user),U=async()=>{try{let e=await C(P);v(e);let t=await k(P,1,1);w(t.total)}catch(e){console.error("Failed to update review stats",e)}},q=async()=>{if(!A){D(!0);return}try{if(b&&S)await (0,T.Qg)(S),y(!1),E(null),N.toast.success("Removed from wishlist");else{var e;let t=await (0,T.U4)(P);y(!0),(null===(e=t.data)||void 0===e?void 0:e.id)&&E(t.data.id),N.toast.success("Added to wishlist")}}catch(e){N.toast.error(e.message||"Failed to update wishlist")}};if(!s)return(0,a.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,a.jsx)(z.A,{className:"w-8 h-8 animate-spin text-orange-500"})});let{firstName:J,lastName:Y,education:Z=[],experience:H=[],certificates:G=[],ClassAbout:Q={},tuitionClasses:$=[],status:K={}}=s,X="".concat(J," ").concat(Y),ee=(null==Q?void 0:Q.profilePhoto)?"".concat("http://localhost:4005/").concat(Q.profilePhoto):"/teacher-profile.jpg",et=(null==Q?void 0:Q.classesLogo)?"".concat("http://localhost:4005/").concat(Q.classesLogo):"/teacher-profile.jpg";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.default,{}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12",children:[(0,a.jsxs)("section",{className:"grid md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"md:col-span-3 space-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 p-6 rounded-2xl shadow-sm border",children:[(0,a.jsx)("div",{className:"relative w-full sm:w-64 h-64 rounded-xl overflow-hidden shadow-lg",children:(0,a.jsx)(l.default,{src:et,alt:"Teacher",fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[X,(null==K?void 0:K.status)==="APPROVED"&&(0,a.jsx)(d.VqV,{className:"text-green-500"})]}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground font-medium",children:(null==Q?void 0:Q.catchyHeadline)||"Professional Educator"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(null==Q?void 0:Q.tutorBio)||"No bio available."})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Resume"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-4 border-b pb-2",children:B.map(s=>{let{key:r,label:l,icon:i}=s;return(0,a.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ".concat(e===r?"bg-orange-100 text-orange-600 font-semibold":"text-muted-foreground hover:bg-gray-100"),onClick:()=>t(r),children:[i,l]},r)})}),(0,a.jsxs)("div",{className:"space-y-4",children:["education"===e&&(0,a.jsx)("div",{className:"grid gap-4",children:Z.length>0&&Z[0].isDegree?Z.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,a.jsxs)("div",{className:"p-4 rounded-lg shadow-sm border",children:[(0,a.jsx)("p",{className:"font-semibold text-foreground",children:e.university}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.degree," — ",e.degreeType]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Passout Year: ",e.passoutYear]})]},t)):(0,a.jsx)("p",{className:"text-muted-foreground",children:"No education details available."})}),"work"===e&&(0,a.jsx)("div",{className:"grid gap-4",children:H.length&&H[0].isExperience?H.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,a.jsxs)("div",{className:"p-4 rounded-lg shadow-sm border",children:[(0,a.jsx)("p",{className:"font-semibold text-foreground",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["From: ",new Date(e.from).toLocaleDateString()," — To:"," ",new Date(e.to).toLocaleDateString()]})]},t)):(0,a.jsx)("p",{className:"text-muted-foreground",children:"No work experience available."})}),"certifications"===e&&(0,a.jsx)("div",{className:"grid gap-4",children:G.length>0&&G[0].isCertificate?G.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,a.jsx)("div",{className:"p-4 rounded-lg shadow-sm border",children:(0,a.jsx)("p",{className:"font-semibold text-foreground",children:e.title})},t)):(0,a.jsx)("p",{className:"text-muted-foreground",children:"No certifications available."})}),"tuition"===e&&(0,a.jsx)("div",{className:"grid gap-4",children:$.length>0?$.map((e,t)=>{var s;return(0,a.jsxs)("div",{className:"p-6 rounded-lg shadow-sm border",children:[(0,a.jsxs)("p",{className:"text-lg font-semibold text-foreground",children:["Tuition #",t+1]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Category:"})," ",e.education||"N/A"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Coaching Type:"})," ",(0,g.sA)(e.coachingType)]}),"Education"===e.education?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Board:"})," ",(0,g.sA)(e.boardType)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Medium:"})," ",(0,g.sA)(e.medium)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Section:"})," ",(0,g.sA)(e.section)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Subject:"})," ",(0,g.sA)(e.subject)]})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Details:"})," ",(0,g.sA)(e.details)]})]}),(null===(s=e.timeSlots)||void 0===s?void 0:s.length)>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"font-medium",children:"Time Slots:"}),(0,a.jsx)("ul",{className:"list-disc ml-6 mt-1 text-sm text-muted-foreground",children:e.timeSlots.map((e,t)=>(0,a.jsxs)("li",{children:[e.from," — ",e.to]},t))})]})]},t)}):(0,a.jsx)("p",{className:"text-muted-foreground",children:"No tuition classes listed yet."})})]})]})]}),(0,a.jsxs)("aside",{className:"sticky top-24 rounded-2xl p-6 shadow-sm border space-y-6",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-xl overflow-hidden",children:(0,a.jsx)(l.default,{src:ee,alt:"Profile",fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-yellow-500",children:["★ ",p.toFixed(1)]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[j," reviews"]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(m.$,{variant:"default",className:"w-full flex gap-2 bg-orange-500 hover:bg-orange-600 transition-colors",onClick:()=>{if(!A){D(!0);return}let e="".concat(s.firstName," ").concat(s.lastName);M.push("/student/chat?userId=".concat(s.id,"&userName=").concat(encodeURIComponent(e)))},children:[(0,a.jsx)(c.VQk,{})," Send Message"]}),(0,a.jsxs)(m.$,{variant:"outline",className:"w-full flex gap-2 hover:bg-orange-50 transition-colors ".concat(b?"bg-orange-50 text-orange-600":""),onClick:q,children:[b?(0,a.jsx)(n.Mbv,{className:"text-orange-500"}):(0,a.jsx)(n.sOK,{}),b?"Saved to Wishlist":"Save to My List"]})]})]})]}),(0,a.jsx)(L,{classId:P,userData:W,onReviewSubmit:U})]}),(0,a.jsx)(h.default,{}),(0,a.jsx)(_.lG,{open:I,onOpenChange:D,children:(0,a.jsxs)(_.Cf,{className:"sm:max-w-md",children:[(0,a.jsx)(_.c7,{children:(0,a.jsx)(_.L3,{className:"text-center",children:"Login Required"})}),(0,a.jsx)("div",{className:"space-y-4 py-4",children:(0,a.jsx)("p",{className:"text-center text-muted-foreground",children:"Please login as a student to add this class to your wishlist or send a message."})})]})})]})}},7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(95155);s(12115);var r=s(6874),l=s.n(r),i=s(66766),n=s(29911);let o=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:r}=e;return(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:r,children:(0,a.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},r)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},12054:(e,t,s)=>{"use strict";s.d(t,{Kh:()=>n,Qg:()=>i,U4:()=>l,o3:()=>o});var a=s(59434);let r="http://localhost:4005/api/v1",l=async e=>{try{let t=(0,a.ZO)();if(!t)throw Error("Authentication required");let s=await fetch("".concat(r,"/student-wishlist"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({classId:e}),credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to add to wishlist")}return await s.json()}catch(e){throw console.error("Error adding to wishlist:",e),e}},i=async e=>{try{let t=(0,a.ZO)();if(!t)throw Error("Authentication required");let s=await fetch("".concat(r,"/student-wishlist/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)},credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to remove from wishlist")}return await s.json()}catch(e){throw console.error("Error removing from wishlist:",e),e}},n=async e=>{try{let t=(0,a.ZO)();if(!t)return{inWishlist:!1};let s=await fetch("".concat(r,"/student-wishlist/check/").concat(e),{method:"GET",headers:{Authorization:"Bearer ".concat(t)},credentials:"include"});if(!s.ok)return{inWishlist:!1};return(await s.json()).data}catch(e){return console.error("Error checking wishlist status:",e),{inWishlist:!1}}},o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{let s=(0,a.ZO)();if(!s)throw Error("Authentication required");let l=await fetch("".concat(r,"/student-wishlist?page=").concat(e,"&limit=").concat(t),{method:"GET",headers:{Authorization:"Bearer ".concat(s)},credentials:"include"});if(!l.ok){let e=await l.json();throw Error(e.message||"Failed to fetch wishlist")}return await l.json()}catch(e){throw console.error("Error fetching wishlist:",e),e}}},20185:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>l,Wz:()=>r,sA:()=>i});var a=s(94314);let r=(e,t)=>{var s,r,l,i,n,o,c,d;e.contactNo&&t((0,a.ac)(a._3.PROFILE)),(null===(r=e.ClassAbout)||void 0===r?void 0:null===(s=r.tutorBio)||void 0===s?void 0:s.length)>50&&t((0,a.ac)(a._3.DESCRIPTION)),(null===(l=e.ClassAbout)||void 0===l?void 0:l.profilePhoto)&&(null===(i=e.ClassAbout)||void 0===i?void 0:i.classesLogo)&&t((0,a.ac)(a._3.PHOTO_LOGO)),(null===(n=e.education)||void 0===n?void 0:n.length)>0&&t((0,a.ac)(a._3.EDUCATION)),(null===(o=e.certificates)||void 0===o?void 0:o.length)>0&&t((0,a.ac)(a._3.CERTIFICATES)),(null===(c=e.experience)||void 0===c?void 0:c.length)>0&&t((0,a.ac)(a._3.EXPERIENCE)),(null===(d=e.tuitionClasses)||void 0===d?void 0:d.length)>0&&t((0,a.ac)(a._3.TUTIONCLASS)),e.address&&t((0,a.ac)(a._3.ADDRESS))},l=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch(t){return[e]}},i=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch(t){return e||"N/A"}};new TextEncoder().encode("secret123")},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>h,gC:()=>u,l6:()=>c,yv:()=>d});var a=s(95155);s(12115);var r=s(59824),l=s(66474),i=s(5196),n=s(47863),o=s(59434);function c(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function m(e){let{className:t,size:s="default",children:i,...n}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[i,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:t,children:s,position:l="popper",...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(g,{})]})})}function h(e){let{className:t,children:s,...l}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function x(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(n.A,{className:"size-4"})})}function g(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(l.A,{className:"size-4"})})}},75937:(e,t,s)=>{"use strict";s.d(t,{lV:()=>d,MJ:()=>p,Rr:()=>v,zB:()=>u,eI:()=>g,lR:()=>f,C5:()=>j});var a=s(95155),r=s(12115),l=s(66634),i=s(62177),n=s(59434),o=s(24265);function c(e){let{className:t,...s}=e;return(0,a.jsx)(o.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}let d=i.Op,m=r.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(m.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},h=()=>{let e=r.useContext(m),t=r.useContext(x),{getFieldState:s}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),l=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},x=r.createContext({});function g(e){let{className:t,...s}=e,l=r.useId();return(0,a.jsx)(x.Provider,{value:{id:l},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",t),...s})})}function f(e){let{className:t,...s}=e,{error:r,formItemId:l}=h();return(0,a.jsx)(c,{"data-slot":"form-label","data-error":!!r,className:(0,n.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...s})}function p(e){let{...t}=e,{error:s,formItemId:r,formDescriptionId:i,formMessageId:n}=h();return(0,a.jsx)(l.DX,{"data-slot":"form-control",id:r,"aria-describedby":s?"".concat(i," ").concat(n):"".concat(i),"aria-invalid":!!s,...t})}function v(e){let{className:t,...s}=e,{formDescriptionId:r}=h();return(0,a.jsx)("p",{"data-slot":"form-description",id:r,className:(0,n.cn)("text-muted-foreground text-sm",t),...s})}function j(e){var t;let{className:s,...r}=e,{error:l,formMessageId:i}=h(),o=l?String(null!==(t=null==l?void 0:l.message)&&void 0!==t?t:""):r.children;return o?(0,a.jsx)("p",{"data-slot":"form-message",id:i,className:(0,n.cn)("text-destructive text-sm",s),...r,children:o}):null}},92065:(e,t,s)=>{Promise.resolve().then(s.bind(s,56671)),Promise.resolve().then(s.bind(s,7084))},94314:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,_3:()=>r,ac:()=>i});var a=s(51990),r=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let l=(0,a.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let s=t.payload;e.completedForms[s]||(e.completedForms[s]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:n}=l.actions,o=l.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6446,512,5479,9204,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,1342,4520,49,347,8441,1684,7358],()=>t(92065)),_N_E=e.O()}]);