"use client";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import mockExamImage from "../../../public/mockExamImage.svg";
import { FaRegClipboard, FaClock, FaCoins, FaBolt, FaStar } from "react-icons/fa6";
import { GiCrown, GiMedal, GiTrophy } from "react-icons/gi";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import MockExamButton from "../mock-test/mockExamButton";
import { BookCheck } from "lucide-react";
import WeeklyExamButton from "../mock-test/weeklyExam";
import { useState, useEffect } from "react";
import { getMockExamTopThreeStudents } from "@/services/LeaderboardUserApi";

const Page = () => {
    const router = useRouter();
    let studentId: string | null = null;
    try {
        const data = localStorage.getItem("student_data");
        studentId = data ? JSON.parse(data).id : null;
    } catch (error: any) {
        console.error("Error retrieving studentId:", error);
        studentId = null;
    }

    const [top3, setTop3] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchTop3 = async () => {
            try {
                setLoading(true);
                const response = await getMockExamTopThreeStudents();
                if (response.success) {
                    setTop3(response.data.data);
                } else {
                    setError(response.error);
                }
            } catch (err: any) {
                setError(`Failed to load top performers ${err.message}`);
            } finally {
                setLoading(false);
            }
        };
        fetchTop3();
    }, []);


    const formatTime = (milliseconds: number) => {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    };

    return (
        <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-800 dark:text-gray-100 transition-colors duration-300">
            <Header />

            {/* Hero Section */}
            <section className="bg-black py-16 flex justify-center items-center">
                <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    className="relative"
                >
                    <Image
                        src={mockExamImage.src}
                        alt="Current Affairs Quiz Logo"
                        width={250}
                        height={80}
                        priority
                        quality={100}
                        className="object-contain rounded-xl shadow-2xl"
                    />
                </motion.div>
            </section>

            {/* Heading Section */}
            <section className="text-center mt-12 px-4 sm:px-6">
                <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="text-4xl sm:text-5xl font-extrabold tracking-tight text-gray-900 dark:text-white"
                >
                    Daily <span className="text-amber-400">Quiz</span>
                </motion.h1>
                <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="mt-4 text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
                >
                    Stay informed, test your knowledge, and earn exclusive rewards!
                </motion.p>
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-8 mx-auto w-full max-w-3xl bg-white dark:bg-gray-900/80 border border-amber-200 dark:border-amber-700/50 rounded-xl p-6 shadow-lg flex flex-col sm:flex-row items-center justify-between gap-4"
                >
                    <div className="flex items-center gap-3">
                        <span className="text-2xl">🛍️</span>
                        <p className="text-base sm:text-lg font-semibold text-gray-800 dark:text-gray-100">
                            The <span className="text-amber-500">Store</span> is now <strong>LIVE</strong> — redeem your coins for exciting rewards!
                        </p>
                    </div>
                    <Button
                        className="bg-amber-500 hover:bg-amber-600 text-white font-semibold rounded-lg px-6 py-2.5 transition-all duration-300"
                        onClick={() => router.push("/store")}
                    >
                        Visit Store
                    </Button>
                </motion.div>
            </section>

            {/* Main Content Section */}
            <section className="px-4 sm:px-6 md:px-8 lg:px-12 py-12 space-y-12">
                {/* Previous Day's Top Performers */}
                <motion.div
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    className="py-12 px-4 sm:px-6 lg:px-8"
                >
                    <div className="max-w-5xl mx-auto bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-3xl shadow-2xl p-6 sm:p-10 border border-gray-100 dark:border-gray-700/30">
                        <motion.div
                            className="flex items-center justify-center gap-4 mb-10"
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
                        >
                            <FaStar className="text-amber-400 text-3xl sm:text-4xl" />
                            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-extrabold text-gray-900 dark:text-white tracking-tight">
                                <span className="bg-clip-text  text-amber-400">
                                    Top Performers
                                </span>{" "}
                                of Yesterday
                            </h2>
                        </motion.div>

                        {loading ? (
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
                                {[...Array(3)].map((_, index) => (
                                    <div
                                        key={index}
                                        className="flex flex-col items-center bg-gray-100 dark:bg-gray-800/50 rounded-2xl p-6 shadow-sm"
                                    >
                                        <div className="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
                                        <div className="mt-4 w-32 h-5 bg-gray-200 dark:bg-gray-700 animate-pulse rounded" />
                                        <div className="mt-3 flex gap-3">
                                            <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 animate-pulse rounded" />
                                            <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 animate-pulse rounded" />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : error ? (
                            <p className="text-center text-rose-500 font-semibold text-lg tracking-wide">
                                {error}
                            </p>
                        ) : top3.length === 0 ? (
                            <p className="text-center text-gray-500 dark:text-gray-400 font-medium text-lg tracking-wide">
                                No data available for yesterday
                            </p>
                        ) : (
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
                                {top3.map((student, index) => (
                                    <motion.div
                                        key={student.rank}
                                        initial={{ y: 30, opacity: 0 }}
                                        animate={{ y: 0, opacity: 1 }}
                                        transition={{ delay: index * 0.3, duration: 0.5, ease: "easeOut" }}
                                        className={`flex flex-col items-center ${index === 0 ? "sm:order-2" : index === 1 ? "sm:order-1" : "sm:order-3"}`}
                                    >
                                        <div className="relative flex flex-col items-center gap-3">
                                            <div
                                                className={`rounded-full border-4 p-1 transition-all duration-500 ease-in-out ${index === 0
                                                        ? "border-amber-400 scale-110 shadow-xl"
                                                        : "border-amber-300 shadow-md"
                                                    }`}
                                            >
                                                {index === 0 && (
                                                    <GiCrown className="absolute -top-8 left-1/2 -translate-x-1/2 text-amber-400 w-8 h-8" />
                                                )}
                                                {student.profilePhoto ? (
                                                    <Image
                                                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${student.profilePhoto.replace(/\\/g, "/")}`}
                                                        alt={`${student.firstName || ""} ${student.lastName || ""}`}
                                                        width={96}
                                                        height={96}
                                                        className="w-24 h-24 rounded-full object-cover border-2 border-amber-200 dark:border-gray-600"
                                                    />
                                                ) : (
                                                    <div className="w-24 h-24 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 flex items-center justify-center text-gray-800 dark:text-white font-bold text-xl">
                                                        {student.firstName?.[0] || "U"}
                                                    </div>
                                                )}
                                            </div>
                                            <div
                                                className={`z-10 rounded-full flex items-center justify-center font-bold text-white mt-[-1.7rem] ${index === 0
                                                        ? "w-8 h-8 bg-amber-500 shadow-lg border-4 border-amber-500"
                                                        : index === 1
                                                            ? "w-7 h-7 bg-amber-400 shadow border-4 border-amber-400"
                                                            : "w-6 h-6 bg-amber-300 shadow border-4 border-amber-300"
                                                    }`}
                                            >
                                                {student.rank}
                                            </div>
                                            <p className="text-lg font-semibold capitalize text-gray-900 dark:text-white mt-2 tracking-tight">
                                                {student.firstName} {student.lastName}
                                            </p>
                                            <div className="mt-3 flex flex-wrap justify-center gap-3">
                                                <div className="flex items-center gap-1.5 px-4 py-1.5 text-sm font-medium rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-700/50">
                                                    <FaBolt /> {student.score}
                                                </div>
                                                {/* <div className="flex items-center gap-1.5 px-4 py-1.5 text-sm font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700/50">
                                                    <Image src="/uest_coin.png" alt="Coin" width={14} height={14} />{" "}
                                                    {student.score > 50 ? 5 : 0}
                                                </div> */}
                                                <div className="flex items-center gap-1.5 px-4 py-1.5 text-sm font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700/50">
                                                    🔥 {student.streakCount}
                                                </div>
                                            </div>
                                            <div className="mt-2 px-4 py-1.5 rounded-full text-sm font-semibold bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-700/50">
                                                ⏰ {formatTime(student.duration)}
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        )}
                    </div>
                </motion.div>

                {/* Weekly Challenge Section */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.4 }}
                >
                    <Card className="max-w-5xl mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl p-6 sm:p-8">
                        <motion.div
                            className="flex justify-center items-center gap-3 mb-6"
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                        >
                            <GiTrophy className="text-amber-400 text-4xl" />
                            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                                Weekly <span className="text-amber-400">Challenge</span>
                            </h2>
                        </motion.div>
                        <motion.p
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: 0.6 }}
                            className="text-base sm:text-lg text-gray-600 dark:text-gray-300 text-center mb-6"
                        >
                            Test your skills every Sunday for a chance to win big rewards!
                        </motion.p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            {[
                                { icon: FaClock, text: "Every Sunday" },
                                { icon: FaRegClipboard, text: "25 Questions" },
                                {
                                    icon: FaCoins,
                                    content: (
                                        <span className="flex flex-col items-start text-base font-semibold">
                                            <span className="flex items-center gap-1 text-gray-500 dark:text-gray-400 text-sm line-through">
                                                <motion.div whileHover={{ scale: 1.1 }} transition={{ duration: 0.2 }}>
                                                    <Image
                                                        src="/uest_coin.png"
                                                        alt="Coin"
                                                        width={14}
                                                        height={14}
                                                        sizes="(max-width: 640px) 14px, 16px"
                                                        loading="lazy"
                                                        className="inline-block"
                                                    />
                                                </motion.div>
                                                9 coins
                                            </span>
                                            <strong className="text-amber-500 dark:text-amber-400">Free Entry</strong>
                                        </span>
                                    ),
                                },
                                { icon: GiMedal, text: "Earn Coins" },
                            ].map((item, index) => (
                                <motion.div
                                    key={index}
                                    className="flex items-center gap-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 shadow-sm transition-all duration-200"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <item.icon className="text-amber-400 text-xl flex-shrink-0" />
                                    {item.content || <span className="text-base font-medium">{item.text}</span>}
                                </motion.div>
                            ))}
                        </div>
                        {/* <motion.div
                            className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5 shadow-sm mb-6"
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5, delay: 1.1 }}
                        >
                            <p className="font-semibold text-gray-900 dark:text-gray-100 text-center mb-4">Weekly Rewards</p>
                            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 text-sm">
                                {[
                                    "0 coins for 50%",
                                    "2 coins for 60%",
                                    "4 coins for 70%",
                                    "6 coins for 80%",
                                    "8 coins for 90%",
                                    "10 coins for 100%",
                                ].map((reward, index) => (
                                    <motion.div
                                        key={index}
                                        className="flex items-center gap-2 text-gray-700 dark:text-gray-200 hover:text-amber-400 transition-colors duration-200"
                                        whileHover={{ x: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <span className="w-2 h-2 bg-amber-400 rounded-full"></span>
                                        {reward}
                                    </motion.div>
                                ))}
                            </div>
                            <p className="font-semibold text-center mt-4 text-gray-900 dark:text-gray-100">
                                <strong>Streak Bonus:</strong> +2 coins per weekly attempt
                            </p>
                        </motion.div> */}
                        <motion.div
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5, delay: 1.2 }}
                            className="flex justify-center"
                        >
                            <WeeklyExamButton />
                        </motion.div>
                    </Card>
                </motion.div>

                {/* Daily Quiz Card */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, delay: 0.5 }}
                >
                    <Card className="max-w-5xl mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl p-6 sm:p-8">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5">
                                {[
                                    { icon: FaRegClipboard, text: "Questions: 10" },
                                    { icon: GiMedal, text: "Badges: Streak & Rank-Based" },
                                    { icon: FaClock, text: "Duration: 8 min" },
                                    { icon: FaCoins, text: "Earn Up to: 5 Coins" },
                                    { icon: BookCheck, text: "Free Gift: Top 3 students get free classmate books" },
                                ].map((item, index) => (
                                    <motion.div
                                        key={index}
                                        className="flex items-center gap-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-all duration-200"
                                        initial={{ opacity: 0, x: -20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                                        whileHover={{ scale: 1.02 }}
                                    >
                                        <item.icon className="text-amber-400 text-xl" />
                                        <span className="text-base font-medium">{item.text}</span>
                                    </motion.div>
                                ))}
                            </div>
                            <div className="flex flex-col justify-between space-y-6">
                                <motion.div
                                    initial={{ opacity: 0, scale: 0.95 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 1.2 }}
                                >
                                    <MockExamButton />
                                </motion.div>
                                <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
                                    Attempt once every 24 hours to earn coins & build your streak!
                                </p>
                                <motion.div
                                    className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5 space-y-4 text-gray-800 dark:text-gray-200"
                                    initial={{ opacity: 0, scale: 0.95 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: 1.3 }}
                                >
                                    <p className="font-semibold text-gray-900 dark:text-gray-100">Coin Rewards:</p>
                                    <ul className="list-none space-y-2">
                                        {[
                                            "0 coins for 50% score",
                                            "1 coin for 60% score",
                                            "2 coins for 70% score",
                                            "3 coins for 80% score",
                                            "4 coins for 90% score",
                                            "5 coins for 100% score",
                                        ].map((reward, index) => (
                                            <motion.li
                                                key={index}
                                                className="flex items-center gap-2 hover:text-amber-400 transition-colors duration-200"
                                                whileHover={{ x: 5 }}
                                                transition={{ duration: 0.3 }}
                                            >
                                                <span className="w-2 h-2 bg-amber-400 rounded-full"></span>
                                                {reward}
                                            </motion.li>
                                        ))}
                                    </ul>
                                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                                        <strong>Streak Bonus:</strong> +1 coin per daily attempt
                                    </p>
                                </motion.div>
                            </div>
                        </div>
                        <div className="mt-8 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4">
                            {[
                                { src: "/scholer.svg", alt: "100 Coins", text: "100 Coins" },
                                { src: "/Mastermind.svg", alt: "500 Coins", text: "500 Coins" },
                                { src: "/Achiever.svg", alt: "1000 Coins", text: "1000 Coins" },
                                { src: "/Perfect Month.svg", alt: "30 Days Streak", text: "30 Days Streak" },
                                { src: "/Perfect Year.svg", alt: "365 Days Streak", text: "365 Days Streak" },
                                { src: "/Streak.svg", alt: "Daily Streak", text: "Daily Streak" },
                            ].map((item, index) => (
                                <motion.div
                                    key={index}
                                    className="flex flex-col items-center bg-white dark:bg-gray-800/50 p-3 rounded-lg shadow-sm"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.5, delay: 1.4 + index * 0.1 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <Image src={item.src} alt={item.alt} width={40} height={40} />
                                    <span className="text-sm font-semibold text-gray-700 dark:text-gray-200 mt-1 text-center">
                                        {item.text}
                                    </span>
                                </motion.div>
                            ))}
                        </div>
                        <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: 1.8 }}
                            >
                                <Button
                                    variant="outline"
                                    className="w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-amber-500 hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-base py-3"
                                    onClick={() => router.push(`/mock-exam-result/${studentId}`)}
                                >
                                    View Quiz Results & Earned Coins
                                </Button>
                            </motion.div>
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: 1.9 }}
                            >
                                <Button
                                    variant="outline"
                                    className="w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-amber-500 hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-base py-3"
                                    onClick={() => router.push("/Leader-Board")}
                                >
                                    View Leaderboards
                                </Button>
                            </motion.div>
                        </div>
                    </Card>
                </motion.div>
            </section>

            <Footer />
        </div>
    );
};

export default Page;
