"use strict";exports.id=1983,exports.ids=[1983],exports.modules={5336:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},19080:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},31983:(e,s,t)=>{t.d(s,{A:()=>v});var a=t(60687),r=t(43210),c=t(66874),i=t(96834),d=t(29523),n=t(85726),l=t(48730),o=t(5336);let m=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var x=t(19080),h=t(71057),g=t(40228),u=t(77306);t(52581),t(4780),t(53386);var p=t(30474);function v({userType:e,loginPath:s}){let[t,v]=(0,r.useState)([]),[j,y]=(0,r.useState)(!0),[N,b]=(0,r.useState)(!1),f=e=>{switch(e){case"PENDING":return(0,a.jsx)(l.A,{className:"w-4 h-4"});case"COMPLETED":return(0,a.jsx)(o.A,{className:"w-4 h-4"});case"CANCELLED":return(0,a.jsx)(m,{className:"w-4 h-4"});default:return(0,a.jsx)(x.A,{className:"w-4 h-4"})}},w=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"COMPLETED":return"bg-green-100 text-green-800 border-green-200";case"CANCELLED":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},k=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return N?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"bg-background dark:bg-gray-900 py-12 border-b",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,a.jsx)("div",{className:"p-3 bg-customOrange/10 rounded-xl",children:(0,a.jsx)(h.A,{className:"w-8 h-8 text-customOrange"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"My Orders"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track and manage your store orders"})]})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8 max-w-6xl",children:j?(0,a.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(n.E,{className:"w-16 h-16 rounded"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(n.E,{className:"h-4 w-1/3"}),(0,a.jsx)(n.E,{className:"h-4 w-1/4"}),(0,a.jsx)(n.E,{className:"h-4 w-1/2"})]}),(0,a.jsx)(n.E,{className:"h-8 w-20"})]})})},e))}):0===t.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"w-16 h-16 mx-auto text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No Orders Yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"You haven't placed any orders yet. Start shopping to see your orders here!"}),(0,a.jsxs)(d.$,{onClick:()=>window.location.href="/store",className:"bg-customOrange hover:bg-orange-600",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Browse Store"]})]}):(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)(c.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(c.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-customOrange/10 rounded-lg",children:(0,a.jsx)(x.A,{className:"w-5 h-5 text-customOrange"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(c.ZB,{className:"text-lg",children:["Order #",e.id.slice(-8)]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),k(e.createdAt)]})]})]}),(0,a.jsxs)(i.E,{className:`${w(e.status)} flex items-center gap-1`,children:[f(e.status),e.status]})]})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100",children:(0,a.jsx)(p.default,{src:e.item?.image?.startsWith("http")?e.item.image:`http://localhost:4005/${e.item?.image?.startsWith("/")?e.item.image.substring(1):e.item?.image||"uploads/store/placeholder.jpg"}`,alt:e.itemName,fill:!0,className:"object-cover",onError:e=>{e.target.src="/placeholder-product.jpg"}})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-foreground",children:e.itemName}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Quantity: ",e.quantity," \xd7 ",e.itemPrice," coins"]}),e.item?.category&&(0,a.jsx)(i.E,{variant:"outline",className:"mt-1 text-xs",children:e.item.category})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 text-lg font-semibold text-customOrange",children:[(0,a.jsx)(u.A,{className:"w-5 h-5"}),e.totalCoins]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total"})]})]})})]},e.id))})})]}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h.A,{className:"w-16 h-16 mx-auto text-gray-400 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Login Required"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Please login to view your orders"}),(0,a.jsx)(d.$,{onClick:()=>window.location.href=s,children:"Login"})]})})}},40228:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},66874:(e,s,t)=>{t.d(s,{BT:()=>n,Wu:()=>l,ZB:()=>d,Zp:()=>c,aR:()=>i,wL:()=>o});var a=t(60687);t(43210);var r=t(4780);function c({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function i({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function d({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...s})}function n({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...s})}function l({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...s})}function o({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...s})}},96834:(e,s,t)=>{t.d(s,{E:()=>n});var a=t(60687);t(43210);var r=t(11329),c=t(24224),i=t(4780);let d=(0,c.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,asChild:t=!1,...c}){let n=t?r.DX:"span";return(0,a.jsx)(n,{"data-slot":"badge",className:(0,i.cn)(d({variant:s}),e),...c})}}};