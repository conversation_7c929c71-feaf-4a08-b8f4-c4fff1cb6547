(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1389],{7583:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(95155);r(12115);var s=r(6874),l=r.n(s),i=r(66766),n=r(29911);let d=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:r,label:s}=e;return(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:s,children:(0,a.jsx)(r,{className:"text-xl text-white hover:text-gray-400 transition"})})},s)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},48852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>q});var a=r(95155),s=r(12115),l=r(62177),i=r(90221),n=r(55594),d=r(30285),o=r(75937),c=r(62523),x=r(88539),m=r(59409),h=r(68763),u=r(7632),b=r(42355),g=r(4516),p=r(62525),j=r(84616),f=r(70347),k=r(7583),w=r(6874),y=r.n(w),v=r(23464);let N="https://staff.uest.in/api",_="allowonly-uest.in-domain-super-key",M=async e=>(await v.A.get("".concat(N,"/jobs/").concat(e),{headers:{"x-career-key":_}})).data.data,C=async e=>(await v.A.post("".concat(N,"/save-career-details/"),e,{headers:{"x-career-key":_,"Content-Type":"multipart/form-data"}})).data;var z=r(56671),D=r(69074),Y=r(85511),F=r(14636),I=r(59434);function S(e){let{field:t,label:r,disabled:s=!1}=e;return(0,a.jsxs)(o.eI,{className:"flex flex-col",children:[(0,a.jsx)(o.lR,{children:r}),(0,a.jsxs)(F.AM,{children:[(0,a.jsx)(F.Wv,{asChild:!0,children:(0,a.jsx)(o.MJ,{children:(0,a.jsxs)(d.$,{type:"button",variant:"outline",disabled:s,className:(0,I.cn)("w-full pl-3 text-left font-normal bg-white dark:bg-black border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-800",!t.value&&"text-muted-foreground"),children:[t.value?t.value:"Pick a date",(0,a.jsx)(D.A,{className:"ml-auto h-4 w-4 opacity-50 dark:text-white"})]})})}),(0,a.jsx)(F.hl,{className:"w-auto p-0 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 shadow-lg",align:"start",children:(0,a.jsx)(Y.V,{mode:"single",captionLayout:"dropdown",fromYear:1950,toYear:new Date().getFullYear(),selected:t.value?(0,h.qg)(t.value,"yyyy-MM-dd",new Date):void 0,onSelect:e=>{e&&t.onChange((0,u.GP)(e,"yyyy-MM-dd"))},disabled:e=>e>new Date,initialFocus:!0,classNames:{caption:"flex justify-center p-2",dropdown:"mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",caption_label:"hidden"}})})]}),(0,a.jsx)(o.C5,{})]})}var B=r(35695);let A=n.Ik({first_name:n.Yj().min(1,"First name is required").max(200),middle_name:n.Yj().min(1,"Middle name is required").max(200),last_name:n.Yj().min(1,"Last name is required").max(200),contact_number:n.Yj().min(10,"Contact number must be 10 digits").max(10).regex(/^\d+$/,"Contact number must be numeric"),email:n.Yj().email("Invalid email address").max(200),date:n.Yj().regex(/^\d{4}-\d{2}-\d{2}$/,"Date must be in YYYY-MM-DD format").refine(e=>(0,h.qg)(e,"yyyy-MM-dd",new Date)<=new Date,{message:"Application date cannot be in the future"}),message:n.Yj().max(1e3).optional().nullable(),address:n.Yj().min(1,"Address is required").max(500),college_name:n.Yj().min(1,"College Name is required").max(200),date_of_birth:n.Yj().regex(/^\d{4}-\d{2}-\d{2}$/,"Date must be in YYYY-MM-DD format").refine(e=>(0,h.qg)(e,"yyyy-MM-dd",new Date)<new Date,{message:"Date of birth must be in the past"}),marital_status:n.k5(["Single","Married"],{errorMap:()=>({message:"Select a valid marital status"})}),qualification:n.Yj().min(1,"Qualification is required").max(200),specialization:n.Yj().min(1,"Specialization is required").max(200),hobbies:n.Yj().min(1,"Hobbies are required").max(500),current_salary:n.ai().min(0,"Current salary must be non-negative").refine(e=>!isNaN(e),{message:"Invalid number"}),expected_salary:n.ai().min(0,"Expected salary must be non-negative").refine(e=>!isNaN(e),{message:"Invalid number"}),work_experiences:n.YO(n.Ik({work_experience:n.Yj().min(1,"Work experience is required"),previous_company:n.Yj().min(1,"Previous company is required"),designation:n.Yj().min(1,"Designation is required"),start_duration:n.Yj().regex(/^\d{4}-\d{2}-\d{2}$/,"Date must be in YYYY-MM-DD format").refine(e=>(0,h.qg)(e,"yyyy-MM-dd",new Date)>=new Date(1900,0,1),{message:"Invalid start date"}),end_duration:n.Yj().regex(/^\d{4}-\d{2}-\d{2}$/,"Date must be in YYYY-MM-DD format").refine(e=>(0,h.qg)(e,"yyyy-MM-dd",new Date)>=new Date(1900,0,1),{message:"Invalid end date"})})).optional(),upload_file:n.Nl(File,{message:"File is required"}).refine(e=>"application/pdf"===e.type,{message:"File must be a PDF"}).refine(e=>e.size<=2097152,{message:"File must be less than 2MB"})});function q(e){let{params:t}=e,{id:r}=(0,s.use)(t),[n,h]=(0,s.useState)(!1),[w,v]=(0,s.useState)({id:"",job_title:"",description:""});(0,s.useEffect)(()=>{!async function(){v(await M(r))}()},[r]);let N=(0,l.mN)({resolver:(0,i.u)(A),defaultValues:{first_name:"",middle_name:"",last_name:"",contact_number:"",email:"",date:(0,u.GP)(new Date,"yyyy-MM-dd"),message:"",address:"",date_of_birth:"",marital_status:"Single",qualification:"",specialization:"",hobbies:"",current_salary:0,expected_salary:0,work_experiences:[],upload_file:void 0}}),{reset:_,setValue:D}=N,Y=(0,B.useRouter)(),{fields:F,append:I,remove:q}=(0,l.jz)({control:N.control,name:"work_experiences"});async function R(e){h(!0);try{let t=new FormData;Object.entries(e).forEach(e=>{let[r,a]=e;"work_experiences"===r&&a?t.append(r,JSON.stringify(a)):"upload_file"===r&&a instanceof File?t.append(r,a):null!=a&&t.append(r,String(a))}),t.append("title",w.job_title);let r=await C(t);r.success?((0,z.toast)(r.success),_(),Y.push("/careers")):z.toast.error(r.error)}catch(e){console.error("Submission error:",e)}finally{h(!1)}}let J=(0,s.useRef)(null);return(0,s.useEffect)(()=>{let e=()=>{if(J.current&&window.google){let e=new window.google.maps.places.Autocomplete(J.current,{types:["geocode","establishment"],componentRestrictions:{country:"in"}});e.addListener("place_changed",()=>{let t=e.getPlace();t.formatted_address&&D("address",t.formatted_address)})}};(()=>{if(document.querySelector('script[src*="maps.googleapis.com"]'))return e();let t=document.createElement("script");t.src="https://maps.googleapis.com/maps/api/js?key=".concat("AIzaSyCcuT3cVwtQU32bqhIXpDqy92oV0KDditA","&libraries=places"),t.async=!0,t.defer=!0,t.onload=e,document.head.appendChild(t)})()},[D]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.default,{}),(0,a.jsxs)("div",{className:"min-h-screen bg-white dark:bg-black text-black dark:text-white",children:[(0,a.jsx)("section",{className:"py-16 bg-black dark:bg-black text-white border-b border-zinc-700",children:(0,a.jsxs)("div",{className:"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(y(),{href:"/careers",className:"inline-flex items-center text-gray-400 hover:text-[#FD904B] font-medium mb-6 transition-colors",children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Back to Careers"]}),(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:w.job_title}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-gray-300",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-[#FD904B]"}),"Morbi"]}),(0,a.jsx)("span",{className:"bg-[#FD904B] text-white px-3 py-1 rounded-full text-sm font-medium",children:"Full-time"})]})]})}),(0,a.jsx)("div",{className:"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mt-10 py-10",children:(0,a.jsx)(o.lV,{...N,children:(0,a.jsxs)("form",{onSubmit:N.handleSubmit(R),className:"space-y-8 bg-white dark:bg-black rounded-lg p-8 shadow-xl border border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(o.zB,{control:N.control,name:"first_name",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"First Name"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"John"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:N.control,name:"middle_name",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Middle Name"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Michael"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:N.control,name:"last_name",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Last Name"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Doe"})}),(0,a.jsx)(o.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(o.zB,{control:N.control,name:"contact_number",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Contact Number"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,type:"tel",className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"1234567890"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:N.control,name:"email",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Email"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,type:"email",className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"<EMAIL>"})}),(0,a.jsx)(o.C5,{})]})}})]}),(0,a.jsx)(o.zB,{control:N.control,name:"address",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Address"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(x.T,{...t,ref:e=>{J.current=e,t.ref(e)},placeholder:"123 Main St, Morbi",className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(o.zB,{control:N.control,name:"date_of_birth",render:e=>{let{field:t}=e;return(0,a.jsx)(S,{field:t,label:"Birthdate"})}}),(0,a.jsx)(o.zB,{control:N.control,name:"marital_status",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Marital Status"}),(0,a.jsxs)(m.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(o.MJ,{children:(0,a.jsx)(m.bq,{className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",children:(0,a.jsx)(m.yv,{placeholder:"Select status"})})}),(0,a.jsxs)(m.gC,{className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",children:[(0,a.jsx)(m.eb,{value:"Single",children:"Single"}),(0,a.jsx)(m.eb,{value:"Married",children:"Married"})]})]}),(0,a.jsx)(o.C5,{})]})}})]}),(0,a.jsx)(o.zB,{control:N.control,name:"message",render:e=>{var t;let{field:r}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Additional Message (Optional)"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(x.T,{...r,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Tell us why you're a great fit...",value:null!==(t=r.value)&&void 0!==t?t:""})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(o.zB,{control:N.control,name:"college_name",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"College Name"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Harvard business school"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:N.control,name:"qualification",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Qualification"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"B.Sc. Computer Science"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:N.control,name:"specialization",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Specialization"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Web Development"})}),(0,a.jsx)(o.C5,{})]})}})]}),(0,a.jsx)(o.zB,{control:N.control,name:"hobbies",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Hobbies"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(x.T,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Reading, hiking, coding"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(o.zB,{control:N.control,name:"current_salary",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Current Salary"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,type:"number",className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"50000",onChange:e=>t.onChange(parseFloat(e.target.value))})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:N.control,name:"expected_salary",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Expected Salary"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,type:"number",className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"60000",onChange:e=>t.onChange(parseFloat(e.target.value))})}),(0,a.jsx)(o.C5,{})]})}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-black dark:text-white mb-4",children:"Work Experience (Optional)"}),F.map((e,t)=>(0,a.jsxs)("div",{className:"space-y-4 mb-6 border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(o.zB,{control:N.control,name:"work_experiences.".concat(t,".work_experience"),render:e=>{let{field:r}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsxs)(o.lR,{className:"text-black dark:text-white",children:["Experience #",t+1]}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...r,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Senior Developer"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)(o.zB,{control:N.control,name:"work_experiences.".concat(t,".previous_company"),render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Company"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Tech Corp"})}),(0,a.jsx)(o.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(o.zB,{control:N.control,name:"work_experiences.".concat(t,".designation"),render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Designation"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{...t,className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",placeholder:"Lead Engineer"})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(o.zB,{control:N.control,name:"work_experiences.".concat(t,".start_duration"),render:e=>{let{field:t}=e;return(0,a.jsx)(S,{field:t,label:"From"})}}),(0,a.jsx)(o.zB,{control:N.control,name:"work_experiences.".concat(t,".end_duration"),render:e=>{let{field:t}=e;return(0,a.jsx)(S,{field:t,label:"to"})}})]})]}),(0,a.jsx)(d.$,{type:"button",size:"sm",onClick:()=>q(t),className:"mt-2 bg-red-600 hover:bg-red-700 text-white",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]},e.id)),(0,a.jsxs)(d.$,{type:"button",variant:"outline",onClick:()=>I({work_experience:"",previous_company:"",designation:"",start_duration:"",end_duration:""}),className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600 hover:bg-[#FD904B] hover:text-white dark:hover:text-white",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Add Work Experience"]})]}),(0,a.jsx)(o.zB,{control:N.control,name:"upload_file",render:e=>{let{field:t}=e;return(0,a.jsxs)(o.eI,{children:[(0,a.jsx)(o.lR,{className:"text-black dark:text-white",children:"Upload Resume (PDF, max 2MB)"}),(0,a.jsx)(o.MJ,{children:(0,a.jsx)(c.p,{type:"file",accept:"application/pdf",className:"bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600",onChange:e=>{var r;return t.onChange((null===(r=e.target.files)||void 0===r?void 0:r[0])||null)}})}),(0,a.jsx)(o.C5,{})]})}}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)(d.$,{type:"submit",disabled:n,className:"bg-[#FD904B] text-white hover:bg-[#e67e22]",children:n?"Submitting...":"Submit Application"})})]})})})]}),(0,a.jsx)(k.default,{})]})}},59409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>x,eb:()=>h,gC:()=>m,l6:()=>o,yv:()=>c});var a=r(95155);r(12115);var s=r(59824),l=r(66474),i=r(5196),n=r(47863),d=r(59434);function o(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,a.jsx)(s.WT,{"data-slot":"select-value",...t})}function x(e){let{className:t,size:r="default",children:i,...n}=e;return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[i,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:r,position:l="popper",...i}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...i,children:[(0,a.jsx)(u,{}),(0,a.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(b,{})]})})}function h(e){let{className:t,children:r,...l}=e;return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:r})]})}function u(e){let{className:t,...r}=e;return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"size-4"})})}function b(e){let{className:t,...r}=e;return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.A,{className:"size-4"})})}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var a=r(95155);r(12115);var s=r(59434);function l(e){let{className:t,type:r,...l}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},75937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>c,MJ:()=>p,Rr:()=>j,zB:()=>m,eI:()=>b,lR:()=>g,C5:()=>f});var a=r(95155),s=r(12115),l=r(66634),i=r(62177),n=r(59434),d=r(24265);function o(e){let{className:t,...r}=e;return(0,a.jsx)(d.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}let c=i.Op,x=s.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(x.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},h=()=>{let e=s.useContext(x),t=s.useContext(u),{getFieldState:r}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},u=s.createContext({});function b(e){let{className:t,...r}=e,l=s.useId();return(0,a.jsx)(u.Provider,{value:{id:l},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",t),...r})})}function g(e){let{className:t,...r}=e,{error:s,formItemId:l}=h();return(0,a.jsx)(o,{"data-slot":"form-label","data-error":!!s,className:(0,n.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...r})}function p(e){let{...t}=e,{error:r,formItemId:s,formDescriptionId:i,formMessageId:n}=h();return(0,a.jsx)(l.DX,{"data-slot":"form-control",id:s,"aria-describedby":r?"".concat(i," ").concat(n):"".concat(i),"aria-invalid":!!r,...t})}function j(e){let{className:t,...r}=e,{formDescriptionId:s}=h();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function f(e){var t;let{className:r,...s}=e,{error:l,formMessageId:i}=h(),d=l?String(null!==(t=null==l?void 0:l.message)&&void 0!==t?t:""):s.children;return d?(0,a.jsx)("p",{"data-slot":"form-message",id:i,className:(0,n.cn)("text-destructive text-sm",r),...s,children:d}):null}},85511:(e,t,r)=>{"use strict";r.d(t,{V:()=>k});var a=r(95155),s=r(12115),l=r(42355),i=r(13052),n=r(53231),d=r(32944),o=r(84423),c=r(72794),x=r(7632),m=r(70542),h=r(3898),u=r(66835),b=r(40714),g=r(48882),p=r(37223),j=r(59434),f=r(30285);function k(e){let{className:t,selected:r,onSelect:k,disabled:w,month:y,onMonthChange:v,fromYear:N,toYear:_,captionLayout:M="buttons",classNames:C,...z}=e,[D,Y]=s.useState(y||r||new Date);s.useEffect(()=>{y&&Y(y)},[y]);let F=(0,n.w)(D),I=(0,d.p)(F),S=(0,o.k)(F),B=(0,c.$)(I),A=[],q=[],R=S,J="";for(;R<=B;){for(let e=0;e<7;e++){J=(0,x.GP)(R,"d");let e=R;q.push((0,a.jsx)("div",{className:(0,j.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer","h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground",{"text-muted-foreground":!(0,m.t)(R,F),"bg-primary text-primary-foreground":r&&(0,h.r)(R,r),"bg-accent text-accent-foreground":(0,u.c)(R)&&(!r||!(0,h.r)(R,r)),"opacity-50 cursor-not-allowed":w&&w(R)}),onClick:()=>{w&&w(e)||null==k||k(e)},children:(0,a.jsx)("span",{className:"font-normal",children:J})},R.toString())),R=(0,b.f)(R,1)}A.push((0,a.jsx)("div",{className:"flex w-full mt-2",children:q},R.toString())),q=[]}return(0,a.jsx)("div",{className:(0,j.cn)("p-3",t),...z,children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)("div",{className:(0,j.cn)("flex justify-center pt-1 relative items-center w-full",null==C?void 0:C.caption),children:"dropdown"===M?(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("select",{value:D.getMonth(),onChange:e=>{let t=new Date(D.getFullYear(),parseInt(e.target.value),1);Y(t),null==v||v(t)},className:(0,j.cn)("mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",null==C?void 0:C.dropdown),children:Array.from({length:12},(e,t)=>(0,a.jsx)("option",{value:t,children:(0,x.GP)(new Date(2e3,t,1),"MMMM")},t))}),(0,a.jsx)("select",{value:D.getFullYear(),onChange:e=>{let t=new Date(parseInt(e.target.value),D.getMonth(),1);Y(t),null==v||v(t)},className:(0,j.cn)("mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",null==C?void 0:C.dropdown),children:Array.from({length:(_||new Date().getFullYear())-(N||1950)+1},(e,t)=>{let r=(N||1950)+t;return(0,a.jsx)("option",{value:r,children:r},r)})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.$,{variant:"outline",size:"sm",className:"absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100",onClick:()=>{let e=(0,p.a)(D,1);Y(e),null==v||v(e)},children:(0,a.jsx)(l.A,{className:"size-4"})}),(0,a.jsx)("div",{className:(0,j.cn)("text-sm font-medium",null==C?void 0:C.caption_label),children:(0,x.GP)(D,"MMMM yyyy")}),(0,a.jsx)(f.$,{variant:"outline",size:"sm",className:"absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100",onClick:()=>{let e=(0,g.P)(D,1);Y(e),null==v||v(e)},children:(0,a.jsx)(i.A,{className:"size-4"})})]})}),(0,a.jsxs)("div",{className:"w-full border-collapse space-x-1",children:[(0,a.jsx)("div",{className:"flex",children:["Su","Mo","Tu","We","Th","Fr","Sa"].map(e=>(0,a.jsx)("div",{className:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center",children:e},e))}),A]})]})})}},85613:(e,t,r)=>{Promise.resolve().then(r.bind(r,48852))},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var a=r(95155);r(12115);var s=r(59434);function l(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,1342,7632,4520,1025,347,8441,1684,7358],()=>t(85613)),_N_E=e.O()}]);