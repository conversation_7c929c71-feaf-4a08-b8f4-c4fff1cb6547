import type { Metadata } from "next";
import UwhizInnerPage from "./UwhizInnerPage";

export const metadata: Metadata = {
  title: "Uwhiz - Online Competitive Exams & Contests | UEST",
  description: "Join Uwhiz competitive exams and contests on UEST. Test your knowledge, compete with students nationwide, and win exciting prizes. Free and paid exams available with UEST coins.",
  keywords: [
    "Uwhiz",
    "online exams",
    "competitive exams",
    "student contests",
    "educational competitions",
    "UEST",
    "exam preparation",
    "student assessment",
    "online testing",
    "academic competitions"
  ],
  openGraph: {
    title: "Uwhiz - Online Competitive Exams & Contests | UEST",
    description: "Join Uwhiz competitive exams and contests on UEST. Test your knowledge, compete with students nationwide, and win exciting prizes.",
    url: "https://www.uest.in/uwhiz",
    type: "website",
    images: [
      {
        url: "https://www.uest.in/exam-logo.jpg",
        width: 800,
        height: 600,
        alt: "Uwhiz Competitive Exams",
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: "https://www.uest.in/uwhiz",
  },
};

export default function UwhizPage() {
  return <UwhizInnerPage />;
}