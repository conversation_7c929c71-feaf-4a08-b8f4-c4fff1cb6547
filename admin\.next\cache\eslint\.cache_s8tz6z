[{"G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\page.tsx": "1", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\[id]\\page.tsx": "2", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-details\\[id]\\page.tsx": "3", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-thoughts\\page.tsx": "4", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\data-table.tsx": "5", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\page.tsx": "6", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\layout.tsx": "7", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\reviews\\page.tsx": "8", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\testimonials\\page.tsx": "9", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\page.tsx": "10", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\result\\[examId]\\page.tsx": "11", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\layout.tsx": "12", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\quiz-termination-log\\page.tsx": "13", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\examSlice.ts": "14", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\layout.tsx": "15", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\login\\page.tsx": "16", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\page.tsx": "17", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\ReduxProvider.tsx": "18", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\store.ts": "19", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\app-sidebar.tsx": "20", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\dataTable.tsx": "21", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-main.tsx": "22", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-user.tsx": "23", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\site-header.tsx": "24", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ConfirmationDialog.tsx": "25", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamForm.tsx": "26", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamTable.tsx": "27", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\TestimonialTable.tsx": "28", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\accordion.tsx": "29", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\alert-dialog.tsx": "30", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\avatar.tsx": "31", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\badge.tsx": "32", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\button.tsx": "33", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\card.tsx": "34", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\chart.tsx": "35", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\checkbox.tsx": "36", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dialog.tsx": "37", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dropdown-menu.tsx": "38", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\form.tsx": "39", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\input.tsx": "40", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\label.tsx": "41", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\select.tsx": "42", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\separator.tsx": "43", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sheet.tsx": "44", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sidebar.tsx": "45", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\skeleton.tsx": "46", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sonner.tsx": "47", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\table.tsx": "48", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tabs.tsx": "49", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle-group.tsx": "50", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle.tsx": "51", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tooltip.tsx": "52", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\hooks\\use-mobile.ts": "53", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\axios.ts": "54", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\types.ts": "55", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\utils.ts": "56", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\examSchema.ts": "57", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\auth.ts": "58", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\blogApi.ts": "59", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesApi.ts": "60", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesThoughtApi.ts": "61", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\email.ts": "62", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApi.ts": "63", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApplicationApi.ts": "64", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionApi.ts": "65", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\quizTerminationLog.ts": "66", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\reviewsApi.ts": "67", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-details\\page.tsx": "68", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\studentApi.ts": "69", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\question-bank\\page.tsx": "70", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionBankApi.ts": "71", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\testimonialApi.ts": "72", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\rank-price\\[examId]\\page.tsx": "73", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\PriceRankTable.tsx": "74", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\priceRankSchema.ts": "75", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizPriceRankApi.ts": "76", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-profile\\[id]\\page.tsx": "77", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-applicant\\[examId]\\page.tsx": "78", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\StudentProfileModal.tsx": "79", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizExamApplicantApi.ts": "80", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\page.tsx": "81", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\ReferralFilters.tsx": "82", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\[linkId]\\referred-users\\page.tsx": "83", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\calendar.tsx": "84", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\not-found.tsx": "85", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\chat\\page.tsx": "86", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddCertificateForm.tsx": "87", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddEducationForm.tsx": "88", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddExperienceForm.tsx": "89", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\CertificateTab.tsx": "90", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\EducationTab.tsx": "91", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ImagesTab.tsx": "92", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ProfileTab.tsx": "93", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassForm.tsx": "94", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassList.tsx": "95", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionTab.tsx": "96", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\WorkTab.tsx": "97", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\page.tsx": "98", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-edit\\[id]\\page.tsx": "99", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminChat.tsx": "100", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\multi-select.tsx": "101", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\classesEditSchema.ts": "102", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\chatApi.ts": "103", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-prefrence\\[examId]\\page.tsx": "104", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-terminated-students\\[examId]\\page.tsx": "105", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\LevelPrenceManager.tsx": "106", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\SubjectPreferenceManager.tsx": "107", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizLevelPrefrenceApi.ts": "108", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizQuizTerminationLogApi.ts": "109", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizSubjectPrefrenceApi.ts": "110", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-monitoring\\page.tsx": "111", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\StudentPhotoMonitoring.tsx": "112", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examMonitoringApi.ts": "113", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-result\\[examId]\\page.tsx": "114", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\notifications\\page.tsx": "115", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminNotificationBell.tsx": "116", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\notificationService.ts": "117", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-student\\[id]\\page.tsx": "118", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\page.tsx": "119", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\sub-detail\\[subDetailId]\\page.tsx": "120", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\page.tsx": "121", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\page.tsx": "122", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\mock-question-bank\\page.tsx": "123", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\ConfirmDialog.tsx": "124", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\pagination.tsx": "125", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classes-student.ts": "126", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\constantsApi.ts": "127", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\mock-examApi.ts": "128", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\store\\page.tsx": "129", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\store-orders\\page.tsx": "130", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\textarea.tsx": "131", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\storeApi.ts": "132", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\storeOrderApi.ts": "133", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhiz-result.ts": "134", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\activity-logs\\page.tsx": "135", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\activityLogApi.ts": "136", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\CoinsTrancationModal.tsx": "137"}, {"size": 6958, "mtime": 1752465850444, "results": "138", "hashOfConfig": "139"}, {"size": 6736, "mtime": 1751274748086, "results": "140", "hashOfConfig": "139"}, {"size": 34334, "mtime": 1753771579253, "results": "141", "hashOfConfig": "139"}, {"size": 8414, "mtime": 1752465851718, "results": "142", "hashOfConfig": "139"}, {"size": 14092, "mtime": 1752465852563, "results": "143", "hashOfConfig": "139"}, {"size": 144, "mtime": 1747109266160, "results": "144", "hashOfConfig": "139"}, {"size": 698, "mtime": 1747109266162, "results": "145", "hashOfConfig": "139"}, {"size": 4377, "mtime": 1752465853423, "results": "146", "hashOfConfig": "139"}, {"size": 248, "mtime": 1747650902107, "results": "147", "hashOfConfig": "139"}, {"size": 272, "mtime": 1747818545268, "results": "148", "hashOfConfig": "139"}, {"size": 1666, "mtime": 1752465855077, "results": "149", "hashOfConfig": "139"}, {"size": 698, "mtime": 1747109266172, "results": "150", "hashOfConfig": "139"}, {"size": 4900, "mtime": 1747109266327, "results": "151", "hashOfConfig": "139"}, {"size": 6769, "mtime": 1747624459637, "results": "152", "hashOfConfig": "139"}, {"size": 587, "mtime": 1747109266338, "results": "153", "hashOfConfig": "139"}, {"size": 3150, "mtime": 1747109266339, "results": "154", "hashOfConfig": "139"}, {"size": 111, "mtime": 1747109266341, "results": "155", "hashOfConfig": "139"}, {"size": 236, "mtime": 1747109266328, "results": "156", "hashOfConfig": "139"}, {"size": 325, "mtime": 1747109266342, "results": "157", "hashOfConfig": "139"}, {"size": 3374, "mtime": 1754326700008, "results": "158", "hashOfConfig": "139"}, {"size": 2911, "mtime": 1752465850414, "results": "159", "hashOfConfig": "139"}, {"size": 3329, "mtime": 1747797160821, "results": "160", "hashOfConfig": "139"}, {"size": 3210, "mtime": 1747109266135, "results": "161", "hashOfConfig": "139"}, {"size": 964, "mtime": 1751625230881, "results": "162", "hashOfConfig": "139"}, {"size": 1292, "mtime": 1751276652287, "results": "163", "hashOfConfig": "139"}, {"size": 15071, "mtime": 1747797160859, "results": "164", "hashOfConfig": "139"}, {"size": 8679, "mtime": 1752465855843, "results": "165", "hashOfConfig": "139"}, {"size": 10610, "mtime": 1752465856725, "results": "166", "hashOfConfig": "139"}, {"size": 2125, "mtime": 1747109266363, "results": "167", "hashOfConfig": "139"}, {"size": 4021, "mtime": 1747289688502, "results": "168", "hashOfConfig": "139"}, {"size": 1090, "mtime": 1747109266365, "results": "169", "hashOfConfig": "139"}, {"size": 1634, "mtime": 1747109266367, "results": "170", "hashOfConfig": "139"}, {"size": 2158, "mtime": 1747109266369, "results": "171", "hashOfConfig": "139"}, {"size": 2003, "mtime": 1747109266374, "results": "172", "hashOfConfig": "139"}, {"size": 10019, "mtime": 1747109266376, "results": "173", "hashOfConfig": "139"}, {"size": 1258, "mtime": 1747109266385, "results": "174", "hashOfConfig": "139"}, {"size": 3915, "mtime": 1748363529403, "results": "175", "hashOfConfig": "139"}, {"size": 8407, "mtime": 1747109266407, "results": "176", "hashOfConfig": "139"}, {"size": 3871, "mtime": 1747109266409, "results": "177", "hashOfConfig": "139"}, {"size": 997, "mtime": 1750649653890, "results": "178", "hashOfConfig": "139"}, {"size": 639, "mtime": 1752465856736, "results": "179", "hashOfConfig": "139"}, {"size": 6433, "mtime": 1750649653892, "results": "180", "hashOfConfig": "139"}, {"size": 738, "mtime": 1747109266416, "results": "181", "hashOfConfig": "139"}, {"size": 4227, "mtime": 1747109266425, "results": "182", "hashOfConfig": "139"}, {"size": 22330, "mtime": 1747109266426, "results": "183", "hashOfConfig": "139"}, {"size": 292, "mtime": 1747109266428, "results": "184", "hashOfConfig": "139"}, {"size": 596, "mtime": 1747109266429, "results": "185", "hashOfConfig": "139"}, {"size": 2458, "mtime": 1747109266431, "results": "186", "hashOfConfig": "139"}, {"size": 2016, "mtime": 1747109266432, "results": "187", "hashOfConfig": "139"}, {"size": 1997, "mtime": 1747109266434, "results": "188", "hashOfConfig": "139"}, {"size": 1622, "mtime": 1747109266437, "results": "189", "hashOfConfig": "139"}, {"size": 1953, "mtime": 1747109266453, "results": "190", "hashOfConfig": "139"}, {"size": 595, "mtime": 1747109266455, "results": "191", "hashOfConfig": "139"}, {"size": 1155, "mtime": 1748962020698, "results": "192", "hashOfConfig": "139"}, {"size": 15691, "mtime": 1754326700688, "results": "193", "hashOfConfig": "139"}, {"size": 407, "mtime": 1747289688590, "results": "194", "hashOfConfig": "139"}, {"size": 2333, "mtime": 1750649653906, "results": "195", "hashOfConfig": "139"}, {"size": 281, "mtime": 1747109266546, "results": "196", "hashOfConfig": "139"}, {"size": 1430, "mtime": 1751272204657, "results": "197", "hashOfConfig": "139"}, {"size": 8100, "mtime": 1749201001867, "results": "198", "hashOfConfig": "139"}, {"size": 1510, "mtime": 1751625231028, "results": "199", "hashOfConfig": "139"}, {"size": 354, "mtime": 1749530596920, "results": "200", "hashOfConfig": "139"}, {"size": 1779, "mtime": 1747797160877, "results": "201", "hashOfConfig": "139"}, {"size": 1017, "mtime": 1747109266581, "results": "202", "hashOfConfig": "139"}, {"size": 2988, "mtime": 1747109266582, "results": "203", "hashOfConfig": "139"}, {"size": 795, "mtime": 1747109266583, "results": "204", "hashOfConfig": "139"}, {"size": 612, "mtime": 1747289688605, "results": "205", "hashOfConfig": "139"}, {"size": 16074, "mtime": 1754453375642, "results": "206", "hashOfConfig": "139"}, {"size": 4251, "mtime": 1751523077949, "results": "207", "hashOfConfig": "139"}, {"size": 49538, "mtime": 1752465855153, "results": "208", "hashOfConfig": "139"}, {"size": 6782, "mtime": 1752489573453, "results": "209", "hashOfConfig": "139"}, {"size": 1472, "mtime": 1747797160878, "results": "210", "hashOfConfig": "139"}, {"size": 9128, "mtime": 1752465855203, "results": "211", "hashOfConfig": "139"}, {"size": 1923, "mtime": 1747797160861, "results": "212", "hashOfConfig": "139"}, {"size": 258, "mtime": 1747797160876, "results": "213", "hashOfConfig": "139"}, {"size": 1933, "mtime": 1747797160878, "results": "214", "hashOfConfig": "139"}, {"size": 16693, "mtime": 1749486774218, "results": "215", "hashOfConfig": "139"}, {"size": 10665, "mtime": 1753697055522, "results": "216", "hashOfConfig": "139"}, {"size": 11914, "mtime": 1749486774211, "results": "217", "hashOfConfig": "139"}, {"size": 2897, "mtime": 1753697055733, "results": "218", "hashOfConfig": "139"}, {"size": 30463, "mtime": 1754326700157, "results": "219", "hashOfConfig": "139"}, {"size": 7414, "mtime": 1748768935822, "results": "220", "hashOfConfig": "139"}, {"size": 30405, "mtime": 1752465852869, "results": "221", "hashOfConfig": "139"}, {"size": 4054, "mtime": 1748363529403, "results": "222", "hashOfConfig": "139"}, {"size": 847, "mtime": 1748768935825, "results": "223", "hashOfConfig": "139"}, {"size": 236, "mtime": 1751276652281, "results": "224", "hashOfConfig": "139"}, {"size": 9836, "mtime": 1749201001627, "results": "225", "hashOfConfig": "139"}, {"size": 14847, "mtime": 1749201001637, "results": "226", "hashOfConfig": "139"}, {"size": 12466, "mtime": 1749201001662, "results": "227", "hashOfConfig": "139"}, {"size": 8747, "mtime": 1749201001679, "results": "228", "hashOfConfig": "139"}, {"size": 9707, "mtime": 1749201001681, "results": "229", "hashOfConfig": "139"}, {"size": 6260, "mtime": 1749201001698, "results": "230", "hashOfConfig": "139"}, {"size": 10604, "mtime": 1749201001711, "results": "231", "hashOfConfig": "139"}, {"size": 13037, "mtime": 1749486774214, "results": "232", "hashOfConfig": "139"}, {"size": 4849, "mtime": 1749201001776, "results": "233", "hashOfConfig": "139"}, {"size": 2628, "mtime": 1749201001787, "results": "234", "hashOfConfig": "139"}, {"size": 9446, "mtime": 1749201001792, "results": "235", "hashOfConfig": "139"}, {"size": 18789, "mtime": 1749486774215, "results": "236", "hashOfConfig": "139"}, {"size": 37891, "mtime": 1749486774217, "results": "237", "hashOfConfig": "139"}, {"size": 31713, "mtime": 1753697055398, "results": "238", "hashOfConfig": "139"}, {"size": 4268, "mtime": 1753697055604, "results": "239", "hashOfConfig": "139"}, {"size": 3207, "mtime": 1749486774424, "results": "240", "hashOfConfig": "139"}, {"size": 1365, "mtime": 1751276652291, "results": "241", "hashOfConfig": "139"}, {"size": 1187, "mtime": 1749486774234, "results": "242", "hashOfConfig": "139"}, {"size": 3013, "mtime": 1752465855220, "results": "243", "hashOfConfig": "139"}, {"size": 8011, "mtime": 1752465855888, "results": "244", "hashOfConfig": "139"}, {"size": 8834, "mtime": 1752465856640, "results": "245", "hashOfConfig": "139"}, {"size": 1675, "mtime": 1750649653907, "results": "246", "hashOfConfig": "139"}, {"size": 1423, "mtime": 1753697055738, "results": "247", "hashOfConfig": "139"}, {"size": 2511, "mtime": 1753697055749, "results": "248", "hashOfConfig": "139"}, {"size": 554, "mtime": 1750649653852, "results": "249", "hashOfConfig": "139"}, {"size": 21435, "mtime": 1753697055584, "results": "250", "hashOfConfig": "139"}, {"size": 1291, "mtime": 1750649653906, "results": "251", "hashOfConfig": "139"}, {"size": 26171, "mtime": 1753697055560, "results": "252", "hashOfConfig": "139"}, {"size": 21456, "mtime": 1751625230898, "results": "253", "hashOfConfig": "139"}, {"size": 9775, "mtime": 1751625230867, "results": "254", "hashOfConfig": "139"}, {"size": 4699, "mtime": 1753697055719, "results": "255", "hashOfConfig": "139"}, {"size": 5474, "mtime": 1752465851656, "results": "256", "hashOfConfig": "139"}, {"size": 13037, "mtime": 1752489573348, "results": "257", "hashOfConfig": "139"}, {"size": 12800, "mtime": 1752489573359, "results": "258", "hashOfConfig": "139"}, {"size": 13012, "mtime": 1752489573392, "results": "259", "hashOfConfig": "139"}, {"size": 12623, "mtime": 1752489573414, "results": "260", "hashOfConfig": "139"}, {"size": 38438, "mtime": 1754326700495, "results": "261", "hashOfConfig": "139"}, {"size": 1574, "mtime": 1752465850247, "results": "262", "hashOfConfig": "139"}, {"size": 1868, "mtime": 1752465850416, "results": "263", "hashOfConfig": "139"}, {"size": 2012, "mtime": 1752465856751, "results": "264", "hashOfConfig": "139"}, {"size": 5280, "mtime": 1752917960042, "results": "265", "hashOfConfig": "139"}, {"size": 3364, "mtime": 1752465857138, "results": "266", "hashOfConfig": "139"}, {"size": 21214, "mtime": 1754550309161, "results": "267", "hashOfConfig": "139"}, {"size": 14574, "mtime": 1754550309132, "results": "268", "hashOfConfig": "139"}, {"size": 646, "mtime": 1753697055606, "results": "269", "hashOfConfig": "139"}, {"size": 2602, "mtime": 1753697055720, "results": "270", "hashOfConfig": "139"}, {"size": 1969, "mtime": 1753952082459, "results": "271", "hashOfConfig": "139"}, {"size": 3182, "mtime": 1753697055724, "results": "272", "hashOfConfig": "139"}, {"size": 10929, "mtime": 1753697055485, "results": "273", "hashOfConfig": "139"}, {"size": 338, "mtime": 1753697055677, "results": "274", "hashOfConfig": "139"}, {"size": 8092, "mtime": 1754453375645, "results": "275", "hashOfConfig": "139"}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k18kcd", {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-details\\[id]\\page.tsx", [], ["687"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-thoughts\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\data-table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\reviews\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\testimonials\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\result\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\quiz-termination-log\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\examSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\ReduxProvider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\store.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\app-sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\dataTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-main.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-user.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\site-header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ConfirmationDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\TestimonialTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\accordion.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\avatar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\badge.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\button.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\card.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\chart.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\input.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\label.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\separator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sheet.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sonner.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tabs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle-group.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\hooks\\use-mobile.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\axios.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\types.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\utils.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\examSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\auth.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\blogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesThoughtApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\email.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApplicationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\quizTerminationLog.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\reviewsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-details\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\studentApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\question-bank\\page.tsx", ["688"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionBankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\testimonialApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\rank-price\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\PriceRankTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\priceRankSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizPriceRankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-profile\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-applicant\\[examId]\\page.tsx", ["689"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\StudentProfileModal.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizExamApplicantApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\ReferralFilters.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\[linkId]\\referred-users\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\calendar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\not-found.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddCertificateForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddEducationForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddExperienceForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\CertificateTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\EducationTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ImagesTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ProfileTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassList.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\WorkTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\page.tsx", [], ["690"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-edit\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminChat.tsx", ["691", "692"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\multi-select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\classesEditSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\chatApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-prefrence\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-terminated-students\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\LevelPrenceManager.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\SubjectPreferenceManager.tsx", ["693"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizLevelPrefrenceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizQuizTerminationLogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizSubjectPrefrenceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-monitoring\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\StudentPhotoMonitoring.tsx", ["694"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examMonitoringApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-result\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\notifications\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminNotificationBell.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\notificationService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-student\\[id]\\page.tsx", ["695", "696"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\page.tsx", ["697"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\sub-detail\\[subDetailId]\\page.tsx", ["698"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\page.tsx", [], ["699"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\page.tsx", [], ["700"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\mock-question-bank\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\ConfirmDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\pagination.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classes-student.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\constantsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\mock-examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\store\\page.tsx", ["701", "702"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\store-orders\\page.tsx", ["703", "704"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\textarea.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\storeApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\storeOrderApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhiz-result.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\activity-logs\\page.tsx", ["705"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\activityLogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\CoinsTrancationModal.tsx", ["706"], [], {"ruleId": "707", "severity": 1, "message": "708", "line": 110, "column": 6, "nodeType": "709", "endLine": 110, "endColumn": 14, "suggestions": "710", "suppressions": "711"}, {"ruleId": "707", "severity": 1, "message": "712", "line": 254, "column": 6, "nodeType": "709", "endLine": 254, "endColumn": 13, "suggestions": "713"}, {"ruleId": "707", "severity": 1, "message": "714", "line": 102, "column": 6, "nodeType": "709", "endLine": 102, "endColumn": 38, "suggestions": "715"}, {"ruleId": "707", "severity": 1, "message": "716", "line": 231, "column": 6, "nodeType": "709", "endLine": 231, "endColumn": 15, "suggestions": "717", "suppressions": "718"}, {"ruleId": "707", "severity": 1, "message": "719", "line": 107, "column": 8, "nodeType": "709", "endLine": 107, "endColumn": 56, "suggestions": "720"}, {"ruleId": "707", "severity": 1, "message": "721", "line": 117, "column": 8, "nodeType": "709", "endLine": 117, "endColumn": 57, "suggestions": "722"}, {"ruleId": "707", "severity": 1, "message": "723", "line": 127, "column": 6, "nodeType": "709", "endLine": 127, "endColumn": 8, "suggestions": "724"}, {"ruleId": "707", "severity": 1, "message": "725", "line": 100, "column": 6, "nodeType": "709", "endLine": 100, "endColumn": 21, "suggestions": "726"}, {"ruleId": "707", "severity": 1, "message": "727", "line": 107, "column": 6, "nodeType": "709", "endLine": 107, "endColumn": 15, "suggestions": "728"}, {"ruleId": "707", "severity": 1, "message": "729", "line": 111, "column": 6, "nodeType": "709", "endLine": 111, "endColumn": 33, "suggestions": "730"}, {"ruleId": "707", "severity": 1, "message": "731", "line": 228, "column": 6, "nodeType": "709", "endLine": 228, "endColumn": 19, "suggestions": "732"}, {"ruleId": "707", "severity": 1, "message": "733", "line": 225, "column": 6, "nodeType": "709", "endLine": 225, "endColumn": 22, "suggestions": "734"}, {"ruleId": "707", "severity": 1, "message": "735", "line": 234, "column": 6, "nodeType": "709", "endLine": 234, "endColumn": 21, "suggestions": "736", "suppressions": "737"}, {"ruleId": "707", "severity": 1, "message": "738", "line": 225, "column": 6, "nodeType": "709", "endLine": 225, "endColumn": 8, "suggestions": "739", "suppressions": "740"}, {"ruleId": "707", "severity": 1, "message": "741", "line": 71, "column": 6, "nodeType": "709", "endLine": 71, "endColumn": 8, "suggestions": "742"}, {"ruleId": "707", "severity": 1, "message": "741", "line": 92, "column": 6, "nodeType": "709", "endLine": 92, "endColumn": 37, "suggestions": "743"}, {"ruleId": "707", "severity": 1, "message": "744", "line": 39, "column": 6, "nodeType": "709", "endLine": 39, "endColumn": 8, "suggestions": "745"}, {"ruleId": "707", "severity": 1, "message": "744", "line": 43, "column": 6, "nodeType": "709", "endLine": 43, "endColumn": 50, "suggestions": "746"}, {"ruleId": "707", "severity": 1, "message": "747", "line": 76, "column": 6, "nodeType": "709", "endLine": 76, "endColumn": 8, "suggestions": "748"}, {"ruleId": "707", "severity": 1, "message": "749", "line": 78, "column": 6, "nodeType": "709", "endLine": 78, "endColumn": 23, "suggestions": "750"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchAddressDetails' and 'fetchTeacher'. Either include them or remove the dependency array.", "ArrayExpression", ["751"], ["752"], "React Hook useEffect has missing dependencies: 'fetchConstants' and 'fetchQuestions'. Either include them or remove the dependency array.", ["753"], "React Hook useEffect has a missing dependency: 'appliedFilters'. Either include it or remove the dependency array.", ["754"], "React Hook useEffect has a missing dependency: 'fetchClassData'. Either include it or remove the dependency array.", ["755"], ["756"], "React Hook useEffect has a missing dependency: 'classes.length'. Either include it or remove the dependency array.", ["757"], "React Hook useEffect has a missing dependency: 'students.length'. Either include it or remove the dependency array.", ["758"], "React Hook useCallback has a missing dependency: 'examId'. Either include it or remove the dependency array.", ["759"], "React Hook useEffect has missing dependencies: 'appliedFilters' and 'fetchApplicants'. Either include them or remove the dependency array.", ["760"], "React Hook useEffect has missing dependencies: 'currentPage', 'getStudents', 'getYears', 'searchTerm', and 'selectedYear'. Either include them or remove the dependency array.", ["761"], "React Hook useEffect has missing dependencies: 'getStudents' and 'searchTerm'. Either include them or remove the dependency array.", ["762"], "React Hook useEffect has a missing dependency: 'fetchSubDetails'. Either include it or remove the dependency array.", ["763"], "React Hook useEffect has a missing dependency: 'fetchValues'. Either include it or remove the dependency array.", ["764"], "React Hook useEffect has a missing dependency: 'fetchDetails'. Either include it or remove the dependency array.", ["765"], ["766"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["767"], ["768"], "React Hook useEffect has a missing dependency: 'loadStoreItems'. Either include it or remove the dependency array.", ["769"], ["770"], "React Hook useEffect has a missing dependency: 'loadStoreOrders'. Either include it or remove the dependency array.", ["771"], ["772"], "React Hook useEffect has a missing dependency: 'fetchActivityLogs'. Either include it or remove the dependency array.", ["773"], "React Hook useEffect has a missing dependency: 'fetchTransactions'. Either include it or remove the dependency array.", ["774"], {"desc": "775", "fix": "776"}, {"kind": "777", "justification": "778"}, {"desc": "779", "fix": "780"}, {"desc": "781", "fix": "782"}, {"desc": "783", "fix": "784"}, {"kind": "777", "justification": "778"}, {"desc": "785", "fix": "786"}, {"desc": "787", "fix": "788"}, {"desc": "789", "fix": "790"}, {"desc": "791", "fix": "792"}, {"desc": "793", "fix": "794"}, {"desc": "795", "fix": "796"}, {"desc": "797", "fix": "798"}, {"desc": "799", "fix": "800"}, {"desc": "801", "fix": "802"}, {"kind": "777", "justification": "778"}, {"desc": "803", "fix": "804"}, {"kind": "777", "justification": "778"}, {"desc": "805", "fix": "806"}, {"desc": "807", "fix": "808"}, {"desc": "809", "fix": "810"}, {"desc": "811", "fix": "812"}, {"desc": "813", "fix": "814"}, {"desc": "815", "fix": "816"}, "Update the dependencies array to be: [fetchAddressDetails, fetchTeacher, userId]", {"range": "817", "text": "818"}, "directive", "", "Update the dependencies array to be: [fetchConstants, fetchQuestions, limit]", {"range": "819", "text": "820"}, "Update the dependencies array to be: [examId, page, limit, fetchData, appliedFilters]", {"range": "821", "text": "822"}, "Update the dependencies array to be: [classId, fetchClassData]", {"range": "823", "text": "824"}, "Update the dependencies array to be: [classSearchQuery, classes.length, isAuthenticated, loadClasses]", {"range": "825", "text": "826"}, "Update the dependencies array to be: [studentSearchQuery, selectedClass, loadStudents, students.length]", {"range": "827", "text": "828"}, "Update the dependencies array to be: [examId]", {"range": "829", "text": "830"}, "Update the dependencies array to be: [limit, examId, appliedFilters, fetchApplicants]", {"range": "831", "text": "832"}, "Update the dependencies array to be: [classId, currentPage, getStudents, getYears, searchTerm, selectedYear]", {"range": "833", "text": "834"}, "Update the dependencies array to be: [currentPage, getStudents, searchTerm, selectedYear]", {"range": "835", "text": "836"}, "Update the dependencies array to be: [fetchDetail, fetchSubDetails]", {"range": "837", "text": "838"}, "Update the dependencies array to be: [fetchSubDetail, fetchValues]", {"range": "839", "text": "840"}, "Update the dependencies array to be: [fetchCategory, fetchDetails]", {"range": "841", "text": "842"}, "Update the dependencies array to be: [fetchCategories]", {"range": "843", "text": "844"}, "Update the dependencies array to be: [loadStoreItems]", {"range": "845", "text": "846"}, "Update the dependencies array to be: [loadStoreItems, searchQuery, selectedCategory]", {"range": "847", "text": "848"}, "Update the dependencies array to be: [loadStoreOrders]", {"range": "849", "text": "850"}, "Update the dependencies array to be: [statusFilter, searchQuery, modelTypeFilter, loadStoreOrders]", {"range": "851", "text": "852"}, "Update the dependencies array to be: [fetchActivityLogs]", {"range": "853", "text": "854"}, "Update the dependencies array to be: [fetchTransactions, open, studentId]", {"range": "855", "text": "856"}, [3672, 3680], "[fetchAdd<PERSON><PERSON><PERSON><PERSON>, fetchTeacher, userId]", [9004, 9011], "[fetchConstants, fetchQuestions, limit]", [3299, 3331], "[examId, page, limit, fetchData, appliedFilters]", [7433, 7442], "[classId, fetchClassData]", [3971, 4019], "[classSearch<PERSON><PERSON>y, classes.length, isAuthenticated, loadClasses]", [4337, 4386], "[studentSearch<PERSON><PERSON>y, selectedClass, loadStudents, students.length]", [3343, 3345], "[examId]", [3044, 3059], "[limit, examId, appliedFilters, fetchApplicants]", [3073, 3082], "[classId, currentPage, getStudents, getYears, searchTerm, selectedYear]", [3171, 3198], "[currentPage, getStudents, searchTerm, selectedYear]", [7233, 7246], "[fetchDetail, fetchSubDetails]", [6733, 6749], "[fetchSubDetail, fetchValues]", [7331, 7346], "[fetch<PERSON>ate<PERSON>y, fetchDetails]", [7161, 7163], "[fetchCategories]", [1998, 2000], "[loadStoreItems]", [2616, 2647], "[loadStoreItems, searchQuery, selectedCategory]", [1526, 1528], "[loadStoreOrders]", [1585, 1629], "[statusFilter, searchQuery, modelTypeFilter, loadStoreOrders]", [2816, 2818], "[fetchActivityLogs]", [2217, 2234], "[fetchTransactions, open, studentId]"]