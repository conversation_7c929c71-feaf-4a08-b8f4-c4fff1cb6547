(()=>{var e={};e.id=3694,e.ids=[3694],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14180:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var r=s(60687),a=s(43210),i=s(16189),n=s(30474),c=s(28559),l=s(77306),o=s(19080),d=s(5748),u=s(96474),m=s(28561),x=s(85778),p=s(29523),h=s(66874),g=s(96834),f=s(85726),v=s(52581),j=s(90269),b=s(46303),N=s(4780),y=s(68767),w=s(24364),k=s(53386),S=s(93500);let E=({params:e})=>{let t=(0,i.useRouter)(),[s,E]=(0,a.useState)(null),[P,A]=(0,a.useState)([]),[O,C]=(0,a.useState)(!0),[q,_]=(0,a.useState)(""),[$,I]=(0,a.useState)(1),[T,F]=(0,a.useState)(!1),[U,G]=(0,a.useState)(!1),[L,R]=(0,a.useState)(!1),[W,z]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(async()=>{let{id:t}=await e;_(t)})()},[e]),(0,a.useEffect)(()=>{G((0,N.wR)().isAuth)},[]),(0,a.useEffect)(()=>{(async()=>{try{if(!q)return;C(!0);let e=await y.Lq(q);if(!e.success){v.toast.error(e.error||"Failed to load product"),t.push("/store");return}E(e.data);let s=await y.dI();if(s.success&&s.data){let e=s.data.filter(e=>e.id!==q).slice(0,4);A(e)}}catch(e){console.error("Error fetching data:",e),v.toast.error("Failed to load product"),t.push("/store")}finally{C(!1)}})()},[q,t]);let D=e=>{if(!(e<1)){if(s&&e>s.availableStock){v.toast.error(`Only ${s.availableStock} items available`);return}I(e)}},Z=async()=>{if(!U){v.toast.error("Please login to add items to cart");return}if(s){if(0===s.availableStock){v.toast.error("Item is out of stock");return}if($>s.availableStock){v.toast.error(`Only ${s.availableStock} items available`);return}try{F(!0),(await w.bE(s.id,$)).success?(v.toast.success(`${$} item(s) added to cart!`),window.dispatchEvent(new CustomEvent("cartUpdated")),I(1)):v.toast.error(`Only ${s.availableStock} items available`)}catch(e){console.error("Error adding to cart:",e),v.toast.error("Item is out of stock")}finally{F(!1)}}},M=async()=>{if(s)try{z(!0);let e=s.coinPrice*$,t={cartItems:[{id:s.id,name:s.name,coinPrice:s.coinPrice,quantity:$,image:s.image||""}],totalCoins:e},r=await k.wE(t);if(!r.success){if("PROFILE_NOT_APPROVED"===r.error){let e=r.data?.message||"Your profile is not approved yet. Please complete your profile and wait for admin approval.";v.toast.error(e),R(!1);return}throw Error(r.error)}let a=r.data?.orderId||r.data?.firstOrderId||"Unknown";v.toast.success(`Order placed successfully! Order ID: ${a.slice(-8)}. Coins deducted. Your order is pending admin approval.`),R(!1),I(1);let i=await y.Lq(q);i.success&&E(i.data);let n=await y.dI();if(n.success&&n.data){let e=n.data.filter(e=>e.id!==q).slice(0,4);A(e)}}catch(e){console.error("Error details:",e),v.toast.error(e.message||"Purchase failed"),R(!1)}finally{z(!1)}};return O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.default,{}),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)(f.E,{className:"h-10 w-32 mb-6"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsx)(f.E,{className:"h-96 w-full rounded-lg"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(f.E,{className:"h-8 w-3/4"}),(0,r.jsx)(f.E,{className:"h-4 w-full"}),(0,r.jsx)(f.E,{className:"h-4 w-2/3"}),(0,r.jsx)(f.E,{className:"h-6 w-1/4"}),(0,r.jsx)(f.E,{className:"h-10 w-full"})]})]})]})}),(0,r.jsx)(b.default,{})]}):s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.default,{}),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)(p.$,{variant:"ghost",onClick:()=>t.push("/store"),className:"mb-6 hover:bg-muted",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Back to Store"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12",children:[(0,r.jsx)(h.Zp,{className:"overflow-hidden",children:(0,r.jsx)(h.Wu,{className:"p-0",children:(0,r.jsxs)("div",{className:"relative h-96 lg:h-[500px] bg-muted/30 flex items-center justify-center",children:[(0,r.jsx)(n.default,{src:s.image?.startsWith("http")?s.image:`http://localhost:4005/${s.image?.startsWith("/")?s.image.substring(1):s.image||"uploads/store/placeholder.jpg"}`,alt:s.name,className:"object-contain w-full h-full",width:500,height:500,onError:e=>{e.target.src="/logo.png"}}),0===s.availableStock&&(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,r.jsx)(g.E,{variant:"destructive",className:"text-lg px-4 py-2",children:"Out of Stock"})})]})})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:s.name}),(0,r.jsx)(g.E,{variant:"secondary",className:"text-sm",children:s.category})]}),(0,r.jsx)("p",{className:"text-muted-foreground text-lg leading-relaxed",children:s.description})]}),(0,r.jsxs)("div",{className:"bg-card p-6 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(l.A,{className:"w-6 h-6 text-customOrange"}),(0,r.jsxs)("span",{className:"text-3xl font-bold text-customOrange",children:[s.coinPrice," coins"]})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pay with your UEST coins"})]}),(0,r.jsxs)("div",{className:"bg-card p-4 rounded-lg border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-muted-foreground"}),(0,r.jsx)("span",{className:"font-medium text-card-foreground",children:"Stock Information"})]}),(0,r.jsx)("div",{className:"space-y-1 text-sm",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Available:"}),(0,r.jsxs)("span",{className:`font-medium ${0===s.availableStock?"text-red-500":"text-green-600"}`,children:[s.availableStock," units"]})]})})]}),s.availableStock>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-card-foreground mb-2",children:"Quantity"}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.$,{variant:"outline",size:"sm",onClick:()=>D($-1),disabled:$<=1,children:(0,r.jsx)(d.A,{className:"w-4 h-4"})}),(0,r.jsx)("span",{className:"w-12 text-center font-medium text-lg",children:$}),(0,r.jsx)(p.$,{variant:"outline",size:"sm",onClick:()=>D($+1),disabled:$>=s.availableStock,children:(0,r.jsx)(u.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Total Cost:"}),(0,r.jsxs)("span",{className:"font-bold text-lg text-customOrange flex items-center",children:[(0,r.jsx)(l.A,{className:"w-5 h-5 mr-1"}),s.coinPrice*$," coins"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,r.jsx)(p.$,{onClick:Z,disabled:T||!U,variant:"outline",className:"w-full border-customOrange text-customOrange hover:bg-customOrange hover:text-white py-3 text-lg",children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"}),"Adding to Cart..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"w-5 h-5 mr-2"}),"Add to Cart"]})}),(0,r.jsxs)(p.$,{onClick:()=>{if(!U){v.toast.error("Please login to purchase items");return}if(s){if(0===s.availableStock){v.toast.error("Item is out of stock");return}if($>s.availableStock){v.toast.error(`Only ${s.availableStock} items available`);return}R(!0)}},disabled:!U,className:"w-full bg-customOrange hover:bg-orange-600 text-white py-3 text-lg",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 mr-2"}),"Buy Now"]})]}),!U&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Please login to purchase items"})]})]}),0===s.availableStock&&(0,r.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 p-4 rounded-lg",children:(0,r.jsx)("p",{className:"text-destructive font-medium text-center",children:"This item is currently out of stock"})})]})]}),(0,r.jsx)(h.Zp,{className:"mb-8",children:(0,r.jsxs)(h.Wu,{className:"p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-card-foreground mb-4",children:"Product Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Category"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:s.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Status"}),(0,r.jsx)(g.E,{variant:"ACTIVE"===s.status?"default":"secondary",children:s.status})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Added On"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:new Date(s.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-card-foreground mb-2",children:"Last Updated"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:new Date(s.updatedAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]})}),P.length>0&&(0,r.jsx)(h.Zp,{className:"mb-8",children:(0,r.jsxs)(h.Wu,{className:"p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-card-foreground mb-6",children:"Latest Items"}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:P.map(e=>(0,r.jsxs)(h.Zp,{className:"overflow-hidden group bg-card border shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer",onClick:()=>t.push(`/store/${e.id}`),children:[(0,r.jsxs)("div",{className:"relative h-48 bg-muted/30 flex items-center justify-center",children:[(0,r.jsx)(n.default,{src:e.image?.startsWith("http")?e.image:`http://localhost:4005/${e.image?.startsWith("/")?e.image.substring(1):e.image||"uploads/store/placeholder.jpg"}`,alt:e.name,className:"object-contain w-full h-full",width:200,height:200,onError:e=>{e.target.src="/logo.png"}}),0===e.availableStock&&(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,r.jsx)(g.E,{variant:"destructive",className:"text-sm",children:"Out of Stock"})})]}),(0,r.jsx)(h.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsx)("h3",{className:"font-semibold text-card-foreground text-sm line-clamp-2 group-hover:text-customOrange transition-colors",children:e.name}),(0,r.jsx)(g.E,{variant:"secondary",className:"text-xs ml-2 shrink-0",children:e.category})]}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 text-customOrange"}),(0,r.jsx)("span",{className:"font-bold text-customOrange text-sm",children:e.coinPrice})]}),(0,r.jsxs)("span",{className:`text-xs ${0===e.availableStock?"text-red-500":"text-green-600"}`,children:[e.availableStock," left"]})]})]})})]},e.id))})]})})]})}),(0,r.jsx)(b.default,{}),(0,r.jsx)(S.Lt,{open:L,onOpenChange:R,children:(0,r.jsxs)(S.EO,{children:[(0,r.jsxs)(S.wd,{children:[(0,r.jsx)(S.r7,{children:"Confirm Direct Purchase"}),(0,r.jsxs)(S.$v,{children:["Are you sure you want to purchase this item directly for ",(0,r.jsxs)("strong",{children:[s?.coinPrice*$," coins"]}),"?",(0,r.jsx)("br",{}),(0,r.jsx)("br",{}),(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{children:[s?.name," x",$]}),(0,r.jsxs)("span",{children:[s?.coinPrice*$," coins"]})]})}),(0,r.jsx)("br",{}),"This action cannot be undone and coins will be deducted from your account immediately."]})]}),(0,r.jsxs)(S.ck,{children:[(0,r.jsx)(S.Zr,{children:"Cancel"}),(0,r.jsx)(S.Rx,{onClick:M,disabled:W,className:"bg-customOrange hover:bg-customOrange/90",children:W?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Processing..."]}):"Confirm Purchase"})]})]})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.default,{}),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-foreground mb-4",children:"Product Not Found"}),(0,r.jsxs)(p.$,{onClick:()=>t.push("/store"),className:"bg-customOrange hover:bg-orange-600",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Back to Store"]})]})}),(0,r.jsx)(b.default,{})]})}},19080:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22710:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\store\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\store\\[id]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33885:(e,t,s)=>{Promise.resolve().then(s.bind(s,14180))},38621:(e,t,s)=>{Promise.resolve().then(s.bind(s,22710))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66874:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n,wL:()=>d});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},68767:(e,t,s)=>{"use strict";s.d(t,{Lq:()=>i,dI:()=>a});var r=s(28527);let a=async e=>{try{let t=new URLSearchParams;e?.category&&t.append("category",e.category),e?.status&&t.append("status",e.status),e?.search&&t.append("search",e.search),t.append("status","ACTIVE");let s=await r.S.get(`/admin/store?${t.toString()}`);return{success:!0,data:s.data.data}}catch(e){return{success:!1,error:e.response?.data?.message||e.message||"Failed to fetch store items"}}},i=async e=>{try{let t=await r.S.get(`/admin/store/${e}`);return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:e.response?.data?.message||e.message||"Failed to fetch store item"}}}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(60687),a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",e),...t})}},89689:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),c=s(30893),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(t,l);let o={children:["",{children:["store",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,22710)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\store\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\store\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/store/[id]/page",pathname:"/store/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(60687);s(43210);var a=s(11329),i=s(24224),n=s(4780);let c=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(c({variant:t}),e),...i})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7013,2800],()=>s(89689));module.exports=r})();