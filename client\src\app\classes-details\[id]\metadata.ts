import type { Metadata } from "next";
import { axiosInstance } from "@/lib/axios";
import { parseAndJoinArray } from "@/lib/helper";

interface TeacherData {
  id: string;
  firstName: string;
  lastName: string;
  className: string;
  email: string;
  contactNo: string;
  ClassAbout?: {
    profilePhoto?: string;
    catchyHeadline?: string;
    tutorBio?: string;
    experience?: string;
    qualifications?: string;
  };
  tuitionClasses?: Array<{
    subject?: string;
    details?: string;
    education?: string;
    coachingType?: string;
    pricingPerMonth?: number;
    pricingPerCourse?: number;
  }>;
  averageRating?: number;
  reviewCount?: number;
  isVerified?: boolean;
}

export async function fetchTeacherData(id: string): Promise<TeacherData | null> {
  try {
    const res = await axiosInstance.get(`classes/details/${id}`);
    return res.data;
  } catch (err) {
    console.error("Failed to fetch teacher data for SEO", err);
    return null;
  }
}

export async function generateClassesMetadata(id: string): Promise<Metadata> {
  const data = await fetchTeacherData(id);

  if (!data) {
    return {
      title: "Teacher Profile Not Found | Uest",
      description: "The requested teacher profile could not be found on Uest platform.",
      robots: {
        index: false,
        follow: false,
      },
    };
  }

  const { firstName, lastName, ClassAbout, tuitionClasses, className, averageRating, reviewCount } = data;
  const fullName = `${firstName} ${lastName}`;
  const profileImg = ClassAbout?.profilePhoto
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.profilePhoto}`
    : "/default-teacher-profile.jpg";
  const catchyHeadline = ClassAbout?.catchyHeadline || "Professional Educator";
  const tutorBio = ClassAbout?.tutorBio || "Explore the expertise of our educators.";
  const experience = ClassAbout?.experience || "";
  const qualifications = ClassAbout?.qualifications || "";
  
  const tuitionDetails = tuitionClasses?.length > 0
    ? parseAndJoinArray(tuitionClasses[0].subject || tuitionClasses[0].details || [])
    : "Educational Services";
  
  const subjects = tuitionClasses?.map(tc => tc.subject || tc.details).filter(Boolean).join(", ") || tuitionDetails;
  const pricing = tuitionClasses?.[0]?.pricingPerMonth || tuitionClasses?.[0]?.pricingPerCourse;

  // Enhanced description with more details
  const enhancedDescription = `${tutorBio} ${fullName} is a ${catchyHeadline} specializing in ${tuitionDetails}. ${experience ? `With ${experience} of experience, ` : ""}${qualifications ? `holding ${qualifications}, ` : ""}book your classes with ${fullName} on Uest today! ${averageRating ? `Rated ${averageRating}/5 by ${reviewCount || 0} students.` : ""}`;

  return {
    title: `${fullName} - ${catchyHeadline} | ${className} | Uest`,
    description: enhancedDescription.slice(0, 160), // Keep under 160 chars for SEO
    keywords: [
      fullName,
      className,
      catchyHeadline,
      tuitionDetails,
      subjects,
      "online education",
      "tuition classes",
      "private tutor",
      "home tuition",
      "online tutor",
      "Uest",
      "teacher profile",
      "book classes",
      "verified tutor",
      ...(tuitionClasses?.map(tc => tc.education).filter(Boolean) || []),
      ...(tuitionClasses?.map(tc => tc.coachingType).filter(Boolean) || []),
    ],
    openGraph: {
      title: `${fullName} - ${catchyHeadline} | Uest`,
      description: `${tutorBio} Specializing in ${tuitionDetails}. ${averageRating ? `⭐ ${averageRating}/5 rating` : ""}`,
      url: `https://www.uest.in/classes-details/${id}`,
      type: "profile",
      images: [
        {
          url: profileImg,
          width: 800,
          height: 600,
          alt: `${fullName} - ${catchyHeadline} Profile Photo`,
        },
      ],
      siteName: "Uest",
    },
    twitter: {
      card: "summary_large_image",
      title: `${fullName} - ${catchyHeadline}`,
      description: `${tutorBio} Specializing in ${tuitionDetails}. Book classes on Uest!`,
      images: [profileImg],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical: `https://www.uest.in/classes-details/${id}`,
    },
    other: {
      "application-name": "Uest",
      "apple-mobile-web-app-title": `${fullName} - Uest`,
      "format-detection": "telephone=no",
    },
  };
}

export function generateClassesStructuredData(data: TeacherData, id: string) {
  const { firstName, lastName, ClassAbout, tuitionClasses, className, averageRating, reviewCount, email, contactNo } = data;
  const fullName = `${firstName} ${lastName}`;
  const profileImg = ClassAbout?.profilePhoto
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.profilePhoto}`
    : "/default-teacher-profile.jpg";
  const catchyHeadline = ClassAbout?.catchyHeadline || "Professional Educator";
  const tutorBio = ClassAbout?.tutorBio || "Professional educator providing quality education.";
  
  const subjects = tuitionClasses?.map(tc => tc.subject || tc.details).filter(Boolean) || [];
  const pricing = tuitionClasses?.[0]?.pricingPerMonth || tuitionClasses?.[0]?.pricingPerCourse;

  return {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": fullName,
    "jobTitle": catchyHeadline,
    "description": tutorBio,
    "image": profileImg,
    "url": `https://www.uest.in/classes-details/${id}`,
    "email": email,
    "telephone": contactNo,
    "worksFor": {
      "@type": "Organization",
      "name": className,
      "url": "https://www.uest.in"
    },
    "knowsAbout": subjects,
    "hasOccupation": {
      "@type": "Occupation",
      "name": "Private Tutor",
      "occupationLocation": {
        "@type": "Place",
        "name": "Online"
      },
      "skills": subjects
    },
    "offers": tuitionClasses?.map(tc => ({
      "@type": "Offer",
      "name": `${tc.subject || tc.details} Classes`,
      "description": `${tc.education} ${tc.coachingType} classes`,
      "price": tc.pricingPerMonth || tc.pricingPerCourse,
      "priceCurrency": "INR",
      "availability": "https://schema.org/InStock"
    })) || [],
    "aggregateRating": averageRating ? {
      "@type": "AggregateRating",
      "ratingValue": averageRating,
      "reviewCount": reviewCount || 0,
      "bestRating": 5,
      "worstRating": 1
    } : undefined,
    "provider": {
      "@type": "Organization",
      "name": "Uest",
      "url": "https://www.uest.in",
      "logo": "https://www.uest.in/logo.png"
    }
  };
}
