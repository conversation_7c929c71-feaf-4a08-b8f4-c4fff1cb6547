(()=>{var e={};e.id=3980,e.ids=[3980],e.modules={69:(e,t,s)=>{Promise.resolve().then(s.bind(s,92564))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20181:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(60687),r=s(92449);s(43210);let i=({count:e})=>(0,a.jsxs)("svg",{className:"h-10 w-10 sm:h-12 sm:w-12",viewBox:"0 0 1550 1808",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M574.764 165.821C692.4 74.0944 857.6 74.0944 975.236 165.821L1262.05 386.652C1354.36 459.21 1407.96 571.58 1407.96 690.458V1117.54C1407.96 1236.42 1354.36 1348.79 1262.05 1421.35L975.236 1642.18C857.6 1733.91 692.4 1733.91 574.764 1642.18L287.951 1421.35C195.639 1348.79 142.037 1236.42 142.037 1117.54V690.458C142.037 571.58 195.639 459.21 287.951 386.652L574.764 165.821Z",fill:"#FDFEF9"}),(0,a.jsx)("mask",{id:"mask0",maskUnits:"userSpaceOnUse",x:"71",y:"180",width:"1408",height:"1484",children:(0,a.jsx)("path",{d:"M574.315 248.124C692.703 157.412 857.297 157.412 975.685 248.124L1262.5 468.955C1354.81 541.513 1408.41 653.884 1408.41 772.762V1199.84C1408.41 1318.72 1354.81 1431.09 1262.5 1503.65L975.685 1724.48C857.297 1815.19 692.703 1815.19 574.315 1724.48L287.502 1503.65C195.19 1431.09 141.588 1318.72 141.588 1199.84V772.762C141.588 653.884 195.19 541.513 287.502 468.955L574.315 248.124Z",fill:"#CCCCCC"})}),(0,a.jsx)("g",{mask:"url(#mask0)",children:(0,a.jsx)("rect",{x:"48",y:"146",width:"1454",height:"821",fill:"#CCCCCC"})}),(0,a.jsx)("path",{d:"M658.6 1303.6C658.6 1257.73 713.743 1221.6 783 1221.6C852.257 1221.6 907.4 1257.73 907.4 1303.6C907.4 1328.13 861.847 1343.6 783 1343.6C704.153 1343.6 658.6 1328.13 658.6 1303.6Z",fill:"#CCCCCC"}),(0,a.jsx)("path",{d:"M771.553 390C809.071 390 837.553 418.482 837.553 456C837.553 475.694 829.359 495.486 814.097 508.145L776.947 539.162C772.316 543.05 766.79 545.197 761.062 545.197C755.335 545.197 749.809 543.05 745.178 539.162L707.303 508.09C691.856 495.367 683.553 475.418 683.553 455.5C683.553 417.982 712.035 390 749.553 390H771.553Z",fill:"white"}),(0,a.jsx)("path",{d:"M774.5 828C743.884 828 719 852.884 719 883.5C719 914.116 743.884 939 774.5 939C805.116 939 830 914.116 830 883.5C830 852.884 805.116 828 774.5 828Z",fill:"#CCCCCC"}),(0,a.jsx)("path",{d:"M1043.07 450.583L1028.87 437.844C1028.55 437.56 1028.07 437.593 1027.79 437.917L1002.56 467.325C1002.28 467.648 1002.31 468.13 1002.64 468.413L1016.84 481.152C1017.16 481.436 1017.64 481.403 1017.93 481.079L1043.16 451.671C1043.44 451.348 1043.41 450.866 1043.07 450.583Z",fill:"white"}),(0,a.jsx)("path",{d:"M1044.49 451.93C1044.25 451.709 1043.87 451.743 1043.66 452.012L1018.13 484.509C1017.92 484.778 1017.95 485.158 1018.22 485.371L1020.46 487.151C1020.73 487.365 1021.11 487.331 1021.32 487.062L1046.85 454.565C1047.06 454.296 1047.03 453.916 1046.76 453.702L1044.49 451.93Z",fill:"white"}),(0,a.jsx)("path",{d:"M1044.53 453.612L1018.39 486.109C1018.17 486.385 1018.2 486.794 1018.46 487.033L1023.27 491.522C1023.53 491.76 1023.95 491.73 1024.18 491.454L1050.32 458.957C1050.54 458.681 1050.51 458.272 1050.25 458.033L1045.44 453.544C1045.18 453.306 1044.77 453.336 1044.53 453.612Z",fill:"white"}),(0,a.jsx)("text",{x:"50%",y:"80%",textAnchor:"middle",fill:"#222",fontSize:"300",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:e})]});var n=s(30474);function l({badge:e}){return e?.badges?.length?(0,a.jsx)("div",{className:"flex gap-3 mt-2",children:e.badges.map((e,t)=>(0,a.jsx)(r.P.div,{className:"relative",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},children:"DailyStreak"===e.badgeType?(0,a.jsx)(i,{count:e.count??0}):(0,a.jsx)(n.default,{src:e.badgeSrc??"/placeholder.png",alt:e.badgeAlt??"Badge",width:48,height:48,className:"object-contain sm:w-12 sm:h-12 w-10 h-10"})},t))}):null}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36517:(e,t,s)=>{Promise.resolve().then(s.bind(s,73646))},40705:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["mock-exam-result",{children:["[studentId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,92564)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/mock-exam-result/[studentId]/page",pathname:"/mock-exam-result/[studentId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57022:(e,t,s)=>{"use strict";s.d(t,{S:()=>i,q:()=>r});var a=s(28527);let r=async e=>{try{let t=await a.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){return{success:!1,error:`Failed to save mock exam result: ${e.response?.data?.message||e.message}`}}},i=async(e,t=1,s=10,r={})=>{try{let i=new URLSearchParams({page:t.toString(),limit:s.toString(),...void 0!==r.isWeekly&&{isWeekly:r.isWeekly.toString()}}).toString(),n=await a.S.get(`/mock-exam-result/${e}?${i}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:n.data}}catch(e){return{success:!1,error:`Failed to get mock exam result: ${e.response?.data?.message||e.message}`}}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73646:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(60687),r=s(43210),i=s(4780);s(57022);var n=s(16189),l=s(90269),o=s(46303),d=s(92449),c=s(30474);let u=({totalCoins:e,badgeSrc:t,badgeAlt:s})=>null===e?null:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm",children:[(0,a.jsx)(c.default,{src:"/uest_coin.png",alt:"Coin",width:20,height:20,className:"w-5 h-5"}),e," Coins"]}),t&&s&&(0,a.jsx)(d.P.div,{animate:{scale:[1,1.05,1]},transition:{repeat:1/0,duration:1.5,ease:"easeInOut"},className:"w-12 h-12 flex items-center justify-center",children:(0,a.jsx)(c.default,{src:t,alt:s,width:48,height:48,className:"object-contain"})})]});var x=s(20181);let m=function(){(0,n.useParams)().studentId;let[e,t]=(0,r.useState)(null),[s,m]=(0,r.useState)(!1),[p,h]=(0,r.useState)(null),[g,f]=(0,r.useState)(null),[b,y]=(0,r.useState)(null),[v,j]=(0,r.useState)(null),[C,w]=(0,r.useState)(1),N=e=>new Date(e).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit",year:"numeric"}).split("/").join("-");return s?(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center text-xl font-medium text-gray-700 animate-pulse",children:"Loading daily quiz results..."})}):p?(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-xl font-medium text-red-600 bg-red-50 p-4 rounded-lg shadow-md",children:["Error: ",p]})}):e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.default,{}),(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen py-16 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-4xl font-extrabold text-gray-800 text-center mb-10 tracking-tight",children:"Daily Quiz Results"}),(0,a.jsx)(d.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},className:"mb-10 rounded-3xl p-[2px] bg-gradient-to-tr from-orange-400 via-yellow-400 to-amber-500",children:(0,a.jsx)("div",{className:"rounded-3xl bg-white/80 backdrop-blur-md p-6 md:p-8 shadow-xl",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start justify-between gap-6 relative",children:[(0,a.jsx)(d.P.div,{animate:{scale:[1,1.1,1],rotate:[0,1,-1,0]},transition:{repeat:1/0,duration:2,ease:"easeInOut"},className:"w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-inner ring-2 ring-amber-200",children:(0,a.jsx)("span",{className:"text-3xl",children:"\uD83D\uDD25"})}),(0,a.jsxs)("div",{className:"text-center sm:text-left flex-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-neutral-800 mb-1 tracking-tight",children:"Daily Streak"}),(0,a.jsxs)("p",{className:"text-4xl font-extrabold text-amber-600 tracking-wider",children:[e.streak.streakCount," ",1===e.streak.streakCount?"Day":"Days"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1 italic",children:"Stay consistent and keep growing"}),(0,a.jsx)(x.A,{badge:e.badge})]}),(0,a.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,a.jsx)("div",{className:"px-4 py-2 bg-white rounded-full shadow ring-1 ring-amber-300 text-sm font-medium text-amber-600",children:"Consistency Reward"}),(0,a.jsx)(u,{totalCoins:g,badgeSrc:b,badgeAlt:v})]})]})})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsx)("div",{className:"space-y-4",children:0===e.mockExamResults.length?(0,a.jsx)("div",{className:"text-center text-gray-500 text-lg font-medium py-8",children:"No daily quiz results found."}):e.mockExamResults.map((e,t)=>(0,a.jsxs)("div",{className:(0,i.cn)("flex items-center justify-between p-5 rounded-xl transition-all duration-200",0===t?"bg-orange-50 border-l-4 border-orange-500":"bg-gray-50 hover:bg-gray-100 hover:shadow-md"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-5",children:[(0,a.jsxs)("div",{className:(0,i.cn)("w-12 h-12 flex items-center justify-center rounded-full font-semibold text-sm shadow-sm",0===t?"bg-orange-500 text-white":"bg-gray-200 text-gray-700"),children:["#",(C-1)*10+t+1]}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:(0,i.cn)("font-semibold",0===t?"text-lg text-gray-800":"text-base text-gray-700"),children:N(e.createdAt)})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[e.coinEarnings>=0&&(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm",children:[(0,a.jsx)(c.default,{src:"/uest_coin.png",alt:"Coin",width:20,height:20,className:"w-5 h-5"}),e.coinEarnings]}),(0,a.jsxs)("div",{className:(0,i.cn)("font-semibold text-orange-600 bg-orange-100 px-4 py-1.5 rounded-full shadow-sm",0===t?"text-base":"text-sm"),children:[e.score,"/10"]})]})]},e.id))}),e?.pagination?.totalPages>1&&(0,a.jsxs)("div",{className:"flex justify-center gap-6 mt-8",children:[(0,a.jsx)("button",{onClick:()=>{C>1&&w(C-1)},disabled:1===C,className:(0,i.cn)("bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",1===C?"opacity-50 cursor-not-allowed":"hover:bg-orange-600 hover:scale-105"),children:"Previous"}),(0,a.jsxs)("span",{className:"self-center text-gray-600 font-medium",children:["Page ",C," of ",e?.pagination?.totalPages||1]}),(0,a.jsx)("button",{onClick:()=>{e?.pagination&&C<e.pagination.totalPages&&w(C+1)},disabled:C===(e?.pagination?.totalPages||1),className:(0,i.cn)("bg-orange-500 text-white font-semibold py-2.5 px-6 rounded-full transition-all duration-200",C===(e?.pagination?.totalPages||1)?"opacity-50 cursor-not-allowed":"hover:bg-orange-600 hover:scale-105"),children:"Next"})]})]})]})}),(0,a.jsx)(o.default,{})]}):(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-200 min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center text-xl font-medium text-gray-700",children:"No data available"})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92564:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\mock-exam-result\\\\[studentId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,7013,2449,2800],()=>s(40705));module.exports=a})();