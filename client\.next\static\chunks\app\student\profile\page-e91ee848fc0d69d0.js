(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3612],{7583:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});var r=a(95155);a(12115);var s=a(6874),l=a.n(s),n=a(66766),o=a(29911);let i=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(n.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:o.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:o.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:o.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:o.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:o.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:o.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:o.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:s}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:s,children:(0,r.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},s)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(n.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},22346:(e,t,a)=>{"use strict";a.d(t,{Separator:()=>n});var r=a(95155);a(12115);var s=a(14050),l=a(59434);function n(e){let{className:t,orientation:a="horizontal",decorative:n=!0,...o}=e;return(0,r.jsx)(s.b,{"data-slot":"separator-root",decorative:n,orientation:a,className:(0,l.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},24944:(e,t,a)=>{"use strict";a.d(t,{k:()=>n});var r=a(95155);a(12115);var s=a(55863),l=a(59434);function n(e){let{className:t,value:a,...n}=e;return(0,r.jsx)(s.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...n,children:(0,r.jsx)(s.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}},39227:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>J});var r=a(95155),s=a(12115),l=a(90221),n=a(62177),o=a(55594),i=a(56671),c=a(35695),d=a(7632),u=a(69074),m=a(84355),h=a(5196),x=a(54416),g=a(29869),p=a(57434),f=a(34540),b=a(56762),j=a(93457),v=a(75937),N=a(62523),y=a(30285),w=a(88539),k=a(59409),C=a(66695),z=a(14636),M=a(85511),A=a(59434),S=a(66766),P=a(70347),B=a(22346),I=a(24944),R=a(7583),D=a(40646);function E(e){let{items:t,activeSection:a,setActiveSection:s}=e,{profileData:l}=(0,f.d4)(e=>e.studentProfile),n=e=>{if(!(null==l?void 0:l.profile))return!1;let t=l.profile;switch(e){case"Personal Info":var a,r,s,n,o;return!!((null===(a=t.student)||void 0===a?void 0:a.firstName)&&(null===(r=t.student)||void 0===r?void 0:r.middleName)&&(null===(s=t.student)||void 0===s?void 0:s.lastName)&&(null===(n=t.student)||void 0===n?void 0:n.contact)&&(null===(o=t.student)||void 0===o?void 0:o.email)&&t.birthday&&t.school&&t.address&&t.medium&&t.classroom&&t.photo&&t.documentUrl);case"Other Info":return!!(t.aadhaarNo||t.bloodGroup||t.birthPlace||t.motherTongue||t.religion||t.caste||t.subCaste);default:return!1}};return(0,r.jsx)("nav",{className:"space-y-1",children:t.map((e,l)=>{let o=a===e.href.replace("#",""),i=n(e.title),c=l>0&&!n(t[l-1].title);return(0,r.jsxs)("button",{onClick:()=>!c&&s(e.href.replace("#","")),className:"flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ".concat(o?"bg-muted text-primary":c?"text-gray-400 cursor-not-allowed":"text-muted-foreground hover:text-primary"),disabled:c,children:[(0,r.jsx)("span",{children:e.title}),i&&(0,r.jsx)(D.A,{size:16,className:"text-green-500"})]},e.href)})})}let F=o.z.object({firstName:o.z.string().min(2,"First name must be at least 2 characters."),middleName:o.z.string().min(2,"Middle name must be at least 2 characters."),lastName:o.z.string().min(2,"Last name must be at least 2 characters."),mothersName:o.z.string().optional(),email:o.z.string().email("Please enter a valid email address.").min(1,"Email is required"),contact:o.z.string().min(10,"Contact number must be at least 10 digits.").max(15,"Contact number must not exceed 15 digits.").regex(/^\d+$/,"Contact number must contain only digits."),contact2:o.z.string().min(10,"Contact number must be at least 10 digits.").max(15,"Contact number must not exceed 15 digits.").regex(/^\d+$/,"Contact number must contain only digits.").optional().or(o.z.literal("")),medium:o.z.string().min(1,"Medium of instruction is required"),classroom:o.z.string().min(1,"Standard is required"),gender:o.z.string().optional(),birthday:o.z.date({required_error:"Please select your date of birth"}),school:o.z.string().min(2,"School name must be at least 2 characters."),address:o.z.string().min(5,"Address must be at least 5 characters."),age:o.z.string().optional(),aadhaarNumber:o.z.string().min(12,"Aadhaar number must be 12 digits.").max(12,"Aadhaar number must be 12 digits.").regex(/^\d+$/,"Aadhaar number must contain only digits.").optional().or(o.z.literal("")),bloodGroup:o.z.string().optional(),birthPlace:o.z.string().optional(),motherTongue:o.z.string().optional(),religion:o.z.string().optional(),caste:o.z.string().optional(),subCaste:o.z.string().optional(),photo:o.z.any().optional(),document:o.z.any().optional()}),T=[{title:"Personal Info",href:"#personal-info"},{title:"Other Info",href:"#other-info"}],L=()=>{let e=(0,c.useRouter)(),t=(0,f.wA)(),a=(0,c.useSearchParams)(),o="true"===a.get("quiz"),D=a.get("examId"),[L,J]=(0,s.useState)("personal-info"),[U,O]=(0,s.useState)(null),[_,V]=(0,s.useState)(!1),[W,G]=(0,s.useState)(!1),[$,q]=(0,s.useState)(null),[Y,H]=(0,s.useState)(null),[Z,K]=(0,s.useState)(!1),[X,Q]=(0,s.useState)(0),[ee,et]=(0,s.useState)(null),ea=(0,s.useRef)(null),er=e=>{let t=new Date,a=t.getFullYear()-e.getFullYear(),r=t.getMonth()-e.getMonth();return(r<0||0===r&&t.getDate()<e.getDate())&&a--,a},{profileData:es,loading:el}=(0,f.d4)(e=>e.studentProfile),en=(null==es?void 0:es.profile)||null,eo=(null==es?void 0:es.classroomOptions)||[],ei=(0,s.useRef)(null),ec=(0,s.useRef)(null),ed=(0,n.mN)({resolver:(0,l.u)(F),defaultValues:{firstName:"",middleName:"",lastName:"",mothersName:"",email:"",contact:"",contact2:"",medium:"",classroom:"",gender:"",birthday:void 0,school:"",address:"",age:"",aadhaarNumber:"",bloodGroup:"",birthPlace:"",motherTongue:"",religion:"",caste:"",subCaste:""},mode:"onSubmit"});(0,s.useEffect)(()=>{localStorage.getItem("studentToken")||(i.toast.error("Please login to access your profile"),e.push("/"))},[e]),(0,s.useEffect)(()=>{localStorage.getItem("studentToken")&&t((0,b.N)())},[t]),(0,s.useEffect)(()=>{if(null==es?void 0:es.profile){var e,t,a,r;let s=es.profile,l=0;(null===(e=s.student)||void 0===e?void 0:e.firstName)&&(null===(t=s.student)||void 0===t?void 0:t.lastName)&&(null===(a=s.student)||void 0===a?void 0:a.contact)&&(null===(r=s.student)||void 0===r?void 0:r.email)&&s.birthday&&s.school&&s.address&&s.medium&&s.classroom&&s.photo&&s.documentUrl&&l++,(s.aadhaarNo||s.bloodGroup||s.birthPlace||s.motherTongue||s.religion||s.caste||s.subCaste)&&l++,Q(l/2*100)}},[es]),(0,s.useEffect)(()=>{var e;if(!es)return;let t=es.profile,a=(null==t?void 0:t.student)||JSON.parse(localStorage.getItem("student_data")||"{}");console.log("aaaaaaaaaaaaa",a),console.log("bbbbbbbbbbbb",t);let r={firstName:(null==a?void 0:a.firstName)||"",middleName:(null==a?void 0:a.middleName)||"",lastName:(null==a?void 0:a.lastName)||"",mothersName:(null==a?void 0:a.mothersName)||"",email:(null==a?void 0:a.email)||"",contact:(null==a?void 0:a.contactNo)||(null==a?void 0:a.contact)||"",contact2:(null==t?void 0:t.contactNo2)||"",medium:(null==t?void 0:t.medium)||"",classroom:(null==t?void 0:t.classroom)||"",gender:(null==t?void 0:t.gender)||"",birthday:(null==t?void 0:t.birthday)?new Date(t.birthday):void 0,school:(null==t?void 0:t.school)||"",address:(null==t?void 0:t.address)||"",age:(null==t?void 0:null===(e=t.age)||void 0===e?void 0:e.toString())||"",aadhaarNumber:(null==t?void 0:t.aadhaarNo)||"",bloodGroup:(null==t?void 0:t.bloodGroup)||"",birthPlace:(null==t?void 0:t.birthPlace)||"",motherTongue:(null==t?void 0:t.motherTongue)||"",religion:(null==t?void 0:t.religion)||"",caste:(null==t?void 0:t.caste)||"",subCaste:(null==t?void 0:t.subCaste)||""};if((null==t?void 0:t.photo)&&!U&&(O(t.photo),ed.setValue("photo",t.photo)),(null==t?void 0:t.documentUrl)&&!Y&&!Z){let e=t.documentUrl.startsWith("http")?t.documentUrl:"".concat("http://localhost:4005/").concat(t.documentUrl),a={name:e.split("/").pop()||"Uploaded Document",size:0,url:e,type:"application/octet-stream"};H(a),ed.setValue("document",a)}let s=ed.getValues(),l=!s.firstName&&!s.lastName&&!s.contact,n=!s.medium||!s.classroom;(l||n)&&ed.reset(r)},[es,ed,U,Y,Z]);let eu=e=>{if(e.size>5242880){i.toast.error("Photo size exceeds 5MB limit");return}et(e);let t=new FileReader;t.onload=e=>{var t;return O(null===(t=e.target)||void 0===t?void 0:t.result)},t.readAsDataURL(e)},em=async()=>{q(null);try{var e;if(!(null===(e=navigator.mediaDevices)||void 0===e?void 0:e.getUserMedia))throw Error("Camera not supported on this device");V(!0);let t=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});ei.current&&(ei.current.srcObject=t,ei.current.onloadedmetadata=()=>{var e;null===(e=ei.current)||void 0===e||e.play().catch(()=>i.toast.error("Error starting camera preview"))})}catch(t){V(!1);let e="NotAllowedError"===t.name?"Please allow camera access in your browser settings.":"Could not access camera. Please check your camera settings.";q(e),i.toast.error(e)}},eh=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:800,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.6;if(!e.getContext("2d"))return"";let r=e.width,s=e.height,l=r,n=s;r>t&&(l=t,n=s*t/r);let o=document.createElement("canvas");o.width=l,o.height=n;let i=o.getContext("2d");return i?(i.drawImage(e,0,0,l,n),o.toDataURL("image/jpeg",a)):""},ex=()=>{if(!ei.current||!ec.current)return;let e=ei.current,a=ec.current,r=a.getContext("2d");a.width=e.videoWidth,a.height=e.videoHeight,null==r||r.clearRect(0,0,a.width,a.height),null==r||r.save(),null==r||r.scale(-1,1),null==r||r.drawImage(e,-a.width,0,a.width,a.height),null==r||r.restore();let s=eh(a,800,.6);if(3*s.split(",")[1].length/4/1024>5120){i.toast.error("Photo size exceeds 5MB limit. Please try again.");return}O(s),ed.setValue("photo",s),t((0,j.XY)(s)),eg()},eg=()=>{var e;(null===(e=ei.current)||void 0===e?void 0:e.srcObject)&&(ei.current.srcObject.getTracks().forEach(e=>e.stop()),ei.current.srcObject=null),V(!1),q(null)},ep=()=>{Y&&"url"in Y&&Y.url.startsWith("blob:")&&URL.revokeObjectURL(Y.url),H(null),K(!0);let e=document.getElementById("document");e&&(e.value=""),ed.setValue("document",null)},ef=e=>e<1024?e+" bytes":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",eb=async a=>{G(!0);try{var r,s,l,n,c,d,u;if(!(U||(null==es?void 0:null===(r=es.profile)||void 0===r?void 0:r.photo))){i.toast.error("Please capture a photo for your profile"),G(!1);return}if(!Y||Z){i.toast.error("Identity document is required. Please upload a document."),G(!1);return}if(!await ed.trigger()){console.log("Form validation failed"),i.toast.error("Please fill in all required fields correctly"),G(!1);return}let m={firstName:a.firstName.charAt(0).toUpperCase()+a.firstName.slice(1).toLowerCase(),middleName:a.middleName.charAt(0).toUpperCase()+a.middleName.slice(1).toLowerCase(),lastName:a.lastName.charAt(0).toUpperCase()+a.lastName.slice(1).toLowerCase(),mothersName:a.mothersName,email:a.email,contact:a.contact,contact2:a.contact2,medium:a.medium,classroom:a.classroom,gender:a.gender,birthday:(null===(s=a.birthday)||void 0===s?void 0:s.toISOString())||"",school:a.school,address:a.address,age:a.age,aadhaarNumber:a.aadhaarNumber,bloodGroup:a.bloodGroup,birthPlace:a.birthPlace,motherTongue:a.motherTongue,religion:a.religion,caste:a.caste,subCaste:a.subCaste};if(null==U?void 0:U.startsWith("data:")){let e=U.split(",")[1];if(3*e.length/4/1024>5120){i.toast.error("Photo size exceeds 5MB limit.");return}m.photo=e,m.photoMimeType="image/jpeg"}if(Y instanceof File||Y&&"url"in Y&&Y.url.startsWith("blob:")){let e=Y instanceof File?Y:await fetch(Y.url).then(e=>e.blob()).then(e=>new File([e],Y.name,{type:Y.type})),t=await new Promise((t,a)=>{let r=new FileReader;r.onload=()=>t(r.result.split(",")[1]),r.onerror=a,r.readAsDataURL(e)});if(3*t.length/4/1024>5120){i.toast.error("Document size exceeds 5MB limit.");return}m.document=t,m.documentMimeType=e.type,m.documentName=e.name}if(Z&&(null==es?void 0:null===(l=es.profile)||void 0===l?void 0:l.documentUrl)&&(m.removeDocument=!0),!localStorage.getItem("studentToken")){i.toast.error("Please login to submit your profile"),e.push("/");return}let h=await t((0,b.A)(m));if("fulfilled"===h.meta.requestStatus){i.toast.success(en?"Profile updated successfully!":"Profile created successfully!");let r=JSON.parse(localStorage.getItem("student_data")||"{}"),s={...r,id:r.id||(null==es?void 0:null===(c=es.profile)||void 0===c?void 0:null===(n=c.student)||void 0===n?void 0:n.id)||"",firstName:a.firstName.charAt(0).toUpperCase()+a.firstName.slice(1).toLowerCase(),middleName:a.middleName.charAt(0).toUpperCase()+a.middleName.slice(1).toLowerCase(),lastName:a.lastName.charAt(0).toUpperCase()+a.lastName.slice(1).toLowerCase(),mothersName:a.mothersName,email:a.email||r.email||(null==es?void 0:null===(u=es.profile)||void 0===u?void 0:null===(d=u.student)||void 0===d?void 0:d.email)||"",contact:a.contact};localStorage.setItem("student_data",JSON.stringify(s)),K(!1),await t((0,b.N)()),o&&(D?e.push("/uwhiz-exam/".concat(D)):e.push("/mock-test"))}else if("rejected"===h.meta.requestStatus){let t=h.payload;t.includes("401")||t.includes("Unauthorized")?(i.toast.error("Your session has expired. Please login again."),localStorage.removeItem("studentToken"),e.push("/")):i.toast.error(t||"Failed to update profile")}}catch(e){i.toast.error("Failed to submit profile information")}finally{G(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(P.default,{}),(0,r.jsxs)("div",{className:"space-y-6 p-10 pb-4 md:block",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Student Profile"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Complete your profile information. Your progress will be automatically saved as you complete each section."})]}),(0,r.jsx)(I.k,{value:X,className:"h-2"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[Math.round(X),"% complete"]}),(0,r.jsx)(B.Separator,{className:"my-6"}),(0,r.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[(0,r.jsx)("aside",{className:"-mx-4 lg:w-1/6 pb-12",children:(0,r.jsx)(E,{items:T,activeSection:L,setActiveSection:J})}),(0,r.jsx)("div",{className:"flex justify-center w-full",children:(0,r.jsx)("div",{className:"flex-1 lg:max-w-2xl pb-12",children:el?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,r.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading profile information..."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-medium",children:["personal-info"===L&&"Personal Information","other-info"===L&&"Other Information"]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["personal-info"===L&&"Enter your basic personal details, contact information, medium, standard, photo and documents","other-info"===L&&"Provide additional details like Aadhaar number, blood group, birth place, etc."]})]}),(0,r.jsx)(B.Separator,{}),(0,r.jsx)(v.lV,{...ed,children:(0,r.jsxs)("form",{onSubmit:ed.handleSubmit(eb),className:"space-y-8",children:["personal-info"===L&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(v.zB,{control:ed.control,name:"firstName",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"First Name *"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter First Name"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"middleName",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Middle Name *"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Middle Name"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"lastName",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Last Name *"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Last Name"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"mothersName",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Mothers Name"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Mother's Name"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}})]}),(0,r.jsx)(v.zB,{control:ed.control,name:"email",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Email *"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Email",type:"email"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(v.zB,{control:ed.control,name:"contact",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Contact Number *"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"8520369851",type:"tel",inputMode:"numeric",pattern:"[0-9]*",onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:e=>{let a=e.target.value.replace(/\D/g,"");t.onChange(a)}})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"contact2",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Contact Number 2"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Alternate Number",type:"tel",inputMode:"numeric",pattern:"[0-9]*",onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:e=>{let a=e.target.value.replace(/\D/g,"");t.onChange(a)}})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(v.zB,{control:ed.control,name:"gender",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Gender"}),(0,r.jsxs)(k.l6,{onValueChange:t.onChange,value:t.value||void 0,children:[(0,r.jsx)(v.MJ,{children:(0,r.jsx)(k.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,r.jsx)(k.yv,{placeholder:"Select"})})}),(0,r.jsxs)(k.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,r.jsx)(k.eb,{value:"male",children:"Male"}),(0,r.jsx)(k.eb,{value:"female",children:"Female"}),(0,r.jsx)(k.eb,{value:"other",children:"Other"})]})]}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"age",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Age"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Age",type:"number",min:"1",max:"100"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"birthday",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{className:"flex flex-col",children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Date of Birth *"}),(0,r.jsxs)(z.AM,{children:[(0,r.jsx)(z.Wv,{asChild:!0,children:(0,r.jsx)(v.MJ,{children:(0,r.jsxs)(y.$,{variant:"outline",className:(0,A.cn)("w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg",!t.value&&"text-muted-foreground"),children:[t.value&&t.value instanceof Date&&!isNaN(t.value.getTime())?(0,d.GP)(t.value,"PPP"):(0,r.jsx)("span",{children:"Select your birthday"}),(0,r.jsx)(u.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,r.jsxs)(z.hl,{className:"w-auto p-0 bg-white border border-gray-300 shadow-lg",align:"start",children:[(0,r.jsx)("div",{className:"p-3 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,r.jsxs)(k.l6,{value:t.value?t.value.getFullYear().toString():"",onValueChange:e=>{let a=new Date(t.value||new Date);a.setFullYear(parseInt(e)),t.onChange(a)},children:[(0,r.jsx)(k.bq,{className:"w-24",children:(0,r.jsx)(k.yv,{placeholder:"Year"})}),(0,r.jsx)(k.gC,{className:"max-h-48",children:Array.from({length:125},(e,t)=>{let a=new Date().getFullYear()-t;return(0,r.jsx)(k.eb,{value:a.toString(),children:a},a)})})]}),(0,r.jsxs)(k.l6,{value:t.value?t.value.getMonth().toString():"",onValueChange:e=>{let a=new Date(t.value||new Date);a.setMonth(parseInt(e)),t.onChange(a)},children:[(0,r.jsx)(k.bq,{className:"w-32",children:(0,r.jsx)(k.yv,{placeholder:"Month"})}),(0,r.jsx)(k.gC,{children:["January","February","March","April","May","June","July","August","September","October","November","December"].map((e,t)=>(0,r.jsx)(k.eb,{value:t.toString(),children:e},t))})]})]})}),(0,r.jsx)(M.V,{mode:"single",selected:t.value,onSelect:e=>{if(t.onChange(e),e){let t=er(e);ed.setValue("age",t.toString())}},disabled:e=>e>new Date||e<new Date("1900-01-01"),month:t.value||new Date,className:"rounded-md border-0"})]})]}),(0,r.jsx)(v.Rr,{className:"text-xs text-gray-500",children:"Your date of birth will be verified with your documents"}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}})]}),(0,r.jsx)(v.zB,{control:ed.control,name:"address",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Address *"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(w.T,{...t,rows:3,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none",placeholder:"Enter your full address"})}),(0,r.jsx)(v.Rr,{className:"text-xs text-gray-500",children:"Provide your complete residential address"}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"school",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"School Name *"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter School"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(v.zB,{control:ed.control,name:"medium",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Medium *"}),(0,r.jsxs)(k.l6,{onValueChange:e=>{t.onChange(e)},value:t.value||void 0,children:[(0,r.jsx)(v.MJ,{children:(0,r.jsx)(k.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,r.jsx)(k.yv,{placeholder:"Select"})})}),(0,r.jsxs)(k.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,r.jsx)(k.eb,{value:"english",children:"English"}),(0,r.jsx)(k.eb,{value:"gujarati",children:"Gujarati"})]})]}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"classroom",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Standard *"}),(0,r.jsxs)(k.l6,{onValueChange:e=>{t.onChange(e)},value:t.value||void 0,children:[(0,r.jsx)(v.MJ,{children:(0,r.jsx)(k.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,r.jsx)(k.yv,{placeholder:"Select"})})}),(0,r.jsx)(k.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:el?(0,r.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}):eo.length>0?eo.map(e=>(0,r.jsx)(k.eb,{value:e.value,children:e.value},e.id)):(0,r.jsx)("div",{className:"p-2 text-center text-gray-500",children:"No classroom options available"})})]}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}})]}),(0,r.jsx)(v.zB,{control:ed.control,name:"photo",render:()=>{var e;return(0,r.jsxs)(C.Zp,{className:"shadow-lg border-0",children:[(0,r.jsxs)(C.aR,{className:"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",children:[(0,r.jsx)(C.ZB,{className:"text-lg font-medium text-gray-800",children:"Student Image *"}),(0,r.jsx)(C.BT,{className:"text-gray-600",children:"Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)"})]}),(0,r.jsxs)(C.Wu,{children:[(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.MJ,{children:(0,r.jsxs)("div",{children:[$&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-700 text-sm",children:$})}),!_&&!U&&(0,r.jsxs)(y.$,{type:"button",onClick:em,className:"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Open Camera"]}),_&&(0,r.jsxs)("div",{className:"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm",children:[(0,r.jsx)("video",{ref:ei,autoPlay:!0,playsInline:!0,className:"w-full h-auto transform scale-x-[-1]"}),(0,r.jsxs)("div",{className:"flex p-4 bg-gray-50",children:[(0,r.jsxs)(y.$,{type:"button",onClick:ex,variant:"default",className:"flex-1 mr-2 bg-black hover:bg-gray-800 text-white",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Capture"]}),(0,r.jsxs)(y.$,{type:"button",onClick:eg,variant:"outline",className:"flex-1 border-gray-300",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Cancel"]})]})]}),!_&&((null==es?void 0:null===(e=es.profile)||void 0===e?void 0:e.photo)||U)&&(0,r.jsx)("div",{className:"flex flex-col sm:flex-row items-center gap-4",children:(0,r.jsx)("div",{className:"border rounded-lg shadow-md bg-gray-50 p-4 max-w-full",children:(0,r.jsx)("div",{className:"flex justify-center",children:(()=>{var e;let t=U||(null==es?void 0:null===(e=es.profile)||void 0===e?void 0:e.photo);return t?(0,r.jsx)(S.default,{src:t.startsWith("data:")?t:t.startsWith("http")?t:"".concat("http://localhost:4005/").concat(t,"?t=").concat(new Date().getTime()),alt:"Student Photo",height:1e3,width:1e3,className:"max-w-full max-h-80 object-contain rounded-lg",style:{height:"auto",width:"auto"},unoptimized:t.startsWith("data:")}):(0,r.jsx)("div",{className:"flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-12 w-12 text-gray-400"})})})()})})}),(0,r.jsx)("canvas",{ref:ec,style:{display:"none"}})]})}),(0,r.jsx)(v.Rr,{className:"text-xs text-gray-500 mt-2",children:"A clear photo helps us identify you and personalize your profile"}),(0,r.jsx)(v.C5,{className:"text-red-500"})]}),(0,r.jsxs)("div",{className:"flex justify-center gap-10",children:[(0,r.jsx)(N.p,{type:"file",accept:".jpg,.jpeg,.png",ref:ea,className:"hidden",onChange:e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];a&&eu(a)}}),(0,r.jsx)(y.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{var e;return null===(e=ea.current)||void 0===e?void 0:e.click()},className:"border-gray-300 p-4",children:ee||U?"Change Photo":"Upload Photo"}),(0,r.jsxs)(y.$,{type:"button",onClick:()=>{O(null),q(null),t((0,j.XY)(void 0)),ed.setValue("photo",null),em()},variant:"outline",className:"border-gray-300",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Retake Photo"]})]})]})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"document",render:e=>{let{field:t}=e;return(0,r.jsxs)(C.Zp,{className:"shadow-lg border-0",children:[(0,r.jsxs)(C.aR,{className:"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",children:[(0,r.jsx)(C.ZB,{className:"text-lg font-medium text-gray-800",children:"Identity Document *"}),(0,r.jsx)(C.BT,{className:"text-gray-600",children:"Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate"})]}),(0,r.jsx)(C.Wu,{children:(0,r.jsxs)(v.eI,{children:[Y?(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-[#fff8f3] rounded-full",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-black"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:Y.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:Y instanceof File?ef(Y.size):"Previously uploaded document"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[Y&&"url"in Y&&(0,r.jsx)(y.$,{type:"button",variant:"outline",size:"sm",onClick:()=>window.open(Y.url,"_blank"),className:"h-8 px-3 border-gray-200",children:"View"}),(0,r.jsx)(y.$,{type:"button",variant:"outline",size:"sm",onClick:ep,className:"h-8 w-8 p-0 border-gray-200",children:(0,r.jsx)(x.A,{className:"h-4 w-4 text-gray-500"})})]})]})}):(0,r.jsx)(v.MJ,{children:(0,r.jsx)("div",{className:"flex items-center justify-center w-full",children:(0,r.jsxs)("label",{className:"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,r.jsx)(g.A,{className:"w-10 h-10 mb-3 text-black"}),(0,r.jsxs)("p",{className:"mb-2 text-sm text-gray-700",children:[(0,r.jsx)("span",{className:"font-semibold",children:"Click to upload"})," or drag and drop"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"PDF, PNG, JPG or JPEG (MAX. 5MB)"})]}),(0,r.jsx)(N.p,{id:"document",type:"file",accept:".pdf,.jpg,.jpeg,.png",className:"hidden",onChange:e=>{var a;let r=null===(a=e.target.files)||void 0===a?void 0:a[0];if(r){if(r.size>5242880){i.toast.error("File size exceeds 5MB limit");return}H({name:r.name,size:r.size,type:r.type,url:URL.createObjectURL(r)}),K(!1),t.onChange(r)}}})]})})}),(0,r.jsx)(v.Rr,{className:"text-xs text-gray-500 mt-2",children:"This document will serve to verify your identity and date of birth."}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})})]})}})]}),"other-info"===L&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(v.zB,{control:ed.control,name:"aadhaarNumber",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Aadhaar Number"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Aadhaar No",type:"tel",inputMode:"numeric",pattern:"[0-9]*",maxLength:12,onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:e=>{let a=e.target.value.replace(/\D/g,"");t.onChange(a)}})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"bloodGroup",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Blood Group"}),(0,r.jsxs)(k.l6,{onValueChange:t.onChange,value:t.value||void 0,children:[(0,r.jsx)(v.MJ,{children:(0,r.jsx)(k.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,r.jsx)(k.yv,{placeholder:"Select"})})}),(0,r.jsxs)(k.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,r.jsx)(k.eb,{value:"A+",children:"A+"}),(0,r.jsx)(k.eb,{value:"A-",children:"A-"}),(0,r.jsx)(k.eb,{value:"B+",children:"B+"}),(0,r.jsx)(k.eb,{value:"B-",children:"B-"}),(0,r.jsx)(k.eb,{value:"AB+",children:"AB+"}),(0,r.jsx)(k.eb,{value:"AB-",children:"AB-"}),(0,r.jsx)(k.eb,{value:"O+",children:"O+"}),(0,r.jsx)(k.eb,{value:"O-",children:"O-"})]})]}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"birthPlace",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Birth Place"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Birth Place"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"motherTongue",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Mother Tongue"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Mother Tongue"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"religion",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Religion"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Religion"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"caste",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Caste"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Caste"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}}),(0,r.jsx)(v.zB,{control:ed.control,name:"subCaste",render:e=>{let{field:t}=e;return(0,r.jsxs)(v.eI,{children:[(0,r.jsx)(v.lR,{className:"text-black font-medium",children:"Sub Caste"}),(0,r.jsx)(v.MJ,{children:(0,r.jsx)(N.p,{...t,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Sub Caste"})}),(0,r.jsx)(v.C5,{className:"text-red-500"})]})}})]})}),(0,r.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-100",children:(0,r.jsx)(y.$,{type:"submit",disabled:W,className:"bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200",children:W?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("svg",{className:"animate-spin h-4 w-4",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):es?"Update Profile":"Save Profile"})})]})})]})})})]})]}),(0,r.jsx)(R.default,{})]})},J=()=>(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),children:(0,r.jsx)(L,{})})},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>c,yv:()=>d});var r=a(95155);a(12115);var s=a(59824),l=a(66474),n=a(5196),o=a(47863),i=a(59434);function c(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:n,...o}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[n,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:l="popper",...n}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...n,children:[(0,r.jsx)(x,{}),(0,r.jsx)(s.LM,{className:(0,i.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(g,{})]})})}function h(e){let{className:t,children:a,...l}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.A,{className:"size-4"})})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,type:a,...l}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},66587:(e,t,a)=>{Promise.resolve().then(a.bind(a,39227))},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>d});var r=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},75937:(e,t,a)=>{"use strict";a.d(t,{lV:()=>d,MJ:()=>f,Rr:()=>b,zB:()=>m,eI:()=>g,lR:()=>p,C5:()=>j});var r=a(95155),s=a(12115),l=a(66634),n=a(62177),o=a(59434),i=a(24265);function c(e){let{className:t,...a}=e;return(0,r.jsx)(i.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}let d=n.Op,u=s.createContext({}),m=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(n.xI,{...t})})},h=()=>{let e=s.useContext(u),t=s.useContext(x),{getFieldState:a}=(0,n.xW)(),r=(0,n.lN)({name:e.name}),l=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...l}},x=s.createContext({});function g(e){let{className:t,...a}=e,l=s.useId();return(0,r.jsx)(x.Provider,{value:{id:l},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...a})})}function p(e){let{className:t,...a}=e,{error:s,formItemId:l}=h();return(0,r.jsx)(c,{"data-slot":"form-label","data-error":!!s,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:l,...a})}function f(e){let{...t}=e,{error:a,formItemId:s,formDescriptionId:n,formMessageId:o}=h();return(0,r.jsx)(l.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(n," ").concat(o):"".concat(n),"aria-invalid":!!a,...t})}function b(e){let{className:t,...a}=e,{formDescriptionId:s}=h();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function j(e){var t;let{className:a,...s}=e,{error:l,formMessageId:n}=h(),i=l?String(null!==(t=null==l?void 0:l.message)&&void 0!==t?t:""):s.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",a),...s,children:i}):null}},85511:(e,t,a)=>{"use strict";a.d(t,{V:()=>v});var r=a(95155),s=a(12115),l=a(42355),n=a(13052),o=a(53231),i=a(32944),c=a(84423),d=a(72794),u=a(7632),m=a(70542),h=a(3898),x=a(66835),g=a(40714),p=a(48882),f=a(37223),b=a(59434),j=a(30285);function v(e){let{className:t,selected:a,onSelect:v,disabled:N,month:y,onMonthChange:w,fromYear:k,toYear:C,captionLayout:z="buttons",classNames:M,...A}=e,[S,P]=s.useState(y||a||new Date);s.useEffect(()=>{y&&P(y)},[y]);let B=(0,o.w)(S),I=(0,i.p)(B),R=(0,c.k)(B),D=(0,d.$)(I),E=[],F=[],T=R,L="";for(;T<=D;){for(let e=0;e<7;e++){L=(0,u.GP)(T,"d");let e=T;F.push((0,r.jsx)("div",{className:(0,b.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer","h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground",{"text-muted-foreground":!(0,m.t)(T,B),"bg-primary text-primary-foreground":a&&(0,h.r)(T,a),"bg-accent text-accent-foreground":(0,x.c)(T)&&(!a||!(0,h.r)(T,a)),"opacity-50 cursor-not-allowed":N&&N(T)}),onClick:()=>{N&&N(e)||null==v||v(e)},children:(0,r.jsx)("span",{className:"font-normal",children:L})},T.toString())),T=(0,g.f)(T,1)}E.push((0,r.jsx)("div",{className:"flex w-full mt-2",children:F},T.toString())),F=[]}return(0,r.jsx)("div",{className:(0,b.cn)("p-3",t),...A,children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("div",{className:(0,b.cn)("flex justify-center pt-1 relative items-center w-full",null==M?void 0:M.caption),children:"dropdown"===z?(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("select",{value:S.getMonth(),onChange:e=>{let t=new Date(S.getFullYear(),parseInt(e.target.value),1);P(t),null==w||w(t)},className:(0,b.cn)("mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",null==M?void 0:M.dropdown),children:Array.from({length:12},(e,t)=>(0,r.jsx)("option",{value:t,children:(0,u.GP)(new Date(2e3,t,1),"MMMM")},t))}),(0,r.jsx)("select",{value:S.getFullYear(),onChange:e=>{let t=new Date(parseInt(e.target.value),S.getMonth(),1);P(t),null==w||w(t)},className:(0,b.cn)("mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",null==M?void 0:M.dropdown),children:Array.from({length:(C||new Date().getFullYear())-(k||1950)+1},(e,t)=>{let a=(k||1950)+t;return(0,r.jsx)("option",{value:a,children:a},a)})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.$,{variant:"outline",size:"sm",className:"absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100",onClick:()=>{let e=(0,f.a)(S,1);P(e),null==w||w(e)},children:(0,r.jsx)(l.A,{className:"size-4"})}),(0,r.jsx)("div",{className:(0,b.cn)("text-sm font-medium",null==M?void 0:M.caption_label),children:(0,u.GP)(S,"MMMM yyyy")}),(0,r.jsx)(j.$,{variant:"outline",size:"sm",className:"absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100",onClick:()=>{let e=(0,p.P)(S,1);P(e),null==w||w(e)},children:(0,r.jsx)(n.A,{className:"size-4"})})]})}),(0,r.jsxs)("div",{className:"w-full border-collapse space-x-1",children:[(0,r.jsx)("div",{className:"flex",children:["Su","Mo","Tu","We","Th","Fr","Sa"].map(e=>(0,r.jsx)("div",{className:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center",children:e},e))}),E]})]})})}},88539:(e,t,a)=>{"use strict";a.d(t,{T:()=>l});var r=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,1342,7632,4520,5042,347,8441,1684,7358],()=>t(66587)),_N_E=e.O()}]);