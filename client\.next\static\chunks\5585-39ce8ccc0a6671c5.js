"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5585],{5585:(t,e,a)=>{a.r(e),a.d(e,{create:()=>o,default:()=>r});var n={};!function t(e,a,n,r){var o,i,l,s,c,h,f,u,d,m,g,p=!!(e.Worker&&e.Blob&&e.Promise&&e.OffscreenCanvas&&e.OffscreenCanvasRenderingContext2D&&e.HTMLCanvasElement&&e.HTMLCanvasElement.prototype.transferControlToOffscreen&&e.URL&&e.URL.createObjectURL),b="function"==typeof Path2D&&"function"==typeof DOMMatrix;function M(){}function v(t){var n=a.exports.Promise,r=void 0!==n?n:e.Promise;return"function"==typeof r?new r(t):(t(<PERSON>,<PERSON>),null)}var y=(o=function(){if(!e.OffscreenCanvas)return!1;var t=new OffscreenCanvas(1,1),a=t.getContext("2d");a.fillRect(0,0,1,1);var n=t.transferToImageBitmap();try{a.createPattern(n,"no-repeat")}catch(t){return!1}return!0}(),i=new Map,{transform:function(t){if(o)return t;if(i.has(t))return i.get(t);var e=new OffscreenCanvas(t.width,t.height);return e.getContext("2d").drawImage(t,0,0),i.set(t,e),e},clear:function(){i.clear()}}),w=(c=Math.floor(1e3/60),h={},f=0,"function"==typeof requestAnimationFrame&&"function"==typeof cancelAnimationFrame?(l=function(t){var e=Math.random();return h[e]=requestAnimationFrame(function a(n){f===n||f+c-1<n?(f=n,delete h[e],t()):h[e]=requestAnimationFrame(a)}),e},s=function(t){h[t]&&cancelAnimationFrame(h[t])}):(l=function(t){return setTimeout(t,c)},s=function(t){return clearTimeout(t)}),{frame:l,cancel:s}),x=(m={},function(){if(u)return u;if(!n&&p){var e=["var CONFETTI, SIZE = {}, module = {};","("+t.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{u=new Worker(URL.createObjectURL(new Blob([e])))}catch(t){return"function"==typeof console.warn&&console.warn("\uD83C\uDF8A Could not load worker",t),null}!function(t){function e(e,a){t.postMessage({options:e||{},callback:a})}t.init=function(e){var a=e.transferControlToOffscreen();t.postMessage({canvas:a},[a])},t.fire=function(a,n,r){if(d)return e(a,null),d;var o=Math.random().toString(36).slice(2);return d=v(function(n){function i(e){e.data.callback===o&&(delete m[o],t.removeEventListener("message",i),d=null,y.clear(),r(),n())}t.addEventListener("message",i),e(a,o),m[o]=i.bind(null,{data:{callback:o}})})},t.reset=function(){for(var e in t.postMessage({reset:!0}),m)m[e](),delete m[e]}}(u)}return u}),C={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function I(t,e,a){var n,r;return r=t&&null!=t[e]?t[e]:C[e],a?a(r):r}function T(t){return t<0?0:Math.floor(t)}function E(t){return parseInt(t,16)}function P(t){return t.map(S)}function S(t){var e=String(t).replace(/[^0-9a-f]/gi,"");return e.length<6&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),{r:E(e.substring(0,2)),g:E(e.substring(2,4)),b:E(e.substring(4,6))}}function k(t){t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight}function O(t){var e=t.getBoundingClientRect();t.width=e.width,t.height=e.height}function B(t,a){var o,i=!t,l=!!I(a||{},"resize"),s=!1,c=I(a,"disableForReducedMotion",Boolean),h=p&&I(a||{},"useWorker")?x():null,f=i?k:O,u=!!t&&!!h&&!!t.__confetti_initialized,d="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function m(a){var m,g=c||I(a,"disableForReducedMotion",Boolean),p=I(a,"zIndex",Number);if(g&&d)return v(function(t){t()});i&&o?t=o.canvas:i&&!t&&((m=document.createElement("canvas")).style.position="fixed",m.style.top="0px",m.style.left="0px",m.style.pointerEvents="none",m.style.zIndex=p,t=m,document.body.appendChild(t)),l&&!u&&f(t);var M={width:t.width,height:t.height};function x(){if(h){var e={getBoundingClientRect:function(){if(!i)return t.getBoundingClientRect()}};f(e),h.postMessage({resize:{width:e.width,height:e.height}});return}M.width=M.height=null}function C(){o=null,l&&(s=!1,e.removeEventListener("resize",x)),i&&t&&(document.body.contains(t)&&document.body.removeChild(t),t=null,u=!1)}return(h&&!u&&h.init(t),u=!0,h&&(t.__confetti_initialized=!0),l&&!s&&(s=!0,e.addEventListener("resize",x,!1)),h)?h.fire(a,M,C):function(e,a,i){for(var l,s,c,h,u,d,m,g=I(e,"particleCount",T),p=I(e,"angle",Number),M=I(e,"spread",Number),x=I(e,"startVelocity",Number),C=I(e,"decay",Number),E=I(e,"gravity",Number),S=I(e,"drift",Number),k=I(e,"colors",P),O=I(e,"ticks",Number),B=I(e,"shapes"),F=I(e,"scalar"),N=!!I(e,"flat"),A=((l=I(e,"origin",Object)).x=I(l,"x",Number),l.y=I(l,"y",Number),l),R=g,z=[],D=t.width*A.x,L=t.height*A.y;R--;)z.push(function(t){var e=t.angle*(Math.PI/180),a=t.spread*(Math.PI/180);return{x:t.x,y:t.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*t.startVelocity+Math.random()*t.startVelocity,angle2D:-e+(.5*a-Math.random()*a),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:t.color,shape:t.shape,tick:0,totalTicks:t.ticks,decay:t.decay,drift:t.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*t.gravity,ovalScalar:.6,scalar:t.scalar,flat:t.flat}}({x:D,y:L,angle:p,spread:M,startVelocity:x,color:k[R%k.length],shape:B[Math.floor(Math.random()*(B.length-0))+0],ticks:O,decay:C,gravity:E,drift:S,scalar:F,flat:N}));return o?o.addFettis(z):(s=t,u=z.slice(),d=s.getContext("2d"),m=v(function(t){function e(){c=h=null,d.clearRect(0,0,a.width,a.height),y.clear(),i(),t()}c=w.frame(function t(){n&&(a.width!==r.width||a.height!==r.height)&&(a.width=s.width=r.width,a.height=s.height=r.height),a.width||a.height||(f(s),a.width=s.width,a.height=s.height),d.clearRect(0,0,a.width,a.height),(u=u.filter(function(t){return function(t,e){e.x+=Math.cos(e.angle2D)*e.velocity+e.drift,e.y+=Math.sin(e.angle2D)*e.velocity+e.gravity,e.velocity*=e.decay,e.flat?(e.wobble=0,e.wobbleX=e.x+10*e.scalar,e.wobbleY=e.y+10*e.scalar,e.tiltSin=0,e.tiltCos=0,e.random=1):(e.wobble+=e.wobbleSpeed,e.wobbleX=e.x+10*e.scalar*Math.cos(e.wobble),e.wobbleY=e.y+10*e.scalar*Math.sin(e.wobble),e.tiltAngle+=.1,e.tiltSin=Math.sin(e.tiltAngle),e.tiltCos=Math.cos(e.tiltAngle),e.random=Math.random()+2);var a,n,r,o,i,l,s,c,h,f,u,d,m,g,p,M,v=e.tick++/e.totalTicks,w=e.x+e.random*e.tiltCos,x=e.y+e.random*e.tiltSin,C=e.wobbleX+e.random*e.tiltCos,I=e.wobbleY+e.random*e.tiltSin;if(t.fillStyle="rgba("+e.color.r+", "+e.color.g+", "+e.color.b+", "+(1-v)+")",t.beginPath(),b&&"path"===e.shape.type&&"string"==typeof e.shape.path&&Array.isArray(e.shape.matrix)){t.fill((a=e.shape.path,n=e.shape.matrix,r=e.x,o=e.y,i=.1*Math.abs(C-w),l=.1*Math.abs(I-x),s=Math.PI/10*e.wobble,c=new Path2D(a),(h=new Path2D).addPath(c,new DOMMatrix(n)),(f=new Path2D).addPath(h,new DOMMatrix([Math.cos(s)*i,Math.sin(s)*i,-Math.sin(s)*l,Math.cos(s)*l,r,o])),f))}else if("bitmap"===e.shape.type){var T=Math.PI/10*e.wobble,E=.1*Math.abs(C-w),P=.1*Math.abs(I-x),S=e.shape.bitmap.width*e.scalar,k=e.shape.bitmap.height*e.scalar,O=new DOMMatrix([Math.cos(T)*E,Math.sin(T)*E,-Math.sin(T)*P,Math.cos(T)*P,e.x,e.y]);O.multiplySelf(new DOMMatrix(e.shape.matrix));var B=t.createPattern(y.transform(e.shape.bitmap),"no-repeat");B.setTransform(O),t.globalAlpha=1-v,t.fillStyle=B,t.fillRect(e.x-S/2,e.y-k/2,S,k),t.globalAlpha=1}else if("circle"===e.shape)t.ellipse?t.ellipse(e.x,e.y,Math.abs(C-w)*e.ovalScalar,Math.abs(I-x)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI):(u=e.x,d=e.y,m=Math.abs(C-w)*e.ovalScalar,g=Math.abs(I-x)*e.ovalScalar,p=Math.PI/10*e.wobble,M=2*Math.PI,t.save(),t.translate(u,d),t.rotate(p),t.scale(m,g),t.arc(0,0,1,0,M,void 0),t.restore());else if("star"===e.shape)for(var F=Math.PI/2*3,N=4*e.scalar,A=8*e.scalar,R=e.x,z=e.y,D=5,L=Math.PI/5;D--;)R=e.x+Math.cos(F)*A,z=e.y+Math.sin(F)*A,t.lineTo(R,z),F+=L,R=e.x+Math.cos(F)*N,z=e.y+Math.sin(F)*N,t.lineTo(R,z),F+=L;else t.moveTo(Math.floor(e.x),Math.floor(e.y)),t.lineTo(Math.floor(e.wobbleX),Math.floor(x)),t.lineTo(Math.floor(C),Math.floor(I)),t.lineTo(Math.floor(w),Math.floor(e.wobbleY));return t.closePath(),t.fill(),e.tick<e.totalTicks}(d,t)})).length?c=w.frame(t):e()}),h=e}),(o={addFettis:function(t){return u=u.concat(t),m},canvas:s,promise:m,reset:function(){c&&w.cancel(c),h&&h()}}).promise)}(a,M,C)}return m.reset=function(){h&&h.reset(),o&&o.reset()},m}function F(){return g||(g=B(null,{useWorker:!0,resize:!0})),g}a.exports=function(){return F().apply(this,arguments)},a.exports.reset=function(){F().reset()},a.exports.create=B,a.exports.shapeFromPath=function(t){if(!b)throw Error("path confetti are not supported in this browser");"string"==typeof t?n=t:(n=t.path,r=t.matrix);var e=new Path2D(n),a=document.createElement("canvas").getContext("2d");if(!r){for(var n,r,o,i,l=1e3,s=1e3,c=0,h=0,f=0;f<1e3;f+=2)for(var u=0;u<1e3;u+=2)a.isPointInPath(e,f,u,"nonzero")&&(l=Math.min(l,f),s=Math.min(s,u),c=Math.max(c,f),h=Math.max(h,u));o=c-l;var d=Math.min(10/o,10/(i=h-s));r=[d,0,0,d,-Math.round(o/2+l)*d,-Math.round(i/2+s)*d]}return{type:"path",path:n,matrix:r}},a.exports.shapeFromText=function(t){var e,a=1,n="#000000",r='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';"string"==typeof t?e=t:(e=t.text,a="scalar"in t?t.scalar:a,r="fontFamily"in t?t.fontFamily:r,n="color"in t?t.color:n);var o=10*a,i=""+o+"px "+r,l=new OffscreenCanvas(o,o),s=l.getContext("2d");s.font=i;var c=s.measureText(e),h=Math.ceil(c.actualBoundingBoxRight+c.actualBoundingBoxLeft),f=Math.ceil(c.actualBoundingBoxAscent+c.actualBoundingBoxDescent),u=c.actualBoundingBoxLeft+2,d=c.actualBoundingBoxAscent+2;h+=4,f+=4,(s=(l=new OffscreenCanvas(h,f)).getContext("2d")).font=i,s.fillStyle=n,s.fillText(e,u,d);var m=1/a;return{type:"bitmap",bitmap:l.transferToImageBitmap(),matrix:[m,0,0,m,-h*m/2,-f*m/2]}}}(function(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:this||{}}(),n,!1);let r=n.exports;var o=n.exports.create}}]);