"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[347],{14636:(e,t,a)=>{a.d(t,{AM:()=>o,Wv:()=>n,hl:()=>i});var s=a(95155);a(12115);var r=a(67140),l=a(59434);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"popover",...t})}function n(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"popover-trigger",...t})}function i(e){let{className:t,align:a="center",sideOffset:o=4,...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.<PERSON>,{"data-slot":"popover-content",align:a,sideOffset:o,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...n})})}},18644:(e,t,a)=>{a.d(t,{HK:()=>r,Ou:()=>u,Pz:()=>l,a1:()=>m,au:()=>h,fm:()=>n,jc:()=>o,kI:()=>c,pO:()=>i,sl:()=>d});var s=a(55077);let r=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await s.S.get("/notifications/classes?page=".concat(e,"&limit=").concat(t))).data.data},l=async()=>(await s.S.get("/notifications/classes/count")).data.data.count,o=async e=>(await s.S.post("/notifications/classes/mark-read/".concat(e))).data,n=async()=>(await s.S.post("/notifications/classes/mark-all-read")).data,i=async()=>(await s.S.delete("/notifications/classes/delete-all")).data,c=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await s.S.get("/notifications/students?page=".concat(e,"&limit=").concat(t))).data.data},d=async()=>(await s.S.get("/notifications/students/count")).data.data.count,u=async e=>(await s.S.post("/notifications/students/mark-read/".concat(e))).data,m=async()=>(await s.S.post("/notifications/students/mark-all-read")).data,h=async()=>(await s.S.delete("/notifications/students/delete-all")).data},21751:(e,t,a)=>{a.d(t,{A$:()=>d,DY:()=>l,Lx:()=>o,RY:()=>n,Ty:()=>i,bi:()=>c,iM:()=>r});var s=a(55077);async function r(e){return(await s.S.post("/auth-client/continue-with-email",e)).data}async function l(e){return(await s.S.post("/auth-client/register",e)).data}async function o(e){return(await s.S.post("/auth-client/login",e)).data}async function n(e){return(await s.S.post("/auth-client/verify-otp",e)).data}async function i(e){return(await s.S.post("/auth-client/resend-otp",e)).data}let c=async(e,t)=>(await s.S.post("/auth-client/generate-jwt",{contact:e,password:t})).data,d=async e=>(await s.S.get("/auth-client/verify-email",{params:{token:e}})).data},28844:(e,t,a)=>{a.d(t,{AR:()=>o,bE:()=>r,dt:()=>n,je:()=>l,sX:()=>i});var s=a(55077);let r=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;try{return(await s.S.post("/cart/add",{itemId:e,quantity:t})).data}catch(e){var a,r;return{success:!1,error:(null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.error)||"Failed to add item to cart"}}},l=async()=>{try{return(await s.S.get("/cart")).data}catch(a){var e,t;return{success:!1,error:(null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||"Failed to fetch cart items"}}},o=async(e,t)=>{try{return(await s.S.put("/cart/item/".concat(e),{quantity:t})).data}catch(e){var a,r;return{success:!1,error:(null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.error)||"Failed to update cart item"}}},n=async e=>{try{return(await s.S.delete("/cart/item/".concat(e))).data}catch(e){var t,a;return{success:!1,error:(null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||"Failed to remove item from cart"}}},i=async()=>{try{return(await s.S.delete("/cart/clear")).data}catch(a){var e,t;return{success:!1,error:(null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||"Failed to clear cart"}}}},30285:(e,t,a)=>{a.d(t,{$:()=>i,r:()=>n});var s=a(95155);a(12115);var r=a(66634),l=a(74466),o=a(59434);let n=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:l,asChild:i=!1,...c}=e,d=i?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(n({variant:a,size:l,className:t})),...c})}},40960:(e,t,a)=>{a.d(t,{IJ:()=>l,wE:()=>r});var s=a(55077);let r=async e=>{try{let t=await s.S.post("/store/purchase",e);return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:(null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||e.message||"Purchase failed"}}},l=async()=>{try{let e=await s.S.get("/store/orders");return{success:!0,data:e.data.data}}catch(a){var e,t;return{success:!1,error:(null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||a.message||"Failed to fetch orders"}}}},54165:(e,t,a)=>{a.d(t,{Cf:()=>u,Es:()=>h,L3:()=>f,c7:()=>m,lG:()=>n,rr:()=>x,zM:()=>i});var s=a(95155);a(12115);var r=a(4033),l=a(54416),o=a(59434);function n(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...n,children:[a,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}},55077:(e,t,a)=>{a.d(t,{S:()=>o});var s=a(23464),r=a(56671);let l="http://localhost:4005/api/v1";console.log("Axios baseURL:",l);let o=s.A.create({baseURL:l,headers:{"Content-Type":"application/json"},withCredentials:!0});o.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":l;{let t=localStorage.getItem("studentToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(r.toast.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},56762:(e,t,a)=>{a.d(t,{A:()=>o,N:()=>l});var s=a(55077),r=a(51990);let l=(0,r.zD)("studentProfile/fetchStudentProfile",async(e,t)=>{let{rejectWithValue:a}=t;try{let e=localStorage.getItem("studentToken");if(!e)return a("No authentication token found");let t=await s.S.get("/student-profile/all-data",{headers:{Authorization:"Bearer ".concat(e)}});if(t.data&&"object"==typeof t.data){if(void 0!==t.data.success&&void 0!==t.data.data)return t.data.data;return t.data}return null}catch(e){var r,l,o;if((null===(r=e.response)||void 0===r?void 0:r.status)===404)return null;return a((null===(o=e.response)||void 0===o?void 0:null===(l=o.data)||void 0===l?void 0:l.message)||"Failed to fetch student data")}}),o=(0,r.zD)("studentProfile/updateStudentProfile",async(e,t)=>{let{rejectWithValue:a}=t;try{let t=localStorage.getItem("studentToken");if(!t)return a("No authentication token found");let r=await (0,s.S)({method:"put",url:"/student-profile/combined",data:e,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}});if(r.data&&"object"==typeof r.data){if(void 0!==r.data.success&&void 0!==r.data.data)return r.data.data;return r.data}return null}catch(e){var r,l;return a((null===(l=e.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message)||"Failed to update student profile")}})},59434:(e,t,a)=>{a.d(t,{MB:()=>n,ZO:()=>o,cn:()=>l,wR:()=>c,xh:()=>i});var s=a(52596),r=a(39688);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}let o=()=>localStorage.getItem("studentToken"),n=()=>{localStorage.removeItem("studentToken")},i=()=>!!o(),c=()=>{if(o())return{isAuth:!0,userType:"STUDENT"};{let e=localStorage.getItem("user");if(e)try{let t=JSON.parse(e);if(t&&t.id)return{isAuth:!0,userType:"CLASS"}}catch(e){console.error("Error parsing user data:",e)}}return{isAuth:!1,userType:null}}},60723:(e,t,a)=>{a.d(t,{RY:()=>i,Ty:()=>c,Xc:()=>d,af:()=>n,bZ:()=>o,iM:()=>r,zy:()=>l});var s=a(55077);let r=async e=>(await s.S.post("/student/continue-with-email",e)).data,l=async e=>(await s.S.post("/student/register",e)).data,o=async e=>(await s.S.post("/student/login",e)).data,n=async()=>{try{let e=await s.S.post("/student/logout");return localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),e.data}catch(e){return localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),{success:!0,message:"Logged out successfully"}}};async function i(e){return(await s.S.post("/student/verify-otp",e)).data}async function c(e){return(await s.S.post("/student/resend-otp",e)).data}let d=async e=>(await s.S.post("/student/verify-email",{token:e})).data},70347:(e,t,a)=>{a.d(t,{default:()=>G});var s=a(95155),r=a(6874),l=a.n(r),o=a(66766),n=a(79772),i=a(81497),c=a(93550),d=a(86151),u=a(73783),m=a(66516),h=a(65932),f=a(27809),x=a(71007),g=a(54416),v=a(74783),p=a(87712),j=a(84616),y=a(81586),N=a(30285),w=a(34540);let b=()=>(0,w.wA)();var k=a(12115),S=a(59434),A=a(35695),C=a(92138);let z=()=>{var e;let t=(0,A.useRouter)(),{profileData:a}=(0,w.d4)(e=>e.studentProfile),r=null!==localStorage.getItem("studentToken"),l=(null==a?void 0:null===(e=a.profile)||void 0===e?void 0:e.id)!==void 0;return!r||l?null:(0,s.jsx)("div",{className:"my-4 mx-10 sm:px-4",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900",children:(0,s.jsxs)("div",{className:"px-3 py-1.5 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"bg-[#ff914d] p-1.5 rounded-full",children:(0,s.jsx)(x.A,{className:"h-3 w-3 text-white"})}),(0,s.jsx)("p",{className:"text-xs font-medium text-gray-800 dark:text-gray-200",children:"Please Complete your profile"})]}),(0,s.jsxs)(N.$,{onClick:()=>t.push("/student/profile"),className:"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0",size:"sm",children:["Complete now ",(0,s.jsx)(C.A,{className:"h-3 w-3"})]})]})})})};var I=a(23861),F=a(14636),E=a(90010),P=a(18644),L=a(56671),T=a(75350);function $(e){let{userType:t}=e,[a,r]=(0,k.useState)([]),[l,o]=(0,k.useState)(0),[n,i]=(0,k.useState)(!1),[c,d]=(0,k.useState)(!1),[u,m]=(0,k.useState)(!1),h=(0,A.useRouter)(),f=Array.isArray(a)?a:[],x=(0,k.useCallback)(async()=>{try{let e,a;d(!0),"class"===t?(e=await (0,P.HK)(1,20),a=await (0,P.Pz)()):(e=await (0,P.kI)(1,20),a=await (0,P.sl)());let s=(null==e?void 0:e.notifications)||e||[];r(Array.isArray(s)?s:[]),o(a)}catch(e){console.error("Error fetching notifications:",e),r([]),o(0)}finally{d(!1)}},[t]),g=async e=>{try{var a,s;"class"===t?await (0,P.jc)(e.id):await (0,P.Ou)(e.id),r(t=>t.map(t=>t.id===e.id?{...t,isRead:!0}:t)),o(e=>Math.max(0,e-1)),i(!1),(null===(a=e.data)||void 0===a?void 0:a.actionType)==="OPEN_CHAT"&&(null===(s=e.data)||void 0===s?void 0:s.redirectUrl)&&h.push(e.data.redirectUrl)}catch(e){console.error("Error handling notification click:",e),L.toast.error("Failed to process notification")}},v=async()=>{try{"class"===t?await (0,P.fm)():await (0,P.a1)(),r(e=>e.map(e=>({...e,isRead:!0}))),o(0),L.toast.success("All notifications marked as read")}catch(e){console.error("Error marking all notifications as read:",e),L.toast.error("Failed to mark all notifications as read")}},p=async()=>{m(!1);try{"class"===t?await (0,P.pO)():await (0,P.au)(),r([]),o(0),L.toast.success("All notifications removed successfully")}catch(e){console.error("Error removing all notifications:",e),L.toast.error("Failed to remove all notifications")}};return(0,k.useEffect)(()=>{x();let e=setInterval(x,3e4);return()=>clearInterval(e)},[x]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(F.AM,{open:n,onOpenChange:i,children:[(0,s.jsx)(F.Wv,{asChild:!0,children:(0,s.jsxs)(N.$,{variant:"ghost",size:"icon",className:"relative bg-black hover:bg-gray-900 transition duration-200 h-9 w-9 md:h-10 md:w-10 rounded-full",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"}),(0,s.jsx)(I.A,{className:"relative z-10 h-4 w-4 md:h-5 md:w-5 text-white transition-colors duration-200"}),l>0&&(0,s.jsx)("div",{className:"absolute -top-1.5 -right-1.5 md:-top-2 md:-right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center z-20",children:(0,s.jsx)("span",{className:"text-white text-[10px] md:text-xs font-semibold leading-none",children:l>99?"99+":l})})]})}),(0,s.jsxs)(F.hl,{className:"w-80 p-0",align:"end",children:[(0,s.jsx)("div",{className:"p-4 border-b",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold",children:"Notifications"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[l>0&&(0,s.jsx)(N.$,{variant:"ghost",size:"sm",onClick:v,className:"text-xs",children:"Mark all read"}),a.length>0&&0===l&&(0,s.jsx)(N.$,{variant:"ghost",size:"sm",onClick:()=>{m(!0)},className:"text-xs text-red-600 hover:text-red-700 hover:bg-red-50",children:"Remove all"})]})]})}),(0,s.jsx)("div",{className:"h-80 overflow-y-auto",children:c?(0,s.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"Loading notifications..."}):0===a.length?(0,s.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"No notifications yet"}):(0,s.jsx)("div",{className:"divide-y",children:Array.isArray(a)&&a.map(e=>(0,s.jsx)("div",{className:"p-4 cursor-pointer hover:bg-muted/50 transition-colors ".concat(e.isRead?"":"bg-blue-50/50"),onClick:()=>g(e),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full mt-2 ".concat(e.isRead?"bg-gray-300":"bg-blue-500")}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-medium text-sm",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:(0,T.m)(new Date(e.createdAt),{addSuffix:!0})})]})]})},e.id))})}),f.length>0&&(0,s.jsx)("div",{className:"p-3 border-t bg-muted/30",children:(0,s.jsx)(N.$,{variant:"ghost",size:"sm",className:"w-full text-xs",onClick:()=>{i(!1),h.push("/notifications")},children:"View All Notifications"})})]})]}),(0,s.jsx)(E.Lt,{open:u,onOpenChange:m,children:(0,s.jsxs)(E.EO,{children:[(0,s.jsxs)(E.wd,{children:[(0,s.jsx)(E.r7,{children:"Remove All Notifications"}),(0,s.jsx)(E.$v,{children:"Are you sure you want to remove all notifications? This action cannot be undone."})]}),(0,s.jsxs)(E.ck,{children:[(0,s.jsx)(E.Zr,{children:"Cancel"}),(0,s.jsx)(E.Rx,{onClick:p,className:"bg-red-600 hover:bg-red-700",children:"Remove All"})]})]})})]})}var _=a(91394),R=a(60723),D=a(92560),O=a(93457),M=a(56762),U=a(55077),q=a(8619),Z=a(14087),J=a(21751),Y=a(76079);let B=e=>{let{studentId:t}=e,[a,r]=(0,k.useState)(0);return(0,k.useEffect)(()=>{(async()=>{if(!t){r(0);return}let e=await (0,Y.Gk)(t);e.success&&e.data?r(e.data.streak||0):r(0)})()},[t]),(0,s.jsxs)("span",{className:"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1",children:["\uD83D\uDD25 ",a]})};var W=a(28844),V=a(40960),X=a(54165);let G=()=>{let{isAuthenticated:e,user:t}=(0,w.d4)(e=>e.user),[a,r]=(0,k.useState)(!1),[C,I]=(0,k.useState)(!1),[E,P]=(0,k.useState)(null),[T,Y]=(0,k.useState)(null),[G,H]=(0,k.useState)([]),[K,Q]=(0,k.useState)(!1),[ee,et]=(0,k.useState)(!1),ea=b(),es=(0,A.useRouter)(),er=(0,k.useRef)(null),[el,eo]=(0,k.useState)(0),en=(0,q.d)(0),ei=el/20,ec=async()=>{try{let e=await W.je();e.success&&e.data&&H(e.data)}catch(e){console.error("Error loading cart:",e)}};(0,k.useEffect)(()=>{let a=(0,S.xh)();if(I(a),a){let e=localStorage.getItem("student_data");e&&P(JSON.parse(e)),ea((0,M.N)()),ec()}e&&ec(),(async()=>{if(e&&(null==t?void 0:t.id))try{let e=await U.S.get("/classes/details/".concat(t.id));e.data&&e.data.status&&Y(e.data.status.status)}catch(e){console.error("Error fetching class status:",e)}})();let s=()=>{let e=(0,S.xh)();if(I(e),e){let e=localStorage.getItem("student_data");e&&P(JSON.parse(e)),ea((0,M.N)()),ec()}else P(null),H([])},r=()=>{(a||e)&&ec()};return window.addEventListener("storage",s),window.addEventListener("cartUpdated",r),er.current&&eo(er.current.getBoundingClientRect().width),()=>{window.removeEventListener("storage",s),window.removeEventListener("cartUpdated",r)}},[ea]),(0,Z.N)((e,t)=>{if(0===el)return;let a=en.get()-ei*t/1e3;a<=-el&&(a=0),en.set(a)});let ed=()=>r(!a),eu=async()=>{try{let e=await (0,R.af)();!1!==e.success?((0,S.MB)(),I(!1),P(null),localStorage.removeItem("student_data"),ea((0,O.Ig)()),L.toast.success("Logged out successfully"),window.dispatchEvent(new Event("storage"))):(L.toast.error(e.message||"Failed to logout"),(0,S.MB)(),I(!1),P(null),localStorage.removeItem("student_data"),ea((0,O.Ig)()))}catch(e){console.log("Failed to logout",e),L.toast.error("Failed to logout"),localStorage.removeItem("student_data"),(0,S.MB)(),I(!1),P(null),ea((0,O.Ig)())}},em=async e=>{try{let t=await W.dt(e);t.success?(await ec(),L.toast.success("Item removed from cart!")):L.toast.error(t.error||"Failed to remove item from cart")}catch(e){console.error("Error removing from cart:",e),L.toast.error("Failed to remove item from cart")}},eh=async(e,t)=>{try{let a=G.find(t=>t.itemId===e);if(a&&t>a.item.availableStock){L.toast.error("Only ".concat(a.item.availableStock," items available in stock"));return}let s=await W.AR(e,t);if(s.success)await ec(),0===t&&L.toast.success("Item removed from cart!");else{let e=s.error||"Failed to update cart item";e.includes("stock")||e.includes("available")?L.toast.error("Item is out of stock or insufficient quantity available"):L.toast.error(e)}}catch(e){console.error("Error updating cart:",e),L.toast.error("Failed to update cart item")}},ef=()=>G.reduce((e,t)=>e+t.item.coinPrice*t.quantity,0),ex=async()=>{if(0===G.length){L.toast.error("Your cart is empty");return}if(!C&&!e){L.toast.error("Please login to checkout");return}try{et(!0);let e=G.map(e=>({id:e.itemId,name:e.item.name,coinPrice:e.item.coinPrice,quantity:e.quantity,image:e.item.image||""})),a=ef(),s=await V.wE({cartItems:e,totalCoins:a});if(!s.success){if("PROFILE_NOT_APPROVED"===s.error){var t;let e=(null===(t=s.data)||void 0===t?void 0:t.message)||"Your profile is not approved yet. Please complete your profile and wait for admin approval.";L.toast.error(e);return}throw Error(s.error)}await W.sX(),await ec(),L.toast.success("Purchase completed successfully!"),Q(!1),C?es.push("/student/my-orders"):es.push("/classes/my-orders")}catch(e){console.error("Checkout error:",e),L.toast.error(e.message||"Checkout failed. Please try again.")}finally{et(!1)}},eg=async()=>{try{let e=await (0,J.bi)(null==t?void 0:t.contactNo,null==t?void 0:t.password);if(e.success){let{token:a}=e.data,s="".concat("http://127.0.0.1:8000","/login-class-link?uid=").concat(null==t?void 0:t.id,"&token=").concat(a);window.location.href=s}else L.toast.error(e.message||"Failed to generate token")}catch(e){console.error("Failed to generate token",e),L.toast.error("Failed to generate token")}},ev=[{href:"/verified-classes",label:"Find Tutor"},{href:"/uwhiz",label:"U - Whiz"},{href:"/mock-exam-card",label:(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:"Daily Quiz"}),C&&(0,s.jsx)(B,{studentId:null==E?void 0:E.id})]}),isNew:!0},{href:"/careers",label:"Career"},{href:"/store",label:"Store"}],ep=[{href:"/classes/profile",icon:(0,s.jsx)(n.A,{className:"w-5 h-5 mr-2"}),label:"Profile"},{href:"/classes/chat",icon:(0,s.jsx)(i.A,{className:"w-5 h-5 mr-2"}),label:"Messages"},{href:"/coins",icon:(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),label:"Coins"},{href:"/classes/my-orders",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Orders"},{onClick:eg,icon:(0,s.jsx)(u.A,{className:"w-5 h-5 mr-2"}),label:"My Dashboard"},{href:"/classes/referral-dashboard",icon:(0,s.jsx)(m.A,{className:"w-5 h-5 mr-2"}),label:"Referral Dashboard"},..."APPROVED"===T?[{href:"/classes/payment",icon:(0,s.jsx)(h.A,{className:"w-5 h-5 mr-2"}),label:"Payment Details"}]:[]];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full bg-black",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,s.jsx)(l(),{href:"/",className:"flex items-center space-x-2 transition-transform hover:scale-105",children:(0,s.jsx)(o.default,{src:"/logo_black.png",alt:"Preply Logo",width:120,height:40,className:"rounded-sm"})}),(0,s.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:ev.map(e=>(0,s.jsxs)(l(),{href:e.href,className:"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400",children:[e.label,e.isNew&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse",children:"Trending"})]},e.href))}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[e||C?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)($,{userType:e?"class":"student"}),G.length>0&&(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(N.$,{variant:"ghost",size:"icon",onClick:()=>Q(!0),className:"text-white hover:bg-gray-800 rounded-full relative",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:G.reduce((e,t)=>e+t.quantity,0)})]})}),(0,s.jsxs)(F.AM,{children:[(0,s.jsx)(F.Wv,{asChild:!0,children:(0,s.jsx)(_.eu,{className:"cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity",children:(0,s.jsx)(_.q5,{className:"bg-white text-black flex items-center justify-center text-sm font-semibold",children:e?(null==t?void 0:t.firstName)&&(null==t?void 0:t.lastName)?"".concat(t.firstName[0]).concat(t.lastName[0]).toUpperCase():"CT":(null==E?void 0:E.firstName)&&(null==E?void 0:E.lastName)?"".concat(E.firstName[0]).concat(E.lastName[0]).toUpperCase():"ST"})})}),(0,s.jsxs)(F.hl,{className:"w-64 bg-white p-4 rounded-lg shadow-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(_.eu,{className:"h-10 w-10",children:(0,s.jsx)(_.q5,{className:"bg-white text-black",children:e?(null==t?void 0:t.firstName)&&(null==t?void 0:t.lastName)?"".concat(t.firstName[0]).concat(t.lastName[0]).toUpperCase():"CT":(null==E?void 0:E.firstName)&&(null==E?void 0:E.lastName)?"".concat(E.firstName[0]).concat(E.lastName[0]).toUpperCase():"ST"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-black",children:e?(null==t?void 0:t.firstName)&&(null==t?void 0:t.lastName)?"".concat(t.firstName," ").concat(t.lastName):(null==t?void 0:t.className)||"Class Account":(null==E?void 0:E.firstName)&&(null==E?void 0:E.lastName)?"".concat(E.firstName," ").concat(E.lastName):"Student Account"}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:e?(null==t?void 0:t.contactNo)||"<EMAIL>":(null==E?void 0:E.contactNo)||"<EMAIL>"})]})]}),(0,s.jsx)("div",{className:"space-y-2",children:e?(0,s.jsxs)(s.Fragment,{children:[ep.map(e=>(0,s.jsx)(N.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground",children:e.href?(0,s.jsxs)(l(),{href:e.href,className:"flex items-center",children:[e.icon,(0,s.jsx)("span",{children:e.label})]}):(0,s.jsxs)("div",{onClick:e.onClick,className:"flex items-center w-full",children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href||e.label)),(0,s.jsxs)(N.$,{variant:"ghost",className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",onClick:async()=>{try{(await U.S.post("/auth-client/logout",{})).data.success&&(es.push("/"),ea((0,D.lM)()),localStorage.removeItem("token"),L.toast.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),L.toast.error("Failed to logout")}},children:[(0,s.jsx)(x.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[[{href:"/student/profile",icon:(0,s.jsx)(n.A,{className:"w-5 h-5 mr-2"}),label:"Profile"},{href:"/student/chat",icon:(0,s.jsx)(i.A,{className:"w-5 h-5 mr-2"}),label:"Messages"},{href:"/coins",icon:(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),label:"Coins"},{href:"/student/wishlist",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Wishlist"},{href:"/student/referral-dashboard",icon:(0,s.jsx)(m.A,{className:"w-5 h-5 mr-2"}),label:"Referral Dashboard"},{href:"/student/my-orders",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Orders"}].map(e=>(0,s.jsx)(N.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground",children:(0,s.jsxs)(l(),{href:e.href,className:"flex items-center",children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href)),(0,s.jsxs)(N.$,{onClick:eu,className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",children:[(0,s.jsx)(x.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]})})})]})]})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,s.jsx)(N.$,{className:"bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md",asChild:!0,children:(0,s.jsx)(l(),{href:"/class/login",children:"Join as Tutor"})}),(0,s.jsx)(N.$,{variant:"ghost",className:"bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700",asChild:!0,children:(0,s.jsx)(l(),{href:"/student/login",children:"Student Login"})})]})}),(0,s.jsx)(N.$,{variant:"ghost",size:"icon",className:"md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full",onClick:ed,children:a?(0,s.jsx)(g.A,{className:"h-6 w-6"}):(0,s.jsx)(v.A,{className:"h-6 w-6"})})]})]})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ".concat(a?"translate-x-0":"translate-x-full"),children:(0,s.jsxs)("div",{className:"flex flex-col h-full p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)(o.default,{src:"/logo_black.png",alt:"Uest Logo",width:100,height:32,className:"rounded-sm"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(e||C)&&(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(N.$,{variant:"ghost",size:"icon",onClick:()=>{Q(!0),ed()},className:"text-orange-400 hover:bg-orange-500/10 rounded-full relative",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),G.length>0&&(0,s.jsx)("span",{className:"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:G.reduce((e,t)=>e+t.quantity,0)})]})}),(0,s.jsx)(N.$,{variant:"ghost",size:"icon",className:"text-orange-400 hover:bg-orange-500/10 rounded-full",onClick:ed,children:(0,s.jsx)(g.A,{className:"h-6 w-6"})})]})]}),(e||C)&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-900 rounded-lg",children:[(0,s.jsx)(_.eu,{className:"h-10 w-10",children:(0,s.jsx)(_.q5,{className:"bg-white text-black",children:e?(null==t?void 0:t.firstName)&&(null==t?void 0:t.lastName)?"".concat(t.firstName[0]).concat(t.lastName[0]).toUpperCase():"CT":(null==E?void 0:E.firstName)&&(null==E?void 0:E.lastName)?"".concat(E.firstName[0]).concat(E.lastName[0]).toUpperCase():"ST"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-white",children:e?(null==t?void 0:t.firstName)&&(null==t?void 0:t.lastName)?"".concat(t.firstName," ").concat(t.lastName):(null==t?void 0:t.className)||"Class Account":(null==E?void 0:E.firstName)&&(null==E?void 0:E.lastName)?"".concat(E.firstName," ").concat(E.lastName):"Student Account"}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:e?(null==t?void 0:t.contactNo)||"<EMAIL>":(null==E?void 0:E.contactNo)||"<EMAIL>"})]})]})}),(0,s.jsx)("nav",{className:"flex flex-col space-y-2",children:ev.map(e=>(0,s.jsxs)(l(),{href:e.href,className:"flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors",onClick:ed,children:[(0,s.jsx)("div",{className:"flex items-center gap-3",children:"string"==typeof e.label?(0,s.jsx)("span",{children:e.label}):e.label}),e.isNew&&(0,s.jsx)("span",{className:"text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white",children:"Trending"})]},e.href))}),(0,s.jsxs)("div",{className:"mt-auto space-y-2",children:[e&&(0,s.jsxs)(s.Fragment,{children:[ep.map(e=>(0,s.jsx)(N.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground",children:e.href?(0,s.jsxs)(l(),{href:e.href,className:"flex items-center",onClick:ed,children:[e.icon,(0,s.jsx)("span",{children:e.label})]}):(0,s.jsxs)("div",{onClick:()=>{ed()},className:"flex items-center w-full",children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href||e.label)),(0,s.jsxs)(N.$,{variant:"ghost",className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",onClick:async()=>{try{(await U.S.post("/auth-client/logout",{})).data.success&&(es.push("/"),ea((0,D.lM)()),localStorage.removeItem("token"),L.toast.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),L.toast.error("Failed to logout")}ed()},children:[(0,s.jsx)(x.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]}),C&&(0,s.jsxs)(s.Fragment,{children:[[{href:"/student/profile",icon:(0,s.jsx)(n.A,{className:"w-5 h-5 mr-2"}),label:"Profile"},{href:"/student/chat",icon:(0,s.jsx)(i.A,{className:"w-5 h-5 mr-2"}),label:"Messages"},{href:"/coins",icon:(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),label:"Coins"},{href:"/student/wishlist",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Wishlist"},{href:"/student/referral-dashboard",icon:(0,s.jsx)(m.A,{className:"w-5 h-5 mr-2"}),label:"Referral Dashboard"},{href:"/student/my-orders",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Orders"}].map(e=>(0,s.jsx)(N.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground ",children:(0,s.jsxs)(l(),{href:e.href,className:"flex items-center",onClick:ed,children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href)),(0,s.jsxs)(N.$,{variant:"ghost",className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",onClick:()=>{eu(),ed()},children:[(0,s.jsx)(x.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]}),!e&&!C&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.$,{className:"w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3",asChild:!0,children:(0,s.jsx)(l(),{href:"/class/login",onClick:ed,children:"Tutor Login"})}),(0,s.jsx)(N.$,{variant:"ghost",className:"w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700",asChild:!0,children:(0,s.jsx)(l(),{href:"/student/login",onClick:ed,children:"Student Login"})})]})]})]})}),C&&(0,s.jsx)(z,{})]}),(0,s.jsx)(X.lG,{open:K,onOpenChange:Q,children:(0,s.jsxs)(X.Cf,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"w-5 h-5"}),"Shopping Cart (",G.reduce((e,t)=>e+t.quantity,0)," items)"]}),(0,s.jsx)(X.rr,{children:"Review your items before checkout"})]}),(0,s.jsx)("div",{className:"space-y-4",children:0===G.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(f.A,{className:"w-12 h-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Your cart is empty"})]}):(0,s.jsxs)(s.Fragment,{children:[G.map(e=>{var t,a;return(0,s.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg bg-card",children:[(0,s.jsx)(o.default,{src:(null===(t=e.item.image)||void 0===t?void 0:t.startsWith("http"))?e.item.image:"".concat("http://localhost:4005/").concat((null===(a=e.item.image)||void 0===a?void 0:a.startsWith("/"))?e.item.image.substring(1):e.item.image||"uploads/store/placeholder.jpg"),alt:e.item.name,width:60,height:60,className:"rounded object-cover",onError:e=>{e.target.src="/logo.png"}}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-card-foreground",children:e.item.name}),(0,s.jsxs)("p",{className:"text-orange-500 font-semibold flex items-center",children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-1"}),e.item.coinPrice," coins"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(N.$,{size:"sm",variant:"outline",onClick:()=>eh(e.itemId,e.quantity-1),children:(0,s.jsx)(p.A,{className:"w-3 h-3"})}),(0,s.jsx)("span",{className:"w-8 text-center",children:e.quantity}),(0,s.jsx)(N.$,{size:"sm",variant:"outline",onClick:()=>eh(e.itemId,e.quantity+1),children:(0,s.jsx)(j.A,{className:"w-3 h-3"})})]}),(0,s.jsx)(N.$,{size:"sm",variant:"destructive",onClick:()=>em(e.itemId),children:"Remove"})]},e.id)}),(0,s.jsx)("div",{className:"border-t pt-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center text-lg font-semibold",children:[(0,s.jsx)("span",{children:"Total:"}),(0,s.jsxs)("span",{className:"text-orange-500 flex items-center",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 mr-1"}),ef()," coins"]})]})})]})}),(0,s.jsxs)(X.Es,{children:[(0,s.jsx)(N.$,{variant:"outline",onClick:()=>Q(!1),children:"Continue Shopping"}),G.length>0&&(0,s.jsx)(N.$,{onClick:ex,disabled:ee,className:"bg-orange-500 hover:bg-orange-600 disabled:opacity-50",children:ee?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Checkout (",ef()," coins)"]})})]})]})})]})}},76079:(e,t,a)=>{a.d(t,{$m:()=>r,Gk:()=>l,If:()=>o,xT:()=>n});var s=a(55077);let r=async e=>{try{let t=await s.S.put("/mock-exam-streak/".concat(e),{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}},l=async e=>{try{let t=await s.S.get("/mock-exam-streak/".concat(e),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to get mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}},o=async e=>{try{let t=await s.S.put("/mock-exam-weekly-streak/".concat(e),{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to save mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}},n=async e=>{try{let t=await s.S.get("/mock-exam-weekly-streak/".concat(e),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){var t,a;return{success:!1,error:"Failed to get mock exam streak: ".concat((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.error)||e.message)}}}},90010:(e,t,a)=>{a.d(t,{$v:()=>x,EO:()=>u,Lt:()=>n,Rx:()=>g,Zr:()=>v,ck:()=>h,r7:()=>f,tv:()=>i,wd:()=>m});var s=a(95155);a(12115);var r=a(35563),l=a(59434),o=a(30285);function n(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"alert-dialog",...t})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"alert-dialog-trigger",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,...a}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{}),(0,s.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.rc,{className:(0,l.cn)((0,o.r)(),t),...a})}function v(e){let{className:t,...a}=e;return(0,s.jsx)(r.ZD,{className:(0,l.cn)((0,o.r)({variant:"outline"}),t),...a})}},91394:(e,t,a)=>{a.d(t,{eu:()=>o,q5:()=>n});var s=a(95155);a(12115);var r=a(87083),l=a(59434);function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},92560:(e,t,a)=>{a.d(t,{Ay:()=>c,gV:()=>n,lM:()=>i});var s=a(51990);let r=localStorage.getItem("user"),l={user:r?JSON.parse(r):null,isAuthenticated:!!r},o=(0,s.Z0)({name:"user",initialState:l,reducers:{setUser:(e,t)=>{e.user=t.payload.user,e.isAuthenticated=!0,localStorage.setItem("user",JSON.stringify(t.payload.user))},clearUser:e=>{e.user=null,e.isAuthenticated=!1,localStorage.removeItem("user")}}}),{setUser:n,clearUser:i}=o.actions,c=o.reducer},93457:(e,t,a)=>{a.d(t,{Ay:()=>c,Ig:()=>i,XY:()=>n});var s=a(51990),r=a(56762);let l=(0,s.Z0)({name:"studentProfile",initialState:{profileData:null,loading:!1,error:null},reducers:{setStudentProfileData(e,t){var a,s;if(e.profileData=t.payload,null===(s=t.payload)||void 0===s?void 0:null===(a=s.profile)||void 0===a?void 0:a.photo)try{localStorage.setItem("student_profile_photo",t.payload.profile.photo)}catch(e){console.error("Failed to persist photo to localStorage:",e)}},updateProfilePhoto(e,t){var a;if(null===(a=e.profileData)||void 0===a?void 0:a.profile){e.profileData.profile.photo=t.payload;try{t.payload?localStorage.setItem("student_profile_photo",t.payload):localStorage.removeItem("student_profile_photo")}catch(e){console.error("Failed to persist photo to localStorage:",e)}}},clearStudentProfileData(e){e.profileData=null,e.loading=!1,e.error=null;try{localStorage.removeItem("student_profile_photo")}catch(e){console.error("Failed to clear photo from localStorage:",e)}}},extraReducers:e=>{e.addCase(r.N.pending,e=>{e.loading=!0,e.error=null}).addCase(r.N.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(r.N.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}).addCase(r.A.pending,e=>{e.loading=!0,e.error=null}).addCase(r.A.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(r.A.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setStudentProfileData:o,updateProfilePhoto:n,clearStudentProfileData:i}=l.actions,c=l.reducer}}]);