(()=>{var e={};e.id=5651,e.ids=[5651],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3416:(e,r,t)=>{"use strict";t.d(r,{sG:()=>u,hO:()=>p});var s=t(43210),a=t(51215),n=t(98599),i=t(60687),l=s.forwardRef((e,r)=>{let{children:t,...a}=e,n=s.Children.toArray(t),l=n.find(d);if(l){let e=l.props.children,t=n.map(r=>r!==l?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(o,{...a,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,t):null})}return(0,i.jsx)(o,{...a,ref:r,children:t})});l.displayName="Slot";var o=s.forwardRef((e,r)=>{let{children:t,...a}=e;if(s.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),i=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{n(...e),a(...e)}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==s.Fragment&&(i.ref=r?(0,n.t)(r,e):e),s.cloneElement(t,i)}return s.Children.count(t)>1?s.Children.only(null):null});o.displayName="SlotClone";var c=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function d(e){return s.isValidElement(e)&&e.type===c}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=s.forwardRef((e,t)=>{let{asChild:s,...a}=e,n=s?l:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n,{...a,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function p(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},5762:(e,r,t)=>{Promise.resolve().then(t.bind(t,18760)),Promise.resolve().then(t.bind(t,35950))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(43210),a=t(60687);function n(e,r=[]){let t=[],i=()=>{let r=t.map(e=>s.createContext(e));return function(t){let a=t?.[e]||r;return s.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return i.scopeName=e,[function(r,n){let i=s.createContext(n),l=t.length;t=[...t,n];let o=r=>{let{scope:t,children:n,...o}=r,c=t?.[e]?.[l]||i,d=s.useMemo(()=>o,Object.values(o));return(0,a.jsx)(c.Provider,{value:d,children:n})};return o.displayName=r+"Provider",[o,function(t,a){let o=a?.[e]?.[l]||i,c=s.useContext(o);if(c)return c;if(void 0!==n)return n;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((r,{useScope:t,scopeName:s})=>{let a=t(e)[`__scope${s}`];return{...r,...a}},{});return s.useMemo(()=>({[`__scope${r.scopeName}`]:a}),[a])}};return t.scopeName=r.scopeName,t}(i,...r)]}},12304:(e,r,t)=>{"use strict";t.d(r,{Separator:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx","Separator")},12412:e=>{"use strict";e.exports=require("assert")},13964:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},18760:(e,r,t)=>{"use strict";t.d(r,{ProfileForm:()=>N});var s=t(60687),a=t(63442),n=t(27605),i=t(45880),l=t(54864),o=t(50346),c=t(16189),d=t(52581),u=t(80942),p=t(89667),m=t(29523),f=t(56896),h=t(28527);t(43210);var x=t(20672),g=t(40988),b=t(40228),j=t(11095),v=t(79663);t(38399);let y=i.z.object({username:i.z.string().min(2,"Username must be at least 2 characters.").max(30),firstName:i.z.string().min(2,"First name must be at least 2 characters."),lastName:i.z.string().min(2,"Last name must be at least 2 characters."),className:i.z.string().min(2,"Class name must be at least 2 characters."),contactNo:i.z.string().min(10,"Contact must be at least 10 digits.").max(10,"Contact must be at least 10 digits."),birthDate:i.z.string({required_error:"Birthdate is required"}).nonempty("Birthdate cannot be empty"),email:i.z.string({required_error:"Email is required"}).nonempty("Email cannot be empty").email("Please enter a valid email address"),isAdult:i.z.literal(!0,{errorMap:()=>({message:"You must confirm you are over 18."})})});function N(){let e=(0,n.mN)({resolver:(0,a.u)(y),defaultValues:{username:"",firstName:"",lastName:"",className:"",birthDate:"",email:"",isAdult:!0},mode:"onChange"}),r=(0,l.wA)(),t=(0,c.useRouter)(),{user:i}=(0,l.d4)(e=>e.user);async function N(e){try{await h.S.post("/classes-profile/about",e),await r((0,x.V)(i.id)),r((0,o.ac)(o._3.PROFILE)),d.toast.success("Profile updated successfully!"),t.push("/classes/profile/description")}catch(e){d.toast.error(e.response.data.message),console.error("Error submitting form:",e)}}(0,l.d4)(e=>e.class.classData);let{reset:w}=e;return(0,s.jsx)(u.lV,{...e,children:(0,s.jsxs)("form",{onSubmit:e.handleSubmit(N),className:"space-y-6",children:[(0,s.jsx)(u.zB,{control:e.control,name:"username",render:({field:e})=>(0,s.jsxs)(u.eI,{children:[(0,s.jsx)(u.lR,{children:"Username"}),(0,s.jsx)(u.MJ,{children:(0,s.jsx)(p.p,{placeholder:"john_doe",...e})}),(0,s.jsx)(u.Rr,{children:"Should be unique"}),(0,s.jsx)(u.C5,{})]})}),(0,s.jsx)(u.zB,{control:e.control,name:"firstName",render:({field:e})=>(0,s.jsxs)(u.eI,{children:[(0,s.jsx)(u.lR,{children:"First Name"}),(0,s.jsx)(u.MJ,{children:(0,s.jsx)(p.p,{placeholder:"John",...e})}),(0,s.jsx)(u.C5,{})]})}),(0,s.jsx)(u.zB,{control:e.control,name:"lastName",render:({field:e})=>(0,s.jsxs)(u.eI,{children:[(0,s.jsx)(u.lR,{children:"Last Name"}),(0,s.jsx)(u.MJ,{children:(0,s.jsx)(p.p,{placeholder:"Doe",...e})}),(0,s.jsx)(u.C5,{})]})}),(0,s.jsx)(u.zB,{control:e.control,name:"className",render:({field:e})=>(0,s.jsxs)(u.eI,{children:[(0,s.jsx)(u.lR,{children:"Class Name"}),(0,s.jsx)(u.MJ,{children:(0,s.jsx)(p.p,{placeholder:"Class Name",...e})}),(0,s.jsx)(u.C5,{})]})}),(0,s.jsx)(u.zB,{control:e.control,name:"email",render:({field:e})=>(0,s.jsxs)(u.eI,{children:[(0,s.jsx)(u.lR,{children:"Email"}),(0,s.jsx)(u.MJ,{children:(0,s.jsx)(p.p,{placeholder:"Email",...e})}),(0,s.jsx)(u.C5,{})]})}),(0,s.jsx)(u.zB,{control:e.control,name:"birthDate",render:({field:e})=>(0,s.jsxs)(u.eI,{className:"flex flex-col",children:[(0,s.jsx)(u.lR,{children:"Birthdate"}),(0,s.jsxs)(g.AM,{children:[(0,s.jsx)(g.Wv,{asChild:!0,children:(0,s.jsx)(u.MJ,{children:(0,s.jsxs)(m.$,{variant:"outline",className:`w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-100 ${e.value?"text-black dark:text-white":"text-muted-foreground"}`,children:[e.value?(0,v.GP)(new Date(e.value),"PPP"):"Pick a date",(0,s.jsx)(b.A,{className:"ml-auto h-4 w-4 opacity-50 dark:text-black"})]})})}),(0,s.jsx)(g.hl,{className:"w-auto p-0 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 shadow-lg",align:"start",children:(0,s.jsx)("div",{className:"w-full rounded-md border shadow-sm",children:(0,s.jsx)(j.V,{mode:"single",captionLayout:"dropdown",fromYear:1950,toYear:new Date().getFullYear(),selected:e.value?new Date(e.value):void 0,onSelect:r=>{if(r){let t=(0,v.GP)(r,"yyyy-MM-dd");e.onChange(t)}},disabled:e=>e>new Date,initialFocus:!0,classNames:{caption:"flex justify-center p-2",dropdown:"mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",caption_label:"hidden"}})})})]}),(0,s.jsx)(u.C5,{})]})}),(0,s.jsx)(u.zB,{control:e.control,name:"contactNo",render:({field:e})=>(0,s.jsxs)(u.eI,{children:[(0,s.jsx)(u.lR,{children:"Contact No"}),(0,s.jsx)(u.MJ,{children:(0,s.jsx)(p.p,{placeholder:"Contact No",...e})}),(0,s.jsx)(u.C5,{})]})}),(0,s.jsx)(u.zB,{control:e.control,name:"isAdult",render:({field:e})=>(0,s.jsxs)(u.eI,{className:"flex items-start gap-2",children:[(0,s.jsx)(u.MJ,{children:(0,s.jsx)(f.S,{checked:e.value,onCheckedChange:e.onChange})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.lR,{children:"I confirm I’m over 18"}),(0,s.jsx)(u.C5,{})]})]})}),(0,s.jsx)(m.$,{type:"submit",children:"Update Profile"})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23546:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes\\\\profile\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx","default")},23562:(e,r,t)=>{"use strict";t.d(r,{k:()=>i});var s=t(60687);t(43210);var a=t(25177),n=t(4780);function i({className:e,value:r,...t}){return(0,s.jsx)(a.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})})}},27910:e=>{"use strict";e.exports=require("stream")},28029:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var s=t(60687),a=t(35950),n=t(85814),i=t.n(n),l=t(16189),o=t(54864),c=t(5336);function d({items:e}){let r=(0,l.usePathname)(),{completedForms:t}=(0,o.d4)(e=>e.formProgress),a=e=>e.toLowerCase().replace(/ & /g,"_").replace(/\s+/g,"_");return(0,s.jsx)("nav",{className:"space-y-1",children:e.map((n,l)=>{let o=a(n.title),d=r===n.href,u=l>0&&!t[a(e[l-1].title)];return(0,s.jsxs)(i(),{href:u?"#":n.href,className:`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${d?"bg-muted text-primary":u?"text-gray-400 cursor-not-allowed":"text-muted-foreground hover:text-primary"}`,onClick:e=>{u&&e.preventDefault()},children:[(0,s.jsx)("span",{children:n.title}),t[o]&&(0,s.jsx)(c.A,{size:16,className:"text-green-500"})]},n.href)})})}var u=t(23562),p=t(43210),m=t(28527);t(36097),t(35817);var f=t(29523),h=t(90269),x=t(46303);let g=[{title:"About",href:"/classes/profile"},{title:"Description",href:"/classes/profile/description"},{title:"Address",href:"/classes/profile/address"},{title:"Photo & Logo",href:"/classes/profile/photo-and-logo"},{title:"Education",href:"/classes/profile/education"},{title:"Experience",href:"/classes/profile/experience"},{title:"Certificates",href:"/classes/profile/certificates"},{title:"Tution Class",href:"/classes/profile/tution-class"}];function b({children:e}){let{completedSteps:r,totalSteps:t}=(0,o.d4)(e=>e.formProgress),{user:n}=function(){let e=(0,o.d4)(e=>e.user.isAuthenticated);return(0,l.useRouter)(),{user:e}}(),{user:i}=(0,o.d4)(e=>e.user);(0,o.wA)();let[c,b]=(0,p.useState)(!1),[j,v]=(0,p.useState)(!1),[y,N]=(0,p.useState)("");if(!n)return null;let w=r/t*100,C=100===Math.round(w),P=async()=>{try{b(!0),await m.S.post(`/classes-profile/send-for-review/${i.id}`),v(!0)}catch(e){console.error("Error sending for review:",e)}finally{b(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.default,{}),(0,s.jsxs)("div",{className:"space-y-6 p-10 pb-4 md:block",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Edit Profile"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Start creating your public profile. Your progress will be automatically saved as you complete each section. You can return at any time to finish your registration."})]}),(0,s.jsx)(u.k,{value:w,className:"h-2"}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[Math.round(w),"% complete"]}),C&&(0,s.jsx)("div",{className:"mt-4",children:j?(0,s.jsx)(f.$,{className:"bg-gray-400 text-white cursor-not-allowed",disabled:!0,children:"APPROVED"===y?"Profile Approved ✅":"Profile Sent for Review"}):(0,s.jsx)(f.$,{className:"bg-green-600 hover:bg-green-700 text-white",disabled:c,onClick:P,children:"Send for Review"})}),(0,s.jsx)(a.Separator,{className:"my-6"}),(0,s.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[(0,s.jsx)("aside",{className:"-mx-4 lg:w-1/6 pb-12",children:(0,s.jsx)(d,{items:g})}),(0,s.jsx)("div",{className:"flex justify-center w-full",children:(0,s.jsx)("div",{className:"flex-1 lg:max-w-2xl pb-12",children:e})})]})]}),(0,s.jsx)(x.default,{})]})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33819:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let c={children:["",{children:["classes",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71105)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,23546)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,3792)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/classes/profile/page",pathname:"/classes/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},35817:(e,r,t)=>{"use strict";t.d(r,{Ow:()=>n,Wz:()=>a,sA:()=>i});var s=t(50346);let a=(e,r)=>{e.contactNo&&r((0,s.ac)(s._3.PROFILE)),e.ClassAbout?.tutorBio?.length>50&&r((0,s.ac)(s._3.DESCRIPTION)),e.ClassAbout?.profilePhoto&&e.ClassAbout?.classesLogo&&r((0,s.ac)(s._3.PHOTO_LOGO)),e.education?.length>0&&r((0,s.ac)(s._3.EDUCATION)),e.certificates?.length>0&&r((0,s.ac)(s._3.CERTIFICATES)),e.experience?.length>0&&r((0,s.ac)(s._3.EXPERIENCE)),e.tuitionClasses?.length>0&&r((0,s.ac)(s._3.TUTIONCLASS)),e.address&&r((0,s.ac)(s._3.ADDRESS))},n=e=>{if(!e)return[];try{let r="string"==typeof e?JSON.parse(e):e;return Array.isArray(r)?r:[r]}catch{return[e]}},i=e=>{try{let r="string"==typeof e?JSON.parse(e):e;return Array.isArray(r)?r.join(", "):r||"N/A"}catch{return e||"N/A"}};new TextEncoder().encode("secret123")},36161:(e,r,t)=>{Promise.resolve().then(t.bind(t,23546))},38399:()=>{},51406:(e,r,t)=>{"use strict";t.d(r,{ProfileForm:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ProfileForm() from the server but ProfileForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx","ProfileForm")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,r,t)=>{"use strict";t.d(r,{S:()=>l});var s=t(60687);t(43210);var a=t(25112),n=t(13964),i=t(4780);function l({className:e,...r}){return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(n.A,{className:"size-3.5"})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65594:(e,r,t)=>{Promise.resolve().then(t.bind(t,51406)),Promise.resolve().then(t.bind(t,12304))},71105:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413),a=t(12304),n=t(51406);function i(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"About"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Start creating your public profile"})]}),(0,s.jsx)(a.Separator,{}),(0,s.jsx)(n.ProfileForm,{})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},94990:(e,r,t)=>{Promise.resolve().then(t.bind(t,28029))},98599:(e,r,t)=>{"use strict";t.d(r,{s:()=>i,t:()=>n});var s=t(43210);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function i(...e){return s.useCallback(n(...e),e)}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7013,2105,9191,9663,5236,2800,7200,2489],()=>t(33819));module.exports=s})();