(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7839],{7583:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(95155);r(12115);var i=r(6874),l=r.n(i),n=r(66766),a=r(29911);let c=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(n.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:a.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:a.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:a.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:a.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:a.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:a.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:a.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:r,label:i}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:i,children:(0,s.jsx)(r,{className:"text-xl text-white hover:text-gray-400 transition"})})},i)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(n.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},56277:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(95155),i=r(7583),l=r(70347),n=r(54165),a=r(21751),c=r(60723),o=r(35695),h=r(12115);let d=()=>{let e=(0,o.useRouter)(),t=(0,o.useSearchParams)(),r=t.get("token"),i=t.get("userType")||"teacher",[l,d]=(0,h.useState)("loading"),[m,x]=(0,h.useState)("");return console.log("Token that is",r),console.log("User type:",i),(0,h.useEffect)(()=>{(async()=>{if(!r){d("error"),x("No verification token");return}try{let t;t="student"===i?await (0,c.Xc)(r):await (0,a.A$)(r),console.log("Verification response:",t),d("success"),x(t.message||"Email verified successfully!"),setTimeout(()=>{e.push("/")},2e3)}catch(e){console.error("Verification error:",e),d("error"),x("Failed to verify email. The token is invalid or expired.")}})()},[r,i,e]),(0,s.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,s.jsxs)(n.lG,{children:["loading"===l&&(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Verifying your email..."}),"success"===l&&(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:["Your Email is",(0,s.jsx)("span",{className:"text-orange-500 italic",children:" Verified"}),(0,s.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:m})]}),"error"===l&&(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:["Verification",(0,s.jsx)("span",{className:"text-red-500 italic",children:" Failed"}),(0,s.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:m})]})]})})},m=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.default,{}),(0,s.jsx)(h.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading verification..."}),children:(0,s.jsx)(d,{})}),(0,s.jsx)(i.default,{})]})},63657:(e,t,r)=>{Promise.resolve().then(r.bind(r,56277))},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>h});var s=r(12115),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=s.createContext&&s.createContext(i),n=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var s,i,l;s=e,i=t,l=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in s?Object.defineProperty(s,i,{value:l,enumerable:!0,configurable:!0,writable:!0}):s[i]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e){return t=>s.createElement(d,a({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,o({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:i,size:l,title:c}=e,h=function(e,t){if(null==e)return{};var r,s,i=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)r=l[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,n),d=l||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,h,{className:r,style:o(o({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==l?s.createElement(l.Consumer,null,e=>t(e)):t(i)}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,347,8441,1684,7358],()=>t(63657)),_N_E=e.O()}]);