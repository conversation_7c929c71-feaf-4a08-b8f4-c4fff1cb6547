"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5845:(e,t,r)=>{r.d(t,{i:()=>o});var n=r(12115),l=r(39033);function o({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,i]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,i=n.useRef(o),a=(0,l.c)(t);return n.useEffect(()=>{i.current!==o&&(a(o),i.current=o)},[o,i,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,s=a?e:o,u=(0,l.c)(r);return[s,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else i(t)},[a,e,i,u])]}},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>o});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},39033:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(12115);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(12115),l=r(95155);function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return i.scopeName=e,[function(t,o){let i=n.createContext(o),a=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,u=r?.[e]?.[a]||i,d=n.useMemo(()=>s,Object.values(s));return(0,l.jsx)(u.Provider,{value:d,children:o})};return s.displayName=t+"Provider",[s,function(r,l){let s=l?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(i,...t)]}},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},52712:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(12115),l=globalThis?.document?n.useLayoutEffect:()=>{}},59824:(e,t,r)=>{r.d(t,{UC:()=>tn,In:()=>tt,q7:()=>to,VF:()=>ta,p4:()=>ti,ZL:()=>tr,bL:()=>e8,wn:()=>tu,PP:()=>ts,l9:()=>e7,WT:()=>te,LM:()=>tl});var n,l=r(12115),o=r(47650);function i(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(85185),s=r(76589),u=r(6101),d=r(46081),c=r(94315),f=r(63540),p=r(39033),v=r(95155),m="dismissableLayer.update",h=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=l.forwardRef((e,t)=>{var r,o;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:c,onInteractOutside:y,onDismiss:x,...b}=e,E=l.useContext(h),[C,S]=l.useState(null),j=null!==(o=null==C?void 0:C.ownerDocument)&&void 0!==o?o:null===(r=globalThis)||void 0===r?void 0:r.document,[,R]=l.useState({}),N=(0,u.s)(t,e=>S(e)),P=Array.from(E.layers),[A]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=P.indexOf(A),k=C?P.indexOf(C):-1,L=E.layersWithOutsidePointerEventsDisabled.size>0,D=k>=T,I=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,p.c)(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){w("dismissableLayer.pointerDownOutside",n,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));!D||r||(null==d||d(e),null==y||y(e),e.defaultPrevented||null==x||x())},j),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,p.c)(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&w("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==c||c(e),null==y||y(e),e.defaultPrevented||null==x||x())},j);return!function(e,t=globalThis?.document){let r=(0,p.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{k===E.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},j),l.useEffect(()=>{if(C)return i&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=j.body.style.pointerEvents,j.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),g(),()=>{i&&1===E.layersWithOutsidePointerEventsDisabled.size&&(j.body.style.pointerEvents=n)}},[C,j,i,E]),l.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),g())},[C,E]),l.useEffect(()=>{let e=()=>R({});return document.addEventListener(m,e),()=>document.removeEventListener(m,e)},[]),(0,v.jsx)(f.sG.div,{...b,ref:N,style:{pointerEvents:L?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function g(){let e=new CustomEvent(m);document.dispatchEvent(e)}function w(e,t,r,n){let{discrete:l}=n,o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),l?(0,f.hO)(o,i):o.dispatchEvent(i)}y.displayName="DismissableLayer",l.forwardRef((e,t)=>{let r=l.useContext(h),n=l.useRef(null),o=(0,u.s)(t,n);return l.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,v.jsx)(f.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var x=0;function b(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var E="focusScope.autoFocusOnMount",C="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},j=l.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[s,d]=l.useState(null),c=(0,p.c)(o),m=(0,p.c)(i),h=l.useRef(null),y=(0,u.s)(t,e=>d(e)),g=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(n){let e=function(e){if(g.paused||!s)return;let t=e.target;s.contains(t)?h.current=t:P(h.current,{select:!0})},t=function(e){if(g.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||P(h.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&P(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,g.paused]),l.useEffect(()=>{if(s){A.add(g);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(E,S);s.addEventListener(E,c),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(P(n,{select:t}),document.activeElement!==r)return}(R(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&P(s))}return()=>{s.removeEventListener(E,c),setTimeout(()=>{let t=new CustomEvent(C,S);s.addEventListener(C,m),s.dispatchEvent(t),t.defaultPrevented||P(null!=e?e:document.body,{select:!0}),s.removeEventListener(C,m),A.remove(g)},0)}}},[s,c,m,g]);let w=l.useCallback(e=>{if(!r&&!n||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,l=document.activeElement;if(t&&l){let t=e.currentTarget,[n,o]=function(e){let t=R(e);return[N(t,e),N(t.reverse(),e)]}(t);n&&o?e.shiftKey||l!==o?e.shiftKey&&l===n&&(e.preventDefault(),r&&P(o,{select:!0})):(e.preventDefault(),r&&P(n,{select:!0})):l===t&&e.preventDefault()}},[r,n,g.paused]);return(0,v.jsx)(f.sG.div,{tabIndex:-1,...a,ref:y,onKeyDown:w})});function R(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function N(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function P(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}j.displayName="FocusScope";var A=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=T(e,t)).unshift(t)},remove(t){var r;null===(r=(e=T(e,t))[0])||void 0===r||r.resume()}}}();function T(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var k=r(61285),L=r(84945),D=r(22475),I=l.forwardRef((e,t)=>{let{children:r,width:n=10,height:l=5,...o}=e;return(0,v.jsx)(f.sG.svg,{...o,ref:t,width:n,height:l,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});I.displayName="Arrow";var O=r(52712),M="Popper",[F,W]=(0,d.A)(M),[B,H]=F(M),_=e=>{let{__scopePopper:t,children:r}=e,[n,o]=l.useState(null);return(0,v.jsx)(B,{scope:t,anchor:n,onAnchorChange:o,children:r})};_.displayName=M;var V="PopperAnchor",G=l.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,i=H(V,r),a=l.useRef(null),s=(0,u.s)(t,a);return l.useEffect(()=>{i.onAnchorChange((null==n?void 0:n.current)||a.current)}),n?null:(0,v.jsx)(f.sG.div,{...o,ref:s})});G.displayName=V;var K="PopperContent",[z,U]=F(K),q=l.forwardRef((e,t)=>{var r,n,o,i,a,s,d,c;let{__scopePopper:m,side:h="bottom",sideOffset:y=0,align:g="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:b=!0,collisionBoundary:E=[],collisionPadding:C=0,sticky:S="partial",hideWhenDetached:j=!1,updatePositionStrategy:R="optimized",onPlaced:N,...P}=e,A=H(K,m),[T,k]=l.useState(null),I=(0,u.s)(t,e=>k(e)),[M,F]=l.useState(null),W=function(e){let[t,r]=l.useState(void 0);return(0,O.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,l;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,l=t.blockSize}else n=e.offsetWidth,l=e.offsetHeight;r({width:n,height:l})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(M),B=null!==(d=null==W?void 0:W.width)&&void 0!==d?d:0,_=null!==(c=null==W?void 0:W.height)&&void 0!==c?c:0,V="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},G=Array.isArray(E)?E:[E],U=G.length>0,q={padding:V,boundary:G.filter(Z),altBoundary:U},{refs:Y,floatingStyles:$,placement:X,isPositioned:ee,middlewareData:et}=(0,L.we)({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,D.ll)(...t,{animationFrame:"always"===R})},elements:{reference:A.anchor},middleware:[(0,L.cY)({mainAxis:y+_,alignmentAxis:w}),b&&(0,L.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?(0,L.ER)():void 0,...q}),b&&(0,L.UU)({...q}),(0,L.Ej)({...q,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:l}=e,{width:o,height:i}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(l,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),M&&(0,L.UE)({element:M,padding:x}),J({arrowWidth:B,arrowHeight:_}),j&&(0,L.jD)({strategy:"referenceHidden",...q})]}),[er,en]=Q(X),el=(0,p.c)(N);(0,O.N)(()=>{ee&&(null==el||el())},[ee,el]);let eo=null===(r=et.arrow)||void 0===r?void 0:r.x,ei=null===(n=et.arrow)||void 0===n?void 0:n.y,ea=(null===(o=et.arrow)||void 0===o?void 0:o.centerOffset)!==0,[es,eu]=l.useState();return(0,O.N)(()=>{T&&eu(window.getComputedStyle(T).zIndex)},[T]),(0,v.jsx)("div",{ref:Y.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:ee?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:es,"--radix-popper-transform-origin":[null===(i=et.transformOrigin)||void 0===i?void 0:i.x,null===(a=et.transformOrigin)||void 0===a?void 0:a.y].join(" "),...(null===(s=et.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(z,{scope:m,placedSide:er,onArrowChange:F,arrowX:eo,arrowY:ei,shouldHideArrow:ea,children:(0,v.jsx)(f.sG.div,{"data-side":er,"data-align":en,...P,ref:I,style:{...P.style,animation:ee?void 0:"none"}})})})});q.displayName=K;var Y="PopperArrow",$={top:"bottom",right:"left",bottom:"top",left:"right"},X=l.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,l=U(Y,r),o=$[l.placedSide];return(0,v.jsx)("span",{ref:l.onArrowChange,style:{position:"absolute",left:l.arrowX,top:l.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[l.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[l.placedSide],visibility:l.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(I,{...n,ref:t,style:{...n.style,display:"block"}})})});function Z(e){return null!==e}X.displayName=Y;var J=e=>({name:"transformOrigin",options:e,fn(t){var r,n,l,o,i;let{placement:a,rects:s,middlewareData:u}=t,d=(null===(r=u.arrow)||void 0===r?void 0:r.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=Q(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(o=null===(n=u.arrow)||void 0===n?void 0:n.x)&&void 0!==o?o:0)+c/2,y=(null!==(i=null===(l=u.arrow)||void 0===l?void 0:l.y)&&void 0!==i?i:0)+f/2,g="",w="";return"bottom"===p?(g=d?m:"".concat(h,"px"),w="".concat(-f,"px")):"top"===p?(g=d?m:"".concat(h,"px"),w="".concat(s.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),w=d?m:"".concat(y,"px")):"left"===p&&(g="".concat(s.floating.width+f,"px"),w=d?m:"".concat(y,"px")),{data:{x:g,y:w}}}});function Q(e){let[t,r="center"]=e.split("-");return[t,r]}var ee=l.forwardRef((e,t)=>{var r,n;let{container:i,...a}=e,[s,u]=l.useState(!1);(0,O.N)(()=>u(!0),[]);let d=i||s&&(null===(n=globalThis)||void 0===n?void 0:null===(r=n.document)||void 0===r?void 0:r.body);return d?o.createPortal((0,v.jsx)(f.sG.div,{...a,ref:t}),d):null});ee.displayName="Portal";var et=l.forwardRef((e,t)=>{let{children:r,...n}=e,o=l.Children.toArray(r),i=o.find(el);if(i){let e=i.props.children,r=o.map(t=>t!==i?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,v.jsx)(er,{...n,ref:t,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,v.jsx)(er,{...n,ref:t,children:r})});et.displayName="Slot";var er=l.forwardRef((e,t)=>{let{children:r,...n}=e;if(l.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==l.Fragment&&(o.ref=t?(0,u.t)(t,e):e),l.cloneElement(r,o)}return l.Children.count(r)>1?l.Children.only(null):null});er.displayName="SlotClone";var en=({children:e})=>(0,v.jsx)(v.Fragment,{children:e});function el(e){return l.isValidElement(e)&&e.type===en}var eo=r(5845),ei=l.forwardRef((e,t)=>(0,v.jsx)(f.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ei.displayName="VisuallyHidden";var ea=r(38168),es=r(31114),eu=[" ","Enter","ArrowUp","ArrowDown"],ed=[" ","Enter"],ec="Select",[ef,ep,ev]=(0,s.N)(ec),[em,eh]=(0,d.A)(ec,[ev,W]),ey=W(),[eg,ew]=em(ec),[ex,eb]=em(ec),eE=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:h,form:y}=e,g=ey(t),[w,x]=l.useState(null),[b,E]=l.useState(null),[C,S]=l.useState(!1),j=(0,c.jH)(d),[R=!1,N]=(0,eo.i)({prop:n,defaultProp:o,onChange:i}),[P,A]=(0,eo.i)({prop:a,defaultProp:s,onChange:u}),T=l.useRef(null),L=!w||y||!!w.closest("form"),[D,I]=l.useState(new Set),O=Array.from(D).map(e=>e.props.value).join(";");return(0,v.jsx)(_,{...g,children:(0,v.jsxs)(eg,{required:h,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:(0,k.B)(),value:P,onValueChange:A,open:R,onOpenChange:N,dir:j,triggerPointerDownPosRef:T,disabled:m,children:[(0,v.jsx)(ef.Provider,{scope:t,children:(0,v.jsx)(ex,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{I(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{I(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),L?(0,v.jsxs)(e9,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>A(e.target.value),disabled:m,form:y,children:[void 0===P?(0,v.jsx)("option",{value:""}):null,Array.from(D)]},O):null]})})};eE.displayName=ec;var eC="SelectTrigger",eS=l.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...o}=e,i=ey(r),s=ew(eC,r),d=s.disabled||n,c=(0,u.s)(t,s.onTriggerChange),p=ep(r),m=l.useRef("touch"),[h,y,g]=e4(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=e3(t,e,r);void 0!==n&&s.onValueChange(n.value)}),w=e=>{d||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,v.jsx)(G,{asChild:!0,...i,children:(0,v.jsx)(f.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":e6(s.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==m.current&&w(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{m.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&eu.includes(e.key)&&(w(),e.preventDefault())})})})});eS.displayName=eC;var ej="SelectValue",eR=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:i="",...a}=e,s=ew(ej,r),{onValueNodeHasChildrenChange:d}=s,c=void 0!==o,p=(0,u.s)(t,s.onValueNodeChange);return(0,O.N)(()=>{d(c)},[d,c]),(0,v.jsx)(f.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:e6(s.value)?(0,v.jsx)(v.Fragment,{children:i}):o})});eR.displayName=ej;var eN=l.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,v.jsx)(f.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});eN.displayName="SelectIcon";var eP=e=>(0,v.jsx)(ee,{asChild:!0,...e});eP.displayName="SelectPortal";var eA="SelectContent",eT=l.forwardRef((e,t)=>{let r=ew(eA,e.__scopeSelect),[n,i]=l.useState();return((0,O.N)(()=>{i(new DocumentFragment)},[]),r.open)?(0,v.jsx)(eD,{...e,ref:t}):n?o.createPortal((0,v.jsx)(ek,{scope:e.__scopeSelect,children:(0,v.jsx)(ef.Slot,{scope:e.__scopeSelect,children:(0,v.jsx)("div",{children:e.children})})}),n):null});eT.displayName=eA;var[ek,eL]=em(eA),eD=l.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:d,sideOffset:c,align:f,alignOffset:p,arrowPadding:m,collisionBoundary:h,collisionPadding:g,sticky:w,hideWhenDetached:E,avoidCollisions:C,...S}=e,R=ew(eA,r),[N,P]=l.useState(null),[A,T]=l.useState(null),k=(0,u.s)(t,e=>P(e)),[L,D]=l.useState(null),[I,O]=l.useState(null),M=ep(r),[F,W]=l.useState(!1),B=l.useRef(!1);l.useEffect(()=>{if(N)return(0,ea.Eq)(N)},[N]),l.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=r[0])&&void 0!==e?e:b()),document.body.insertAdjacentElement("beforeend",null!==(t=r[1])&&void 0!==t?t:b()),x++,()=>{1===x&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),x--}},[]);let H=l.useCallback(e=>{let[t,...r]=M().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&A&&(A.scrollTop=0),r===n&&A&&(A.scrollTop=A.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[M,A]),_=l.useCallback(()=>H([L,N]),[H,L,N]);l.useEffect(()=>{F&&_()},[F,_]);let{onOpenChange:V,triggerPointerDownPosRef:G}=R;l.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=G.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=G.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||V(!1),document.removeEventListener("pointermove",t),G.current=null};return null!==G.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,V,G]),l.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[K,z]=e4(e=>{let t=M().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=e3(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),U=l.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==R.value&&R.value===t||n)&&(D(e),n&&(B.current=!0))},[R.value]),q=l.useCallback(()=>null==N?void 0:N.focus(),[N]),Y=l.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==R.value&&R.value===t||n)&&O(e)},[R.value]),$="popper"===n?eO:eI,X=$===eO?{side:d,sideOffset:c,align:f,alignOffset:p,arrowPadding:m,collisionBoundary:h,collisionPadding:g,sticky:w,hideWhenDetached:E,avoidCollisions:C}:{};return(0,v.jsx)(ek,{scope:r,content:N,viewport:A,onViewportChange:T,itemRefCallback:U,selectedItem:L,onItemLeave:q,itemTextRefCallback:Y,focusSelectedItem:_,selectedItemText:I,position:n,isPositioned:F,searchRef:K,children:(0,v.jsx)(es.A,{as:et,allowPinchZoom:!0,children:(0,v.jsx)(j,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null===(t=R.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,v.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,v.jsx)($,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...S,...X,onPlaced:()=>W(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,a.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});eD.displayName="SelectContentImpl";var eI=l.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...o}=e,a=ew(eA,r),s=eL(eA,r),[d,c]=l.useState(null),[p,m]=l.useState(null),h=(0,u.s)(t,e=>m(e)),y=ep(r),g=l.useRef(!1),w=l.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:C}=s,S=l.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&p&&x&&b&&E){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),l=E.getBoundingClientRect();if("rtl"!==a.dir){let n=l.left-t.left,o=r.left-n,a=e.left-o,s=e.width+a,u=Math.max(s,t.width),c=i(o,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let n=t.right-l.right,o=window.innerWidth-r.right-n,a=window.innerWidth-e.right-o,s=e.width+a,u=Math.max(s,t.width),c=i(o,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let o=y(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),h=f+v+u+parseInt(c.paddingBottom,10)+m,w=Math.min(5*b.offsetHeight,h),C=window.getComputedStyle(x),S=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,N=b.offsetHeight/2,P=f+v+(b.offsetTop+N);if(P<=R){let e=o.length>0&&b===o[o.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-R,N+(e?j:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+m);d.style.height=P+t+"px"}else{let e=o.length>0&&b===o[0].ref.current;d.style.top="0px";let t=Math.max(R,f+x.offsetTop+(e?S:0)+N);d.style.height=t+(h-P)+"px",x.scrollTop=P-R+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=w+"px",d.style.maxHeight=s+"px",null==n||n(),requestAnimationFrame(()=>g.current=!0)}},[y,a.trigger,a.valueNode,d,p,x,b,E,a.dir,n]);(0,O.N)(()=>S(),[S]);let[j,R]=l.useState();(0,O.N)(()=>{p&&R(window.getComputedStyle(p).zIndex)},[p]);let N=l.useCallback(e=>{e&&!0===w.current&&(S(),null==C||C(),w.current=!1)},[S,C]);return(0,v.jsx)(eM,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:g,onScrollButtonChange:N,children:(0,v.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,v.jsx)(f.sG.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});eI.displayName="SelectItemAlignedPosition";var eO=l.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,i=ey(r);return(0,v.jsx)(q,{...i,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eO.displayName="SelectPopperPosition";var[eM,eF]=em(eA,{}),eW="SelectViewport",eB=l.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...o}=e,i=eL(eW,r),s=eF(eW,r),d=(0,u.s)(t,i.onViewportChange),c=l.useRef(0);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,v.jsx)(ef.Slot,{scope:r,children:(0,v.jsx)(f.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,i=Math.min(n,o),a=o-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eB.displayName=eW;var eH="SelectGroup",[e_,eV]=em(eH);l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,k.B)();return(0,v.jsx)(e_,{scope:r,id:l,children:(0,v.jsx)(f.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=eH;var eG="SelectLabel";l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=eV(eG,r);return(0,v.jsx)(f.sG.div,{id:l.id,...n,ref:t})}).displayName=eG;var eK="SelectItem",[ez,eU]=em(eK),eq=l.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:o=!1,textValue:i,...s}=e,d=ew(eK,r),c=eL(eK,r),p=d.value===n,[m,h]=l.useState(null!=i?i:""),[y,g]=l.useState(!1),w=(0,u.s)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,n,o)}),x=(0,k.B)(),b=l.useRef("touch"),E=()=>{o||(d.onValueChange(n),d.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,v.jsx)(ez,{scope:r,value:n,disabled:o,textId:x,isSelected:p,onItemTextChange:l.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,v.jsx)(ef.ItemSlot,{scope:r,value:n,disabled:o,textValue:m,children:(0,v.jsx)(f.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":y?"":void 0,"aria-selected":p&&y,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:w,onFocus:(0,a.m)(s.onFocus,()=>g(!0)),onBlur:(0,a.m)(s.onBlur,()=>g(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{var t;((null===(t=c.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(ed.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eq.displayName=eK;var eY="SelectItemText",e$=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:i,...a}=e,s=ew(eY,r),d=eL(eY,r),c=eU(eY,r),p=eb(eY,r),[m,h]=l.useState(null),y=(0,u.s)(t,e=>h(e),c.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,c.value,c.disabled)}),g=null==m?void 0:m.textContent,w=l.useMemo(()=>(0,v.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value),[c.disabled,c.value,g]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=p;return(0,O.N)(()=>(x(w),()=>b(w)),[x,b,w]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(f.sG.span,{id:c.textId,...a,ref:y}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(a.children,s.valueNode):null]})});e$.displayName=eY;var eX="SelectItemIndicator",eZ=l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eU(eX,r).isSelected?(0,v.jsx)(f.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eZ.displayName=eX;var eJ="SelectScrollUpButton",eQ=l.forwardRef((e,t)=>{let r=eL(eJ,e.__scopeSelect),n=eF(eJ,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,u.s)(t,n.onScrollButtonChange);return(0,O.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,v.jsx)(e5,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eQ.displayName=eJ;var e0="SelectScrollDownButton",e1=l.forwardRef((e,t)=>{let r=eL(e0,e.__scopeSelect),n=eF(e0,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,u.s)(t,n.onScrollButtonChange);return(0,O.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,v.jsx)(e5,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});e1.displayName=e0;var e5=l.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...o}=e,i=eL("SelectScrollButton",r),s=l.useRef(null),u=ep(r),d=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),(0,O.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,v.jsx)(f.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})});l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,v.jsx)(f.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var e2="SelectArrow";function e6(e){return""===e||void 0===e}l.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ey(r),o=ew(e2,r),i=eL(e2,r);return o.open&&"popper"===i.position?(0,v.jsx)(X,{...l,...n,ref:t}):null}).displayName=e2;var e9=l.forwardRef((e,t)=>{let{value:r,...n}=e,o=l.useRef(null),i=(0,u.s)(t,o),a=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return l.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,v.jsx)(ei,{asChild:!0,children:(0,v.jsx)("select",{...n,ref:i,defaultValue:r})})});function e4(e){let t=(0,p.c)(e),r=l.useRef(""),n=l.useRef(0),o=l.useCallback(e=>{let l=r.current+e;t(l),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(l)},[t]),i=l.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,i]}function e3(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,l=Math.max(i,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}e9.displayName="BubbleSelect";var e8=eE,e7=eS,te=eR,tt=eN,tr=eP,tn=eT,tl=eB,to=eq,ti=e$,ta=eZ,ts=eQ,tu=e1},61285:(e,t,r)=>{r.d(t,{B:()=>s});var n,l=r(12115),o=r(52712),i=(n||(n=r.t(l,2)))["useId".toString()]||(()=>void 0),a=0;function s(e){let[t,r]=l.useState(i());return(0,o.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},63540:(e,t,r)=>{r.d(t,{sG:()=>c,hO:()=>f});var n=r(12115),l=r(47650),o=r(6101),i=r(95155),a=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),a=o.find(d);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(s,{...l,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,o.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===u}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...l}=e,o=n?a:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function f(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},76589:(e,t,r)=>{r.d(t,{N:()=>c});var n=r(12115),l=r(46081),o=r(6101),i=r(95155),a=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),a=o.find(d);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(s,{...l,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,o.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===u}function c(e){let t=e+"CollectionProvider",[r,s]=(0,l.A)(t),[u,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,l=n.useRef(null),o=n.useRef(new Map).current;return(0,i.jsx)(u,{scope:t,itemMap:o,collectionRef:l,children:r})};c.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=d(f,r),s=(0,o.s)(t,l.collectionRef);return(0,i.jsx)(a,{ref:s,children:n})});p.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",h=n.forwardRef((e,t)=>{let{scope:r,children:l,...s}=e,u=n.useRef(null),c=(0,o.s)(t,u),f=d(v,r);return n.useEffect(()=>(f.itemMap.set(u,{ref:u,...s}),()=>void f.itemMap.delete(u))),(0,i.jsx)(a,{[m]:"",ref:c,children:l})});return h.displayName=v,[{Provider:c,Slot:p,ItemSlot:h},function(t){let r=d(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},94315:(e,t,r)=>{r.d(t,{jH:()=>o});var n=r(12115);r(95155);var l=n.createContext(void 0);function o(e){let t=n.useContext(l);return e||t||"ltr"}}}]);