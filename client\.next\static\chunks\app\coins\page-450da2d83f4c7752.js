(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[523],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(95155);s(12115);var a=s(6874),n=s.n(a),i=s(66766),l=s(29911);let o=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(n(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:l.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:l.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:l.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:l.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:l.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:l.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:l.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(n(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(n(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(n(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(n(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(95155);s(12115);var a=s(66634),n=s(74466),i=s(59434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:s,asChild:n=!1,...o}=e,c=n?a.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:s}),t),...o})}},47863:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(95155);s(12115);var a=s(59434);function n(e){let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>d});var r=s(95155);s(12115);var a=s(59434);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},66932:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68856:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(95155),a=s(59434);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",t),...s})}},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var r=s(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=r.createContext&&r.createContext(a),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(this,arguments)}function o(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,r)}return s}function c(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?o(Object(s),!0).forEach(function(t){var r,a,n;r=e,a=t,n=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in r?Object.defineProperty(r,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>r.createElement(u,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,s)=>r.createElement(t.tag,c({key:s},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var s,{attr:a,size:n,title:o}=e,d=function(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;s[r]=e[r]}return s}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)s=n[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}(e,i),u=n||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),r.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:s,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&r.createElement("title",null,o),e.children)};return void 0!==n?r.createElement(n.Consumer,null,e=>t(e)):t(a)}},76625:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(95155),a=s(12115),n=s(26126),i=s(30285),l=s(66695),o=s(7632),c=s(55077),d=s(34540),u=s(70347),m=s(7583),h=s(66766),x=s(68856),f=s(47863),p=s(66474),g=s(53904),v=s(66932),b=s(56671),j=s(62523),y=s(59434),w=s(55594),N=s(62177),k=s(90221),C=s(54165);let S=w.z.object({amount:w.z.string().min(1,"Amount is required").refine(e=>{let t=parseInt(e,10);return!isNaN(t)&&t>=1&&t<=1e4},{message:"Amount must be between ₹1 and ₹10,000"})}),O=w.z.object({amount:w.z.string().min(1,"Amount is required").refine(e=>{let t=parseInt(e,10);return!isNaN(t)&&t>=1&&t<=1e4},{message:"Amount must be between 1 and 10,000 coins"}),reason:w.z.string().optional()}),E=w.z.object({recipientContact:w.z.string().min(10,"Contact number must be 10 digits").max(10,"Contact number must be 10 digits").regex(/^\d+$/,"Contact number must contain only digits")}),A=()=>{let[e,t]=(0,a.useState)(0),[s,w]=(0,a.useState)([]),[A,P]=(0,a.useState)(!0),[z,_]=(0,a.useState)("all"),[T,D]=(0,a.useState)("desc"),{user:F}=(0,d.d4)(e=>e.user),[I,L]=(0,a.useState)(!1),[M,$]=(0,a.useState)(!1),[U,R]=(0,a.useState)(!1),[q,W]=(0,a.useState)(""),[Z,H]=(0,a.useState)(!1),[G,B]=(0,a.useState)(!1),{register:V,handleSubmit:Y,formState:{errors:K,isSubmitting:Q},reset:X}=(0,N.mN)({resolver:(0,k.u)(S)}),{register:J,handleSubmit:ee,formState:{errors:et},reset:es}=(0,N.mN)({resolver:(0,k.u)(E),defaultValues:{recipientContact:""}}),{register:er,handleSubmit:ea,formState:{errors:en},reset:ei}=(0,N.mN)({resolver:(0,k.u)(O),defaultValues:{amount:"",reason:""}}),el=async e=>{await ed(parseInt(e.amount))},eo=async e=>{H(!0);try{let t=await c.S.post("/coins/check-student",{contact:e.recipientContact});t.data.success&&t.data.exists?(R(!0),W(e.recipientContact),b.toast.success("Student found!")):(R(!1),b.toast.error(t.data.message||"Student not found."))}catch(e){var t,s;console.error("Error checking recipient:",e),b.toast.error("Failed to check student: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message))}finally{H(!1)}},ec=async e=>{B(!0);try{(await c.S.post("/coins/transfer-coins",{recipientContact:q,amount:parseInt(e.amount),reason:e.reason||"Coin transfer"})).data.success&&(b.toast.success("Coins transferred successfully!"),$(!1),ei(),es(),R(!1),eu())}catch(e){var t,s;console.error("Error transferring coins:",e),b.toast.error("Failed to transfer coins: ".concat((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message))}finally{B(!1)}},ed=async e=>{let t=null!==localStorage.getItem("studentToken"),s=(null==F?void 0:F.role)==="STUDENT"||t;L(!0);try{let{order:t}=(await c.S.post(s?"/coins/create-order":"/coins/create-order/class",{amount:100*e})).data,r={key:"rzp_test_Opr6M8CKpK12pF",amount:t.amount,currency:"INR",name:"Uest Coins",description:"Add Uest Coins",order_id:t.id,prefill:{name:(null==F?void 0:F.firstName)+" "+(null==F?void 0:F.lastName),email:null==F?void 0:F.email},handler:async function(t){try{await c.S.post(s?"/coins/verify":"/coins/verify/class",{razorpay_order_id:t.razorpay_order_id,razorpay_payment_id:t.razorpay_payment_id,razorpay_signature:t.razorpay_signature,amount:100*e}),b.toast.success("Coins added successfully!"),eu(),X()}catch(e){b.toast.error("Payment verification failed")}},theme:{color:"#f97316"}};new window.Razorpay(r).open()}catch(e){b.toast.error("Payment initialization failed")}finally{L(!1)}};(0,a.useEffect)(()=>{let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,document.body.appendChild(e)},[]);let eu=(0,a.useCallback)(async()=>{P(!0);try{let e=null!==localStorage.getItem("studentToken"),s=(null==F?void 0:F.role)==="STUDENT"||e,[r,a]=await Promise.all([c.S.get(s?"/coins/get-total-coins/student":"/coins/get-total-coins"),c.S.get(s?"/coins/transaction-history/student":"/coins/transaction-history")]);t(r.data.coins),w(a.data.transactions)}catch(e){console.error("Error fetching data:",e),b.toast.error("Failed to load coin data. Please try again.")}finally{P(!1)}},[null==F?void 0:F.role]);(0,a.useEffect)(()=>{eu()},[null==F?void 0:F.id,eu]),(0,a.useEffect)(()=>{let e=()=>eu();return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[eu]);let em=s.filter(e=>"all"===z||e.type.toLowerCase()===z).sort((e,t)=>{let s=new Date(e.createdAt).getTime(),r=new Date(t.createdAt).getTime();return"desc"===T?r-s:s-r}),eh=e=>{let{txn:t}=e,[s,n]=(0,a.useState)(!1);return(0,r.jsxs)(l.Zp,{className:"p-4 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200",onClick:()=>n(!s),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("h3",{className:"text-base font-semibold text-foreground",children:"CREDIT"===t.type?(0,r.jsx)("span",{className:"text-green-500",children:"Credit"}):(0,r.jsx)("span",{className:"text-red-500",children:"Debit"})}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[t.amount," Coins •"," ",(0,o.GP)(new Date(t.createdAt),"MMM dd, yyyy")]})]}),s?(0,r.jsx)(f.A,{className:"h-5 w-5 text-muted-foreground"}):(0,r.jsx)(p.A,{className:"h-5 w-5 text-muted-foreground"})]}),s&&(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t text-sm text-muted-foreground space-y-1 animate-in fade-in",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Reason:"})," ",t.reason]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Time:"})," ",(0,o.GP)(new Date(t.createdAt),"p")]})]})]})};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.default,{}),(0,r.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8 py-12 max-w-7xl mx-auto space-y-6",children:[(0,r.jsx)("div",{className:"sticky top-16 z-10 bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60 py-4",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"relative w-12 h-12 rounded-full bg-orange-100 p-2",children:(0,r.jsx)(h.default,{src:"/uest_coin.png",alt:"Coin Icon",fill:!0,className:"object-contain"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-foreground",children:[localStorage.getItem("studentToken")?"Student":"Class"," ","Uest Coin Balance"]}),(0,r.jsxs)(n.E,{variant:"outline",className:"text-customOrange text-xl font-semibold border-customOrange mt-1",children:[e," Coins"]})]})]}),(0,r.jsxs)(i.$,{variant:"outline",onClick:eu,disabled:A,className:"flex gap-2 w-full sm:w-auto",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 ".concat(A?"animate-spin":"")}),"Refresh"]})]})}),(0,r.jsx)("form",{onSubmit:Y(el),children:(0,r.jsxs)(l.Zp,{className:"p-6 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-foreground mb-4",children:"Add Uest Coins"}),(0,r.jsx)("div",{className:"grid gap-4 sm:grid-cols-2",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(j.p,{type:"number",placeholder:"Enter amount (₹1 - ₹10,000)",className:"w-full border-customOrange/30 focus:border-customOrange pr-10",disabled:I||Q,...V("amount")}),(0,r.jsx)("span",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm",children:"₹"}),K.amount&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:K.amount.message})]}),(0,r.jsxs)("div",{className:"flex justify-between gap-4",children:[(0,r.jsxs)(i.$,{type:"submit",disabled:I||Q,className:(0,y.cn)("w-full bg-customOrange hover:bg-orange-600 text-white",(I||Q)&&"opacity-75 cursor-not-allowed"),children:[(I||Q)&&(0,r.jsx)(g.A,{className:"h-4 w-4 animate-spin mr-2"}),I||Q?"Processing...":"Add Coins"]}),(0,r.jsx)(i.$,{type:"button",onClick:()=>{$(!0),ei(),es(),R(!1)},disabled:I||Q,className:(0,y.cn)("w-full bg-customOrange hover:bg-orange-600 text-white",(I||Q)&&"opacity-75 cursor-not-allowed"),children:"Transfer Coins"})]})]})})]})}),(0,r.jsx)(C.lG,{open:M,onOpenChange:e=>{$(e),e||(ei(),es(),R(!1))},children:(0,r.jsxs)(C.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(C.c7,{children:[(0,r.jsx)(C.L3,{children:"Transfer Coins"}),(0,r.jsx)(C.rr,{children:U?"Enter amount and reason to transfer coins.":"Enter the recipient's mobile number to transfer coins."})]}),U?(0,r.jsx)("form",{onSubmit:ea(ec),children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(j.p,{type:"number",placeholder:"Enter amount (1 - 10,000)",...er("amount"),className:"w-full"}),en.amount&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:en.amount.message})]}),(0,r.jsx)("div",{children:(0,r.jsx)(j.p,{type:"text",placeholder:"Reason (optional)",...er("reason"),className:"w-full"})}),(0,r.jsx)(i.$,{type:"submit",disabled:G,className:"w-full",children:G?"Transferring...":"Transfer"})]})}):(0,r.jsx)("form",{onSubmit:ee(eo),children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(j.p,{type:"text",placeholder:"Enter mobile number",...J("recipientContact"),className:"w-full"}),et.recipientContact&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:et.recipientContact.message})]}),(0,r.jsx)(i.$,{type:"submit",disabled:Z,className:"w-full",children:Z?"Checking...":"Check Student"})]})})]})}),A?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(x.E,{className:"h-20 w-full rounded-lg"}),[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(x.E,{className:"h-16 w-full rounded-lg"},t))]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-center",children:[(0,r.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,r.jsx)(i.$,{variant:"all"===z?"default":"outline",onClick:()=>_("all"),children:"All"}),(0,r.jsx)(i.$,{variant:"credit"===z?"default":"outline",onClick:()=>_("credit"),children:"Credits"}),(0,r.jsx)(i.$,{variant:"debit"===z?"default":"outline",onClick:()=>_("debit"),children:"Debits"})]}),(0,r.jsxs)(i.$,{variant:"outline",onClick:()=>D("desc"===T?"asc":"desc"),children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"desc"===T?"Newest First":"Oldest First"]})]}),em.length>0?(0,r.jsx)("div",{className:"grid gap-4",children:em.map(e=>(0,r.jsx)(eh,{txn:e},e.id))}):(0,r.jsx)("p",{className:"text-center text-muted-foreground",children:"No transactions found"})]})]}),(0,r.jsx)(m.default,{})]})}},92659:(e,t,s)=>{Promise.resolve().then(s.bind(s,76625))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,1342,7632,347,8441,1684,7358],()=>t(92659)),_N_E=e.O()}]);