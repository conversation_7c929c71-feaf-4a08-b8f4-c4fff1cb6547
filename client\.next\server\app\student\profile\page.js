(()=>{var e={};e.id=3612,e.ids=[3612],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>d,yv:()=>c});var a=r(60687);r(43210);var s=r(50039),l=r(78272),o=r(13964),n=r(3589),i=r(4780);function d({...e}){return(0,a.jsx)(s.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(s.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...o}){return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[r,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:r="popper",...l}){return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...l,children:[(0,a.jsx)(x,{}),(0,a.jsx)(s.LM,{className:(0,i.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(p,{})]})})}function h({className:e,children:t,...r}){return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(o.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}function p({className:e,...t}){return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}},15585:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(65239),s=r(48088),l=r(88170),o=r.n(l),n=r(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let d={children:["",{children:["student",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59006)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/student/profile/page",pathname:"/student/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},18333:(e,t,r)=>{Promise.resolve().then(r.bind(r,59006))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23562:(e,t,r)=>{"use strict";r.d(t,{k:()=>o});var a=r(60687);r(43210);var s=r(25177),l=r(4780);function o({className:e,value:t,...r}){return(0,a.jsx)(s.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...r,children:(0,a.jsx)(s.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},25177:(e,t,r)=>{"use strict";r.d(t,{C1:()=>y,bL:()=>v});var a=r(43210),s=r(11273),l=r(3416),o=r(60687),n="Progress",[i,d]=(0,s.A)(n),[c,u]=i(n),m=a.forwardRef((e,t)=>{var r,a;let{__scopeProgress:s,value:n=null,max:i,getValueLabel:d=p,...u}=e;(i||0===i)&&!b(i)&&console.error((r=`${i}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=b(i)?i:100;null===n||j(n,m)||console.error((a=`${n}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=j(n,m)?n:null,x=f(h)?d(h,m):void 0;return(0,o.jsx)(c,{scope:s,value:h,max:m,children:(0,o.jsx)(l.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":f(h)?h:void 0,"aria-valuetext":x,role:"progressbar","data-state":g(h,m),"data-value":h??void 0,"data-max":m,...u,ref:t})})});m.displayName=n;var h="ProgressIndicator",x=a.forwardRef((e,t)=>{let{__scopeProgress:r,...a}=e,s=u(h,r);return(0,o.jsx)(l.sG.div,{"data-state":g(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...a,ref:t})});function p(e,t){return`${Math.round(e/t*100)}%`}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function f(e){return"number"==typeof e}function b(e){return f(e)&&!isNaN(e)&&e>0}function j(e,t){return f(e)&&!isNaN(e)&&e<=t&&e>=0}x.displayName=h;var v=m,y=x},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var a=r(60687);r(43210);var s=r(4780);function l({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},35950:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>o});var a=r(60687);r(43210);var s=r(42123),l=r(4780);function o({className:e,orientation:t="horizontal",decorative:r=!0,...o}){return(0,a.jsx)(s.b,{"data-slot":"separator-root",decorative:r,orientation:t,className:(0,l.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...o})}},36893:(e,t,r)=>{Promise.resolve().then(r.bind(r,95409))},42123:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var a=r(43210);r(51215);var s=r(11329),l=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),o=a.forwardRef((e,a)=>{let{asChild:s,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?r:t,{...o,ref:a})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),n="horizontal",i=["horizontal","vertical"],d=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:s=n,...d}=e,c=(r=s,i.includes(r))?s:n;return(0,l.jsx)(o.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:t})});d.displayName="Separator";var c=d},51361:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59006:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\student\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx","default")},61170:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var a=r(43210);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var l=r(60687),o=Symbol("radix.slottable");function n(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...l}=e;if(a.isValidElement(r)){var o;let e,n;let i=(o=r,(n=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(n=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,t){let r={...t};for(let a in t){let s=e[a],l=t[a];/^on[A-Z]/.test(a)?s&&l?r[a]=(...e)=>{l(...e),s(...e)}:s&&(r[a]=s):"style"===a?r[a]={...s,...l}:"className"===a&&(r[a]=[s,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==a.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}(t,i):i),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...o}=e,i=a.Children.toArray(s),d=i.find(n);if(d){let e=d.props.children,s=i.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...o,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,l.jsx)(t,{...o,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),o=a.forwardRef((e,a)=>{let{asChild:s,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?r:t,{...o,ref:a})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),d=a.forwardRef((e,t)=>(0,l.jsx)(i.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66874:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>n,Zp:()=>l,aR:()=>o,wL:()=>c});var a=r(60687);r(43210);var s=r(4780);function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var a=r(60687);r(43210);var s=r(4780);function l({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")},95409:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>U});var a=r(60687),s=r(43210),l=r(63442),o=r(27605),n=r(45880),i=r(52581),d=r(16189),c=r(79663),u=r(40228),m=r(51361),h=r(13964),x=r(11860),p=r(62688);let g=(0,p.A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),f=(0,p.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var b=r(54864),j=r(74004),v=r(45201),y=r(80942),N=r(89667),w=r(29523),k=r(34729),C=r(15079),z=r(66874),P=r(40988),M=r(11095),A=r(4780),S=r(30474),R=r(90269),B=r(35950),E=r(23562),I=r(46303),D=r(5336);function q({items:e,activeSection:t,setActiveSection:r}){let{profileData:s}=(0,b.d4)(e=>e.studentProfile),l=e=>{if(!s?.profile)return!1;let t=s.profile;switch(e){case"Personal Info":return!!(t.student?.firstName&&t.student?.middleName&&t.student?.lastName&&t.student?.contact&&t.student?.email&&t.birthday&&t.school&&t.address&&t.medium&&t.classroom&&t.photo&&t.documentUrl);case"Other Info":return!!(t.aadhaarNo||t.bloodGroup||t.birthPlace||t.motherTongue||t.religion||t.caste||t.subCaste);default:return!1}};return(0,a.jsx)("nav",{className:"space-y-1",children:e.map((s,o)=>{let n=t===s.href.replace("#",""),i=l(s.title),d=o>0&&!l(e[o-1].title);return(0,a.jsxs)("button",{onClick:()=>!d&&r(s.href.replace("#","")),className:`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${n?"bg-muted text-primary":d?"text-gray-400 cursor-not-allowed":"text-muted-foreground hover:text-primary"}`,disabled:d,children:[(0,a.jsx)("span",{children:s.title}),i&&(0,a.jsx)(D.A,{size:16,className:"text-green-500"})]},s.href)})})}let T=n.z.object({firstName:n.z.string().min(2,"First name must be at least 2 characters."),middleName:n.z.string().min(2,"Middle name must be at least 2 characters."),lastName:n.z.string().min(2,"Last name must be at least 2 characters."),mothersName:n.z.string().optional(),email:n.z.string().email("Please enter a valid email address.").min(1,"Email is required"),contact:n.z.string().min(10,"Contact number must be at least 10 digits.").max(15,"Contact number must not exceed 15 digits.").regex(/^\d+$/,"Contact number must contain only digits."),contact2:n.z.string().min(10,"Contact number must be at least 10 digits.").max(15,"Contact number must not exceed 15 digits.").regex(/^\d+$/,"Contact number must contain only digits.").optional().or(n.z.literal("")),medium:n.z.string().min(1,"Medium of instruction is required"),classroom:n.z.string().min(1,"Standard is required"),gender:n.z.string().optional(),birthday:n.z.date({required_error:"Please select your date of birth"}),school:n.z.string().min(2,"School name must be at least 2 characters."),address:n.z.string().min(5,"Address must be at least 5 characters."),age:n.z.string().optional(),aadhaarNumber:n.z.string().min(12,"Aadhaar number must be 12 digits.").max(12,"Aadhaar number must be 12 digits.").regex(/^\d+$/,"Aadhaar number must contain only digits.").optional().or(n.z.literal("")),bloodGroup:n.z.string().optional(),birthPlace:n.z.string().optional(),motherTongue:n.z.string().optional(),religion:n.z.string().optional(),caste:n.z.string().optional(),subCaste:n.z.string().optional(),photo:n.z.any().optional(),document:n.z.any().optional()}),_=[{title:"Personal Info",href:"#personal-info"},{title:"Other Info",href:"#other-info"}],L=()=>{let e=(0,d.useRouter)(),t=(0,b.wA)(),r=(0,d.useSearchParams)(),n="true"===r.get("quiz"),p=r.get("examId"),[D,L]=(0,s.useState)("personal-info"),[U,$]=(0,s.useState)(null),[J,G]=(0,s.useState)(!1),[O,V]=(0,s.useState)(!1),[F,W]=(0,s.useState)(null),[H,Y]=(0,s.useState)(null),[Z,K]=(0,s.useState)(!1),[X,Q]=(0,s.useState)(0),[ee,et]=(0,s.useState)(null),er=(0,s.useRef)(null),ea=e=>{let t=new Date,r=t.getFullYear()-e.getFullYear(),a=t.getMonth()-e.getMonth();return(a<0||0===a&&t.getDate()<e.getDate())&&r--,r},{profileData:es,loading:el}=(0,b.d4)(e=>e.studentProfile),eo=es?.profile||null,en=es?.classroomOptions||[],ei=(0,s.useRef)(null),ed=(0,s.useRef)(null),ec=(0,o.mN)({resolver:(0,l.u)(T),defaultValues:{firstName:"",middleName:"",lastName:"",mothersName:"",email:"",contact:"",contact2:"",medium:"",classroom:"",gender:"",birthday:void 0,school:"",address:"",age:"",aadhaarNumber:"",bloodGroup:"",birthPlace:"",motherTongue:"",religion:"",caste:"",subCaste:""},mode:"onSubmit"});(0,s.useEffect)(()=>{localStorage.getItem("studentToken")||(i.toast.error("Please login to access your profile"),e.push("/"))},[e]),(0,s.useEffect)(()=>{localStorage.getItem("studentToken")&&t((0,j.N)())},[t]),(0,s.useEffect)(()=>{if(es?.profile){let e=es.profile,t=0;e.student?.firstName&&e.student?.lastName&&e.student?.contact&&e.student?.email&&e.birthday&&e.school&&e.address&&e.medium&&e.classroom&&e.photo&&e.documentUrl&&t++,(e.aadhaarNo||e.bloodGroup||e.birthPlace||e.motherTongue||e.religion||e.caste||e.subCaste)&&t++,Q(t/2*100)}},[es]),(0,s.useEffect)(()=>{if(!es)return;let e=es.profile,t=e?.student||JSON.parse(localStorage.getItem("student_data")||"{}");console.log("aaaaaaaaaaaaa",t),console.log("bbbbbbbbbbbb",e);let r={firstName:t?.firstName||"",middleName:t?.middleName||"",lastName:t?.lastName||"",mothersName:t?.mothersName||"",email:t?.email||"",contact:t?.contactNo||t?.contact||"",contact2:e?.contactNo2||"",medium:e?.medium||"",classroom:e?.classroom||"",gender:e?.gender||"",birthday:e?.birthday?new Date(e.birthday):void 0,school:e?.school||"",address:e?.address||"",age:e?.age?.toString()||"",aadhaarNumber:e?.aadhaarNo||"",bloodGroup:e?.bloodGroup||"",birthPlace:e?.birthPlace||"",motherTongue:e?.motherTongue||"",religion:e?.religion||"",caste:e?.caste||"",subCaste:e?.subCaste||""};if(e?.photo&&!U&&($(e.photo),ec.setValue("photo",e.photo)),e?.documentUrl&&!H&&!Z){let t=e.documentUrl.startsWith("http")?e.documentUrl:`http://localhost:4005/${e.documentUrl}`,r={name:t.split("/").pop()||"Uploaded Document",size:0,url:t,type:"application/octet-stream"};Y(r),ec.setValue("document",r)}let a=ec.getValues(),s=!a.firstName&&!a.lastName&&!a.contact,l=!a.medium||!a.classroom;(s||l)&&ec.reset(r)},[es,ec,U,H,Z]);let eu=e=>{if(e.size>5242880){i.toast.error("Photo size exceeds 5MB limit");return}et(e);let t=new FileReader;t.onload=e=>$(e.target?.result),t.readAsDataURL(e)},em=async()=>{W(null);try{if(!navigator.mediaDevices?.getUserMedia)throw Error("Camera not supported on this device");G(!0);let e=await navigator.mediaDevices.getUserMedia({video:{facingMode:"user"}});ei.current&&(ei.current.srcObject=e,ei.current.onloadedmetadata=()=>{ei.current?.play().catch(()=>i.toast.error("Error starting camera preview"))})}catch(t){G(!1);let e="NotAllowedError"===t.name?"Please allow camera access in your browser settings.":"Could not access camera. Please check your camera settings.";W(e),i.toast.error(e)}},eh=(e,t=800,r=.6)=>{if(!e.getContext("2d"))return"";let a=e.width,s=e.height,l=a,o=s;a>t&&(l=t,o=s*t/a);let n=document.createElement("canvas");n.width=l,n.height=o;let i=n.getContext("2d");return i?(i.drawImage(e,0,0,l,o),n.toDataURL("image/jpeg",r)):""},ex=()=>{if(!ei.current||!ed.current)return;let e=ei.current,r=ed.current,a=r.getContext("2d");r.width=e.videoWidth,r.height=e.videoHeight,a?.clearRect(0,0,r.width,r.height),a?.save(),a?.scale(-1,1),a?.drawImage(e,-r.width,0,r.width,r.height),a?.restore();let s=eh(r,800,.6);if(3*s.split(",")[1].length/4/1024>5120){i.toast.error("Photo size exceeds 5MB limit. Please try again.");return}$(s),ec.setValue("photo",s),t((0,v.XY)(s)),ep()},ep=()=>{ei.current?.srcObject&&(ei.current.srcObject.getTracks().forEach(e=>e.stop()),ei.current.srcObject=null),G(!1),W(null)},eg=()=>{H&&"url"in H&&H.url.startsWith("blob:")&&URL.revokeObjectURL(H.url),Y(null),K(!0);let e=document.getElementById("document");e&&(e.value=""),ec.setValue("document",null)},ef=e=>e<1024?e+" bytes":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",eb=async r=>{V(!0);try{if(!(U||es?.profile?.photo)){i.toast.error("Please capture a photo for your profile"),V(!1);return}if(!H||Z){i.toast.error("Identity document is required. Please upload a document."),V(!1);return}if(!await ec.trigger()){console.log("Form validation failed"),i.toast.error("Please fill in all required fields correctly"),V(!1);return}let a={firstName:r.firstName.charAt(0).toUpperCase()+r.firstName.slice(1).toLowerCase(),middleName:r.middleName.charAt(0).toUpperCase()+r.middleName.slice(1).toLowerCase(),lastName:r.lastName.charAt(0).toUpperCase()+r.lastName.slice(1).toLowerCase(),mothersName:r.mothersName,email:r.email,contact:r.contact,contact2:r.contact2,medium:r.medium,classroom:r.classroom,gender:r.gender,birthday:r.birthday?.toISOString()||"",school:r.school,address:r.address,age:r.age,aadhaarNumber:r.aadhaarNumber,bloodGroup:r.bloodGroup,birthPlace:r.birthPlace,motherTongue:r.motherTongue,religion:r.religion,caste:r.caste,subCaste:r.subCaste};if(U?.startsWith("data:")){let e=U.split(",")[1];if(3*e.length/4/1024>5120){i.toast.error("Photo size exceeds 5MB limit.");return}a.photo=e,a.photoMimeType="image/jpeg"}if(H instanceof File||H&&"url"in H&&H.url.startsWith("blob:")){let e=H instanceof File?H:await fetch(H.url).then(e=>e.blob()).then(e=>new File([e],H.name,{type:H.type})),t=await new Promise((t,r)=>{let a=new FileReader;a.onload=()=>t(a.result.split(",")[1]),a.onerror=r,a.readAsDataURL(e)});if(3*t.length/4/1024>5120){i.toast.error("Document size exceeds 5MB limit.");return}a.document=t,a.documentMimeType=e.type,a.documentName=e.name}if(Z&&es?.profile?.documentUrl&&(a.removeDocument=!0),!localStorage.getItem("studentToken")){i.toast.error("Please login to submit your profile"),e.push("/");return}let s=await t((0,j.A)(a));if("fulfilled"===s.meta.requestStatus){i.toast.success(eo?"Profile updated successfully!":"Profile created successfully!");let a=JSON.parse(localStorage.getItem("student_data")||"{}"),s={...a,id:a.id||es?.profile?.student?.id||"",firstName:r.firstName.charAt(0).toUpperCase()+r.firstName.slice(1).toLowerCase(),middleName:r.middleName.charAt(0).toUpperCase()+r.middleName.slice(1).toLowerCase(),lastName:r.lastName.charAt(0).toUpperCase()+r.lastName.slice(1).toLowerCase(),mothersName:r.mothersName,email:r.email||a.email||es?.profile?.student?.email||"",contact:r.contact};localStorage.setItem("student_data",JSON.stringify(s)),K(!1),await t((0,j.N)()),n&&(p?e.push(`/uwhiz-exam/${p}`):e.push("/mock-test"))}else if("rejected"===s.meta.requestStatus){let t=s.payload;t.includes("401")||t.includes("Unauthorized")?(i.toast.error("Your session has expired. Please login again."),localStorage.removeItem("studentToken"),e.push("/")):i.toast.error(t||"Failed to update profile")}}catch{i.toast.error("Failed to submit profile information")}finally{V(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(R.default,{}),(0,a.jsxs)("div",{className:"space-y-6 p-10 pb-4 md:block",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Student Profile"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Complete your profile information. Your progress will be automatically saved as you complete each section."})]}),(0,a.jsx)(E.k,{value:X,className:"h-2"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[Math.round(X),"% complete"]}),(0,a.jsx)(B.Separator,{className:"my-6"}),(0,a.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[(0,a.jsx)("aside",{className:"-mx-4 lg:w-1/6 pb-12",children:(0,a.jsx)(q,{items:_,activeSection:D,setActiveSection:L})}),(0,a.jsx)("div",{className:"flex justify-center w-full",children:(0,a.jsx)("div",{className:"flex-1 lg:max-w-2xl pb-12",children:el?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,a.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading profile information..."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium",children:["personal-info"===D&&"Personal Information","other-info"===D&&"Other Information"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["personal-info"===D&&"Enter your basic personal details, contact information, medium, standard, photo and documents","other-info"===D&&"Provide additional details like Aadhaar number, blood group, birth place, etc."]})]}),(0,a.jsx)(B.Separator,{}),(0,a.jsx)(y.lV,{...ec,children:(0,a.jsxs)("form",{onSubmit:ec.handleSubmit(eb),className:"space-y-8",children:["personal-info"===D&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(y.zB,{control:ec.control,name:"firstName",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"First Name *"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter First Name"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"middleName",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Middle Name *"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Middle Name"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"lastName",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Last Name *"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Last Name"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"mothersName",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Mothers Name"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Mother's Name"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})})]}),(0,a.jsx)(y.zB,{control:ec.control,name:"email",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Email *"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Email",type:"email"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(y.zB,{control:ec.control,name:"contact",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Contact Number *"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"8520369851",type:"tel",inputMode:"numeric",pattern:"[0-9]*",onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:t=>{let r=t.target.value.replace(/\D/g,"");e.onChange(r)}})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"contact2",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Contact Number 2"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Alternate Number",type:"tel",inputMode:"numeric",pattern:"[0-9]*",onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:t=>{let r=t.target.value.replace(/\D/g,"");e.onChange(r)}})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(y.zB,{control:ec.control,name:"gender",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Gender"}),(0,a.jsxs)(C.l6,{onValueChange:e.onChange,value:e.value||void 0,children:[(0,a.jsx)(y.MJ,{children:(0,a.jsx)(C.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,a.jsx)(C.yv,{placeholder:"Select"})})}),(0,a.jsxs)(C.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,a.jsx)(C.eb,{value:"male",children:"Male"}),(0,a.jsx)(C.eb,{value:"female",children:"Female"}),(0,a.jsx)(C.eb,{value:"other",children:"Other"})]})]}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"age",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Age"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Age",type:"number",min:"1",max:"100"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"birthday",render:({field:e})=>(0,a.jsxs)(y.eI,{className:"flex flex-col",children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Date of Birth *"}),(0,a.jsxs)(P.AM,{children:[(0,a.jsx)(P.Wv,{asChild:!0,children:(0,a.jsx)(y.MJ,{children:(0,a.jsxs)(w.$,{variant:"outline",className:(0,A.cn)("w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg",!e.value&&"text-muted-foreground"),children:[e.value&&e.value instanceof Date&&!isNaN(e.value.getTime())?(0,c.GP)(e.value,"PPP"):(0,a.jsx)("span",{children:"Select your birthday"}),(0,a.jsx)(u.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,a.jsxs)(P.hl,{className:"w-auto p-0 bg-white border border-gray-300 shadow-lg",align:"start",children:[(0,a.jsx)("div",{className:"p-3 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,a.jsxs)(C.l6,{value:e.value?e.value.getFullYear().toString():"",onValueChange:t=>{let r=new Date(e.value||new Date);r.setFullYear(parseInt(t)),e.onChange(r)},children:[(0,a.jsx)(C.bq,{className:"w-24",children:(0,a.jsx)(C.yv,{placeholder:"Year"})}),(0,a.jsx)(C.gC,{className:"max-h-48",children:Array.from({length:125},(e,t)=>{let r=new Date().getFullYear()-t;return(0,a.jsx)(C.eb,{value:r.toString(),children:r},r)})})]}),(0,a.jsxs)(C.l6,{value:e.value?e.value.getMonth().toString():"",onValueChange:t=>{let r=new Date(e.value||new Date);r.setMonth(parseInt(t)),e.onChange(r)},children:[(0,a.jsx)(C.bq,{className:"w-32",children:(0,a.jsx)(C.yv,{placeholder:"Month"})}),(0,a.jsx)(C.gC,{children:["January","February","March","April","May","June","July","August","September","October","November","December"].map((e,t)=>(0,a.jsx)(C.eb,{value:t.toString(),children:e},t))})]})]})}),(0,a.jsx)(M.V,{mode:"single",selected:e.value,onSelect:t=>{if(e.onChange(t),t){let e=ea(t);ec.setValue("age",e.toString())}},disabled:e=>e>new Date||e<new Date("1900-01-01"),month:e.value||new Date,className:"rounded-md border-0"})]})]}),(0,a.jsx)(y.Rr,{className:"text-xs text-gray-500",children:"Your date of birth will be verified with your documents"}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})})]}),(0,a.jsx)(y.zB,{control:ec.control,name:"address",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Address *"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(k.T,{...e,rows:3,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none",placeholder:"Enter your full address"})}),(0,a.jsx)(y.Rr,{className:"text-xs text-gray-500",children:"Provide your complete residential address"}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"school",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"School Name *"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter School"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(y.zB,{control:ec.control,name:"medium",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Medium *"}),(0,a.jsxs)(C.l6,{onValueChange:t=>{e.onChange(t)},value:e.value||void 0,children:[(0,a.jsx)(y.MJ,{children:(0,a.jsx)(C.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,a.jsx)(C.yv,{placeholder:"Select"})})}),(0,a.jsxs)(C.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,a.jsx)(C.eb,{value:"english",children:"English"}),(0,a.jsx)(C.eb,{value:"gujarati",children:"Gujarati"})]})]}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"classroom",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Standard *"}),(0,a.jsxs)(C.l6,{onValueChange:t=>{e.onChange(t)},value:e.value||void 0,children:[(0,a.jsx)(y.MJ,{children:(0,a.jsx)(C.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,a.jsx)(C.yv,{placeholder:"Select"})})}),(0,a.jsx)(C.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:el?(0,a.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,a.jsxs)("svg",{className:"animate-spin h-5 w-5 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}):en.length>0?en.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.value},e.id)):(0,a.jsx)("div",{className:"p-2 text-center text-gray-500",children:"No classroom options available"})})]}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})})]}),(0,a.jsx)(y.zB,{control:ec.control,name:"photo",render:()=>(0,a.jsxs)(z.Zp,{className:"shadow-lg border-0",children:[(0,a.jsxs)(z.aR,{className:"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",children:[(0,a.jsx)(z.ZB,{className:"text-lg font-medium text-gray-800",children:"Student Image *"}),(0,a.jsx)(z.BT,{className:"text-gray-600",children:"Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)"})]}),(0,a.jsxs)(z.Wu,{children:[(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.MJ,{children:(0,a.jsxs)("div",{children:[F&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-700 text-sm",children:F})}),!J&&!U&&(0,a.jsxs)(w.$,{type:"button",onClick:em,className:"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Open Camera"]}),J&&(0,a.jsxs)("div",{className:"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm",children:[(0,a.jsx)("video",{ref:ei,autoPlay:!0,playsInline:!0,className:"w-full h-auto transform scale-x-[-1]"}),(0,a.jsxs)("div",{className:"flex p-4 bg-gray-50",children:[(0,a.jsxs)(w.$,{type:"button",onClick:ex,variant:"default",className:"flex-1 mr-2 bg-black hover:bg-gray-800 text-white",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Capture"]}),(0,a.jsxs)(w.$,{type:"button",onClick:ep,variant:"outline",className:"flex-1 border-gray-300",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Cancel"]})]})]}),!J&&(es?.profile?.photo||U)&&(0,a.jsx)("div",{className:"flex flex-col sm:flex-row items-center gap-4",children:(0,a.jsx)("div",{className:"border rounded-lg shadow-md bg-gray-50 p-4 max-w-full",children:(0,a.jsx)("div",{className:"flex justify-center",children:(()=>{let e=U||es?.profile?.photo;return e?(0,a.jsx)(S.default,{src:e.startsWith("data:")?e:e.startsWith("http")?e:`http://localhost:4005/${e}?t=${new Date().getTime()}`,alt:"Student Photo",height:1e3,width:1e3,className:"max-w-full max-h-80 object-contain rounded-lg",style:{height:"auto",width:"auto"},unoptimized:e.startsWith("data:")}):(0,a.jsx)("div",{className:"flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg",children:(0,a.jsx)(m.A,{className:"h-12 w-12 text-gray-400"})})})()})})}),(0,a.jsx)("canvas",{ref:ed,style:{display:"none"}})]})}),(0,a.jsx)(y.Rr,{className:"text-xs text-gray-500 mt-2",children:"A clear photo helps us identify you and personalize your profile"}),(0,a.jsx)(y.C5,{className:"text-red-500"})]}),(0,a.jsxs)("div",{className:"flex justify-center gap-10",children:[(0,a.jsx)(N.p,{type:"file",accept:".jpg,.jpeg,.png",ref:er,className:"hidden",onChange:e=>{let t=e.target.files?.[0];t&&eu(t)}}),(0,a.jsx)(w.$,{type:"button",variant:"outline",size:"sm",onClick:()=>er.current?.click(),className:"border-gray-300 p-4",children:ee||U?"Change Photo":"Upload Photo"}),(0,a.jsxs)(w.$,{type:"button",onClick:()=>{$(null),W(null),t((0,v.XY)(void 0)),ec.setValue("photo",null),em()},variant:"outline",className:"border-gray-300",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Retake Photo"]})]})]})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"document",render:({field:e})=>(0,a.jsxs)(z.Zp,{className:"shadow-lg border-0",children:[(0,a.jsxs)(z.aR,{className:"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",children:[(0,a.jsx)(z.ZB,{className:"text-lg font-medium text-gray-800",children:"Identity Document *"}),(0,a.jsx)(z.BT,{className:"text-gray-600",children:"Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate"})]}),(0,a.jsx)(z.Wu,{children:(0,a.jsxs)(y.eI,{children:[H?(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-[#fff8f3] rounded-full",children:(0,a.jsx)(f,{className:"h-5 w-5 text-black"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:H.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:H instanceof File?ef(H.size):"Previously uploaded document"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[H&&"url"in H&&(0,a.jsx)(w.$,{type:"button",variant:"outline",size:"sm",onClick:()=>window.open(H.url,"_blank"),className:"h-8 px-3 border-gray-200",children:"View"}),(0,a.jsx)(w.$,{type:"button",variant:"outline",size:"sm",onClick:eg,className:"h-8 w-8 p-0 border-gray-200",children:(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-500"})})]})]})}):(0,a.jsx)(y.MJ,{children:(0,a.jsx)("div",{className:"flex items-center justify-center w-full",children:(0,a.jsxs)("label",{className:"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,a.jsx)(g,{className:"w-10 h-10 mb-3 text-black"}),(0,a.jsxs)("p",{className:"mb-2 text-sm text-gray-700",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Click to upload"})," or drag and drop"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"PDF, PNG, JPG or JPEG (MAX. 5MB)"})]}),(0,a.jsx)(N.p,{id:"document",type:"file",accept:".pdf,.jpg,.jpeg,.png",className:"hidden",onChange:t=>{let r=t.target.files?.[0];if(r){if(r.size>5242880){i.toast.error("File size exceeds 5MB limit");return}Y({name:r.name,size:r.size,type:r.type,url:URL.createObjectURL(r)}),K(!1),e.onChange(r)}}})]})})}),(0,a.jsx)(y.Rr,{className:"text-xs text-gray-500 mt-2",children:"This document will serve to verify your identity and date of birth."}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})})]})})]}),"other-info"===D&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(y.zB,{control:ec.control,name:"aadhaarNumber",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Aadhaar Number"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Aadhaar No",type:"tel",inputMode:"numeric",pattern:"[0-9]*",maxLength:12,onKeyDown:e=>{!(["Backspace","Tab","Enter","Escape","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key)||e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase()))&&(/^\d$/.test(e.key)||e.preventDefault())},onChange:t=>{let r=t.target.value.replace(/\D/g,"");e.onChange(r)}})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"bloodGroup",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Blood Group"}),(0,a.jsxs)(C.l6,{onValueChange:e.onChange,value:e.value||void 0,children:[(0,a.jsx)(y.MJ,{children:(0,a.jsx)(C.bq,{className:"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",children:(0,a.jsx)(C.yv,{placeholder:"Select"})})}),(0,a.jsxs)(C.gC,{className:"bg-white w-[var(--radix-select-trigger-width)]",children:[(0,a.jsx)(C.eb,{value:"A+",children:"A+"}),(0,a.jsx)(C.eb,{value:"A-",children:"A-"}),(0,a.jsx)(C.eb,{value:"B+",children:"B+"}),(0,a.jsx)(C.eb,{value:"B-",children:"B-"}),(0,a.jsx)(C.eb,{value:"AB+",children:"AB+"}),(0,a.jsx)(C.eb,{value:"AB-",children:"AB-"}),(0,a.jsx)(C.eb,{value:"O+",children:"O+"}),(0,a.jsx)(C.eb,{value:"O-",children:"O-"})]})]}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"birthPlace",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Birth Place"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Birth Place"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"motherTongue",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Mother Tongue"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Mother Tongue"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"religion",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Religion"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Religion"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"caste",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Caste"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Caste"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})}),(0,a.jsx)(y.zB,{control:ec.control,name:"subCaste",render:({field:e})=>(0,a.jsxs)(y.eI,{children:[(0,a.jsx)(y.lR,{className:"text-black font-medium",children:"Sub Caste"}),(0,a.jsx)(y.MJ,{children:(0,a.jsx)(N.p,{...e,className:"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",placeholder:"Enter Sub Caste"})}),(0,a.jsx)(y.C5,{className:"text-red-500"})]})})]})}),(0,a.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-100",children:(0,a.jsx)(w.$,{type:"submit",disabled:O,className:"bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200",children:O?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin h-4 w-4",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):es?"Update Profile":"Save Profile"})})]})})]})})})]})]}),(0,a.jsx)(I.default,{})]})},U=()=>(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("svg",{className:"animate-spin h-10 w-10 text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),children:(0,a.jsx)(L,{})})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7013,2105,9663,3099,2800,2489],()=>r(15585));module.exports=a})();