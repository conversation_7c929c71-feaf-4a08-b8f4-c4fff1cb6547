(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6915],{14994:(e,t,a)=>{Promise.resolve().then(a.bind(a,94347))},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,r:()=>n});var r=a(95155);a(12115);var s=a(66634),o=a(74466),l=a(59434);let n=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:o,asChild:i=!1,...d}=e,c=i?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:a,size:o,className:t})),...d})}},36754:(e,t,a)=>{"use strict";a.d(t,{$5:()=>n,BU:()=>s,c5:()=>i,cc:()=>d,dZ:()=>l,sq:()=>o});var r=a(55077);let s=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{return(await r.S.get("/blogs/approved",{params:{page:e,limit:t}})).data}catch(e){var a,s;throw Error((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to fetch approved blogs: ".concat(e.message))}},o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0;try{return(await r.S.get("/blogs/my-blogs",{params:{page:e,limit:t,status:a}})).data}catch(e){var s,o;throw Error((null===(o=e.response)||void 0===o?void 0:null===(s=o.data)||void 0===s?void 0:s.message)||"Failed to fetch your blogs: ".concat(e.message))}},l=async e=>{try{return(await r.S.get("/blogs/".concat(e))).data.data}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to fetch blog: ".concat(e.message))}},n=async e=>{try{let t=new FormData;return t.append("blogTitle",e.blogTitle),t.append("blogDescription",e.blogDescription),e.blogImage&&t.append("blogImage",e.blogImage),(await r.S.post("/blogs",t,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to create blog: ".concat(e.message))}},i=async(e,t)=>{try{let a=new FormData;return t.blogTitle&&a.append("blogTitle",t.blogTitle),t.blogDescription&&a.append("blogDescription",t.blogDescription),t.blogImage&&a.append("blogImage",t.blogImage),t.status&&a.append("status",t.status),(await r.S.put("/blogs/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var a,s;throw Error((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to update blog: ".concat(e.message))}},d=async e=>{try{await r.S.delete("/blogs/".concat(e))}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to delete blog: ".concat(e.message))}}},55077:(e,t,a)=>{"use strict";a.d(t,{S:()=>l});var r=a(23464),s=a(56671);let o="http://localhost:4005/api/v1";console.log("Axios baseURL:",o);let l=r.A.create({baseURL:o,headers:{"Content-Type":"application/json"},withCredentials:!0});l.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":o;{let t=localStorage.getItem("studentToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(s.toast.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},55365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>i,TN:()=>d});var r=a(95155),s=a(12115),o=a(74466),l=a(59434);let n=(0,o.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=s.forwardRef((e,t)=>{let{className:a,variant:s,...o}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,l.cn)(n({variant:s}),a),...o})});i.displayName="Alert",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",a),...s})}).displayName="AlertTitle";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",a),...s})});d.displayName="AlertDescription"},59434:(e,t,a)=>{"use strict";a.d(t,{MB:()=>n,ZO:()=>l,cn:()=>o,wR:()=>d,xh:()=>i});var r=a(52596),s=a(39688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}let l=()=>localStorage.getItem("studentToken"),n=()=>{localStorage.removeItem("studentToken")},i=()=>!!l(),d=()=>{if(l())return{isAuth:!0,userType:"STUDENT"};{let e=localStorage.getItem("user");if(e)try{let t=JSON.parse(e);if(t&&t.id)return{isAuth:!0,userType:"CLASS"}}catch(e){console.error("Error parsing user data:",e)}}return{isAuth:!1,userType:null}}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>o});var r=a(95155);a(12115);var s=a(59434);function o(e){let{className:t,type:a,...o}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>n,Zp:()=>o,aR:()=>l,wL:()=>c});var r=a(95155);a(12115);var s=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},75937:(e,t,a)=>{"use strict";a.d(t,{lV:()=>c,MJ:()=>h,Rr:()=>x,zB:()=>g,eI:()=>b,lR:()=>v,C5:()=>f});var r=a(95155),s=a(12115),o=a(66634),l=a(62177),n=a(59434),i=a(24265);function d(e){let{className:t,...a}=e;return(0,r.jsx)(i.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}let c=l.Op,u=s.createContext({}),g=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(l.xI,{...t})})},m=()=>{let e=s.useContext(u),t=s.useContext(p),{getFieldState:a}=(0,l.xW)(),r=(0,l.lN)({name:e.name}),o=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...o}},p=s.createContext({});function b(e){let{className:t,...a}=e,o=s.useId();return(0,r.jsx)(p.Provider,{value:{id:o},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,n.cn)("grid gap-2",t),...a})})}function v(e){let{className:t,...a}=e,{error:s,formItemId:o}=m();return(0,r.jsx)(d,{"data-slot":"form-label","data-error":!!s,className:(0,n.cn)("data-[error=true]:text-destructive",t),htmlFor:o,...a})}function h(e){let{...t}=e,{error:a,formItemId:s,formDescriptionId:l,formMessageId:n}=m();return(0,r.jsx)(o.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(l," ").concat(n):"".concat(l),"aria-invalid":!!a,...t})}function x(e){let{className:t,...a}=e,{formDescriptionId:s}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function f(e){var t;let{className:a,...s}=e,{error:o,formMessageId:l}=m(),i=o?String(null!==(t=null==o?void 0:o.message)&&void 0!==t?t:""):s.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:l,className:(0,n.cn)("text-destructive text-sm",a),...s,children:i}):null}},94347:(e,t,a)=>{"use strict";a.d(t,{default:()=>I});var r=a(95155),s=a(12115),o=a(66766),l=a(34540),n=a(35695),i=a(55028),d=a(56671),c=a(30285),u=a(66695),g=a(62523),m=a(85339),p=a(51154),b=a(36754),v=a(62177),h=a(90221),x=a(55594),f=a(55365),j=a(75937);a(47703);let y=(0,i.default)(()=>a.e(5760).then(a.bind(a,35760)),{loadableGenerated:{webpack:()=>[35760]},ssr:!1}),w=x.z.object({blogTitle:x.z.string().min(3,"Blog title must be at least 3 characters"),blogDescription:x.z.string().min(10,"Blog description must be at least 10 characters"),blogImage:x.z.custom(e=>e instanceof File||!!e,{message:"Blog image is required"})}),N=e=>{let{message:t}=e;return t?(0,r.jsxs)(f.Fc,{className:"mb-4 border-red-500 bg-red-50 dark:bg-red-900/20",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)(f.TN,{className:"text-red-500",children:t})]}):null},I=()=>{let[e,t]=(0,s.useState)(null),[a,i]=(0,s.useState)(!1),[m,x]=(0,s.useState)(""),{user:f}=(0,l.d4)(e=>e.user),I=(0,n.useRouter)(),S=(0,n.useSearchParams)().get("id"),k=!!S,T=(0,v.mN)({resolver:(0,h.u)(w),defaultValues:{blogTitle:"",blogDescription:"",blogImage:void 0},mode:"onChange"}),C=(0,s.useCallback)(async e=>{try{i(!0);let a=await (0,b.dZ)(e);if(T.setValue("blogTitle",a.blogTitle),T.setValue("blogDescription",a.blogDescription),a.blogImage){let e=a.blogImage.replace(/^\/+/,""),r="".concat("http://localhost:4005/").concat(e);t(r),T.setValue("blogImage",new File([],"existing-image.jpg"))}}catch(e){d.toast.error(e.message||"Failed to fetch blog"),I.push("/classes/blogs")}finally{i(!1)}},[I,T]);(0,s.useEffect)(()=>{if(!f){I.push("/");return}k&&S&&C(S)},[f,I,k,S,C]);let F=e=>{if(e.target.files&&e.target.files[0]){let a=e.target.files[0];if(!["image/jpeg","image/jpg","image/png"].includes(a.type)){d.toast.error("Only image files (.jpg, .jpeg, .png) are allowed"),e.target.value="";return}T.setValue("blogImage",a);let r=new FileReader;r.onload=()=>{t(r.result)},r.readAsDataURL(a)}},D=async e=>{x("");try{k&&S?(await (0,b.c5)(S,{blogTitle:e.blogTitle,blogDescription:e.blogDescription,blogImage:e.blogImage}),d.toast.success("Blog updated successfully")):(await (0,b.$5)({blogTitle:e.blogTitle,blogDescription:e.blogDescription,blogImage:e.blogImage}),d.toast.success("Blog created successfully")),I.push("/classes/blogs")}catch(e){x(e.message||"Failed to save blog")}};return a?(0,r.jsx)("div",{className:"container mx-auto py-6 px-4 flex justify-center items-center h-64",children:(0,r.jsx)(p.A,{className:"h-8 w-8 animate-spin text-primary"})}):(0,r.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,r.jsxs)(u.Zp,{className:"max-w-3xl mx-auto",children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:k?"Edit Blog":"Create New Blog"})}),(0,r.jsxs)(u.Wu,{children:[m&&(0,r.jsx)(N,{message:m}),(0,r.jsx)(j.lV,{...T,children:(0,r.jsxs)("form",{onSubmit:T.handleSubmit(D),className:"space-y-6",children:[(0,r.jsx)(j.zB,{control:T.control,name:"blogTitle",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{children:"Title"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)(g.p,{placeholder:"Enter blog title",className:"w-full",...t})}),(0,r.jsx)(j.C5,{})]})}}),(0,r.jsx)(j.zB,{control:T.control,name:"blogImage",render:t=>{let{}=t;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{children:"Image"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)(g.p,{id:"image",type:"file",accept:".jpg,.jpeg,.png",onChange:F,className:"w-full"})}),e&&(0,r.jsx)("div",{className:"mt-4 flex justify-center items-center h-60 w-full border rounded-md p-2 overflow-hidden",children:(0,r.jsx)(o.default,{src:e,alt:"Blog Preview",width:500,height:500,className:"object-contain rounded-md",style:{maxHeight:"100%",maxWidth:"100%",display:"block",margin:"auto"}})}),(0,r.jsx)(j.C5,{})]})}}),(0,r.jsx)(j.zB,{control:T.control,name:"blogDescription",render:e=>{let{field:t}=e;return(0,r.jsxs)(j.eI,{children:[(0,r.jsx)(j.lR,{children:"Description"}),(0,r.jsx)(j.MJ,{children:(0,r.jsx)("div",{className:"h-80",children:(0,r.jsx)(y,{theme:"snow",value:t.value,onChange:t.onChange,className:"h-full",modules:{toolbar:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{align:[]}],["link","image"],["clean"]]}})})}),(0,r.jsx)(j.C5,{})]})}}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-20",children:[(0,r.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>I.push("/classes/blogs"),children:"Cancel"}),(0,r.jsx)(c.$,{type:"submit",disabled:T.formState.isSubmitting,children:T.formState.isSubmitting?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),k?"Updating...":"Creating..."]}):(0,r.jsx)(r.Fragment,{children:k?"Update":"Create"})})]})]})})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9598,7040,5186,4540,4212,1342,837,8441,1684,7358],()=>t(14994)),_N_E=e.O()}]);