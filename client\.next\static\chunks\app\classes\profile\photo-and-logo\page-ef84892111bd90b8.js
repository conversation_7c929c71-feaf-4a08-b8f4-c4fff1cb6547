(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7624],{9604:(e,t,r)=>{Promise.resolve().then(r.bind(r,35335)),Promise.resolve().then(r.bind(r,22346))},22346:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>i});var o=r(95155);r(12115);var a=r(14050),s=r(59434);function i(e){let{className:t,orientation:r="horizontal",decorative:i=!0,...l}=e;return(0,o.jsx)(a.b,{"data-slot":"separator-root",decorative:i,orientation:r,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...l})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>n,r:()=>l});var o=r(95155);r(12115);var a=r(66634),s=r(74466),i=r(59434);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function n(e){let{className:t,variant:r,size:s,asChild:n=!1,...d}=e,c=n?a.DX:"button";return(0,o.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:s,className:t})),...d})}},35335:(e,t,r)=>{"use strict";r.d(t,{default:()=>y});var o=r(95155),a=r(90221),s=r(62177),i=r(55594),l=r(66766),n=r(12115),d=r(34540),c=r(94314),u=r(35695),p=r(56671),m=r(3159),g=r(75937),h=r(30285),v=r(62523),f=r(55077),x=r(45436);let b=["image/jpeg","image/png","image/gif","image/webp"],w=i.z.object({photo:i.z.instanceof(File).refine(e=>(null==e?void 0:e.size)<2097152,{message:"Photo must be less than 2MB"}).refine(e=>!e||b.includes(e.type),{message:"Only image files (JPEG, PNG, GIF, WEBP) are allowed"}).optional(),logo:i.z.instanceof(File).refine(e=>(null==e?void 0:e.size)<1048576,{message:"Logo must be less than 1MB"}).refine(e=>!e||b.includes(e.type),{message:"Only image files (JPEG, PNG, GIF, WEBP) are allowed"}).optional()});function y(){let[e,t]=(0,n.useState)(null),[r,i]=(0,n.useState)(null),[y,j]=(0,n.useState)(null),[S,C]=(0,n.useState)(null),[P,N]=(0,n.useState)(null),[I,E]=(0,n.useState)(null),[k,F]=(0,n.useState)({x:0,y:0}),[z,A]=(0,n.useState)(1),[O,T]=(0,n.useState)(null),_=(0,s.mN)({resolver:(0,a.u)(w),mode:"onChange",defaultValues:{photo:void 0,logo:void 0}}),R=(0,d.wA)(),L=(0,u.useRouter)(),D=(e,t)=>{var r;let o=null===(r=e.target.files)||void 0===r?void 0:r[0];if(o){if(!b.includes(o.type)){p.toast.error("Only image files (JPEG, PNG, GIF, WEBP) are allowed"),e.target.value="";return}let r=new FileReader;r.onload=()=>{E(r.result),N(t)},r.readAsDataURL(o)}},B=async(e,t)=>{let r=new window.Image;r.src=e,await new Promise(e=>r.onload=e);let o=document.createElement("canvas"),a=o.getContext("2d");return o.width=t.width,o.height=t.height,a.drawImage(r,t.x,t.y,t.width,t.height,0,0,t.width,t.height),new Promise(e=>{o.toBlob(t=>{t&&e(new File([t],"cropped-image.jpg",{type:"image/jpeg"}))},"image/jpeg")})},G=async()=>{if(I&&O)try{let e=await B(I,O),r=URL.createObjectURL(e);"photo"===P?(_.setValue("photo",e,{shouldValidate:!0}),t(r)):"logo"===P&&(_.setValue("logo",e,{shouldValidate:!0}),i(r)),N(null),E(null),F({x:0,y:0}),A(1)}catch(e){console.error("Error cropping image:",e),p.toast.error("Failed to crop image")}},{user:U}=(0,d.d4)(e=>e.user),V=async e=>{try{let t=new FormData;e.photo&&t.append("profilePhoto",e.photo),e.logo&&t.append("classesLogo",e.logo),await f.S.post("/classes-profile/images",t,{headers:{"Content-Type":"multipart/form-data"}}),await R((0,x.V)(U.id)),p.toast.success("Photos uploaded successfully!"),R((0,c.ac)(c._3.PHOTO_LOGO)),L.push("/classes/profile/education")}catch(e){console.error("Error uploading files:",e),p.toast.error("Failed to upload files")}},M=(0,d.d4)(e=>e.class.classData);return(0,n.useEffect)(()=>{if(M||(null==M?void 0:M.ClassAbout)){var e,t,r,o;(null==M?void 0:null===(e=M.ClassAbout)||void 0===e?void 0:e.profilePhoto)&&j("http://localhost:4005/"+(null==M?void 0:null===(r=M.ClassAbout)||void 0===r?void 0:r.profilePhoto)),(null==M?void 0:null===(t=M.ClassAbout)||void 0===t?void 0:t.classesLogo)&&C("http://localhost:4005/"+(null==M?void 0:null===(o=M.ClassAbout)||void 0===o?void 0:o.classesLogo))}},[M]),(0,o.jsxs)("div",{children:[(0,o.jsx)(g.lV,{..._,children:(0,o.jsxs)("form",{onSubmit:_.handleSubmit(V),className:"space-y-6",children:[(0,o.jsx)(g.zB,{control:_.control,name:"photo",render:()=>(0,o.jsxs)(g.eI,{children:[(0,o.jsx)(g.lR,{children:"Profile Photo"}),(0,o.jsx)(g.MJ,{children:(0,o.jsx)(v.p,{type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>D(e,"photo")})}),e||y?(0,o.jsx)(l.default,{src:e||y,alt:"Profile Preview",width:120,height:120,className:"rounded-full mt-2 border"}):null,(0,o.jsx)(g.C5,{})]})}),(0,o.jsx)(g.zB,{control:_.control,name:"logo",render:()=>(0,o.jsxs)(g.eI,{children:[(0,o.jsx)(g.lR,{children:"Classes Logo"}),(0,o.jsx)(g.MJ,{children:(0,o.jsx)(v.p,{type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>D(e,"logo")})}),r||S?(0,o.jsx)(l.default,{src:r||S,alt:"Logo Preview",width:120,height:120,className:"rounded-md mt-2 border bg-white"}):null,(0,o.jsx)(g.C5,{})]})}),(0,o.jsx)(h.$,{type:"submit",children:"Upload"})]})}),P&&I&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg w-[90%] max-w-[500px]",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Crop Image"}),(0,o.jsx)("div",{className:"relative w-full h-[300px]",children:(0,o.jsx)(m.Ay,{image:I,crop:k,zoom:z,aspect:"photo"===P?1:4/3,onCropChange:F,onZoomChange:A,onCropComplete:(e,t)=>{T(t)}})}),(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsx)("input",{type:"range",min:1,max:3,step:.1,value:z,onChange:e=>A(Number(e.target.value)),className:"w-full"})}),(0,o.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,o.jsx)(h.$,{variant:"outline",onClick:()=>N(null),children:"Cancel"}),(0,o.jsx)(h.$,{onClick:G,children:"Save Crop"})]})]})})]})}},45436:(e,t,r)=>{"use strict";r.d(t,{V:()=>a});var o=r(55077);let a=(0,r(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:r}=t;try{return(await o.S.get("/classes/details/".concat(e))).data}catch(e){var a;return r((null===(a=e.response)||void 0===a?void 0:a.data)||"Fetch failed")}})},55077:(e,t,r)=>{"use strict";r.d(t,{S:()=>i});var o=r(23464),a=r(56671);let s="http://localhost:4005/api/v1";console.log("Axios baseURL:",s);let i=o.A.create({baseURL:s,headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":s;{let t=localStorage.getItem("studentToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(a.toast.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,r)=>{"use strict";r.d(t,{MB:()=>l,ZO:()=>i,cn:()=>s,wR:()=>d,xh:()=>n});var o=r(52596),a=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,o.$)(t))}let i=()=>localStorage.getItem("studentToken"),l=()=>{localStorage.removeItem("studentToken")},n=()=>!!i(),d=()=>{if(i())return{isAuth:!0,userType:"STUDENT"};{let e=localStorage.getItem("user");if(e)try{let t=JSON.parse(e);if(t&&t.id)return{isAuth:!0,userType:"CLASS"}}catch(e){console.error("Error parsing user data:",e)}}return{isAuth:!1,userType:null}}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var o=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,o.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},75937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>c,MJ:()=>f,Rr:()=>x,zB:()=>p,eI:()=>h,lR:()=>v,C5:()=>b});var o=r(95155),a=r(12115),s=r(66634),i=r(62177),l=r(59434),n=r(24265);function d(e){let{className:t,...r}=e;return(0,o.jsx)(n.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}let c=i.Op,u=a.createContext({}),p=e=>{let{...t}=e;return(0,o.jsx)(u.Provider,{value:{name:t.name},children:(0,o.jsx)(i.xI,{...t})})},m=()=>{let e=a.useContext(u),t=a.useContext(g),{getFieldState:r}=(0,i.xW)(),o=(0,i.lN)({name:e.name}),s=r(e.name,o);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...s}},g=a.createContext({});function h(e){let{className:t,...r}=e,s=a.useId();return(0,o.jsx)(g.Provider,{value:{id:s},children:(0,o.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",t),...r})})}function v(e){let{className:t,...r}=e,{error:a,formItemId:s}=m();return(0,o.jsx)(d,{"data-slot":"form-label","data-error":!!a,className:(0,l.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...r})}function f(e){let{...t}=e,{error:r,formItemId:a,formDescriptionId:i,formMessageId:l}=m();return(0,o.jsx)(s.DX,{"data-slot":"form-control",id:a,"aria-describedby":r?"".concat(i," ").concat(l):"".concat(i),"aria-invalid":!!r,...t})}function x(e){let{className:t,...r}=e,{formDescriptionId:a}=m();return(0,o.jsx)("p",{"data-slot":"form-description",id:a,className:(0,l.cn)("text-muted-foreground text-sm",t),...r})}function b(e){var t;let{className:r,...a}=e,{error:s,formMessageId:i}=m(),n=s?String(null!==(t=null==s?void 0:s.message)&&void 0!==t?t:""):a.children;return n?(0,o.jsx)("p",{"data-slot":"form-message",id:i,className:(0,l.cn)("text-destructive text-sm",r),...a,children:n}):null}},94314:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n,_3:()=>a,ac:()=>i});var o=r(51990),a=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let s=(0,o.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let r=t.payload;e.completedForms[r]||(e.completedForms[r]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:l}=s.actions,n=s.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4540,1990,4212,1342,6980,8441,1684,7358],()=>t(9604)),_N_E=e.O()}]);