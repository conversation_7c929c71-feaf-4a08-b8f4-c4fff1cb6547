(()=>{var e={};e.id=7839,e.ids=[7839],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(46303),a=r(90269),n=r(63503),o=r(58759),l=r(41831),p=r(16189),u=r(43210);let c=()=>{let e=(0,p.useRouter)(),t=(0,p.useSearchParams)(),r=t.get("token"),i=t.get("userType")||"teacher",[a,c]=(0,u.useState)("loading"),[d,x]=(0,u.useState)("");return console.log("Token that is",r),console.log("User type:",i),(0,u.useEffect)(()=>{(async()=>{if(!r){c("error"),x("No verification token");return}try{let t;t="student"===i?await (0,l.Xc)(r):await (0,o.A$)(r),console.log("Verification response:",t),c("success"),x(t.message||"Email verified successfully!"),setTimeout(()=>{e.push("/")},2e3)}catch(e){console.error("Verification error:",e),c("error"),x("Failed to verify email. The token is invalid or expired.")}})()},[r,i,e]),(0,s.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,s.jsxs)(n.lG,{children:["loading"===a&&(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Verifying your email..."}),"success"===a&&(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:["Your Email is",(0,s.jsx)("span",{className:"text-orange-500 italic",children:" Verified"}),(0,s.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:d})]}),"error"===a&&(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:["Verification",(0,s.jsx)("span",{className:"text-red-500 italic",children:" Failed"}),(0,s.jsx)("p",{className:"text-sm mt-2 text-gray-600",children:d})]})]})})},d=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{}),(0,s.jsx)(u.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading verification..."}),children:(0,s.jsx)(c,{})}),(0,s.jsx)(i.default,{})]})},21820:e=>{"use strict";e.exports=require("os")},23539:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\verify-email\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx","default")},26575:(e,t,r)=>{Promise.resolve().then(r.bind(r,20681))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73431:(e,t,r)=>{Promise.resolve().then(r.bind(r,23539))},74075:e=>{"use strict";e.exports=require("zlib")},77323:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>d,tree:()=>p});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let p={children:["",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23539)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/verify-email/page",pathname:"/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7013,2800],()=>r(77323));module.exports=s})();