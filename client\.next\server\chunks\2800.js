exports.id=2800,exports.ids=[2800],exports.modules={4038:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,47429,23)),Promise.resolve().then(a.bind(a,48482)),Promise.resolve().then(a.bind(a,95631)),Promise.resolve().then(a.bind(a,99420))},4780:(e,t,a)=>{"use strict";a.d(t,{MB:()=>o,ZO:()=>i,cn:()=>n,wR:()=>c,xh:()=>l});var s=a(49384),r=a(82348);function n(...e){return(0,r.QP)((0,s.$)(e))}let i=()=>null,o=()=>{localStorage.removeItem("studentToken")},l=()=>!!i(),c=()=>i()?{isAuth:!0,userType:"STUDENT"}:{isAuth:!1,userType:null}},9236:(e,t,a)=>{"use strict";a.d(t,{HK:()=>r,Ou:()=>u,Pz:()=>n,a1:()=>m,au:()=>h,fm:()=>o,jc:()=>i,kI:()=>c,pO:()=>l,sl:()=>d});var s=a(28527);let r=async(e=1,t=10)=>(await s.S.get(`/notifications/classes?page=${e}&limit=${t}`)).data.data,n=async()=>(await s.S.get("/notifications/classes/count")).data.data.count,i=async e=>(await s.S.post(`/notifications/classes/mark-read/${e}`)).data,o=async()=>(await s.S.post("/notifications/classes/mark-all-read")).data,l=async()=>(await s.S.delete("/notifications/classes/delete-all")).data,c=async(e=1,t=10)=>(await s.S.get(`/notifications/students?page=${e}&limit=${t}`)).data.data,d=async()=>(await s.S.get("/notifications/students/count")).data.data.count,u=async e=>(await s.S.post(`/notifications/students/mark-read/${e}`)).data,m=async()=>(await s.S.post("/notifications/students/mark-all-read")).data,h=async()=>(await s.S.delete("/notifications/students/delete-all")).data},15347:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},17741:(e,t,a)=>{"use strict";a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app-components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx","default")},20672:(e,t,a)=>{"use strict";a.d(t,{V:()=>r});var s=a(28527);let r=(0,a(9317).zD)("class/fetchClassDetails",async(e,{rejectWithValue:t})=>{try{return(await s.S.get(`/classes/details/${e}`)).data}catch(e){return t(e.response?.data||"Fetch failed")}})},24364:(e,t,a)=>{"use strict";a.d(t,{AR:()=>i,bE:()=>r,dt:()=>o,je:()=>n,sX:()=>l});var s=a(28527);let r=async(e,t=1)=>{try{return(await s.S.post("/cart/add",{itemId:e,quantity:t})).data}catch(e){return{success:!1,error:e.response?.data?.error||"Failed to add item to cart"}}},n=async()=>{try{return(await s.S.get("/cart")).data}catch(e){return{success:!1,error:e.response?.data?.error||"Failed to fetch cart items"}}},i=async(e,t)=>{try{return(await s.S.put(`/cart/item/${e}`,{quantity:t})).data}catch(e){return{success:!1,error:e.response?.data?.error||"Failed to update cart item"}}},o=async e=>{try{return(await s.S.delete(`/cart/item/${e}`)).data}catch(e){return{success:!1,error:e.response?.data?.error||"Failed to remove item from cart"}}},l=async()=>{try{return(await s.S.delete("/cart/clear")).data}catch(e){return{success:!1,error:e.response?.data?.error||"Failed to clear cart"}}}},27808:(e,t,a)=>{"use strict";a.d(t,{ReduxProvider:()=>u});var s=a(60687),r=a(54864),n=a(9317),i=a(30072),o=a(50346),l=a(36097),c=a(45201);let d=(0,n.U1)({reducer:{user:i.Ay,formProgress:o.Ay,class:l.A,studentProfile:c.Ay}});function u({children:e}){return(0,s.jsx)(r.Kq,{store:d,children:e})}},28281:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,85814,23)),Promise.resolve().then(a.bind(a,46303)),Promise.resolve().then(a.bind(a,90269))},28527:(e,t,a)=>{"use strict";a.d(t,{S:()=>i});var s=a(51060),r=a(52581);let n="http://localhost:4005/api/v1";console.log("Axios baseURL:",n);let i=s.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!0});i.interceptors.request.use(e=>{let t=e.headers["Server-Select"];return e.baseURL="uwhizServer"===t?"http://localhost:4006":n,e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&r.toast.error(e.response.data.message||"Unauthorized"),Promise.reject(e)))},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>o});var s=a(60687);a(43210);var r=a(11329),n=a(24224),i=a(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:n=!1,...l}){let c=n?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:a,className:e})),...l})}},30072:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i,gV:()=>r,lM:()=>n});let s=(0,a(9317).Z0)({name:"user",initialState:{user:null,isAuthenticated:!1},reducers:{setUser:(e,t)=>{e.user=t.payload.user,e.isAuthenticated=!0,localStorage.setItem("user",JSON.stringify(t.payload.user))},clearUser:e=>{e.user=null,e.isAuthenticated=!1,localStorage.removeItem("user")}}}),{setUser:r,clearUser:n}=s.actions,i=s.reducer},32584:(e,t,a)=>{"use strict";a.d(t,{eu:()=>i,q5:()=>o});var s=a(60687);a(43210);var r=a(17403),n=a(4780);function i({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function o({className:e,...t}){return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},36097:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,B:()=>i});var s=a(9317),r=a(20672);let n=(0,s.Z0)({name:"class",initialState:{classData:null,loading:!1,error:null},reducers:{setClassData(e,t){e.classData=t.payload}},extraReducers:e=>{e.addCase(r.V.pending,e=>{e.loading=!0,e.error=null}).addCase(r.V.fulfilled,(e,t)=>{e.loading=!1,e.classData=t.payload}).addCase(r.V.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setClassData:i}=n.actions,o=n.reducer},37075:(e,t,a)=>{"use strict";a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app-components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx","default")},38542:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,79167,23)),Promise.resolve().then(a.bind(a,64616)),Promise.resolve().then(a.bind(a,49360)),Promise.resolve().then(a.bind(a,27808))},39777:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h,metadata:()=>m});var s=a(37413);a(61135);var r=a(99420),n=a(48482),i=a(73125),o=a.n(i),l=a(36162),c=a(95631),d=a(61120);let u="G-N06ZRQXN1Y",m={title:"Uest - Your Gateway to Educational Excellence",description:"Your Gateway to Educational Excellence"};function h({children:e}){return(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsx)("head",{children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.default,{src:`https://www.googletagmanager.com/gtag/js?id=${u}`,strategy:"afterInteractive"}),(0,s.jsx)(l.default,{id:"gtag-init",strategy:"afterInteractive",children:`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${u}', {
                page_path: window.location.pathname,
              });
            `})]})}),(0,s.jsx)("body",{className:`antialiased ${o().className}`,children:(0,s.jsxs)(r.ReduxProvider,{children:[(0,s.jsx)(d.Suspense,{fallback:null,children:(0,s.jsx)(c.default,{})}),e,(0,s.jsx)(n.Toaster,{})]})})]})}},40988:(e,t,a)=>{"use strict";a.d(t,{AM:()=>i,Wv:()=>o,hl:()=>l});var s=a(60687);a(43210);var r=a(36141),n=a(4780);function i({...e}){return(0,s.jsx)(r.bL,{"data-slot":"popover",...e})}function o({...e}){return(0,s.jsx)(r.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:a=4,...i}){return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"popover-content",align:t,sideOffset:a,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...i})})}},41831:(e,t,a)=>{"use strict";a.d(t,{RY:()=>l,Ty:()=>c,Xc:()=>d,af:()=>o,bZ:()=>i,iM:()=>r,zy:()=>n});var s=a(28527);let r=async e=>(await s.S.post("/student/continue-with-email",e)).data,n=async e=>(await s.S.post("/student/register",e)).data,i=async e=>(await s.S.post("/student/login",e)).data,o=async()=>{try{let e=await s.S.post("/student/logout");return localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),e.data}catch{return localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),{success:!0,message:"Logged out successfully"}}};async function l(e){return(await s.S.post("/student/verify-otp",e)).data}async function c(e){return(await s.S.post("/student/resend-otp",e)).data}let d=async e=>(await s.S.post("/student/verify-email",{token:e})).data},45201:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>c,Ig:()=>l,XY:()=>o});var s=a(9317),r=a(74004);let n=(0,s.Z0)({name:"studentProfile",initialState:{profileData:null,loading:!1,error:null},reducers:{setStudentProfileData(e,t){e.profileData=t.payload},updateProfilePhoto(e,t){e.profileData?.profile&&(e.profileData.profile.photo=t.payload)},clearStudentProfileData(e){e.profileData=null,e.loading=!1,e.error=null}},extraReducers:e=>{e.addCase(r.N.pending,e=>{e.loading=!0,e.error=null}).addCase(r.N.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(r.N.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}).addCase(r.A.pending,e=>{e.loading=!0,e.error=null}).addCase(r.A.fulfilled,(e,t)=>{e.loading=!1,t.payload&&(e.profileData=t.payload)}).addCase(r.A.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setStudentProfileData:i,updateProfilePhoto:o,clearStudentProfileData:l}=n.actions,c=n.reducer},46303:(e,t,a)=>{"use strict";a.d(t,{default:()=>l});var s=a(60687);a(43210);var r=a(85814),n=a.n(r),i=a(30474),o=a(69587);let l=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(n(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:o.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:o.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:o.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:o.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:o.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:o.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:o.kUm,label:"Tumblr"}].map(({href:e,icon:t,label:a})=>(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(n(),{href:e,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,s.jsx)(t,{className:"text-xl text-white hover:text-gray-400 transition"})})},a))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(n(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(n(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},48482:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx","Toaster")},49360:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});var s=a(16189);function r(){return(0,s.usePathname)(),(0,s.useSearchParams)(),null}a(43210)},50346:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>l,_3:()=>r,ac:()=>i});var s=a(9317),r=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let n=(0,s.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let a=t.payload;e.completedForms[a]||(e.completedForms[a]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:i,setCurrentStep:o}=n.actions,l=n.reducer},52299:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},53386:(e,t,a)=>{"use strict";a.d(t,{IJ:()=>n,wE:()=>r});var s=a(28527);let r=async e=>{try{let t=await s.S.post("/store/purchase",e);return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:e.response?.data?.message||e.message||"Purchase failed"}}},n=async()=>{try{let e=await s.S.get("/store/orders");return{success:!0,data:e.data.data}}catch(e){return{success:!1,error:e.response?.data?.message||e.message||"Failed to fetch orders"}}}},54413:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(37413),r=a(4536),n=a.n(r),i=a(37075),o=a(17741);function l(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.default,{}),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white overflow-hidden",children:(0,s.jsxs)("div",{className:"text-center px-4",children:[(0,s.jsx)("h1",{className:"text-9xl font-extrabold text-[#FD904B] tracking-widest mb-5",children:"404"}),(0,s.jsx)("h2",{className:"mt-4 text-3xl font-semibold text-gray-800",children:"Page Not Found"}),(0,s.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"Oops! The page you're looking for doesn't exist."}),(0,s.jsx)(n(),{href:"/",children:(0,s.jsx)("button",{className:"mt-8 px-6 py-3 bg-[#FD904B] text-white font-medium rounded-lg hover:bg-[#974813] transition-colors duration-300",children:"Back to Home"})})]})}),(0,s.jsx)(o.default,{})]})}},58759:(e,t,a)=>{"use strict";a.d(t,{A$:()=>d,DY:()=>n,Lx:()=>i,RY:()=>o,Ty:()=>l,bi:()=>c,iM:()=>r});var s=a(28527);async function r(e){return(await s.S.post("/auth-client/continue-with-email",e)).data}async function n(e){return(await s.S.post("/auth-client/register",e)).data}async function i(e){return(await s.S.post("/auth-client/login",e)).data}async function o(e){return(await s.S.post("/auth-client/verify-otp",e)).data}async function l(e){return(await s.S.post("/auth-client/resend-otp",e)).data}let c=async(e,t)=>(await s.S.post("/auth-client/generate-jwt",{contact:e,password:t})).data,d=async e=>(await s.S.get("/auth-client/verify-email",{params:{token:e}})).data},61135:()=>{},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>h,L3:()=>x,c7:()=>m,lG:()=>o,rr:()=>f,zM:()=>l});var s=a(60687);a(43210);var r=a(6491),n=a(11860),i=a(4780);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,...a}){return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...a,children:[t,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function h({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function f({className:e,...t}){return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},64616:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>i});var s=a(60687),r=a(10218),n=a(52581);let i=({...e})=>{let{theme:t="system"}=(0,r.D)();return(0,s.jsx)(n.l,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74004:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,N:()=>n});var s=a(28527),r=a(9317);let n=(0,r.zD)("studentProfile/fetchStudentProfile",async(e,{rejectWithValue:t})=>{try{let e=localStorage.getItem("studentToken");if(!e)return t("No authentication token found");let a=await s.S.get("/student-profile/all-data",{headers:{Authorization:`Bearer ${e}`}});if(a.data&&"object"==typeof a.data){if(void 0!==a.data.success&&void 0!==a.data.data)return a.data.data;return a.data}return null}catch(e){if(e.response?.status===404)return null;return t(e.response?.data?.message||"Failed to fetch student data")}}),i=(0,r.zD)("studentProfile/updateStudentProfile",async(e,{rejectWithValue:t})=>{try{let a=localStorage.getItem("studentToken");if(!a)return t("No authentication token found");let r=await (0,s.S)({method:"put",url:"/student-profile/combined",data:e,headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`}});if(r.data&&"object"==typeof r.data){if(void 0!==r.data.success&&void 0!==r.data.data)return r.data.data;return r.data}return null}catch(e){return t(e.response?.data?.message||"Failed to update student profile")}})},90269:(e,t,a)=>{"use strict";a.d(t,{default:()=>V});var s=a(60687),r=a(85814),n=a.n(r),i=a(30474),o=a(86356),l=a(58887),c=a(77306),d=a(71057),u=a(49625),m=a(81620),h=a(49158),x=a(28561),f=a(58869),p=a(11860),g=a(12941),v=a(5748),j=a(96474),b=a(85778),y=a(29523),w=a(54864);let N=()=>(0,w.wA)();var k=a(43210),S=a(4780),C=a(16189);a(70334);let A=()=>{(0,C.useRouter)();let{profileData:e}=(0,w.d4)(e=>e.studentProfile);return e?.profile?.id,null};var P=a(97051),$=a(40988),E=a(93500),F=a(9236),T=a(52581),z=a(50765);function I({userType:e}){let[t,a]=(0,k.useState)([]),[r,n]=(0,k.useState)(0),[i,o]=(0,k.useState)(!1),[l,c]=(0,k.useState)(!1),[d,u]=(0,k.useState)(!1),m=(0,C.useRouter)(),h=Array.isArray(t)?t:[];(0,k.useCallback)(async()=>{try{let t,s;c(!0),"class"===e?(t=await (0,F.HK)(1,20),s=await (0,F.Pz)()):(t=await (0,F.kI)(1,20),s=await (0,F.sl)());let r=t?.notifications||t||[];a(Array.isArray(r)?r:[]),n(s)}catch(e){console.error("Error fetching notifications:",e),a([]),n(0)}finally{c(!1)}},[e]);let x=async t=>{try{"class"===e?await (0,F.jc)(t.id):await (0,F.Ou)(t.id),a(e=>e.map(e=>e.id===t.id?{...e,isRead:!0}:e)),n(e=>Math.max(0,e-1)),o(!1),t.data?.actionType==="OPEN_CHAT"&&t.data?.redirectUrl&&m.push(t.data.redirectUrl)}catch(e){console.error("Error handling notification click:",e),T.toast.error("Failed to process notification")}},f=async()=>{try{"class"===e?await (0,F.fm)():await (0,F.a1)(),a(e=>e.map(e=>({...e,isRead:!0}))),n(0),T.toast.success("All notifications marked as read")}catch(e){console.error("Error marking all notifications as read:",e),T.toast.error("Failed to mark all notifications as read")}},p=async()=>{u(!1);try{"class"===e?await (0,F.pO)():await (0,F.au)(),a([]),n(0),T.toast.success("All notifications removed successfully")}catch(e){console.error("Error removing all notifications:",e),T.toast.error("Failed to remove all notifications")}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)($.AM,{open:i,onOpenChange:o,children:[(0,s.jsx)($.Wv,{asChild:!0,children:(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",className:"relative bg-black hover:bg-gray-900 transition duration-200 h-9 w-9 md:h-10 md:w-10 rounded-full",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"}),(0,s.jsx)(P.A,{className:"relative z-10 h-4 w-4 md:h-5 md:w-5 text-white transition-colors duration-200"}),r>0&&(0,s.jsx)("div",{className:"absolute -top-1.5 -right-1.5 md:-top-2 md:-right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center z-20",children:(0,s.jsx)("span",{className:"text-white text-[10px] md:text-xs font-semibold leading-none",children:r>99?"99+":r})})]})}),(0,s.jsxs)($.hl,{className:"w-80 p-0",align:"end",children:[(0,s.jsx)("div",{className:"p-4 border-b",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold",children:"Notifications"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[r>0&&(0,s.jsx)(y.$,{variant:"ghost",size:"sm",onClick:f,className:"text-xs",children:"Mark all read"}),t.length>0&&0===r&&(0,s.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>{u(!0)},className:"text-xs text-red-600 hover:text-red-700 hover:bg-red-50",children:"Remove all"})]})]})}),(0,s.jsx)("div",{className:"h-80 overflow-y-auto",children:l?(0,s.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"Loading notifications..."}):0===t.length?(0,s.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:"No notifications yet"}):(0,s.jsx)("div",{className:"divide-y",children:Array.isArray(t)&&t.map(e=>(0,s.jsx)("div",{className:`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${e.isRead?"":"bg-blue-50/50"}`,onClick:()=>x(e),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full mt-2 ${e.isRead?"bg-gray-300":"bg-blue-500"}`}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-medium text-sm",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:(0,z.m)(new Date(e.createdAt),{addSuffix:!0})})]})]})},e.id))})}),h.length>0&&(0,s.jsx)("div",{className:"p-3 border-t bg-muted/30",children:(0,s.jsx)(y.$,{variant:"ghost",size:"sm",className:"w-full text-xs",onClick:()=>{o(!1),m.push("/notifications")},children:"View All Notifications"})})]})]}),(0,s.jsx)(E.Lt,{open:d,onOpenChange:u,children:(0,s.jsxs)(E.EO,{children:[(0,s.jsxs)(E.wd,{children:[(0,s.jsx)(E.r7,{children:"Remove All Notifications"}),(0,s.jsx)(E.$v,{children:"Are you sure you want to remove all notifications? This action cannot be undone."})]}),(0,s.jsxs)(E.ck,{children:[(0,s.jsx)(E.Zr,{children:"Cancel"}),(0,s.jsx)(E.Rx,{onClick:p,className:"bg-red-600 hover:bg-red-700",children:"Remove All"})]})]})})]})}var R=a(32584),D=a(41831),L=a(30072),U=a(45201),_=a(74004),O=a(28527),M=a(17135),q=a(84517),G=a(58759),Y=a(90971);let Z=({studentId:e})=>{let[t,a]=(0,k.useState)(0);return(0,k.useEffect)(()=>{(async()=>{if(!e){a(0);return}let t=await (0,Y.Gk)(e);t.success&&t.data?a(t.data.streak||0):a(0)})()},[e]),(0,s.jsxs)("span",{className:"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1",children:["\uD83D\uDD25 ",t]})};var W=a(24364),B=a(53386),H=a(63503);let V=()=>{let{isAuthenticated:e,user:t}=(0,w.d4)(e=>e.user),[a,r]=(0,k.useState)(!1),[P,E]=(0,k.useState)(!1),[F,z]=(0,k.useState)(null),[Y,V]=(0,k.useState)(null),[J,X]=(0,k.useState)([]),[Q,K]=(0,k.useState)(!1),[ee,et]=(0,k.useState)(!1),ea=N(),es=(0,C.useRouter)(),er=(0,k.useRef)(null),[en,ei]=(0,k.useState)(0),eo=(0,M.d)(0),el=en/20,ec=async()=>{try{let e=await W.je();e.success&&e.data&&X(e.data)}catch(e){console.error("Error loading cart:",e)}};(0,k.useEffect)(()=>{let a=(0,S.xh)();if(E(a),a){let e=localStorage.getItem("student_data");e&&z(JSON.parse(e)),ea((0,_.N)()),ec()}e&&ec(),(async()=>{if(e&&t?.id)try{let e=await O.S.get(`/classes/details/${t.id}`);e.data&&e.data.status&&V(e.data.status.status)}catch(e){console.error("Error fetching class status:",e)}})();let s=()=>{let e=(0,S.xh)();if(E(e),e){let e=localStorage.getItem("student_data");e&&z(JSON.parse(e)),ea((0,_.N)()),ec()}else z(null),X([])},r=()=>{(a||e)&&ec()};return window.addEventListener("storage",s),window.addEventListener("cartUpdated",r),er.current&&ei(er.current.getBoundingClientRect().width),()=>{window.removeEventListener("storage",s),window.removeEventListener("cartUpdated",r)}},[ea]),(0,q.N)((e,t)=>{if(0===en)return;let a=eo.get()-el*t/1e3;a<=-en&&(a=0),eo.set(a)});let ed=()=>r(!a),eu=async()=>{try{let e=await (0,D.af)();!1!==e.success?((0,S.MB)(),E(!1),z(null),localStorage.removeItem("student_data"),ea((0,U.Ig)()),T.toast.success("Logged out successfully"),window.dispatchEvent(new Event("storage"))):(T.toast.error(e.message||"Failed to logout"),(0,S.MB)(),E(!1),z(null),localStorage.removeItem("student_data"),ea((0,U.Ig)()))}catch(e){console.log("Failed to logout",e),T.toast.error("Failed to logout"),localStorage.removeItem("student_data"),(0,S.MB)(),E(!1),z(null),ea((0,U.Ig)())}},em=async e=>{try{let t=await W.dt(e);t.success?(await ec(),T.toast.success("Item removed from cart!")):T.toast.error(t.error||"Failed to remove item from cart")}catch(e){console.error("Error removing from cart:",e),T.toast.error("Failed to remove item from cart")}},eh=async(e,t)=>{try{let a=J.find(t=>t.itemId===e);if(a&&t>a.item.availableStock){T.toast.error(`Only ${a.item.availableStock} items available in stock`);return}let s=await W.AR(e,t);if(s.success)await ec(),0===t&&T.toast.success("Item removed from cart!");else{let e=s.error||"Failed to update cart item";e.includes("stock")||e.includes("available")?T.toast.error("Item is out of stock or insufficient quantity available"):T.toast.error(e)}}catch(e){console.error("Error updating cart:",e),T.toast.error("Failed to update cart item")}},ex=()=>J.reduce((e,t)=>e+t.item.coinPrice*t.quantity,0),ef=async()=>{if(0===J.length){T.toast.error("Your cart is empty");return}if(!P&&!e){T.toast.error("Please login to checkout");return}try{et(!0);let e=J.map(e=>({id:e.itemId,name:e.item.name,coinPrice:e.item.coinPrice,quantity:e.quantity,image:e.item.image||""})),t=ex(),a=await B.wE({cartItems:e,totalCoins:t});if(!a.success){if("PROFILE_NOT_APPROVED"===a.error){let e=a.data?.message||"Your profile is not approved yet. Please complete your profile and wait for admin approval.";T.toast.error(e);return}throw Error(a.error)}await W.sX(),await ec(),T.toast.success("Purchase completed successfully!"),K(!1),P?es.push("/student/my-orders"):es.push("/classes/my-orders")}catch(e){console.error("Checkout error:",e),T.toast.error(e.message||"Checkout failed. Please try again.")}finally{et(!1)}},ep=async()=>{try{let e=await (0,G.bi)(t?.contactNo,t?.password);if(e.success){let{token:a}=e.data,s=`http://127.0.0.1:8000/login-class-link?uid=${t?.id}&token=${a}`;window.location.href=s}else T.toast.error(e.message||"Failed to generate token")}catch(e){console.error("Failed to generate token",e),T.toast.error("Failed to generate token")}},eg=[{href:"/verified-classes",label:"Find Tutor"},{href:"/uwhiz",label:"U - Whiz"},{href:"/mock-exam-card",label:(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:"Daily Quiz"}),P&&(0,s.jsx)(Z,{studentId:F?.id})]}),isNew:!0},{href:"/careers",label:"Career"},{href:"/store",label:"Store"}],ev=[{href:"/classes/profile",icon:(0,s.jsx)(o.A,{className:"w-5 h-5 mr-2"}),label:"Profile"},{href:"/classes/chat",icon:(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2"}),label:"Messages"},{href:"/coins",icon:(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),label:"Coins"},{href:"/classes/my-orders",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Orders"},{onClick:ep,icon:(0,s.jsx)(u.A,{className:"w-5 h-5 mr-2"}),label:"My Dashboard"},{href:"/classes/referral-dashboard",icon:(0,s.jsx)(m.A,{className:"w-5 h-5 mr-2"}),label:"Referral Dashboard"},..."APPROVED"===Y?[{href:"/classes/payment",icon:(0,s.jsx)(h.A,{className:"w-5 h-5 mr-2"}),label:"Payment Details"}]:[]];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full bg-black",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,s.jsx)(n(),{href:"/",className:"flex items-center space-x-2 transition-transform hover:scale-105",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Preply Logo",width:120,height:40,className:"rounded-sm"})}),(0,s.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:eg.map(e=>(0,s.jsxs)(n(),{href:e.href,className:"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400",children:[e.label,e.isNew&&(0,s.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse",children:"Trending"})]},e.href))}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[e||P?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(I,{userType:e?"class":"student"}),J.length>0&&(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",onClick:()=>K(!0),className:"text-white hover:bg-gray-800 rounded-full relative",children:[(0,s.jsx)(x.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:J.reduce((e,t)=>e+t.quantity,0)})]})}),(0,s.jsxs)($.AM,{children:[(0,s.jsx)($.Wv,{asChild:!0,children:(0,s.jsx)(R.eu,{className:"cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity",children:(0,s.jsx)(R.q5,{className:"bg-white text-black flex items-center justify-center text-sm font-semibold",children:e?t?.firstName&&t?.lastName?`${t.firstName[0]}${t.lastName[0]}`.toUpperCase():"CT":F?.firstName&&F?.lastName?`${F.firstName[0]}${F.lastName[0]}`.toUpperCase():"ST"})})}),(0,s.jsxs)($.hl,{className:"w-64 bg-white p-4 rounded-lg shadow-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(R.eu,{className:"h-10 w-10",children:(0,s.jsx)(R.q5,{className:"bg-white text-black",children:e?t?.firstName&&t?.lastName?`${t.firstName[0]}${t.lastName[0]}`.toUpperCase():"CT":F?.firstName&&F?.lastName?`${F.firstName[0]}${F.lastName[0]}`.toUpperCase():"ST"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-black",children:e?t?.firstName&&t?.lastName?`${t.firstName} ${t.lastName}`:t?.className||"Class Account":F?.firstName&&F?.lastName?`${F.firstName} ${F.lastName}`:"Student Account"}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:e?t?.contactNo||"<EMAIL>":F?.contactNo||"<EMAIL>"})]})]}),(0,s.jsx)("div",{className:"space-y-2",children:e?(0,s.jsxs)(s.Fragment,{children:[ev.map(e=>(0,s.jsx)(y.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground",children:e.href?(0,s.jsxs)(n(),{href:e.href,className:"flex items-center",children:[e.icon,(0,s.jsx)("span",{children:e.label})]}):(0,s.jsxs)("div",{onClick:e.onClick,className:"flex items-center w-full",children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href||e.label)),(0,s.jsxs)(y.$,{variant:"ghost",className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",onClick:async()=>{try{(await O.S.post("/auth-client/logout",{})).data.success&&(es.push("/"),ea((0,L.lM)()),localStorage.removeItem("token"),T.toast.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),T.toast.error("Failed to logout")}},children:[(0,s.jsx)(f.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[[{href:"/student/profile",icon:(0,s.jsx)(o.A,{className:"w-5 h-5 mr-2"}),label:"Profile"},{href:"/student/chat",icon:(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2"}),label:"Messages"},{href:"/coins",icon:(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),label:"Coins"},{href:"/student/wishlist",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Wishlist"},{href:"/student/referral-dashboard",icon:(0,s.jsx)(m.A,{className:"w-5 h-5 mr-2"}),label:"Referral Dashboard"},{href:"/student/my-orders",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Orders"}].map(e=>(0,s.jsx)(y.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground",children:(0,s.jsxs)(n(),{href:e.href,className:"flex items-center",children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href)),(0,s.jsxs)(y.$,{onClick:eu,className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",children:[(0,s.jsx)(f.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]})})})]})]})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,s.jsx)(y.$,{className:"bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md",asChild:!0,children:(0,s.jsx)(n(),{href:"/class/login",children:"Join as Tutor"})}),(0,s.jsx)(y.$,{variant:"ghost",className:"bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700",asChild:!0,children:(0,s.jsx)(n(),{href:"/student/login",children:"Student Login"})})]})}),(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full",onClick:ed,children:a?(0,s.jsx)(p.A,{className:"h-6 w-6"}):(0,s.jsx)(g.A,{className:"h-6 w-6"})})]})]})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:`fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ${a?"translate-x-0":"translate-x-full"}`,children:(0,s.jsxs)("div",{className:"flex flex-col h-full p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Uest Logo",width:100,height:32,className:"rounded-sm"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(e||P)&&(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(y.$,{variant:"ghost",size:"icon",onClick:()=>{K(!0),ed()},className:"text-orange-400 hover:bg-orange-500/10 rounded-full relative",children:[(0,s.jsx)(x.A,{className:"h-5 w-5"}),J.length>0&&(0,s.jsx)("span",{className:"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:J.reduce((e,t)=>e+t.quantity,0)})]})}),(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"text-orange-400 hover:bg-orange-500/10 rounded-full",onClick:ed,children:(0,s.jsx)(p.A,{className:"h-6 w-6"})})]})]}),(e||P)&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-900 rounded-lg",children:[(0,s.jsx)(R.eu,{className:"h-10 w-10",children:(0,s.jsx)(R.q5,{className:"bg-white text-black",children:e?t?.firstName&&t?.lastName?`${t.firstName[0]}${t.lastName[0]}`.toUpperCase():"CT":F?.firstName&&F?.lastName?`${F.firstName[0]}${F.lastName[0]}`.toUpperCase():"ST"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-white",children:e?t?.firstName&&t?.lastName?`${t.firstName} ${t.lastName}`:t?.className||"Class Account":F?.firstName&&F?.lastName?`${F.firstName} ${F.lastName}`:"Student Account"}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:e?t?.contactNo||"<EMAIL>":F?.contactNo||"<EMAIL>"})]})]})}),(0,s.jsx)("nav",{className:"flex flex-col space-y-2",children:eg.map(e=>(0,s.jsxs)(n(),{href:e.href,className:"flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors",onClick:ed,children:[(0,s.jsx)("div",{className:"flex items-center gap-3",children:"string"==typeof e.label?(0,s.jsx)("span",{children:e.label}):e.label}),e.isNew&&(0,s.jsx)("span",{className:"text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white",children:"Trending"})]},e.href))}),(0,s.jsxs)("div",{className:"mt-auto space-y-2",children:[e&&(0,s.jsxs)(s.Fragment,{children:[ev.map(e=>(0,s.jsx)(y.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground",children:e.href?(0,s.jsxs)(n(),{href:e.href,className:"flex items-center",onClick:ed,children:[e.icon,(0,s.jsx)("span",{children:e.label})]}):(0,s.jsxs)("div",{onClick:()=>{ed()},className:"flex items-center w-full",children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href||e.label)),(0,s.jsxs)(y.$,{variant:"ghost",className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",onClick:async()=>{try{(await O.S.post("/auth-client/logout",{})).data.success&&(es.push("/"),ea((0,L.lM)()),localStorage.removeItem("token"),T.toast.success("Logged out successfully"))}catch(e){console.error("Logout error:",e),T.toast.error("Failed to logout")}ed()},children:[(0,s.jsx)(f.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]}),P&&(0,s.jsxs)(s.Fragment,{children:[[{href:"/student/profile",icon:(0,s.jsx)(o.A,{className:"w-5 h-5 mr-2"}),label:"Profile"},{href:"/student/chat",icon:(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2"}),label:"Messages"},{href:"/coins",icon:(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),label:"Coins"},{href:"/student/wishlist",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Wishlist"},{href:"/student/referral-dashboard",icon:(0,s.jsx)(m.A,{className:"w-5 h-5 mr-2"}),label:"Referral Dashboard"},{href:"/student/my-orders",icon:(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),label:"My Orders"}].map(e=>(0,s.jsx)(y.$,{asChild:!0,variant:"ghost",className:"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground ",children:(0,s.jsxs)(n(),{href:e.href,className:"flex items-center",onClick:ed,children:[e.icon,(0,s.jsx)("span",{children:e.label})]})},e.href)),(0,s.jsxs)(y.$,{variant:"ghost",className:"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md",onClick:()=>{eu(),ed()},children:[(0,s.jsx)(f.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:"Logout"})]})]}),!e&&!P&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(y.$,{className:"w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3",asChild:!0,children:(0,s.jsx)(n(),{href:"/class/login",onClick:ed,children:"Tutor Login"})}),(0,s.jsx)(y.$,{variant:"ghost",className:"w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700",asChild:!0,children:(0,s.jsx)(n(),{href:"/student/login",onClick:ed,children:"Student Login"})})]})]})]})}),P&&(0,s.jsx)(A,{})]}),(0,s.jsx)(H.lG,{open:Q,onOpenChange:K,children:(0,s.jsxs)(H.Cf,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,s.jsxs)(H.c7,{children:[(0,s.jsxs)(H.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-5 h-5"}),"Shopping Cart (",J.reduce((e,t)=>e+t.quantity,0)," items)"]}),(0,s.jsx)(H.rr,{children:"Review your items before checkout"})]}),(0,s.jsx)("div",{className:"space-y-4",children:0===J.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(x.A,{className:"w-12 h-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Your cart is empty"})]}):(0,s.jsxs)(s.Fragment,{children:[J.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg bg-card",children:[(0,s.jsx)(i.default,{src:e.item.image?.startsWith("http")?e.item.image:`http://localhost:4005/${e.item.image?.startsWith("/")?e.item.image.substring(1):e.item.image||"uploads/store/placeholder.jpg"}`,alt:e.item.name,width:60,height:60,className:"rounded object-cover",onError:e=>{e.target.src="/logo.png"}}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-card-foreground",children:e.item.name}),(0,s.jsxs)("p",{className:"text-orange-500 font-semibold flex items-center",children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-1"}),e.item.coinPrice," coins"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.$,{size:"sm",variant:"outline",onClick:()=>eh(e.itemId,e.quantity-1),children:(0,s.jsx)(v.A,{className:"w-3 h-3"})}),(0,s.jsx)("span",{className:"w-8 text-center",children:e.quantity}),(0,s.jsx)(y.$,{size:"sm",variant:"outline",onClick:()=>eh(e.itemId,e.quantity+1),children:(0,s.jsx)(j.A,{className:"w-3 h-3"})})]}),(0,s.jsx)(y.$,{size:"sm",variant:"destructive",onClick:()=>em(e.itemId),children:"Remove"})]},e.id)),(0,s.jsx)("div",{className:"border-t pt-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center text-lg font-semibold",children:[(0,s.jsx)("span",{children:"Total:"}),(0,s.jsxs)("span",{className:"text-orange-500 flex items-center",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 mr-1"}),ex()," coins"]})]})})]})}),(0,s.jsxs)(H.Es,{children:[(0,s.jsx)(y.$,{variant:"outline",onClick:()=>K(!1),children:"Continue Shopping"}),J.length>0&&(0,s.jsx)(y.$,{onClick:ef,disabled:ee,className:"bg-orange-500 hover:bg-orange-600 disabled:opacity-50",children:ee?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Checkout (",ex()," coins)"]})})]})]})})]})}},90971:(e,t,a)=>{"use strict";a.d(t,{$m:()=>r,Gk:()=>n,If:()=>i,xT:()=>o});var s=a(28527);let r=async e=>{try{let t=await s.S.put(`/mock-exam-streak/${e}`,{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:`Failed to save mock exam streak: ${e.response?.data?.error||e.message}`}}},n=async e=>{try{let t=await s.S.get(`/mock-exam-streak/${e}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:`Failed to get mock exam streak: ${e.response?.data?.error||e.message}`}}},i=async e=>{try{let t=await s.S.put(`/mock-exam-weekly-streak/${e}`,{},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:`Failed to save mock exam streak: ${e.response?.data?.error||e.message}`}}},o=async e=>{try{let t=await s.S.get(`/mock-exam-weekly-streak/${e}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data.data}}catch(e){return{success:!1,error:`Failed to get mock exam streak: ${e.response?.data?.error||e.message}`}}}},91833:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,4536,23)),Promise.resolve().then(a.bind(a,17741)),Promise.resolve().then(a.bind(a,37075))},93500:(e,t,a)=>{"use strict";a.d(t,{$v:()=>f,EO:()=>u,Lt:()=>o,Rx:()=>p,Zr:()=>g,ck:()=>h,r7:()=>x,tv:()=>l,wd:()=>m});var s=a(60687);a(43210);var r=a(32771),n=a(4780),i=a(29523);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"alert-dialog",...e})}function l({...e}){return(0,s.jsx)(r.l9,{"data-slot":"alert-dialog-trigger",...e})}function c({...e}){return(0,s.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...e})}function d({className:e,...t}){return(0,s.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,...t}){return(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{}),(0,s.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function m({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function h({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,s.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,n.cn)("text-lg font-semibold",e),...t})}function f({className:e,...t}){return(0,s.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function p({className:e,...t}){return(0,s.jsx)(r.rc,{className:(0,n.cn)((0,i.r)(),e),...t})}function g({className:e,...t}){return(0,s.jsx)(r.ZD,{className:(0,n.cn)((0,i.r)({variant:"outline"}),e),...t})}},95631:(e,t,a)=>{"use strict";a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\hooks\\\\AnalyticsProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx","default")},99420:(e,t,a)=>{"use strict";a.d(t,{ReduxProvider:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx","ReduxProvider")}};