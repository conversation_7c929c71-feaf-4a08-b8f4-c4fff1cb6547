(()=>{var e={};e.id=7127,e.ids=[7127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8380:(e,t,r)=>{"use strict";r.d(t,{FU:()=>i,NL:()=>n,dS:()=>s});var a=r(28527);let s=async(e,t=1,r=10)=>{try{let s=await a.S.get(`/mock-exam-leaderboard/leaderboard/${e}?page=${t}&limit=${r}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){return{success:!1,error:`Failed to get mock exam leaderboard data: ${e.response?.data?.error||e.message}`}}},n=async()=>{try{let e=await a.S.get("/mock-exam-leaderboard/previous-day",{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:e.data}}catch(e){return{success:!1,error:`Failed to get yesterday's top performers: ${e.response?.data?.error||e.message}`}}},i=async(e,t,r)=>{try{let s=await a.S.post("/reactions",{studentId:e,reactionType:t,reactorId:r},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){return{success:!1,error:`Failed to send reaction: ${e.response?.data?.error||e.message}`}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12331:(e,t,r)=>{Promise.resolve().then(r.bind(r,73243))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20181:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(60687),s=r(92449);r(43210);let n=({count:e})=>(0,a.jsxs)("svg",{className:"h-10 w-10 sm:h-12 sm:w-12",viewBox:"0 0 1550 1808",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M574.764 165.821C692.4 74.0944 857.6 74.0944 975.236 165.821L1262.05 386.652C1354.36 459.21 1407.96 571.58 1407.96 690.458V1117.54C1407.96 1236.42 1354.36 1348.79 1262.05 1421.35L975.236 1642.18C857.6 1733.91 692.4 1733.91 574.764 1642.18L287.951 1421.35C195.639 1348.79 142.037 1236.42 142.037 1117.54V690.458C142.037 571.58 195.639 459.21 287.951 386.652L574.764 165.821Z",fill:"#FDFEF9"}),(0,a.jsx)("mask",{id:"mask0",maskUnits:"userSpaceOnUse",x:"71",y:"180",width:"1408",height:"1484",children:(0,a.jsx)("path",{d:"M574.315 248.124C692.703 157.412 857.297 157.412 975.685 248.124L1262.5 468.955C1354.81 541.513 1408.41 653.884 1408.41 772.762V1199.84C1408.41 1318.72 1354.81 1431.09 1262.5 1503.65L975.685 1724.48C857.297 1815.19 692.703 1815.19 574.315 1724.48L287.502 1503.65C195.19 1431.09 141.588 1318.72 141.588 1199.84V772.762C141.588 653.884 195.19 541.513 287.502 468.955L574.315 248.124Z",fill:"#CCCCCC"})}),(0,a.jsx)("g",{mask:"url(#mask0)",children:(0,a.jsx)("rect",{x:"48",y:"146",width:"1454",height:"821",fill:"#CCCCCC"})}),(0,a.jsx)("path",{d:"M658.6 1303.6C658.6 1257.73 713.743 1221.6 783 1221.6C852.257 1221.6 907.4 1257.73 907.4 1303.6C907.4 1328.13 861.847 1343.6 783 1343.6C704.153 1343.6 658.6 1328.13 658.6 1303.6Z",fill:"#CCCCCC"}),(0,a.jsx)("path",{d:"M771.553 390C809.071 390 837.553 418.482 837.553 456C837.553 475.694 829.359 495.486 814.097 508.145L776.947 539.162C772.316 543.05 766.79 545.197 761.062 545.197C755.335 545.197 749.809 543.05 745.178 539.162L707.303 508.09C691.856 495.367 683.553 475.418 683.553 455.5C683.553 417.982 712.035 390 749.553 390H771.553Z",fill:"white"}),(0,a.jsx)("path",{d:"M774.5 828C743.884 828 719 852.884 719 883.5C719 914.116 743.884 939 774.5 939C805.116 939 830 914.116 830 883.5C830 852.884 805.116 828 774.5 828Z",fill:"#CCCCCC"}),(0,a.jsx)("path",{d:"M1043.07 450.583L1028.87 437.844C1028.55 437.56 1028.07 437.593 1027.79 437.917L1002.56 467.325C1002.28 467.648 1002.31 468.13 1002.64 468.413L1016.84 481.152C1017.16 481.436 1017.64 481.403 1017.93 481.079L1043.16 451.671C1043.44 451.348 1043.41 450.866 1043.07 450.583Z",fill:"white"}),(0,a.jsx)("path",{d:"M1044.49 451.93C1044.25 451.709 1043.87 451.743 1043.66 452.012L1018.13 484.509C1017.92 484.778 1017.95 485.158 1018.22 485.371L1020.46 487.151C1020.73 487.365 1021.11 487.331 1021.32 487.062L1046.85 454.565C1047.06 454.296 1047.03 453.916 1046.76 453.702L1044.49 451.93Z",fill:"white"}),(0,a.jsx)("path",{d:"M1044.53 453.612L1018.39 486.109C1018.17 486.385 1018.2 486.794 1018.46 487.033L1023.27 491.522C1023.53 491.76 1023.95 491.73 1024.18 491.454L1050.32 458.957C1050.54 458.681 1050.51 458.272 1050.25 458.033L1045.44 453.544C1045.18 453.306 1044.77 453.336 1044.53 453.612Z",fill:"white"}),(0,a.jsx)("text",{x:"50%",y:"80%",textAnchor:"middle",fill:"#222",fontSize:"300",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:e})]});var i=r(30474);function o({badge:e}){return e?.badges?.length?(0,a.jsx)("div",{className:"flex gap-3 mt-2",children:e.badges.map((e,t)=>(0,a.jsx)(s.P.div,{className:"relative",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},children:"DailyStreak"===e.badgeType?(0,a.jsx)(n,{count:e.count??0}):(0,a.jsx)(i.default,{src:e.badgeSrc??"/placeholder.png",alt:e.badgeAlt??"Badge",width:48,height:48,className:"object-contain sm:w-12 sm:h-12 w-10 h-10"})},t))}):null}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50463:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var a=r(60687),s=r(43210),n=r(29523),i=r(92363),o=r(92449),l=r(88920),c=r(90269),d=r(46303),u=r(30474),h=r(69587),m=r(20181),p={};!function e(t,r,a,s){var n,i,o,l,c,d,u,h,m,p,f,x=!!(t.Worker&&t.Blob&&t.Promise&&t.OffscreenCanvas&&t.OffscreenCanvasRenderingContext2D&&t.HTMLCanvasElement&&t.HTMLCanvasElement.prototype.transferControlToOffscreen&&t.URL&&t.URL.createObjectURL),g="function"==typeof Path2D&&"function"==typeof DOMMatrix;function b(){}function w(e){var a=r.exports.Promise,s=void 0!==a?a:t.Promise;return"function"==typeof s?new s(e):(e(b,b),null)}var y=(n=function(){if(!t.OffscreenCanvas)return!1;var e=new OffscreenCanvas(1,1),r=e.getContext("2d");r.fillRect(0,0,1,1);var a=e.transferToImageBitmap();try{r.createPattern(a,"no-repeat")}catch(e){return!1}return!0}(),i=new Map,{transform:function(e){if(n)return e;if(i.has(e))return i.get(e);var t=new OffscreenCanvas(e.width,e.height);return t.getContext("2d").drawImage(e,0,0),i.set(e,t),t},clear:function(){i.clear()}}),v=(c=Math.floor(1e3/60),d={},u=0,"function"==typeof requestAnimationFrame&&"function"==typeof cancelAnimationFrame?(o=function(e){var t=Math.random();return d[t]=requestAnimationFrame(function r(a){u===a||u+c-1<a?(u=a,delete d[t],e()):d[t]=requestAnimationFrame(r)}),t},l=function(e){d[e]&&cancelAnimationFrame(d[e])}):(o=function(e){return setTimeout(e,c)},l=function(e){return clearTimeout(e)}),{frame:o,cancel:l}),j=(p={},function(){if(h)return h;if(!a&&x){var t=["var CONFETTI, SIZE = {}, module = {};","("+e.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{h=new Worker(URL.createObjectURL(new Blob([t])))}catch(e){return"function"==typeof console.warn&&console.warn("\uD83C\uDF8A Could not load worker",e),null}!function(e){function t(t,r){e.postMessage({options:t||{},callback:r})}e.init=function(t){var r=t.transferControlToOffscreen();e.postMessage({canvas:r},[r])},e.fire=function(r,a,s){if(m)return t(r,null),m;var n=Math.random().toString(36).slice(2);return m=w(function(a){function i(t){t.data.callback===n&&(delete p[n],e.removeEventListener("message",i),m=null,y.clear(),s(),a())}e.addEventListener("message",i),t(r,n),p[n]=i.bind(null,{data:{callback:n}})})},e.reset=function(){for(var t in e.postMessage({reset:!0}),p)p[t](),delete p[t]}}(h)}return h}),C={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function M(e,t,r){var a,s;return s=e&&null!=e[t]?e[t]:C[t],r?r(s):s}function N(e){return e<0?0:Math.floor(e)}function S(e){return parseInt(e,16)}function P(e){return e.map(E)}function E(e){var t=String(e).replace(/[^0-9a-f]/gi,"");return t.length<6&&(t=t[0]+t[0]+t[1]+t[1]+t[2]+t[2]),{r:S(t.substring(0,2)),g:S(t.substring(2,4)),b:S(t.substring(4,6))}}function I(e){e.width=document.documentElement.clientWidth,e.height=document.documentElement.clientHeight}function L(e){var t=e.getBoundingClientRect();e.width=t.width,e.height=t.height}function k(e,r){var n,i=!e,o=!!M(r||{},"resize"),l=!1,c=M(r,"disableForReducedMotion",Boolean),d=x&&M(r||{},"useWorker")?j():null,u=i?I:L,h=!!e&&!!d&&!!e.__confetti_initialized,m="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function p(r){var p,f=c||M(r,"disableForReducedMotion",Boolean),x=M(r,"zIndex",Number);if(f&&m)return w(function(e){e()});i&&n?e=n.canvas:i&&!e&&((p=document.createElement("canvas")).style.position="fixed",p.style.top="0px",p.style.left="0px",p.style.pointerEvents="none",p.style.zIndex=x,e=p,document.body.appendChild(e)),o&&!h&&u(e);var b={width:e.width,height:e.height};function j(){if(d){var t={getBoundingClientRect:function(){if(!i)return e.getBoundingClientRect()}};u(t),d.postMessage({resize:{width:t.width,height:t.height}});return}b.width=b.height=null}function C(){n=null,o&&(l=!1,t.removeEventListener("resize",j)),i&&e&&(document.body.contains(e)&&document.body.removeChild(e),e=null,h=!1)}return(d&&!h&&d.init(e),h=!0,d&&(e.__confetti_initialized=!0),o&&!l&&(l=!0,t.addEventListener("resize",j,!1)),d)?d.fire(r,b,C):function(t,r,i){for(var o,l,c,d,h,m,p,f=M(t,"particleCount",N),x=M(t,"angle",Number),b=M(t,"spread",Number),j=M(t,"startVelocity",Number),C=M(t,"decay",Number),S=M(t,"gravity",Number),E=M(t,"drift",Number),I=M(t,"colors",P),L=M(t,"ticks",Number),k=M(t,"shapes"),T=M(t,"scalar"),_=!!M(t,"flat"),z=((o=M(t,"origin",Object)).x=M(o,"x",Number),o.y=M(o,"y",Number),o),A=f,F=[],O=e.width*z.x,B=e.height*z.y;A--;)F.push(function(e){var t=e.angle*(Math.PI/180),r=e.spread*(Math.PI/180);return{x:e.x,y:e.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*e.startVelocity+Math.random()*e.startVelocity,angle2D:-t+(.5*r-Math.random()*r),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:e.color,shape:e.shape,tick:0,totalTicks:e.ticks,decay:e.decay,drift:e.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*e.gravity,ovalScalar:.6,scalar:e.scalar,flat:e.flat}}({x:O,y:B,angle:x,spread:b,startVelocity:j,color:I[A%I.length],shape:k[Math.floor(Math.random()*(k.length-0))+0],ticks:L,decay:C,gravity:S,drift:E,scalar:T,flat:_}));return n?n.addFettis(F):(l=e,h=F.slice(),m=l.getContext("2d"),p=w(function(e){function t(){c=d=null,m.clearRect(0,0,r.width,r.height),y.clear(),i(),e()}c=v.frame(function e(){a&&(r.width!==s.width||r.height!==s.height)&&(r.width=l.width=s.width,r.height=l.height=s.height),r.width||r.height||(u(l),r.width=l.width,r.height=l.height),m.clearRect(0,0,r.width,r.height),(h=h.filter(function(e){return function(e,t){t.x+=Math.cos(t.angle2D)*t.velocity+t.drift,t.y+=Math.sin(t.angle2D)*t.velocity+t.gravity,t.velocity*=t.decay,t.flat?(t.wobble=0,t.wobbleX=t.x+10*t.scalar,t.wobbleY=t.y+10*t.scalar,t.tiltSin=0,t.tiltCos=0,t.random=1):(t.wobble+=t.wobbleSpeed,t.wobbleX=t.x+10*t.scalar*Math.cos(t.wobble),t.wobbleY=t.y+10*t.scalar*Math.sin(t.wobble),t.tiltAngle+=.1,t.tiltSin=Math.sin(t.tiltAngle),t.tiltCos=Math.cos(t.tiltAngle),t.random=Math.random()+2);var r,a,s,n,i,o,l,c,d,u,h,m,p,f,x,b,w=t.tick++/t.totalTicks,v=t.x+t.random*t.tiltCos,j=t.y+t.random*t.tiltSin,C=t.wobbleX+t.random*t.tiltCos,M=t.wobbleY+t.random*t.tiltSin;if(e.fillStyle="rgba("+t.color.r+", "+t.color.g+", "+t.color.b+", "+(1-w)+")",e.beginPath(),g&&"path"===t.shape.type&&"string"==typeof t.shape.path&&Array.isArray(t.shape.matrix)){e.fill((r=t.shape.path,a=t.shape.matrix,s=t.x,n=t.y,i=.1*Math.abs(C-v),o=.1*Math.abs(M-j),l=Math.PI/10*t.wobble,c=new Path2D(r),(d=new Path2D).addPath(c,new DOMMatrix(a)),(u=new Path2D).addPath(d,new DOMMatrix([Math.cos(l)*i,Math.sin(l)*i,-Math.sin(l)*o,Math.cos(l)*o,s,n])),u))}else if("bitmap"===t.shape.type){var N=Math.PI/10*t.wobble,S=.1*Math.abs(C-v),P=.1*Math.abs(M-j),E=t.shape.bitmap.width*t.scalar,I=t.shape.bitmap.height*t.scalar,L=new DOMMatrix([Math.cos(N)*S,Math.sin(N)*S,-Math.sin(N)*P,Math.cos(N)*P,t.x,t.y]);L.multiplySelf(new DOMMatrix(t.shape.matrix));var k=e.createPattern(y.transform(t.shape.bitmap),"no-repeat");k.setTransform(L),e.globalAlpha=1-w,e.fillStyle=k,e.fillRect(t.x-E/2,t.y-I/2,E,I),e.globalAlpha=1}else if("circle"===t.shape)e.ellipse?e.ellipse(t.x,t.y,Math.abs(C-v)*t.ovalScalar,Math.abs(M-j)*t.ovalScalar,Math.PI/10*t.wobble,0,2*Math.PI):(h=t.x,m=t.y,p=Math.abs(C-v)*t.ovalScalar,f=Math.abs(M-j)*t.ovalScalar,x=Math.PI/10*t.wobble,b=2*Math.PI,e.save(),e.translate(h,m),e.rotate(x),e.scale(p,f),e.arc(0,0,1,0,b,void 0),e.restore());else if("star"===t.shape)for(var T=Math.PI/2*3,_=4*t.scalar,z=8*t.scalar,A=t.x,F=t.y,O=5,B=Math.PI/5;O--;)A=t.x+Math.cos(T)*z,F=t.y+Math.sin(T)*z,e.lineTo(A,F),T+=B,A=t.x+Math.cos(T)*_,F=t.y+Math.sin(T)*_,e.lineTo(A,F),T+=B;else e.moveTo(Math.floor(t.x),Math.floor(t.y)),e.lineTo(Math.floor(t.wobbleX),Math.floor(j)),e.lineTo(Math.floor(C),Math.floor(M)),e.lineTo(Math.floor(v),Math.floor(t.wobbleY));return e.closePath(),e.fill(),t.tick<t.totalTicks}(m,e)})).length?c=v.frame(e):t()}),d=t}),(n={addFettis:function(e){return h=h.concat(e),p},canvas:l,promise:p,reset:function(){c&&v.cancel(c),d&&d()}}).promise)}(r,b,C)}return p.reset=function(){d&&d.reset(),n&&n.reset()},p}function T(){return f||(f=k(null,{useWorker:!0,resize:!0})),f}r.exports=function(){return T().apply(this,arguments)},r.exports.reset=function(){T().reset()},r.exports.create=k,r.exports.shapeFromPath=function(e){if(!g)throw Error("path confetti are not supported in this browser");"string"==typeof e?a=e:(a=e.path,s=e.matrix);var t=new Path2D(a),r=document.createElement("canvas").getContext("2d");if(!s){for(var a,s,n,i,o=1e3,l=1e3,c=0,d=0,u=0;u<1e3;u+=2)for(var h=0;h<1e3;h+=2)r.isPointInPath(t,u,h,"nonzero")&&(o=Math.min(o,u),l=Math.min(l,h),c=Math.max(c,u),d=Math.max(d,h));n=c-o;var m=Math.min(10/n,10/(i=d-l));s=[m,0,0,m,-Math.round(n/2+o)*m,-Math.round(i/2+l)*m]}return{type:"path",path:a,matrix:s}},r.exports.shapeFromText=function(e){var t,r=1,a="#000000",s='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';"string"==typeof e?t=e:(t=e.text,r="scalar"in e?e.scalar:r,s="fontFamily"in e?e.fontFamily:s,a="color"in e?e.color:a);var n=10*r,i=""+n+"px "+s,o=new OffscreenCanvas(n,n),l=o.getContext("2d");l.font=i;var c=l.measureText(t),d=Math.ceil(c.actualBoundingBoxRight+c.actualBoundingBoxLeft),u=Math.ceil(c.actualBoundingBoxAscent+c.actualBoundingBoxDescent),h=c.actualBoundingBoxLeft+2,m=c.actualBoundingBoxAscent+2;d+=4,u+=4,(l=(o=new OffscreenCanvas(d,u)).getContext("2d")).font=i,l.fillStyle=a,l.fillText(t,h,m);var p=1/r;return{type:"bitmap",bitmap:o.transferToImageBitmap(),matrix:[p,0,0,p,-d*p/2,-u*p/2]}}}(function(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:this||{}}(),p,!1);let f=p.exports;p.exports.create;var x=r(8380),g=r(52581),b=r(4780);let w=["Today","Weekly","All time"];function y(){let[e,t]=(0,s.useState)("Today"),[r,p]=(0,s.useState)([]),[y,v]=(0,s.useState)(0),[j,C]=(0,s.useState)(!1),[M,N]=(0,s.useState)(1),[S,P]=(0,s.useState)(!1),[E,I]=(0,s.useState)(null),[L,k]=(0,s.useState)({}),[T,_]=(0,s.useState)(null),z=(0,s.useRef)({}),A=(0,s.useRef)(null),F=null;try{let e=localStorage.getItem("student_data");F=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),F=null}let O=e=>e>=100&&e<=499?"/scholer.svg":e>=500&&e<=999?"/Mastermind.svg":e>=1e3?"/Achiever.svg":null,B=e=>{let t=z.current[e];if(t){let e=t.getBoundingClientRect();f({particleCount:100,spread:70,origin:{x:(e.left+e.width/2)/window.innerWidth,y:(e.top+e.height/2)/window.innerHeight},disableForReducedMotion:!0,zIndex:1e3})}},R=async(e,t)=>{try{return(await (0,x.dS)(e,t,10)).data}catch(e){throw Error("Failed to fetch leaderboard: "+e.message)}},D=async(e,t)=>{if(F&&e!==F){let r=L[e];if(r?.reaction!==t){k(r=>{let a=r[e]?.reaction,s={...r[e]?.counts||{}};return a&&(s[a]=Math.max((s[a]||1)-1,0)),s[t]=(s[t]||0)+1,{...r,[e]:{reaction:t,counts:s}}});try{await (0,x.FU)(e,t,F),B(e)}catch(e){console.error("Failed to save reaction:",e),I("Failed to save reaction: "+e.message)}_(null)}}else g.toast.success("Please Login to Celebrate!")},$=async()=>{C(!0),I(null);let t=M+1,r=e.toLowerCase().replace(" ","-");try{let e=await R(r,t);p(t=>[...t,...e.data]),k(t=>({...t,...e.reactions})),N(t)}catch(e){I("Failed to load more records: "+(e instanceof Error?e.message:String(e)))}C(!1)},q=r.length<y,U=(e,t=120,r=!1)=>{let s=(e.firstName?.charAt(0)||"")+(e.lastName?.charAt(0)||""),n=(0,a.jsx)("div",{style:{width:t,height:t},className:"flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-2xl border-4 border-customOrange overflow-hidden",children:e.profilePhoto&&""!==e.profilePhoto.trim()?(0,a.jsx)(u.default,{src:`http://localhost:4005/${e.profilePhoto}`,alt:`${e.firstName||""} ${e.lastName||""}`,width:t,height:t,className:"object-cover rounded-full w-full h-full"}):(0,a.jsx)("span",{children:s})});return r?(0,a.jsx)(o.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:n}):n},Z=e=>(0,a.jsx)(l.N,{children:(0,a.jsx)(o.P.div,{ref:A,initial:{scale:.8,opacity:0,y:10},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:10},transition:{duration:.2},className:(0,b.cn)("absolute bottom-14 z-30 flex bg-white p-2 rounded-lg shadow-lg border border-gray-100 gap-1.5","flex-col right-2 left-auto","sm:flex-row sm:left-1/2 sm:-translate-x-1/2 sm:right-auto"),children:[{emoji:"\uD83D\uDC4D",label:"thumbsup",color:"bg-blue-50 border-blue-200"},{emoji:"\uD83D\uDE19",label:"whistle",color:"bg-green-50 border-green-200"},{emoji:"\uD83C\uDF89",label:"party",color:"bg-yellow-50 border-yellow-200"},{emoji:"\uD83D\uDC4F",label:"clap",color:"bg-red-50 border-red-200"},{emoji:"\uD83D\uDE23",label:"angry",color:"bg-orange-50 border-orange-200"},{emoji:"\uD83D\uDC4E",label:"thumbsdown",color:"bg-purple-50 border-purple-200"}].map(t=>(0,a.jsxs)(o.P.button,{onClick:()=>D(e,t.label),"aria-label":t.label,whileHover:{scale:1.05},whileTap:{scale:.95},className:`flex items-center justify-between w-full h-6 rounded-md ${t.color} border transition-all duration-200 px-1 sm:w-12 sm:h-7 sm:px-1.5 min-w-[40px]`,children:[(0,a.jsx)("span",{className:"text-sm",children:t.emoji}),(0,a.jsx)("span",{className:"text-xs font-semibold text-gray-700",children:L[e]?.counts?.[t.label]||0})]},t.label))})}),G=r.slice(0,3),H=r.slice(3);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.default,{}),(0,a.jsx)("div",{className:"min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-4 flex justify-center",children:(0,a.jsxs)("div",{className:"w-full max-w-5xl space-y-6 sm:space-y-8 pt-8",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange",children:"Daily Quiz Leaderboard"}),(0,a.jsx)("p",{className:"text-center text-sm sm:text-base text-muted-foreground font-medium",children:"\uD83C\uDF81 Top 3 students get free classmate books everyday!"}),(0,a.jsx)("div",{className:"flex justify-center gap-4 sm:gap-10 overflow-x-auto",children:w.map(r=>(0,a.jsxs)(n.$,{variant:e===r?"default":"outline",className:`rounded-full px-4 sm:px-6 py-1 sm:py-2 text-sm sm:text-base font-semibold ${e===r?"text-white":"border-orange-400 text-orange-400"} whitespace-nowrap`,"aria-label":`Select ${r} leaderboard`,onClick:()=>{t(r),N(1)},children:[r," ",e===r&&"("+y+")"]},r))}),S&&(0,a.jsx)("p",{className:"text-center text-gray-500",children:"Loading..."}),E&&(0,a.jsx)("p",{className:"text-center text-red-500",children:E}),!S&&!E&&(0,a.jsx)("div",{className:"flex flex-col sm:flex-row justify-around items-center sm:items-end gap-4 sm:gap-6 mt-6 sm:mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg",children:G.map((e,t)=>(0,a.jsx)(o.P.div,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2*t},className:`flex flex-col items-center mt-10 ${0===t?"order-2":1===t?"order-1":"order-3"} relative`,children:(0,a.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,a.jsxs)("div",{className:`relative rounded-full border-4 p-2 ${0===t?"shadow-2xl scale-110 border-customOrange":"border-orange-500"}`,children:[0===t&&(0,a.jsx)(i.A,{className:"absolute -top-6 sm:-top-8 left-1/2 -translate-x-1/2 text-customOrange w-6 sm:w-8 h-6 sm:h-8"}),U(e,64,0===t),(0,a.jsx)("div",{className:`absolute -bottom-4 sm:-bottom-5 left-1/2 -translate-x-1/2 rounded-full flex items-center justify-center font-bold ${0===t?"w-7 h-7 sm:w-9 sm:h-9 bg-orange-500 text-white shadow-lg border-4 border-orange-500":1===t?"w-6 h-6 sm:w-8 sm:h-8 bg-orange-500 text-white shadow border-4 border-orange-500":"w-5 h-5 sm:w-7 sm:h-7 bg-orange-500 text-white border-4 border-orange-500"}`,children:e.rank})]}),(0,a.jsxs)("p",{ref:t=>{z.current[e.studentId]=t},className:"mt-6 sm:mt-8 font-semibold text-base sm:text-lg text-center relative",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"mt-2 w-full flex justify-center",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 sm:gap-3",children:[O(e.coinEarnings)&&(0,a.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,a.jsx)(u.default,{src:O(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,a.jsx)(m.A,{badge:e.badge})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-2 sm:gap-5 mt-2",children:[(0,a.jsx)("div",{className:`px-3 sm:px-4 py-1 rounded-full border border-orange-300 text-orange-600 font-bold text-xs sm:text-sm ${0===t?"animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"}`,children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.lHQ,{})," ",e.score]})}),(0,a.jsxs)("div",{className:`px-3 sm:px-4 py-1 rounded-full border border-green-300 text-green-600 font-bold text-xs sm:text-sm flex items-center gap-1 ${0===t?"animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"}`,children:[(0,a.jsx)(u.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,a.jsxs)("div",{className:`px-3 sm:px-4 py-1 rounded-full border border-blue-300 text-blue-600 font-bold text-xs sm:text-sm ${0===t?"animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-blue-100"}`,children:["\uD83D\uDD25 ",e.streakCount]})]}),e.studentId!==F&&(0,a.jsxs)("div",{className:"mt-3 relative flex justify-center",children:[(0,a.jsx)(n.$,{variant:"outline",className:"rounded-full px-4 py-1 text-sm border-gray-300 text-gray-600 hover:bg-gray-100 min-w-[80px] sm:min-w-[100px]",onClick:()=>_(T===e.studentId?null:e.studentId),children:(()=>{let t=L[e.studentId]?.counts;if(t){let e=Object.entries(t).reduce((e,[t,r])=>r>e.count?{reaction:t,count:r}:e,{reaction:"",count:-1});if(e.count>0&&e.reaction in t)return(0,a.jsxs)("span",{className:"flex items-center gap-1 truncate",children:[{thumbsup:"\uD83D\uDC4D",whistle:"\uD83D\uDE19",party:"\uD83C\uDF89",clap:"\uD83D\uDC4F",angry:"\uD83D\uDE23",thumbsdown:"\uD83D\uDC4E"}[e.reaction]," ",e.count]})}return"Celebrate"})()}),T===e.studentId&&Z(e.studentId)]})]})},e.studentId))}),!S&&!E&&(0,a.jsx)("div",{className:"rounded-lg mt-6 sm:mt-10 bg-white space-y-3 sm:space-y-4",children:H.map(e=>(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between p-3 sm:p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 sm:gap-4 w-full sm:w-auto",children:[(0,a.jsx)("div",{className:"relative flex items-center justify-center w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-orange-100 text-orange-500 font-bold text-sm sm:text-lg",children:e.rank}),U(e,48),(0,a.jsxs)("p",{ref:t=>{z.current[e.studentId]=t},className:"font-semibold text-base sm:text-lg text-black relative",children:[e.firstName," ",e.lastName]}),e.studentId!==F&&(0,a.jsxs)("div",{className:"mt-3 relative",children:[(0,a.jsx)(n.$,{variant:"outline",className:"rounded-full px-4 py-1 text-sm border-gray-300 text-gray-600 hover:bg-gray-100",onClick:()=>_(T===e.studentId?null:e.studentId),children:(()=>{let t=L[e.studentId]?.counts;if(t){let e=Object.entries(t).reduce((e,[t,r])=>r>e.count?{reaction:t,count:r}:e,{reaction:"",count:-1});if(e.count>0)return(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[{thumbsup:"\uD83D\uDC4D",whistle:"\uD83D\uDE19",party:"\uD83C\uDF89",clap:"\uD83D\uDC4F",angry:"\uD83D\uDE23",thumbsdown:"\uD83D\uDC4E"}[e.reaction]," ",e.count]})}return"Celebrate"})()}),T===e.studentId&&Z(e.studentId)]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-3 sm:gap-5 mt-3 sm:mt-0 w-full sm:w-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[O(e.coinEarnings)&&(0,a.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,a.jsx)(u.default,{src:O(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,a.jsx)(m.A,{badge:e.badge})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap justify-center",children:[(0,a.jsx)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-orange-300 bg-orange-100 text-orange-600",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.lHQ,{className:"mr-1"})," ",e.score]})}),(0,a.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-green-300 bg-green-100 text-green-700 flex items-center gap-1",children:[(0,a.jsx)(u.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,a.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-blue-300 bg-blue-100 text-blue-700",children:["\uD83D\uDD25 ",e.streakCount]})]})]})]},e.studentId))}),q&&(0,a.jsx)("div",{className:"flex justify-center mt-6 sm:mt-8",children:(0,a.jsx)(n.$,{onClick:$,disabled:j,className:"px-6 sm:px-8 py-2 sm:py-3 rounded-full bg-customOrange text-white hover:bg-orange-600 disabled:bg-gray-300 text-sm sm:text-base font-semibold min-w-[120px]","aria-label":"Load more records",children:j?"Loading...":"Load More"})})]})}),(0,a.jsx)(d.default,{})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64151:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=r(65239),s=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["Leader-Board",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73243)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/Leader-Board/page",pathname:"/Leader-Board",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},72003:(e,t,r)=>{Promise.resolve().then(r.bind(r,50463))},73243:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\Leader-Board\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88920:(e,t,r)=>{"use strict";r.d(t,{N:()=>b});var a=r(60687),s=r(43210),n=r(12157),i=r(72789),o=r(15124),l=r(21279),c=r(18171),d=r(32582);class u extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,c.s)(e)&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=t.offsetHeight||0,a.width=t.offsetWidth||0,a.top=t.offsetTop,a.left=t.offsetLeft,a.right=r-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t,anchorX:r,root:n}){let i=(0,s.useId)(),o=(0,s.useRef)(null),l=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,s.useContext)(d.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:a,top:s,left:d,right:u}=l.current;if(t||!o.current||!e||!a)return;let h="left"===r?`left: ${d}`:`right: ${u}`;o.current.dataset.motionPopId=i;let m=document.createElement("style");c&&(m.nonce=c);let p=n??document.head;return p.appendChild(m),m.sheet&&m.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${h}px !important;
            top: ${s}px !important;
          }
        `),()=>{p.removeChild(m),p.contains(m)&&p.removeChild(m)}},[t]),(0,a.jsx)(u,{isPresent:t,childRef:o,sizeRef:l,children:s.cloneElement(e,{ref:o})})}let m=({children:e,initial:t,isPresent:r,onExitComplete:n,custom:o,presenceAffectsLayout:c,mode:d,anchorX:u,root:m})=>{let f=(0,i.M)(p),x=(0,s.useId)(),g=!0,b=(0,s.useMemo)(()=>(g=!1,{id:x,initial:t,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;n&&n()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[r,f,n]);return c&&g&&(b={...b}),(0,s.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[r]),s.useEffect(()=>{r||f.size||!n||n()},[r]),"popLayout"===d&&(e=(0,a.jsx)(h,{isPresent:r,anchorX:u,root:m,children:e})),(0,a.jsx)(l.t.Provider,{value:b,children:e})};function p(){return new Map}var f=r(86044);let x=e=>e.key||"";function g(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}let b=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:c=!0,mode:d="sync",propagate:u=!1,anchorX:h="left",root:p})=>{let[b,w]=(0,f.xQ)(u),y=(0,s.useMemo)(()=>g(e),[e]),v=u&&!b?[]:y.map(x),j=(0,s.useRef)(!0),C=(0,s.useRef)(y),M=(0,i.M)(()=>new Map),[N,S]=(0,s.useState)(y),[P,E]=(0,s.useState)(y);(0,o.E)(()=>{j.current=!1,C.current=y;for(let e=0;e<P.length;e++){let t=x(P[e]);v.includes(t)?M.delete(t):!0!==M.get(t)&&M.set(t,!1)}},[P,v.length,v.join("-")]);let I=[];if(y!==N){let e=[...y];for(let t=0;t<P.length;t++){let r=P[t],a=x(r);v.includes(a)||(e.splice(t,0,r),I.push(r))}return"wait"===d&&I.length&&(e=I),E(g(e)),S(y),null}let{forceRender:L}=(0,s.useContext)(n.L);return(0,a.jsx)(a.Fragment,{children:P.map(e=>{let s=x(e),n=(!u||!!b)&&(y===P||v.includes(s));return(0,a.jsx)(m,{isPresent:n,initial:(!j.current||!!r)&&void 0,custom:t,presenceAffectsLayout:c,mode:d,root:p,onExitComplete:n?void 0:()=>{if(!M.has(s))return;M.set(s,!0);let e=!0;M.forEach(t=>{t||(e=!1)}),e&&(L?.(),E(C.current),u&&w?.(),l&&l())},anchorX:h,children:e},s)})})}},92363:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7013,2449,2800],()=>r(64151));module.exports=a})();