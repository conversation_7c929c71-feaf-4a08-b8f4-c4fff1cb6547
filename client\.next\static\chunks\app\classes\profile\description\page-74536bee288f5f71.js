(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2990],{14050:(e,t,r)=>{"use strict";r.d(t,{b:()=>u});var a=r(12115);r(47650);var n=r(66634),i=r(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.TL)(`Primitive.${t}`),o=a.forwardRef((e,a)=>{let{asChild:n,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...o,ref:a})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),s="horizontal",l=["horizontal","vertical"],d=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:n=s,...d}=e,u=(r=n,l.includes(r))?n:s;return(0,i.jsx)(o.div,{"data-orientation":u,...a?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...d,ref:t})});d.displayName="Separator";var u=d},22346:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>o});var a=r(95155);r(12115);var n=r(14050),i=r(59434);function o(e){let{className:t,orientation:r="horizontal",decorative:o=!0,...s}=e;return(0,a.jsx)(n.b,{"data-slot":"separator-root",decorative:o,orientation:r,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...s})}},24265:(e,t,r)=>{"use strict";r.d(t,{b:()=>u});var a=r(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(47650);var i=r(95155),o=Symbol("radix.slottable");function s(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...i}=e;if(a.isValidElement(r)){var o;let e,s;let l=(o=r,(s=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(s=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,t){let r={...t};for(let a in t){let n=e[a],i=t[a];/^on[A-Z]/.test(a)?n&&i?r[a]=(...e)=>{i(...e),n(...e)}:n&&(r[a]=n):"style"===a?r[a]={...n,...i}:"className"===a&&(r[a]=[n,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==a.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}(t,l):l),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...o}=e,l=a.Children.toArray(n),d=l.find(s);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,i.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),o=a.forwardRef((e,a)=>{let{asChild:n,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...o,ref:a})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),d=a.forwardRef((e,t)=>(0,i.jsx)(l.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var u=d},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>s});var a=r(95155);r(12115);var n=r(66634),i=r(74466),o=r(59434);let s=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...d}=e,u=l?n.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,o.cn)(s({variant:r,size:i,className:t})),...d})}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},39096:(e,t,r)=>{Promise.resolve().then(r.bind(r,47164)),Promise.resolve().then(r.bind(r,22346))},45436:(e,t,r)=>{"use strict";r.d(t,{V:()=>n});var a=r(55077);let n=(0,r(51990).zD)("class/fetchClassDetails",async(e,t)=>{let{rejectWithValue:r}=t;try{return(await a.S.get("/classes/details/".concat(e))).data}catch(e){var n;return r((null===(n=e.response)||void 0===n?void 0:n.data)||"Fetch failed")}})},47164:(e,t,r)=>{"use strict";r.d(t,{DescriptionForm:()=>x});var a=r(95155),n=r(90221),i=r(62177),o=r(55594),s=r(56671),l=r(34540),d=r(94314),u=r(35695),c=r(30285),p=r(75937),m=r(62523),f=r(88539),v=r(55077),h=r(12115),g=r(45436);let b=o.z.object({headline:o.z.string().min(10,{message:"Headline must be at least 10 characters."}).max(100,{message:"Headline must be less than 100 characters."}),description:o.z.string().min(100,{message:"Bio must be at least 100 characters."}).max(1e3,{message:"Bio must be less than 1000 characters."})});function x(){let e=(0,i.mN)({resolver:(0,n.u)(b),defaultValues:{headline:"",description:""}}),t=(0,l.wA)(),r=(0,u.useRouter)(),{user:o}=(0,l.d4)(e=>e.user),x=async e=>{try{await v.S.post("/classes-profile/description",e),await t((0,g.V)(o.id)),s.toast.success("Profile updated successfully!"),t((0,d.ac)(d._3.DESCRIPTION)),r.push("/classes/profile/photo-and-logo")}catch(e){console.error("Error submitting form:",e),s.toast.error("Failed to update profile")}},{reset:y}=e,w=(0,l.d4)(e=>e.class.classData);return(0,h.useEffect)(()=>{var e,t,r,a;((null==w?void 0:null===(e=w.ClassAbout)||void 0===e?void 0:e.tutorBio)||(null==w?void 0:null===(t=w.ClassAbout)||void 0===t?void 0:t.catchyHeadline))&&y({headline:(null==w?void 0:null===(r=w.ClassAbout)||void 0===r?void 0:r.catchyHeadline)||"",description:(null==w?void 0:null===(a=w.ClassAbout)||void 0===a?void 0:a.tutorBio)||""})},[w,y]),(0,a.jsx)(p.lV,{...e,children:(0,a.jsxs)("form",{onSubmit:e.handleSubmit(x),className:"space-y-6",children:[(0,a.jsx)(p.zB,{control:e.control,name:"headline",render:e=>{let{field:t}=e;return(0,a.jsxs)(p.eI,{children:[(0,a.jsx)(p.lR,{children:"Catchy Headline"}),(0,a.jsx)(p.MJ,{children:(0,a.jsx)(m.p,{placeholder:"e.g. Passionate Math Tutor with 7+ Years of Experience",...t})}),(0,a.jsx)(p.C5,{})]})}}),(0,a.jsx)(p.zB,{control:e.control,name:"description",render:e=>{let{field:t}=e;return(0,a.jsxs)(p.eI,{children:[(0,a.jsx)(p.lR,{children:"Tutor Bio"}),(0,a.jsx)(p.MJ,{children:(0,a.jsx)(f.T,{placeholder:"1. Introduce yourself\n2. Teaching experience\n3. Motivate potential students",rows:6,...t})}),(0,a.jsx)(p.C5,{})]})}}),(0,a.jsx)(c.$,{type:"submit",children:"Save Profile"})]})})}},55077:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var a=r(23464),n=r(56671);let i="http://localhost:4005/api/v1";console.log("Axios baseURL:",i);let o=a.A.create({baseURL:i,headers:{"Content-Type":"application/json"},withCredentials:!0});o.interceptors.request.use(e=>{let t=e.headers["Server-Select"];e.baseURL="uwhizServer"===t?"http://localhost:4006":i;{let t=localStorage.getItem("studentToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(n.toast.error(e.response.data.message||"Unauthorized"),localStorage.removeItem("user"),localStorage.removeItem("studentToken"),localStorage.removeItem("student_data"),window.location.replace("/?authError=1")),Promise.reject(e)))},59434:(e,t,r)=>{"use strict";r.d(t,{MB:()=>s,ZO:()=>o,cn:()=>i,wR:()=>d,xh:()=>l});var a=r(52596),n=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}let o=()=>localStorage.getItem("studentToken"),s=()=>{localStorage.removeItem("studentToken")},l=()=>!!o(),d=()=>{if(o())return{isAuth:!0,userType:"STUDENT"};{let e=localStorage.getItem("user");if(e)try{let t=JSON.parse(e);if(t&&t.id)return{isAuth:!0,userType:"CLASS"}}catch(e){console.error("Error parsing user data:",e)}}return{isAuth:!1,userType:null}}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},75937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>u,MJ:()=>g,Rr:()=>b,zB:()=>p,eI:()=>v,lR:()=>h,C5:()=>x});var a=r(95155),n=r(12115),i=r(66634),o=r(62177),s=r(59434),l=r(24265);function d(e){let{className:t,...r}=e;return(0,a.jsx)(l.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}let u=o.Op,c=n.createContext({}),p=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(o.xI,{...t})})},m=()=>{let e=n.useContext(c),t=n.useContext(f),{getFieldState:r}=(0,o.xW)(),a=(0,o.lN)({name:e.name}),i=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:"".concat(s,"-form-item"),formDescriptionId:"".concat(s,"-form-item-description"),formMessageId:"".concat(s,"-form-item-message"),...i}},f=n.createContext({});function v(e){let{className:t,...r}=e,i=n.useId();return(0,a.jsx)(f.Provider,{value:{id:i},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,s.cn)("grid gap-2",t),...r})})}function h(e){let{className:t,...r}=e,{error:n,formItemId:i}=m();return(0,a.jsx)(d,{"data-slot":"form-label","data-error":!!n,className:(0,s.cn)("data-[error=true]:text-destructive",t),htmlFor:i,...r})}function g(e){let{...t}=e,{error:r,formItemId:n,formDescriptionId:o,formMessageId:s}=m();return(0,a.jsx)(i.DX,{"data-slot":"form-control",id:n,"aria-describedby":r?"".concat(o," ").concat(s):"".concat(o),"aria-invalid":!!r,...t})}function b(e){let{className:t,...r}=e,{formDescriptionId:n}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:n,className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function x(e){var t;let{className:r,...n}=e,{error:i,formMessageId:o}=m(),l=i?String(null!==(t=null==i?void 0:i.message)&&void 0!==t?t:""):n.children;return l?(0,a.jsx)("p",{"data-slot":"form-message",id:o,className:(0,s.cn)("text-destructive text-sm",r),...n,children:l}):null}},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}},94314:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,_3:()=>n,ac:()=>o});var a=r(51990),n=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let i=(0,a.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let r=t.payload;e.completedForms[r]||(e.completedForms[r]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:o,setCurrentStep:s}=i.actions,l=i.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[7040,5186,4540,1990,1342,8441,1684,7358],()=>t(39096)),_N_E=e.O()}]);