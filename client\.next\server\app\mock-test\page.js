(()=>{var e={};e.id=2328,e.ids=[2328],e.modules={1508:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\mock-test\\\\mockExam.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6174:(e,t,r)=>{"undefined"!=typeof process&&"renderer"===process.type?e.exports=r(50622):e.exports=r(14664)},10380:(e,t,r)=>{var n=r(6174)("jsonp");e.exports=function(e,t,r){"function"==typeof t&&(r=t,t={}),t||(t={});var i,o,l=t.prefix||"__jp",c=t.name||l+a++,u=t.param||"callback",d=null!=t.timeout?t.timeout:6e4,m=encodeURIComponent,h=document.getElementsByTagName("script")[0]||document.head;function p(){i.parentNode&&i.parentNode.removeChild(i),window[c]=s,o&&clearTimeout(o)}return d&&(o=setTimeout(function(){p(),r&&r(Error("Timeout"))},d)),window[c]=function(e){n("jsonp got",e),p(),r&&r(null,e)},e+=(~e.indexOf("?")?"&":"?")+u+"="+m(c),n('jsonp req "%s"',e=e.replace("?&","?")),(i=document.createElement("script")).src=e,h.parentNode.insertBefore(i,h),function(){window[c]&&p()}};var a=0;function s(){}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14664:(e,t,r)=>{var n=r(83997),a=r(28354);(t=e.exports=r(35781)).init=function(e){e.inspectOpts={};for(var r=Object.keys(t.inspectOpts),n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(){return i.write(a.format.apply(a,arguments)+"\n")},t.formatArgs=function(e){var r=this.namespace;if(this.useColors){var n=this.color,a="  \x1b[3"+n+";1m"+r+" \x1b[0m";e[0]=a+e[0].split("\n").join("\n"+a),e.push("\x1b[3"+n+"m+"+t.humanize(this.diff)+"\x1b[0m")}else e[0]=new Date().toUTCString()+" "+r+" "+e[0]},t.save=function(e){null==e?delete process.env.DEBUG:process.env.DEBUG=e},t.load=o,t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(s)},t.colors=[6,2,3,4,5,1],t.inspectOpts=Object.keys(process.env).filter(function(e){return/^debug_/i.test(e)}).reduce(function(e,t){var r=t.substring(6).toLowerCase().replace(/_([a-z])/g,function(e,t){return t.toUpperCase()}),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{});var s=parseInt(process.env.DEBUG_FD,10)||2;1!==s&&2!==s&&a.deprecate(function(){},"except for stderr(2) and stdout(1), any other usage of DEBUG_FD is deprecated. Override debug.log if you want to use a different log function (https://git.io/debug_fd)")();var i=1===s?process.stdout:2===s?process.stderr:function(e){var t;switch(process.binding("tty_wrap").guessHandleType(e)){case"TTY":(t=new n.WriteStream(e))._type="tty",t._handle&&t._handle.unref&&t._handle.unref();break;case"FILE":(t=new(r(29021)).SyncWriteStream(e,{autoClose:!1}))._type="fs";break;case"PIPE":case"TCP":(t=new(r(91645)).Socket({fd:e,readable:!1,writable:!0})).readable=!1,t.read=null,t._type="pipe",t._handle&&t._handle.unref&&t._handle.unref();break;default:throw Error("Implement me. Unknown stream file type!")}return t.fd=e,t._isStdio=!0,t}(s);function o(){return process.env.DEBUG}t.formatters.o=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts).split("\n").map(function(e){return e.trim()}).join(" ")},t.formatters.O=function(e){return this.inspectOpts.colors=this.useColors,a.inspect(e,this.inspectOpts)},t.enable(o())},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23031:(e,t,r)=>{Promise.resolve().then(r.bind(r,77876))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35781:(e,t,r)=>{var n;function a(e){function r(){if(r.enabled){var e=+new Date;r.diff=e-(n||e),r.prev=n,r.curr=e,n=e;for(var a=Array(arguments.length),s=0;s<a.length;s++)a[s]=arguments[s];a[0]=t.coerce(a[0]),"string"!=typeof a[0]&&a.unshift("%O");var i=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,function(e,n){if("%%"===e)return e;i++;var s=t.formatters[n];if("function"==typeof s){var o=a[i];e=s.call(r,o),a.splice(i,1),i--}return e}),t.formatArgs.call(r,a),(r.log||t.log||console.log.bind(console)).apply(r,a)}}return r.namespace=e,r.enabled=t.enabled(e),r.useColors=t.useColors(),r.color=function(e){var r,n=0;for(r in e)n=(n<<5)-n+e.charCodeAt(r)|0;return t.colors[Math.abs(n)%t.colors.length]}(e),"function"==typeof t.init&&t.init(r),r}(t=e.exports=a.debug=a.default=a).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e),t.names=[],t.skips=[];for(var r=("string"==typeof e?e:"").split(/[\s,]+/),n=r.length,a=0;a<n;a++)r[a]&&("-"===(e=r[a].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.substr(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){var r,n;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(73308),t.names=[],t.skips=[],t.formatters={}},43125:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50622:(e,t,r)=>{function n(){var e;try{e=t.storage.debug}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e}(t=e.exports=r(35781)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var r=this.useColors;if(e[0]=(r?"%c":"")+this.namespace+(r?" %c":" ")+e[0]+(r?"%c ":" ")+"+"+t.humanize(this.diff),r){var n="color: "+this.color;e.splice(1,0,n,"color: inherit");var a=0,s=0;e[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&(a++,"%c"===e&&(s=a))}),e.splice(s,0,n)}},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=n,t.useColors=function(){return"undefined"!=typeof window&&!!window.process&&"renderer"===window.process.type||"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(n())},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57022:(e,t,r)=>{"use strict";r.d(t,{S:()=>s,q:()=>a});var n=r(28527);let a=async e=>{try{let t=await n.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){return{success:!1,error:`Failed to save mock exam result: ${e.response?.data?.message||e.message}`}}},s=async(e,t=1,r=10,a={})=>{try{let s=new URLSearchParams({page:t.toString(),limit:r.toString(),...void 0!==a.isWeekly&&{isWeekly:a.isWeekly.toString()}}).toString(),i=await n.S.get(`/mock-exam-result/${e}?${s}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:i.data}}catch(e){return{success:!1,error:`Failed to get mock exam result: ${e.response?.data?.message||e.message}`}}}},62325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>c});var n=r(65239),a=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["mock-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68454)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/mock-test/page",pathname:"/mock-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68454:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(37413),a=r(61120),s=r(1508);function i(){return(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)("div",{children:"Loading..."}),children:(0,n.jsx)(s.default,{})})}},69662:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=s(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=s(t,r));return t}(r)))}return e}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0!==(r=(function(){return a}).apply(t,[]))&&(e.exports=r)}()},71062:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n={src:"/_next/static/media/uwhizExam.5364baa3.png",height:626,width:798,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAElBMVEURCwcCAgEcEQogIB49Pj0lJSX5PC0XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAJ0lEQVR4nCXKuREAMAgEsb2H/lv2GDIFQtg2YsGHBIfkwGRP2k7KAwYIAEmvy1CUAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:6}},73308:e=>{function t(e,t,r){return e<t?void 0:e<1.5*t?Math.floor(e/t)+" "+r:Math.ceil(e/t)+" "+r+"s"}e.exports=function(e,r){r=r||{};var n,a,s=typeof e;if("string"===s&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(e);if("number"===s&&!1===isNaN(e)){return r.long?t(n=e,864e5,"day")||t(n,36e5,"hour")||t(n,6e4,"minute")||t(n,1e3,"second")||n+" ms":(a=e)>=864e5?Math.round(a/864e5)+"d":a>=36e5?Math.round(a/36e5)+"h":a>=6e4?Math.round(a/6e4)+"m":a>=1e3?Math.round(a/1e3)+"s":a+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},74075:e=>{"use strict";e.exports=require("zlib")},77876:(e,t,r)=>{"use strict";r.d(t,{default:()=>eW});var n=r(60687),a=r(29523),s=r(43210),i=r.n(s),o=r(16189),l=r(28527);let c=async e=>{try{return(await l.S.get(`/uwhizStudentData/${e}`)).data}catch(e){return{success:!1,error:`Failed To Get Student Detail: ${e.response?.data?.message||e.message}`}}};r(57022);let u=async e=>{try{return(await l.S.post("/mock-exam-terminate",e,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed to save termination log: ${e.response?.data?.message||e.message}`}}},d=async e=>{try{return(await l.S.get(`mock-exam-terminate/count?studentId=${e}`,{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){return{success:!1,error:`Failed To Get Count of termination: ${e.response?.data?.message||e.message}`}}};var m=r(43125),h=r(48730),p=r(52581),f=r(30474),g=r(71062);let w=async(e,t,r)=>{try{return(await l.S.get(`mock-exam/${e}/${t}`,{params:{isWeekly:r.toString()},headers:{"Server-Select":"uwhizServer"}})).data}catch(e){throw Error(`Failed To Get Question: ${e.response?.data?.message||e.message}`)}},x=e=>{let t=Date.now(),r=localStorage.getItem("examAttempts"),n=r?JSON.parse(r):{};n[e]=t,localStorage.setItem("examAttempts",JSON.stringify(n))};r(90971);let y=()=>{let e=(0,o.useSearchParams)(),t=(0,o.useRouter)(),[r,n]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{let r=e.get("token");if(r)try{let e=(await l.S.get("/student/login-with-jwt",{params:{token:r},withCredentials:!0})).data.data;if(e?.userId){let t={id:e.userId,contactNo:e.contactNo,firstName:e.firstName,lastName:e.lastName};localStorage.setItem("student_data",JSON.stringify(t)),localStorage.setItem("studentToken",r),localStorage.setItem("mobile_request","true"),n(e.userId)}let a=window.location.pathname;t.replace(a)}catch(e){console.error("JWT login failed:",e)}else{let e=localStorage.getItem("student_data"),t=e?JSON.parse(e):null;n(t?.id||null)}})()},[e,t]),r};var b=r(69662),C=r(10380),v=Object.defineProperty,k=Object.defineProperties,j=Object.getOwnPropertyDescriptors,S=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable,A=(e,t,r)=>t in e?v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,z=(e,t)=>{for(var r in t||(t={}))N.call(t,r)&&A(e,r,t[r]);if(S)for(var r of S(t))E.call(t,r)&&A(e,r,t[r]);return e},$=(e,t)=>k(e,j(t)),L=(e,t)=>{var r={};for(var n in e)N.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&S)for(var n of S(e))0>t.indexOf(n)&&E.call(e,n)&&(r[n]=e[n]);return r};function P(e){return t=>{var{bgStyle:r={},borderRadius:a=0,iconFillColor:s="white",round:i=!1,size:o=64}=t,l=L(t,["bgStyle","borderRadius","iconFillColor","round","size"]);return(0,n.jsxs)("svg",$(z({viewBox:"0 0 64 64",width:o,height:o},l),{children:[i?(0,n.jsx)("circle",{cx:"32",cy:"32",r:"32",fill:e.color,style:r}):(0,n.jsx)("rect",{width:"64",height:"64",rx:a,ry:a,fill:e.color,style:r}),(0,n.jsx)("path",{d:e.path,fill:s})]}))}}P({color:"#1185FE",path:"M21.945 18.886C26.015 21.941 30.393 28.137 32 31.461 33.607 28.137 37.985 21.941 42.055 18.886 44.992 16.681 49.75 14.975 49.75 20.403 49.75 21.487 49.128 29.51 48.764 30.813 47.497 35.341 42.879 36.496 38.772 35.797 45.951 37.019 47.778 41.067 43.833 45.114 36.342 52.801 33.066 43.186 32.227 40.722 32.073 40.27 32.001 40.059 32 40.238 31.999 40.059 31.927 40.27 31.773 40.722 30.934 43.186 27.658 52.801 20.167 45.114 16.222 41.067 18.049 37.019 25.228 35.797 21.121 36.496 16.503 35.341 15.236 30.813 14.872 29.51 14.25 21.487 14.25 20.403 14.25 14.975 19.008 16.681 21.945 18.886Z"});class O extends Error{constructor(e){super(e),this.name="AssertionError"}}function M(e,t){if(!e)throw new O(t)}function F(e){let t=Object.entries(e).filter(([,e])=>null!=e).map(([e,t])=>`${encodeURIComponent(e)}=${encodeURIComponent(String(t))}`);return t.length>0?`?${t.join("&")}`:""}let D=e=>!!e&&("object"==typeof e||"function"==typeof e)&&"then"in e&&"function"==typeof e.then,T=(e,t)=>({left:window.outerWidth/2+(window.screenX||window.screenLeft||0)-e/2,top:window.outerHeight/2+(window.screenY||window.screenTop||0)-t/2}),H=(e,t)=>({top:(window.screen.height-t)/2,left:(window.screen.width-e)/2});function _(e){var{beforeOnClick:t,children:r,className:a,disabled:s,disabledStyle:i={opacity:.6},forwardedRef:o,htmlTitle:l,networkLink:c,networkName:u,onClick:d,onShareWindowClose:m,openShareDialogOnClick:h=!0,opts:p,resetButtonStyle:f=!0,style:g,title:w,url:x,windowHeight:y=400,windowPosition:C="windowCenter",windowWidth:v=550}=e,k=L(e,["beforeOnClick","children","className","disabled","disabledStyle","forwardedRef","htmlTitle","networkLink","networkName","onClick","onShareWindowClose","openShareDialogOnClick","opts","resetButtonStyle","style","title","url","windowHeight","windowPosition","windowWidth"]);let j=async e=>{let r=c(x,p);if(!s){if(e.preventDefault(),t){let e=t();D(e)&&await e}h&&function(e,t,r){var{height:n,width:a}=t;let s=z({height:n,width:a,location:"no",toolbar:"no",status:"no",directories:"no",menubar:"no",scrollbars:"yes",resizable:"no",centerscreen:"yes",chrome:"yes"},L(t,["height","width"])),i=window.open(e,"",Object.keys(s).map(e=>`${e}=${s[e]}`).join(", "));if(r){let e=window.setInterval(()=>{try{(null===i||i.closed)&&(window.clearInterval(e),r(i))}catch(e){console.error(e)}},1e3)}}(r,z({height:y,width:v},"windowCenter"===C?T(v,y):H(v,y)),m),d&&d(e,r)}},S=b("react-share__ShareButton",{"react-share__ShareButton--disabled":!!s,disabled:!!s},a),N=f?z(z({backgroundColor:"transparent",border:"none",padding:0,font:"inherit",color:"inherit",cursor:"pointer"},g),s&&i):z(z({},g),s&&i);return(0,n.jsx)("button",$(z({},k),{className:S,onClick:j,ref:o,style:N,title:l,children:r}))}function R(e,t,r,a){let i=(s,i)=>{let o=r(s),l=z({},s);return Object.keys(o).forEach(e=>{delete l[e]}),(0,n.jsx)(_,$(z(z({},a),l),{forwardedRef:i,networkName:e,networkLink:t,opts:o}))};return i.displayName=`ShareButton-${e}`,(0,s.forwardRef)(i)}function q(e){var{children:t=e=>e,className:r,getCount:a,url:i}=e,o=L(e,["children","className","getCount","url"]);let l=function(){let e=(0,s.useRef)(!1);return(0,s.useEffect)(()=>(e.current=!0,()=>{e.current=!1}),[]),(0,s.useCallback)(()=>e.current,[])}(),[c,u]=(0,s.useState)(void 0),[d,m]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{m(!0),a(i,e=>{l()&&(u(e),m(!1))})},[i]),(0,n.jsx)("span",$(z({className:b("react-share__ShareCount",r)},o),{children:!d&&void 0!==c&&t(c)}))}function I(e){let t=t=>(0,n.jsx)(q,z({getCount:e},t));return t.displayName=`ShareCount(${e.name})`,t}R("bluesky",function(e,{title:t,separator:r}){return M(e,"bluesky.url"),"https://bsky.app/intent/compose"+F({text:t?t+r+e:e})},e=>({title:e.title,separator:e.separator||" "}),{windowWidth:660,windowHeight:460,windowPosition:"windowCenter"}),P({color:"#7f7f7f",path:"M17,22v20h30V22H17z M41.1,25L32,32.1L22.9,25H41.1z M20,39V26.6l12,9.3l12-9.3V39H20z"}),R("email",function(e,{subject:t,body:r,separator:n}){return"mailto:"+F({subject:t,body:r?r+n+e:e})},e=>({subject:e.subject,body:e.body,separator:e.separator||" "}),{openShareDialogOnClick:!1,onClick:(e,t)=>{window.location.href=t}}),P({color:"#0965FE",path:"M34.1,47V33.3h4.6l0.7-5.3h-5.3v-3.4c0-1.5,0.4-2.6,2.6-2.6l2.8,0v-4.8c-0.5-0.1-2.2-0.2-4.1-0.2 c-4.1,0-6.9,2.5-6.9,7V28H24v5.3h4.6V47H34.1z"}),P({color:"#0A7CFF",path:"M 53.066406 21.871094 C 52.667969 21.339844 51.941406 21.179688 51.359375 21.496094 L 37.492188 29.058594 L 28.867188 21.660156 C 28.339844 21.207031 27.550781 21.238281 27.054688 21.730469 L 11.058594 37.726562 C 10.539062 38.25 10.542969 39.09375 11.0625 39.613281 C 11.480469 40.027344 12.121094 40.121094 12.640625 39.839844 L 26.503906 32.28125 L 35.136719 39.679688 C 35.667969 40.132812 36.457031 40.101562 36.949219 39.609375 L 52.949219 23.613281 C 53.414062 23.140625 53.464844 22.398438 53.066406 21.871094 Z M 53.066406 21.871094"}),R("facebookmessenger",function(e,{appId:t,redirectUri:r,to:n}){return"https://www.facebook.com/dialog/send"+F({link:e,redirect_uri:r||e,app_id:t,to:n})},e=>({appId:e.appId,redirectUri:e.redirectUri,to:e.to}),{windowWidth:1e3,windowHeight:820}),R("facebook",function(e,{hashtag:t}){return M(e,"facebook.url"),"https://www.facebook.com/sharer/sharer.php"+F({u:e,hashtag:t})},e=>({hashtag:e.hashtag}),{windowWidth:550,windowHeight:400}),I(function(e,t){C(`https://graph.facebook.com/?id=${e}&fields=og_object{engagement}`,(e,r)=>{t(!e&&r&&r.og_object&&r.og_object.engagement?r.og_object.engagement.count:void 0)})}),P({color:"#009ad9",path:"M 36.164062 33.554688 C 34.988281 32.234375 33.347656 31.5 31.253906 31.34375 C 33.125 30.835938 34.476562 30.09375 35.335938 29.09375 C 36.191406 28.09375 36.609375 26.78125 36.609375 25.101562 C 36.628906 23.875 36.332031 22.660156 35.75 21.578125 C 35.160156 20.558594 34.292969 19.71875 33.253906 19.160156 C 32.304688 18.640625 31.175781 18.265625 29.847656 18.042969 C 28.523438 17.824219 26.195312 17.730469 22.867188 17.730469 L 14.769531 17.730469 L 14.769531 47.269531 L 23.113281 47.269531 C 26.46875 47.269531 28.886719 47.15625 30.367188 46.929688 C 31.851562 46.695312 33.085938 46.304688 34.085938 45.773438 C 35.289062 45.148438 36.28125 44.179688 36.933594 42.992188 C 37.597656 41.796875 37.933594 40.402344 37.933594 38.816406 C 37.933594 36.621094 37.347656 34.867188 36.164062 33.554688 Z M 22.257812 24.269531 L 23.984375 24.269531 C 25.988281 24.269531 27.332031 24.496094 28.015625 24.945312 C 28.703125 25.402344 29.042969 26.183594 29.042969 27.285156 C 29.042969 28.390625 28.664062 29.105469 27.9375 29.550781 C 27.210938 29.992188 25.84375 30.199219 23.855469 30.199219 L 22.257812 30.199219 Z M 29.121094 41.210938 C 28.328125 41.691406 26.976562 41.925781 25.078125 41.925781 L 22.257812 41.925781 L 22.257812 35.488281 L 25.195312 35.488281 C 27.144531 35.488281 28.496094 35.738281 29.210938 36.230469 C 29.925781 36.726562 30.304688 37.582031 30.304688 38.832031 C 30.304688 40.078125 29.914062 40.742188 29.105469 41.222656 Z M 29.121094 41.210938 M 46.488281 39.792969 C 44.421875 39.792969 42.742188 41.46875 42.742188 43.535156 C 42.742188 45.605469 44.421875 47.28125 46.488281 47.28125 C 48.554688 47.28125 50.230469 45.605469 50.230469 43.535156 C 50.230469 41.46875 48.554688 39.792969 46.488281 39.792969 Z M 46.488281 39.792969 M 43.238281 17.730469 L 49.738281 17.730469 L 49.738281 37.429688 L 43.238281 37.429688 Z M 43.238281 17.730469 "}),R("hatena",function(e,{title:t}){return M(e,"hatena.url"),`http://b.hatena.ne.jp/add?mode=confirm&url=${e}&title=${t}`},e=>({title:e.title}),{windowWidth:660,windowHeight:460,windowPosition:"windowCenter"}),I(function(e,t){C("https://bookmark.hatenaapis.com/count/entry"+F({url:e}),(e,r)=>{t(null!=r?r:void 0)})}),P({color:"#1F1F1F",path:"M35.688 43.012c0 2.425.361 2.785 3.912 3.056V48H24.401v-1.932c3.555-.27 3.912-.63 3.912-3.056V20.944c0-2.379-.36-2.785-3.912-3.056V16H39.6v1.888c-3.55.27-3.912.675-3.912 3.056v22.068h.001z"}),R("instapaper",function(e,{title:t,description:r}){return M(e,"instapaper.url"),"http://www.instapaper.com/hello2"+F({url:e,title:t,description:r})},e=>({title:e.title,description:e.description}),{windowWidth:500,windowHeight:500,windowPosition:"windowCenter"}),P({color:"#00b800",path:"M52.62 30.138c0 3.693-1.432 7.019-4.42 10.296h.001c-4.326 4.979-14 11.044-16.201 11.972-2.2.927-1.876-.591-1.786-1.112l.294-1.765c.069-.527.142-1.343-.066-1.865-.232-.574-1.146-.872-1.817-1.016-9.909-1.31-17.245-8.238-17.245-16.51 0-9.226 9.251-16.733 20.62-16.733 11.37 0 20.62 7.507 20.62 16.733zM27.81 25.68h-1.446a.402.402 0 0 0-.402.401v8.985c0 .221.18.4.402.4h1.446a.401.401 0 0 0 .402-.4v-8.985a.402.402 0 0 0-.402-.401zm9.956 0H36.32a.402.402 0 0 0-.402.401v5.338L31.8 25.858a.39.39 0 0 0-.031-.04l-.002-.003-.024-.025-.008-.007a.313.313 0 0 0-.032-.026.255.255 0 0 1-.021-.014l-.012-.007-.021-.012-.013-.006-.023-.01-.013-.005-.024-.008-.014-.003-.023-.005-.017-.002-.021-.003-.021-.002h-1.46a.402.402 0 0 0-.402.401v8.985c0 .221.18.4.402.4h1.446a.401.401 0 0 0 .402-.4v-5.337l4.123 5.568c.**************.101.099l.004.003a.236.236 0 0 0 .025.015l.012.006.019.01a.154.154 0 0 1 .019.008l.012.004.028.01.005.001a.442.442 0 0 0 .104.013h1.446a.4.4 0 0 0 .401-.4v-8.985a.402.402 0 0 0-.401-.401zm-13.442 7.537h-3.93v-7.136a.401.401 0 0 0-.401-.401h-1.447a.4.4 0 0 0-.401.401v8.984a.392.392 0 0 0 .123.29c.072.068.17.111.278.111h5.778a.4.4 0 0 0 .401-.401v-1.447a.401.401 0 0 0-.401-.401zm21.429-5.287c.222 0 .401-.18.401-.402v-1.446a.401.401 0 0 0-.401-.402h-5.778a.398.398 0 0 0-.279.113l-.005.004-.006.008a.397.397 0 0 0-.111.276v8.984c0 .108.043.206.112.278l.005.006a.401.401 0 0 0 .284.117h5.778a.4.4 0 0 0 .401-.401v-1.447a.401.401 0 0 0-.401-.401h-3.93v-1.519h3.93c.222 0 .401-.18.401-.402V29.85a.401.401 0 0 0-.401-.402h-3.93V27.93h3.93z"}),R("line",function(e,{title:t}){return M(e,"line.url"),"https://social-plugins.line.me/lineit/share"+F({url:e,text:t})},e=>({title:e.title}),{windowWidth:500,windowHeight:500}),P({color:"#0077B5",path:"M20.4,44h5.4V26.6h-5.4V44z M23.1,18c-1.7,0-3.1,1.4-3.1,3.1c0,1.7,1.4,3.1,3.1,3.1 c1.7,0,3.1-1.4,3.1-3.1C26.2,19.4,24.8,18,23.1,18z M39.5,26.2c-2.6,0-4.4,1.4-5.1,2.8h-0.1v-2.4h-5.2V44h5.4v-8.6 c0-2.3,0.4-4.5,3.2-4.5c2.8,0,2.8,2.6,2.8,4.6V44H46v-9.5C46,29.8,45,26.2,39.5,26.2z"}),R("linkedin",function(e,{title:t,summary:r,source:n}){return M(e,"linkedin.url"),"https://linkedin.com/shareArticle"+F({url:e,mini:"true",title:t,summary:r,source:n})},({title:e,summary:t,source:r})=>({title:e,summary:t,source:r}),{windowWidth:750,windowHeight:600}),P({color:"#21A5D8",path:"M18.3407821,28.1764706 L21.9441341,31.789916 L33.0055865,42.882353 C33.0055865,42.882353 33.0893855,42.9663866 33.0893855,42.9663866 L46.6648046,47 C46.6648046,47 46.6648046,47 46.7486034,47 C46.8324022,47 46.8324022,47 46.9162012,46.9159664 C47,46.8319327 47,46.8319327 47,46.7478991 L42.9776536,33.1344537 C42.9776536,33.1344537 42.9776536,33.1344537 42.8938548,33.0504202 L31.1620111,21.3697479 L31.1620111,21.3697479 L28.1452514,18.2605042 C27.3072626,17.4201681 26.5530726,17 25.7150838,17 C24.2905028,17 23.0335195,18.3445378 21.5251397,19.8571429 C21.273743,20.1092437 20.9385475,20.4453781 20.6871508,20.697479 C20.3519553,21.0336134 20.1005586,21.2857143 19.849162,21.5378151 C18.3407821,22.9663866 17.0837989,24.2268908 17,25.7394958 C17.0837989,26.4957983 17.5027933,27.3361345 18.3407821,28.1764706 Z M39.9012319,39.6134454 C39.7336341,39.4453781 39.4822374,37.6806724 40.2364275,36.8403362 C40.9906174,36.0840337 41.6610084,36 42.1638017,36 C42.3313995,36 42.4989973,36 42.5827961,36 L44.8453659,43.5630253 L43.5883828,44.8235295 L36.0464833,42.5546218 C35.9626843,42.2184874 35.8788855,41.2100841 36.8844722,40.2016807 C37.2196676,39.8655463 37.8900587,39.6134454 38.5604498,39.6134454 C39.147042,39.6134454 39.5660364,39.7815126 39.5660364,39.7815126 C39.6498353,39.8655463 39.8174331,39.8655463 39.8174331,39.7815126 C39.9850307,39.7815126 39.9850307,39.697479 39.9012319,39.6134454 Z"}),R("livejournal",function(e,{title:t,description:r}){return M(e,"livejournal.url"),"https://www.livejournal.com/update.bml"+F({subject:t,event:r})},e=>({title:e.title,description:e.description}),{windowWidth:660,windowHeight:460}),P({color:"#168DE2",path:"M39.7107745,17 C41.6619755,17 43.3204965,18.732852 43.3204965,21.0072202 C43.3204965,23.2815885 41.7595357,25.0144404 39.7107745,25.0144404 C37.7595732,25.0144404 36.1010522,23.2815885 36.1010522,21.0072202 C36.1010522,18.732852 37.7595732,17 39.7107745,17 Z M24.3938451,17 C26.3450463,17 28.0035672,18.732852 28.0035672,21.0072202 C28.0035672,23.2815885 26.4426063,25.0144404 24.3938451,25.0144404 C22.4426439,25.0144404 20.7841229,23.2815885 20.7841229,21.0072202 C20.7841229,18.732852 22.4426439,17 24.3938451,17 Z M51.9057817,43.4259928 C51.7106617,44.0758123 51.4179815,44.6173285 50.9301812,44.9422383 C50.637501,45.1588448 50.2472607,45.267148 49.8570205,45.267148 C49.07654,45.267148 48.3936197,44.833935 48.0033795,44.0758123 L46.2472985,40.7184115 L45.759498,41.2599278 C42.5400162,44.9422383 37.466893,47 32.0035297,47 C26.5401664,47 21.5646034,44.9422383 18.2475614,41.2599278 L17.7597611,40.7184115 L16.00368,44.0758123 C15.6134398,44.833935 14.9305194,45.267148 14.1500389,45.267148 C13.7597986,45.267148 13.3695584,45.1588448 13.0768782,44.9422383 C12.0037176,44.2924187 11.7110374,42.7761733 12.2963978,41.5848375 L16.7841605,33.0288807 C17.1744007,32.270758 17.8573211,31.8375453 18.6378016,31.8375453 C19.0280418,31.8375453 19.4182821,31.9458485 19.7109623,32.1624548 C20.7841229,32.8122743 21.0768031,34.3285197 20.4914427,35.5198555 L20.1012025,36.2779783 L20.2963226,36.602888 C22.4426439,39.9602888 27.0279667,42.234657 31.9059697,42.234657 C36.7839727,42.234657 41.3692955,40.068592 43.5156167,36.602888 L43.7107367,36.2779783 L43.3204965,35.6281587 C43.0278165,35.0866425 42.9302562,34.436823 43.1253765,33.7870035 C43.3204965,33.137184 43.6131767,32.5956678 44.100977,32.270758 C44.3936572,32.0541515 44.7838975,31.9458485 45.1741377,31.9458485 C45.9546182,31.9458485 46.6375385,32.3790613 47.0277787,33.137184 L51.5155415,41.6931408 C52.003342,42.234657 52.100902,42.8844765 51.9057817,43.4259928 Z"}),R("mailru",function(e,{title:t,description:r,imageUrl:n}){return M(e,"mailru.url"),"https://connect.mail.ru/share"+F({url:e,title:t,description:r,image_url:n})},e=>({title:e.title,description:e.description,imageUrl:e.imageUrl}),{windowWidth:660,windowHeight:460}),P({color:"#F97400",path:"M39,30c-1,0-3,2-7,2s-6-2-7-2c-1.1,0-2,0.9-2,2c0,1,0.6,1.5,1,1.7c1.2,0.7,5,2.3,5,2.3l-4.3,5.4   c0,0-0.8,0.9-0.8,1.6c0,1.1,0.9,2,2,2c1,0,1.5-0.7,1.5-0.7S32,39,32,39c0,0,4.5,5.3,4.5,5.3S37,45,38,45c1.1,0,2-0.9,2-2   c0-0.6-0.8-1.6-0.8-1.6L35,36c0,0,3.8-1.6,5-2.3c0.4-0.3,1-0.7,1-1.7C41,30.9,40.1,30,39,30z M32,15c-3.9,0-7,3.1-7,7s3.1,7,7,7c3.9,0,7-3.1,7-7S35.9,15,32,15z M32,25.5   c-1.9,0-3.5-1.6-3.5-3.5c0-1.9,1.6-3.5,3.5-3.5c1.9,0,3.5,1.6,3.5,3.5C35.5,23.9,33.9,22.5,35,22.5z "}),R("ok",function(e,{title:t,description:r,image:n}){return M(e,"ok.url"),"https://connect.ok.ru/offer"+F({url:e,title:t,description:r,imageUrl:n})},e=>({title:e.title,description:e.description,image:e.image}),{windowWidth:588,windowHeight:480,windowPosition:"screenCenter"}),I(function(e,t){window.OK||(window.OK={Share:{count:function(e,t){var r,n;null==(n=(r=window.OK.callbacks)[e])||n.call(r,t)}},callbacks:[]});let r=window.OK.callbacks.length;return window.ODKL={updateCount(e,t){var r,n;let a=""===e?0:parseInt(e.replace("react-share-",""),10);null==(n=(r=window.OK.callbacks)[a])||n.call(r,""===t?void 0:parseInt(t,10))}},window.OK.callbacks.push(t),C("https://connect.ok.ru/dk"+F({"st.cmd":"extLike",uid:`react-share-${r}`,ref:e}))}),P({color:"#E60023",path:"M32,16c-8.8,0-16,7.2-16,16c0,6.6,3.9,12.2,9.6,14.7c0-1.1,0-2.5,0.3-3.7 c0.3-1.3,2.1-8.7,2.1-8.7s-0.5-1-0.5-2.5c0-2.4,1.4-4.1,3.1-4.1c1.5,0,2.2,1.1,2.2,2.4c0,1.5-0.9,3.7-1.4,5.7 c-0.4,1.7,0.9,3.1,2.5,3.1c3,0,5.1-3.9,5.1-8.5c0-3.5-2.4-6.1-6.7-6.1c-4.9,0-7.9,3.6-7.9,7.7c0,1.4,0.4,2.4,1.1,3.1 c0.3,0.3,0.3,0.5,0.2,0.9c-0.1,0.3-0.3,1-0.3,1.3c-0.1,0.4-0.4,0.6-0.8,0.4c-2.2-0.9-3.3-3.4-3.3-6.1c0-4.5,3.8-10,11.4-10 c6.1,0,10.1,4.4,10.1,9.2c0,6.3-3.5,11-8.6,11c-1.7,0-3.4-0.9-3.9-2c0,0-0.9,3.7-1.1,4.4c-0.3,1.2-1,2.5-1.6,3.4 c1.4,0.4,3,0.7,4.5,0.7c8.8,0,16-7.2,16-16C48,23.2,40.8,16,32,16z"}),R("pinterest",function(e,{media:t,description:r,pinId:n}){return n?`https://pinterest.com/pin/${n}/repin/x/`:(M(e,"pinterest.url"),M(t,"pinterest.media"),"https://pinterest.com/pin/create/button/"+F({url:e,media:t,description:r}))},e=>({media:e.media,description:e.description,pinId:e.pinId}),{windowWidth:1e3,windowHeight:730}),I(function(e,t){C("https://api.pinterest.com/v1/urls/count.json"+F({url:e}),(e,r)=>{t(r?r.count:void 0)})}),P({color:"#EF3F56",path:"M41.084 29.065l-7.528 7.882a2.104 2.104 0 0 1-1.521.666 2.106 2.106 0 0 1-1.522-.666l-7.528-7.882c-.876-.914-.902-2.43-.065-3.384.84-.955 2.228-.987 3.1-.072l6.015 6.286 6.022-6.286c.88-.918 2.263-.883 3.102.071.841.938.82 2.465-.06 3.383l-.015.002zm6.777-10.976C47.463 16.84 46.361 16 45.14 16H18.905c-1.2 0-2.289.82-2.716 2.044-.125.363-.189.743-.189 1.125v10.539l.112 2.096c.464 4.766 2.73 8.933 6.243 11.838.**************.19.153l.04.033c1.882 1.499 3.986 2.514 6.259 3.014a14.662 14.662 0 0 0 6.13.052c.118-.042.235-.065.353-.087.03 0 .065-.022.098-.042a15.395 15.395 0 0 0 6.011-2.945l.039-.045.18-.153c3.502-2.902 5.765-7.072 6.248-11.852L48 29.674v-10.52c0-.366-.041-.728-.161-1.08l.022.015z"}),R("pocket",function(e,{title:t}){return M(e,"pocket.url"),"https://getpocket.com/save"+F({url:e,title:t})},e=>({title:e.title}),{windowWidth:500,windowHeight:500}),P({color:"#FF5700",path:"M 53.34375 32 C 53.277344 30.160156 52.136719 28.53125 50.429688 27.839844 C 48.722656 27.148438 46.769531 27.523438 45.441406 28.800781 C 41.800781 26.324219 37.519531 24.957031 33.121094 24.863281 L 35.199219 14.878906 L 42.046875 16.320312 C 42.214844 17.882812 43.496094 19.09375 45.066406 19.171875 C 46.636719 19.253906 48.03125 18.183594 48.359375 16.644531 C 48.6875 15.105469 47.847656 13.558594 46.382812 12.992188 C 44.914062 12.425781 43.253906 13.007812 42.464844 14.367188 L 34.625 12.800781 C 34.363281 12.742188 34.09375 12.792969 33.871094 12.9375 C 33.648438 13.082031 33.492188 13.308594 33.441406 13.566406 L 31.070312 24.671875 C 26.617188 24.738281 22.277344 26.105469 18.59375 28.609375 C 17.242188 27.339844 15.273438 26.988281 13.570312 27.707031 C 11.863281 28.429688 10.746094 30.089844 10.71875 31.941406 C 10.691406 33.789062 11.757812 35.484375 13.441406 36.257812 C 13.402344 36.726562 13.402344 37.195312 13.441406 37.664062 C 13.441406 44.832031 21.792969 50.65625 32.097656 50.65625 C 42.398438 50.65625 50.753906 44.832031 50.753906 37.664062 C 50.789062 37.195312 50.789062 36.726562 50.753906 36.257812 C 52.363281 35.453125 53.371094 33.800781 53.34375 32 Z M 21.34375 35.199219 C 21.34375 33.433594 22.777344 32 24.542969 32 C 26.3125 32 27.742188 33.433594 27.742188 35.199219 C 27.742188 36.96875 26.3125 38.398438 24.542969 38.398438 C 22.777344 38.398438 21.34375 36.96875 21.34375 35.199219 Z M 39.9375 44 C 37.664062 45.710938 34.871094 46.582031 32.03125 46.464844 C 29.191406 46.582031 26.398438 45.710938 24.128906 44 C 23.847656 43.65625 23.871094 43.15625 24.183594 42.839844 C 24.5 42.527344 25 42.503906 25.34375 42.785156 C 27.269531 44.195312 29.617188 44.90625 32 44.800781 C 34.386719 44.929688 36.746094 44.242188 38.6875 42.847656 C 39.042969 42.503906 39.605469 42.511719 39.953125 42.863281 C 40.296875 43.21875 40.289062 43.785156 39.9375 44.128906 Z M 39.359375 38.527344 C 37.59375 38.527344 36.160156 37.09375 36.160156 35.328125 C 36.160156 33.5625 37.59375 32.128906 39.359375 32.128906 C 41.128906 32.128906 42.558594 33.5625 42.558594 35.328125 C 42.59375 36.203125 42.269531 37.054688 41.65625 37.6875 C 41.046875 38.316406 40.203125 38.664062 39.328125 38.65625 Z M 39.359375 38.527344"}),R("reddit",function(e,{title:t}){return M(e,"reddit.url"),"https://www.reddit.com/web/submit"+F({url:e,title:t})},e=>({title:e.title}),{windowWidth:660,windowHeight:460,windowPosition:"windowCenter"}),R("gab",function(e,{title:t}){return M(e,"gab.url"),"https://gab.com/compose"+F({url:e,text:t})},e=>({title:e.title}),{windowWidth:660,windowHeight:640,windowPosition:"windowCenter"}),P({color:"#00d178",path:"m17.0506,23.97457l5.18518,0l0,14.23933c0,6.82699 -3.72695,10.09328 -9.33471,10.09328c-2.55969,0 -4.82842,-0.87286 -6.22084,-2.0713l2.07477,-3.88283c1.19844,0.81051 2.33108,1.29543 3.85511,1.29543c2.75366,0 4.44049,-1.97432 4.44049,-4.82149l0,-0.87286c-1.16728,1.39242 -2.81947,2.0713 -4.63446,2.0713c-4.44048,0 -7.81068,-3.68885 -7.81068,-8.28521c0,-4.59289 3.37019,-8.28174 7.81068,-8.28174c1.81499,0 3.46718,0.67888 4.63446,2.0713l0,-1.55521zm-3.62997,11.39217c1.97777,0 3.62997,-1.6522 3.62997,-3.62652c0,-1.97432 -1.6522,-3.62305 -3.62997,-3.62305c-1.97778,0 -3.62997,1.64873 -3.62997,3.62305c0,1.97432 1.65219,3.62652 3.62997,3.62652zm25.7077,4.13913l-5.18518,0l0,-1.29197c-1.00448,1.13264 -2.3969,1.81152 -4.21188,1.81152c-3.62997,0 -5.63893,-2.52504 -5.63893,-5.4034c0,-4.27076 5.251,-5.85715 9.78846,-4.49937c-0.09698,-1.39241 -0.9733,-2.39343 -2.78829,-2.39343c-1.26426,0 -2.72248,0.48492 -3.62997,1.00102l-1.5552,-3.72003c1.19844,-0.77587 3.40136,-1.55174 5.96452,-1.55174c3.78931,0 7.25648,2.13365 7.25648,7.95962l0,8.08777zm-5.18518,-6.14809c-2.42806,-0.77587 -4.66563,-0.3533 -4.66563,1.36124c0,1.00101 0.84168,1.6799 1.84616,1.6799c1.20191,0 2.56315,-0.96984 2.81947,-3.04115zm13.00626,-17.66495l0,9.83695c1.16727,-1.39242 2.81946,-2.0713 4.63445,-2.0713c4.44048,0 7.81068,3.68885 7.81068,8.28174c0,4.59636 -3.37019,8.28521 -7.81068,8.28521c-1.81499,0 -3.46718,-0.67888 -4.63445,-2.0713l0,1.55174l-5.18519,0l0,-23.81304l5.18519,0zm3.62997,19.67391c1.97777,0 3.62997,-1.6522 3.62997,-3.62652c0,-1.97432 -1.6522,-3.62305 -3.62997,-3.62305c-1.97778,0 -3.62997,1.64873 -3.62997,3.62305c0,1.97432 1.65219,3.62652 3.62997,3.62652zm0,0"}),I(function(e,t){C(`https://www.reddit.com/api/info.json?limit=1&url=${e}`,{param:"jsonp"},(e,r)=>{t(!e&&r&&r.data&&r.data.children.length>0&&r.data.children[0].data.score?r.data.children[0].data.score:void 0)})}),P({color:"#25A3E3",path:"m45.90873,15.44335c-0.6901,-0.0281 -1.37668,0.14048 -1.96142,0.41265c-0.84989,0.32661 -8.63939,3.33986 -16.5237,6.39174c-3.9685,1.53296 -7.93349,3.06593 -10.98537,4.24067c-3.05012,1.1765 -5.34694,2.05098 -5.4681,2.09312c-0.80775,0.28096 -1.89996,0.63566 -2.82712,1.72788c-0.23354,0.27218 -0.46884,0.62161 -0.58825,1.10275c-0.11941,0.48114 -0.06673,1.09222 0.16682,1.5716c0.46533,0.96052 1.25376,1.35737 2.18443,1.71383c3.09051,0.99037 6.28638,1.93508 8.93263,2.8236c0.97632,3.44171 1.91401,6.89571 2.84116,10.34268c0.30554,0.69185 0.97105,0.94823 1.65764,0.95525l-0.00351,0.03512c0,0 0.53908,0.05268 1.06412,-0.07375c0.52679,-0.12292 1.18879,-0.42846 1.79109,-0.99212c0.662,-0.62161 2.45836,-2.38812 3.47683,-3.38552l7.6736,5.66477l0.06146,0.03512c0,0 0.84989,0.59703 2.09312,0.68132c0.62161,0.04214 1.4399,-0.07726 2.14229,-0.59176c0.70766,-0.51626 1.1765,-1.34683 1.396,-2.29506c0.65673,-2.86224 5.00979,-23.57745 5.75257,-27.00686l-0.02107,0.08077c0.51977,-1.93157 0.32837,-3.70159 -0.87096,-4.74991c-0.60054,-0.52152 -1.2924,-0.7498 -1.98425,-0.77965l0,0.00176zm-0.2072,3.29069c0.04741,0.0439 0.0439,0.0439 0.00351,0.04741c-0.01229,-0.00351 0.14048,0.2072 -0.15804,1.32576l-0.01229,0.04214l-0.00878,0.03863c-0.75858,3.50668 -5.15554,24.40802 -5.74203,26.96472c-0.08077,0.34417 -0.11414,0.31959 -0.09482,0.29852c-0.1756,-0.02634 -0.50045,-0.16506 -0.52679,-0.1756l-13.13468,-9.70175c4.4988,-4.33199 9.09945,-8.25307 13.744,-12.43229c0.8218,-0.41265 0.68483,-1.68573 -0.29852,-1.70681c-1.04305,0.24584 -1.92279,0.99564 -2.8798,1.47502c-5.49971,3.2626 -11.11882,6.13186 -16.55882,9.49279c-2.792,-0.97105 -5.57873,-1.77704 -8.15298,-2.57601c2.2336,-0.89555 4.00889,-1.55579 5.75608,-2.23009c3.05188,-1.1765 7.01687,-2.7042 10.98537,-4.24067c7.94051,-3.06944 15.92667,-6.16346 16.62028,-6.43037l0.05619,-0.02283l0.05268,-0.02283c0.19316,-0.0878 0.30378,-0.09658 0.35471,-0.10009c0,0 -0.01756,-0.05795 -0.00351,-0.04566l-0.00176,0zm-20.91715,22.0638l2.16687,1.60145c-0.93418,0.91311 -1.81743,1.77353 -2.45485,2.38812l0.28798,-3.98957"}),R("telegram",function(e,{title:t}){return M(e,"telegram.url"),"https://telegram.me/share/url"+F({url:e,text:t})},e=>({title:e.title}),{windowWidth:550,windowHeight:400}),P({color:"#000000",path:"M41.4569 31.0027C41.2867 30.9181 41.1138 30.8366 40.9386 30.7586C40.6336 24.9274 37.5624 21.5891 32.4055 21.5549C32.3821 21.5548 32.3589 21.5548 32.3355 21.5548C29.251 21.5548 26.6857 22.9207 25.1067 25.4063L27.9429 27.4247C29.1224 25.5681 30.9736 25.1723 32.3369 25.1723C32.3526 25.1723 32.3684 25.1723 32.384 25.1724C34.082 25.1837 35.3633 25.6958 36.1926 26.6947C36.7961 27.4218 37.1997 28.4267 37.3996 29.6949C35.8941 29.4294 34.266 29.3478 32.5255 29.4513C27.6225 29.7443 24.4705 32.711 24.6822 36.8332C24.7896 38.9242 25.7937 40.7231 27.5094 41.8982C28.96 42.8916 30.8282 43.3774 32.7699 43.2674C35.3341 43.1216 37.3456 42.1066 38.749 40.2507C39.8148 38.8413 40.4889 37.0149 40.7865 34.7136C42.0085 35.4787 42.9142 36.4855 43.4144 37.6959C44.2649 39.7534 44.3145 43.1344 41.6553 45.8908C39.3255 48.3055 36.525 49.3501 32.2926 49.3824C27.5977 49.3463 24.0471 47.7842 21.7385 44.7396C19.5768 41.8886 18.4595 37.7706 18.4179 32.5C18.4595 27.2293 19.5768 23.1113 21.7385 20.2604C24.0471 17.2157 27.5977 15.6537 32.2925 15.6175C37.0215 15.654 40.634 17.2235 43.0309 20.2829C44.2062 21.7831 45.0923 23.6698 45.6764 25.8696L49 24.9496C48.2919 22.2419 47.1778 19.9087 45.6616 17.9736C42.5888 14.0514 38.0947 12.0417 32.3041 12H32.2809C26.5022 12.0415 22.0584 14.0589 19.073 17.9961C16.4165 21.4997 15.0462 26.3747 15.0001 32.4856L15 32.5L15.0001 32.5144C15.0462 38.6252 16.4165 43.5004 19.073 47.004C22.0584 50.941 26.5022 52.9586 32.2809 53H32.3041C37.4418 52.9631 41.0632 51.5676 44.0465 48.4753C47.9496 44.4297 47.8321 39.3587 46.5457 36.2457C45.6227 34.0134 43.8631 32.2002 41.4569 31.0027ZM32.5863 39.6551C30.4374 39.7807 28.205 38.78 28.0949 36.6367C28.0133 35.0476 29.185 33.2743 32.7182 33.0631C33.1228 33.0389 33.5199 33.027 33.9099 33.027C35.1933 33.027 36.3939 33.1564 37.4854 33.4039C37.0783 38.6788 34.6902 39.5353 32.5863 39.6551Z"}),R("threads",function(e,{title:t}){return M(e,"threads.url"),"https://threads.net/intent/post"+F({url:e,text:t})},e=>({title:e.title}),{windowWidth:550,windowHeight:600}),P({color:"#34526f",path:"M39.2,41c-0.6,0.3-1.6,0.5-2.4,0.5c-2.4,0.1-2.9-1.7-2.9-3v-9.3h6v-4.5h-6V17c0,0-4.3,0-4.4,0 c-0.1,0-0.2,0.1-0.2,0.2c-0.3,2.3-1.4,6.4-5.9,8.1v3.9h3V39c0,3.4,2.5,8.1,9,8c2.2,0,4.7-1,5.2-1.8L39.2,41z"}),R("tumblr",function(e,{title:t,caption:r,tags:n,posttype:a}){return M(e,"tumblr.url"),"https://www.tumblr.com/widgets/share/tool"+F({canonicalUrl:e,title:t,caption:r,tags:n,posttype:a})},e=>({title:e.title,tags:(e.tags||[]).join(","),caption:e.caption,posttype:e.posttype||"link"}),{windowWidth:660,windowHeight:460}),I(function(e,t){return C("https://api.tumblr.com/v2/share/stats"+F({url:e}),(e,r)=>{t(!e&&r&&r.response?r.response.note_count:void 0)})}),P({color:"#00aced",path:"M48,22.1c-1.2,0.5-2.4,0.9-3.8,1c1.4-0.8,2.4-2.1,2.9-3.6c-1.3,0.8-2.7,1.3-4.2,1.6 C41.7,19.8,40,19,38.2,19c-3.6,0-6.6,2.9-6.6,6.6c0,0.5,0.1,1,0.2,1.5c-5.5-0.3-10.3-2.9-13.5-6.9c-0.6,1-0.9,2.1-0.9,3.3 c0,2.3,1.2,4.3,2.9,5.5c-1.1,0-2.1-0.3-3-0.8c0,0,0,0.1,0,0.1c0,3.2,2.3,5.8,5.3,6.4c-0.6,0.1-1.1,0.2-1.7,0.2c-0.4,0-0.8,0-1.2-0.1 c0.8,2.6,3.3,4.5,6.1,4.6c-2.2,1.8-5.1,2.8-8.2,2.8c-0.5,0-1.1,0-1.6-0.1c2.9,1.9,6.4,2.9,10.1,2.9c12.1,0,18.7-10,18.7-18.7 c0-0.3,0-0.6,0-0.8C46,24.5,47.1,23.4,48,22.1z"}),R("twitter",function(e,{title:t,via:r,hashtags:n=[],related:a=[]}){return M(e,"twitter.url"),M(Array.isArray(n),"twitter.hashtags is not an array"),M(Array.isArray(a),"twitter.related is not an array"),"https://twitter.com/intent/tweet"+F({url:e,text:t,via:r,hashtags:n.length>0?n.join(","):void 0,related:a.length>0?a.join(","):void 0})},e=>({hashtags:e.hashtags,title:e.title,via:e.via,related:e.related}),{windowWidth:550,windowHeight:400}),P({color:"#7360f2",path:"m31.0,12.3c9.0,0.2 16.4,6.2 18.0,15.2c0.2,1.5 0.3,3.0 0.4,4.6a1.0,1.0 0 0 1 -0.8,1.2l-0.1,0a1.1,1.1 0 0 1 -1.0,-1.2l0,0c-0.0,-1.2 -0.1,-2.5 -0.3,-3.8a16.1,16.1 0 0 0 -13.0,-13.5c-1.0,-0.1 -2.0,-0.2 -3.0,-0.3c-0.6,-0.0 -1.4,-0.1 -1.6,-0.8a1.1,1.1 0 0 1 0.9,-1.2l0.6,0l0.0,-0.0zm10.6,39.2a19.9,19.9 0 0 1 -2.1,-0.6c-6.9,-2.9 -13.2,-6.6 -18.3,-12.2a47.5,47.5 0 0 1 -7.0,-10.7c-0.8,-1.8 -1.6,-3.7 -2.4,-5.6c-0.6,-1.7 0.3,-3.4 1.4,-4.7a11.3,11.3 0 0 1 3.7,-2.8a2.4,2.4 0 0 1 3.0,0.7a39.0,39.0 0 0 1 4.7,6.5a3.1,3.1 0 0 1 -0.8,4.2c-0.3,0.2 -0.6,0.5 -1.0,0.8a3.3,3.3 0 0 0 -0.7,0.7a2.1,2.1 0 0 0 -0.1,1.9c1.7,4.9 4.7,8.7 9.7,10.8a5.0,5.0 0 0 0 2.5,0.6c1.5,-0.1 2.0,-1.8 3.1,-2.7a2.9,2.9 0 0 1 3.5,-0.1c1.1,0.7 2.2,1.4 3.3,2.2a37.8,37.8 0 0 1 3.1,2.4a2.4,2.4 0 0 1 0.7,3.0a10.4,10.4 0 0 1 -4.4,4.8a10.8,10.8 0 0 1 -1.9,0.6c-0.7,-0.2 0.6,-0.2 0,0l0.0,0l0,-0.0zm3.1,-21.4a4.2,4.2 0 0 1 -0.0,0.6a1.0,1.0 0 0 1 -1.9,0.1a2.7,2.7 0 0 1 -0.1,-0.8a10.9,10.9 0 0 0 -1.4,-5.5a10.2,10.2 0 0 0 -4.2,-4.0a12.3,12.3 0 0 0 -3.4,-1.0c-0.5,-0.0 -1.0,-0.1 -1.5,-0.2a0.9,0.9 0 0 1 -0.9,-1.0l0,-0.1a0.9,0.9 0 0 1 0.9,-0.9l0.1,0a14.1,14.1 0 0 1 5.9,1.5a11.9,11.9 0 0 1 6.5,9.3c0,0.1 0.0,0.3 0.0,0.5c0,0.4 0.0,0.9 0.0,1.5l0,0l0.0,0.0zm-5.6,-0.2a1.1,1.1 0 0 1 -1.2,-0.9l0,-0.1a11.3,11.3 0 0 0 -0.2,-1.4a4.0,4.0 0 0 0 -1.5,-2.3a3.9,3.9 0 0 0 -1.2,-0.5c-0.5,-0.1 -1.1,-0.1 -1.6,-0.2a1.0,1.0 0 0 1 -0.8,-1.1l0,0l0,0a1.0,1.0 0 0 1 1.1,-0.8c3.4,0.2 6.0,2.0 6.3,6.2a2.8,2.8 0 0 1 0,0.8a0.8,0.8 0 0 1 -0.8,0.7l0,0l0.0,-0.0z"}),R("viber",function(e,{title:t,separator:r}){return M(e,"viber.url"),"viber://forward"+F({text:t?t+r+e:e})},e=>({title:e.title,separator:e.separator||" "}),{windowWidth:660,windowHeight:460}),P({color:"#4C75A3",path:"M44.94,44.84h-0.2c-2.17-.36-3.66-1.92-4.92-3.37C39.1,40.66,38,38.81,36.7,39c-1.85.3-.93,3.52-1.71,4.9-0.62,1.11-3.29.91-5.12,0.71-5.79-.62-8.75-3.77-11.35-7.14A64.13,64.13,0,0,1,11.6,26a10.59,10.59,0,0,1-1.51-4.49C11,20.7,12.56,21,14.11,21c1.31,0,3.36-.29,4.32.2C19,21.46,19.57,23,20,24a37.18,37.18,0,0,0,3.31,5.82c0.56,0.81,1.41,2.35,2.41,2.14s1.06-2.63,1.1-4.18c0-1.77,0-4-.5-4.9S25,22,24.15,21.47c0.73-1.49,2.72-1.63,5.12-1.63,2,0,4.84-.23,5.62,1.12s0.25,3.85.2,5.71c-0.06,2.09-.41,4.25,1,5.21,1.09-.12,1.68-1.2,2.31-2A28,28,0,0,0,41.72,24c0.44-1,.91-2.65,1.71-3,1.21-.47,3.15-0.1,4.92-0.1,1.46,0,4.05-.41,4.52.61,0.39,0.85-.75,3-1.1,3.57a61.88,61.88,0,0,1-4.12,5.61c-0.58.78-1.78,2-1.71,3.27,0.05,0.94,1,1.67,1.71,2.35a33.12,33.12,0,0,1,3.92,4.18c0.47,0.62,1.5,2,1.4,2.76C52.66,45.81,46.88,44.24,44.94,44.84Z"}),R("vk",function(e,{title:t,image:r,noParse:n,noVkLinks:a}){return M(e,"vk.url"),"https://vk.com/share.php"+F({url:e,title:t,image:r,noparse:+!!n,no_vk_links:+!!a})},e=>({title:e.title,image:e.image,noParse:e.noParse,noVkLinks:e.noVkLinks}),{windowWidth:660,windowHeight:460}),I(function(e,t){window.VK||(window.VK={}),window.VK.Share={count:(e,t)=>{var r,n;return null==(n=null==(r=window.VK.callbacks)?void 0:r[e])?void 0:n.call(r,t)}},window.VK.callbacks=[];let r=window.VK.callbacks.length;return window.VK.callbacks.push(t),C("https://vk.com/share.php"+F({act:"count",index:r,url:e}))}),P({color:"#DF2029",path:"M40.9756152,15.0217119 C40.5000732,15.0546301 39.9999314,15.1204666 39.5325878,15.2192213 C38.6634928,15.4085016 38.0977589,16.2643757 38.2863368,17.1284787 C38.4667163,18.0008129 39.3194143,18.5686519 40.1885094,18.3793715 C42.8613908,17.8115326 45.7720411,18.6427174 47.7316073,20.8153207 C49.6911735,22.996153 50.2077122,25.975254 49.3714112,28.5840234 C49.1008441,29.4316684 49.5763861,30.3533789 50.4208857,30.6249537 C51.2653852,30.8965286 52.1754769,30.4192153 52.4542425,29.5715703 C53.6349013,25.9011885 52.9133876,21.7699494 50.1585171,18.7085538 C48.0923641,16.4042776 45.2063093,15.1533848 42.3530505,15.0217119 C41.8775084,14.9970227 41.4511594,14.9887937 40.9756152,15.0217119 Z M27.9227762,19.8277737 C24.9957268,20.140498 20.863421,22.4365431 17.2312548,26.0822378 C13.2711279,30.0571148 11,34.2871065 11,37.9328012 C11,44.9032373 19.8713401,49.125 28.5786978,49.125 C39.9917329,49.125 47.600423,42.4261409 47.600423,37.1427636 C47.600423,33.9496952 44.9603397,32.1638816 42.549827,31.4149913 C41.9594976,31.2339421 41.5167516,31.1434164 41.8283133,30.3616079 C42.5006339,28.66632 42.6236176,27.1932286 41.8939054,26.1480742 C40.5328692,24.1894405 36.7203236,24.2881952 32.448635,26.0822378 C32.448635,26.0822378 31.1203949,26.6912261 31.4647526,25.6213825 C32.1206742,23.4981576 32.0304845,21.712342 31.0056075,20.6836478 C30.2840938,19.9512176 29.2510184,19.6878718 27.9227762,19.8277737 Z M42.0906819,20.6836478 C41.6233383,20.6589586 41.1723917,20.716566 40.7132466,20.8153207 C39.9671353,20.9716828 39.4997917,21.7781784 39.6637721,22.5270687 C39.8277525,23.275959 40.5574647,23.7450433 41.303576,23.5804521 C42.1972686,23.3911718 43.2057485,23.6380596 43.8616701,24.3704897 C44.5175916,25.1029198 44.6733735,26.0657797 44.3864073,26.9381118 C44.1486363,27.6705419 44.5093932,28.4770397 45.2391054,28.7156963 C45.9688176,28.9461239 46.780521,28.5922524 47.0100936,27.8598223 C47.584026,26.0740087 47.2396661,24.0248493 45.8950269,22.5270687 C44.886547,21.4078489 43.4845162,20.7494842 42.0906819,20.6836478 Z M29.496988,29.9665891 C35.3100922,30.1723275 39.9917329,33.0691319 40.3852858,37.0769272 C40.8362324,41.6607904 35.5970585,45.9319315 28.6442899,46.6232144 C21.6915214,47.3144973 15.6488446,44.154347 15.197898,39.5787128 C14.7469514,34.9948495 20.059916,30.7237084 27.004486,30.0324256 C27.8735831,29.950131 28.6688875,29.9336709 29.496988,29.9665891 Z M25.5614586,34.3776322 C23.183744,34.5916017 20.9372116,35.9577073 19.9205332,37.9328012 C18.5348994,40.6238672 19.9041362,43.6029661 23.0689567,44.582284 C26.340366,45.5945202 30.1857056,44.0638213 31.5303448,41.1587879 C32.8503864,38.3195909 31.1613894,35.3734082 27.9227762,34.5751416 C27.1438688,34.3776322 26.356763,34.3035667 25.5614586,34.3776322 Z M24.052839,38.7228388 C24.3316067,38.7310678 24.5857748,38.8215935 24.8399449,38.9203482 C25.8648218,39.3400561 26.1845841,40.4428158 25.5614586,41.4221338 C24.9219361,42.3932227 23.5690963,42.8623069 22.5442194,42.4096807 C21.5357395,41.9652856 21.2487754,40.8542948 21.8882979,39.9078951 C22.3638421,39.2001542 23.2247386,38.7146097 24.052839,38.7228388 Z"}),R("weibo",function(e,{title:t,image:r}){return M(e,"weibo.url"),"http://service.weibo.com/share/share.php"+F({url:e,title:t,pic:r})},e=>({title:e.title,image:e.image}),{windowWidth:660,windowHeight:550,windowPosition:"screenCenter"});let U=P({color:"#25D366",path:"m42.32286,33.93287c-0.5178,-0.2589 -3.04726,-1.49644 -3.52105,-1.66732c-0.4712,-0.17346 -0.81554,-0.2589 -1.15987,0.2589c-0.34175,0.51004 -1.33075,1.66474 -1.63108,2.00648c-0.30032,0.33658 -0.60064,0.36247 -1.11327,0.12945c-0.5178,-0.2589 -2.17994,-0.80259 -4.14759,-2.56312c-1.53269,-1.37217 -2.56312,-3.05503 -2.86603,-3.57283c-0.30033,-0.5178 -0.03366,-0.80259 0.22524,-1.06149c0.23301,-0.23301 0.5178,-0.59547 0.7767,-0.90616c0.25372,-0.31068 0.33657,-0.5178 0.51262,-0.85437c0.17088,-0.36246 0.08544,-0.64725 -0.04402,-0.90615c-0.12945,-0.2589 -1.15987,-2.79613 -1.58964,-3.80584c-0.41424,-1.00971 -0.84142,-0.88027 -1.15987,-0.88027c-0.29773,-0.02588 -0.64208,-0.02588 -0.98382,-0.02588c-0.34693,0 -0.90616,0.12945 -1.37736,0.62136c-0.4712,0.5178 -1.80194,1.76053 -1.80194,4.27186c0,2.51134 1.84596,4.945 2.10227,5.30747c0.2589,0.33657 3.63497,5.51458 8.80262,7.74113c1.23237,0.5178 2.1903,0.82848 2.94111,1.08738c1.23237,0.38836 2.35599,0.33657 3.24402,0.20712c0.99159,-0.15534 3.04985,-1.24272 3.47963,-2.45956c0.44013,-1.21683 0.44013,-2.22654 0.31068,-2.45955c-0.12945,-0.23301 -0.46601,-0.36247 -0.98382,-0.59548m-9.40068,12.84407l-0.02589,0c-3.05503,0 -6.08417,-0.82849 -8.72495,-2.38189l-0.62136,-0.37023l-6.47252,1.68286l1.73463,-6.29129l-0.41424,-0.64725c-1.70875,-2.71846 -2.6149,-5.85116 -2.6149,-9.07706c0,-9.39809 7.68934,-17.06155 17.15993,-17.06155c4.58253,0 8.88029,1.78642 12.11655,5.02268c3.23625,3.21036 5.02267,7.50812 5.02267,12.06476c-0.0078,9.3981 -7.69712,17.06155 -17.14699,17.06155m14.58906,-31.58846c-3.93529,-3.80584 -9.1133,-5.95471 -14.62789,-5.95471c-11.36055,0 -20.60848,9.2065 -20.61625,20.52564c0,3.61684 0.94757,7.14565 2.75211,10.26282l-2.92557,10.63564l10.93337,-2.85309c3.0136,1.63108 6.4052,2.4958 9.85634,2.49839l0.01037,0c11.36574,0 20.61884,-9.2091 20.62403,-20.53082c0,-5.48093 -2.14111,-10.64081 -6.03239,-14.51915"}),W=R("whatsapp",function(e,{title:t,separator:r}){return M(e,"whatsapp.url"),"https://"+(/(android|iphone|ipad|mobile)/i.test(navigator.userAgent)?"api":"web")+".whatsapp.com/send"+F({text:t?t+r+e:e})},e=>({title:e.title,separator:e.separator||" "}),{windowWidth:550,windowHeight:400});P({color:"#4326c4",path:"M34.019,10.292c0.21,0.017,0.423,0.034,0.636,0.049 c3.657,0.262,6.976,1.464,9.929,3.635c3.331,2.448,5.635,5.65,6.914,9.584c0.699,2.152,0.983,4.365,0.885,6.623 c-0.136,3.171-1.008,6.13-2.619,8.867c-0.442,0.75-0.908,1.492-1.495,2.141c-0.588,0.651-1.29,1.141-2.146,1.383 c-1.496,0.426-3.247-0.283-3.961-1.642c-0.26-0.494-0.442-1.028-0.654-1.548c-1.156-2.838-2.311-5.679-3.465-8.519 c-0.017-0.042-0.037-0.082-0.065-0.145c-0.101,0.245-0.192,0.472-0.284,0.698c-1.237,3.051-2.475,6.103-3.711,9.155 c-0.466,1.153-1.351,1.815-2.538,2.045c-1.391,0.267-2.577-0.154-3.496-1.247c-0.174-0.209-0.31-0.464-0.415-0.717 c-2.128-5.22-4.248-10.442-6.37-15.665c-0.012-0.029-0.021-0.059-0.036-0.104c0.054-0.003,0.103-0.006,0.15-0.006 c1.498-0.001,2.997,0,4.495-0.004c0.12-0.001,0.176,0.03,0.222,0.146c1.557,3.846,3.117,7.691,4.679,11.536 c0.018,0.046,0.039,0.091,0.067,0.159c0.273-0.673,0.536-1.32,0.797-1.968c1.064-2.627,2.137-5.25,3.19-7.883 c0.482-1.208,1.376-1.917,2.621-2.135c1.454-0.255,2.644,0.257,3.522,1.449c0.133,0.18,0.229,0.393,0.313,0.603 c1.425,3.495,2.848,6.991,4.269,10.488c0.02,0.047,0.04,0.093,0.073,0.172c0.196-0.327,0.385-0.625,0.559-0.935 c0.783-1.397,1.323-2.886,1.614-4.461c0.242-1.312,0.304-2.634,0.187-3.962c-0.242-2.721-1.16-5.192-2.792-7.38 c-2.193-2.939-5.086-4.824-8.673-5.625c-1.553-0.346-3.124-0.405-4.705-0.257c-3.162,0.298-6.036,1.366-8.585,3.258 c-3.414,2.534-5.638,5.871-6.623,10.016c-0.417,1.76-0.546,3.547-0.384,5.348c0.417,4.601,2.359,8.444,5.804,11.517 c2.325,2.073,5.037,3.393,8.094,3.989c1.617,0.317,3.247,0.395,4.889,0.242c1-0.094,1.982-0.268,2.952-0.529 c0.04-0.01,0.081-0.018,0.128-0.028c0,1.526,0,3.047,0,4.586c-0.402,0.074-0.805,0.154-1.21,0.221 c-0.861,0.14-1.728,0.231-2.601,0.258c-0.035,0.002-0.071,0.013-0.108,0.021c-0.493,0-0.983,0-1.476,0 c-0.049-0.007-0.1-0.018-0.149-0.022c-0.315-0.019-0.629-0.033-0.945-0.058c-1.362-0.105-2.702-0.346-4.017-0.716 c-3.254-0.914-6.145-2.495-8.66-4.752c-2.195-1.971-3.926-4.29-5.176-6.963c-1.152-2.466-1.822-5.057-1.993-7.774 c-0.014-0.226-0.033-0.451-0.05-0.676c0-0.502,0-1.003,0-1.504c0.008-0.049,0.02-0.099,0.022-0.148 c0.036-1.025,0.152-2.043,0.338-3.052c0.481-2.616,1.409-5.066,2.8-7.331c2.226-3.625,5.25-6.386,9.074-8.254 c2.536-1.24,5.217-1.947,8.037-2.126c0.23-0.015,0.461-0.034,0.691-0.051C33.052,10.292,33.535,10.292,34.019,10.292z"}),R("workplace",function(e,{quote:t,hashtag:r}){return M(e,"workplace.url"),"https://work.facebook.com/sharer.php"+F({u:e,quote:t,hashtag:r})},e=>({quote:e.quote,hashtag:e.hashtag}),{windowWidth:550,windowHeight:400}),P({color:"#000000",path:"M 41.116 18.375 h 4.962 l -10.8405 12.39 l 12.753 16.86 H 38.005 l -7.821 -10.2255 L 21.235 47.625 H 16.27 l 11.595 -13.2525 L 15.631 18.375 H 25.87 l 7.0695 9.3465 z m -1.7415 26.28 h 2.7495 L 24.376 21.189 H 21.4255 z"});let V=(()=>{let e=0,t=()=>`0000${(1679616*Math.random()<<0).toString(36)}`.slice(-4);return()=>(e+=1,`u${t()}${e}`)})();function G(e){let t=[];for(let r=0,n=e.length;r<n;r++)t.push(e[r]);return t}let Z=null;function K(e={}){return Z||(Z=e.includeStyleProperties?e.includeStyleProperties:G(window.getComputedStyle(document.documentElement)))}function B(e,t){let r=(e.ownerDocument.defaultView||window).getComputedStyle(e).getPropertyValue(t);return r?parseFloat(r.replace("px","")):0}function Q(e,t={}){return{width:t.width||function(e){let t=B(e,"border-left-width"),r=B(e,"border-right-width");return e.clientWidth+t+r}(e),height:t.height||function(e){let t=B(e,"border-top-width"),r=B(e,"border-bottom-width");return e.clientHeight+t+r}(e)}}function J(e){return new Promise((t,r)=>{let n=new Image;n.onload=()=>{n.decode().then(()=>{requestAnimationFrame(()=>t(n))})},n.onerror=r,n.crossOrigin="anonymous",n.decoding="async",n.src=e})}async function Y(e){return Promise.resolve().then(()=>new XMLSerializer().serializeToString(e)).then(encodeURIComponent).then(e=>`data:image/svg+xml;charset=utf-8,${e}`)}async function X(e,t,r){let n="http://www.w3.org/2000/svg",a=document.createElementNS(n,"svg"),s=document.createElementNS(n,"foreignObject");return a.setAttribute("width",`${t}`),a.setAttribute("height",`${r}`),a.setAttribute("viewBox",`0 0 ${t} ${r}`),s.setAttribute("width","100%"),s.setAttribute("height","100%"),s.setAttribute("x","0"),s.setAttribute("y","0"),s.setAttribute("externalResourcesRequired","true"),a.appendChild(s),s.appendChild(e),Y(a)}let ee=(e,t)=>{if(e instanceof t)return!0;let r=Object.getPrototypeOf(e);return null!==r&&(r.constructor.name===t.name||ee(r,t))};function et(e,t,r,n){let a=window.getComputedStyle(e,r),s=a.getPropertyValue("content");if(""===s||"none"===s)return;let i=V();try{t.className=`${t.className} ${i}`}catch(e){return}let o=document.createElement("style");o.appendChild(function(e,t,r,n){let a=`.${e}:${t}`,s=r.cssText?function(e){let t=e.getPropertyValue("content");return`${e.cssText} content: '${t.replace(/'|"/g,"")}';`}(r):K(n).map(e=>{let t=r.getPropertyValue(e),n=r.getPropertyPriority(e);return`${e}: ${t}${n?" !important":""};`}).join(" ");return document.createTextNode(`${a}{${s}}`)}(i,r,a,n)),t.appendChild(o)}let er="application/font-woff",en="image/jpeg",ea={woff:er,woff2:er,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:en,jpeg:en,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml",webp:"image/webp"};function es(e){return ea[(function(e){let t=/\.([^./]*?)$/g.exec(e);return t?t[1]:""})(e).toLowerCase()]||""}function ei(e){return -1!==e.search(/^(data:)/)}function eo(e,t){return`data:${t};base64,${e}`}async function el(e,t,r){let n=await fetch(e,t);if(404===n.status)throw Error(`Resource "${n.url}" not found`);let a=await n.blob();return new Promise((e,t)=>{let s=new FileReader;s.onerror=t,s.onloadend=()=>{try{e(r({res:n,result:s.result}))}catch(e){t(e)}},s.readAsDataURL(a)})}let ec={};async function eu(e,t,r){var n,a,s;let i,o;let l=(n=e,a=t,s=r.includeQueryParams,o=n.replace(/\?.*/,""),s&&(o=n),/ttf|otf|eot|woff2?/i.test(o)&&(o=o.replace(/.*\//,"")),a?`[${a}]${o}`:o);if(null!=ec[l])return ec[l];r.cacheBust&&(e+=(/\?/.test(e)?"&":"?")+new Date().getTime());try{let n=await el(e,r.fetchRequestInit,({res:e,result:r})=>(t||(t=e.headers.get("Content-Type")||""),r.split(/,/)[1]));i=eo(n,t)}catch(n){i=r.imagePlaceholder||"";let t=`Failed to fetch resource: ${e}`;n&&(t="string"==typeof n?n:n.message),t&&console.warn(t)}return ec[l]=i,i}async function ed(e){let t=e.toDataURL();return"data:,"===t?e.cloneNode(!1):J(t)}async function em(e,t){if(e.currentSrc){let t=document.createElement("canvas"),r=t.getContext("2d");return t.width=e.clientWidth,t.height=e.clientHeight,null==r||r.drawImage(e,0,0,t.width,t.height),J(t.toDataURL())}let r=e.poster,n=es(r);return J(await eu(r,n,t))}async function eh(e,t){var r;try{if(null===(r=null==e?void 0:e.contentDocument)||void 0===r?void 0:r.body)return await ey(e.contentDocument.body,t,!0)}catch(e){}return e.cloneNode(!1)}async function ep(e,t){return ee(e,HTMLCanvasElement)?ed(e):ee(e,HTMLVideoElement)?em(e,t):ee(e,HTMLIFrameElement)?eh(e,t):e.cloneNode(eg(e))}let ef=e=>null!=e.tagName&&"SLOT"===e.tagName.toUpperCase(),eg=e=>null!=e.tagName&&"SVG"===e.tagName.toUpperCase();async function ew(e,t,r){var n,a;if(eg(t))return t;let s=[];return 0===(s=ef(e)&&e.assignedNodes?G(e.assignedNodes()):ee(e,HTMLIFrameElement)&&(null===(n=e.contentDocument)||void 0===n?void 0:n.body)?G(e.contentDocument.body.childNodes):G((null!==(a=e.shadowRoot)&&void 0!==a?a:e).childNodes)).length||ee(e,HTMLVideoElement)||await s.reduce((e,n)=>e.then(()=>ey(n,r)).then(e=>{e&&t.appendChild(e)}),Promise.resolve()),t}async function ex(e,t){let r=e.querySelectorAll?e.querySelectorAll("use"):[];if(0===r.length)return e;let n={};for(let a=0;a<r.length;a++){let s=r[a].getAttribute("xlink:href");if(s){let r=e.querySelector(s),a=document.querySelector(s);r||!a||n[s]||(n[s]=await ey(a,t,!0))}}let a=Object.values(n);if(a.length){let t="http://www.w3.org/1999/xhtml",r=document.createElementNS(t,"svg");r.setAttribute("xmlns",t),r.style.position="absolute",r.style.width="0",r.style.height="0",r.style.overflow="hidden",r.style.display="none";let n=document.createElementNS(t,"defs");r.appendChild(n);for(let e=0;e<a.length;e++)n.appendChild(a[e]);e.appendChild(r)}return e}async function ey(e,t,r){return r||!t.filter||t.filter(e)?Promise.resolve(e).then(e=>ep(e,t)).then(r=>ew(e,r,t)).then(r=>(function(e,t,r){if(ee(t,Element))(function(e,t,r){let n=t.style;if(!n)return;let a=window.getComputedStyle(e);a.cssText?(n.cssText=a.cssText,n.transformOrigin=a.transformOrigin):K(r).forEach(r=>{let s=a.getPropertyValue(r);if("font-size"===r&&s.endsWith("px")){let e=Math.floor(parseFloat(s.substring(0,s.length-2)))-.1;s=`${e}px`}ee(e,HTMLIFrameElement)&&"display"===r&&"inline"===s&&(s="block"),"d"===r&&t.getAttribute("d")&&(s=`path(${t.getAttribute("d")})`),n.setProperty(r,s,a.getPropertyPriority(r))})})(e,t,r),et(e,t,":before",r),et(e,t,":after",r),ee(e,HTMLTextAreaElement)&&(t.innerHTML=e.value),ee(e,HTMLInputElement)&&t.setAttribute("value",e.value),function(e,t){if(ee(e,HTMLSelectElement)){let r=Array.from(t.children).find(t=>e.value===t.getAttribute("value"));r&&r.setAttribute("selected","")}}(e,t);return t})(e,r,t)).then(e=>ex(e,t)):null}let eb=/url\((['"]?)([^'"]+?)\1\)/g,eC=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,ev=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;async function ek(e,t,r,n,a){try{let s;let i=r?function(e,t){if(e.match(/^[a-z]+:\/\//i))return e;if(e.match(/^\/\//))return window.location.protocol+e;if(e.match(/^[a-z]+:/i))return e;let r=document.implementation.createHTMLDocument(),n=r.createElement("base"),a=r.createElement("a");return r.head.appendChild(n),r.body.appendChild(a),t&&(n.href=t),a.href=e,a.href}(t,r):t,o=es(t);if(a){let e=await a(i);s=eo(e,o)}else s=await eu(i,o,n);return e.replace(function(e){let t=e.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return RegExp(`(url\\(['"]?)(${t})(['"]?\\))`,"g")}(t),`$1${s}$3`)}catch(e){}return e}function ej(e){return -1!==e.search(eb)}async function eS(e,t,r){if(!ej(e))return e;let n=function(e,{preferredFontFormat:t}){return t?e.replace(ev,e=>{for(;;){let[r,,n]=eC.exec(e)||[];if(!n)return"";if(n===t)return`src: ${r};`}}):e}(e,r);return(function(e){let t=[];return e.replace(eb,(e,r,n)=>(t.push(n),e)),t.filter(e=>!ei(e))})(n).reduce((e,n)=>e.then(e=>ek(e,n,t,r)),Promise.resolve(n))}async function eN(e,t,r){var n;let a=null===(n=t.style)||void 0===n?void 0:n.getPropertyValue(e);if(a){let n=await eS(a,null,r);return t.style.setProperty(e,n,t.style.getPropertyPriority(e)),!0}return!1}async function eE(e,t){await eN("background",e,t)||await eN("background-image",e,t),await eN("mask",e,t)||await eN("-webkit-mask",e,t)||await eN("mask-image",e,t)||await eN("-webkit-mask-image",e,t)}async function eA(e,t){let r=ee(e,HTMLImageElement);if(!(r&&!ei(e.src))&&!(ee(e,SVGImageElement)&&!ei(e.href.baseVal)))return;let n=r?e.src:e.href.baseVal,a=await eu(n,es(n),t);await new Promise((n,s)=>{e.onload=n,e.onerror=t.onImageErrorHandler?(...e)=>{try{n(t.onImageErrorHandler(...e))}catch(e){s(e)}}:s,e.decode&&(e.decode=n),"lazy"===e.loading&&(e.loading="eager"),r?(e.srcset="",e.src=a):e.href.baseVal=a})}async function ez(e,t){let r=G(e.childNodes).map(e=>e$(e,t));await Promise.all(r).then(()=>e)}async function e$(e,t){ee(e,Element)&&(await eE(e,t),await eA(e,t),await ez(e,t))}let eL={};async function eP(e){let t=eL[e];if(null!=t)return t;let r=await fetch(e);return t={url:e,cssText:await r.text()},eL[e]=t,t}async function eO(e,t){let r=e.cssText,n=/url\(["']?([^"')]+)["']?\)/g;return Promise.all((r.match(/url\([^)]+\)/g)||[]).map(async a=>{let s=a.replace(n,"$1");return s.startsWith("https://")||(s=new URL(s,e.url).href),el(s,t.fetchRequestInit,({result:e})=>(r=r.replace(a,`url(${e})`),[a,e]))})).then(()=>r)}function eM(e){if(null==e)return[];let t=[],r=e.replace(/(\/\*[\s\S]*?\*\/)/gi,""),n=RegExp("((@.*?keyframes [\\s\\S]*?){([\\s\\S]*?}\\s*?)})","gi");for(;;){let e=n.exec(r);if(null===e)break;t.push(e[0])}r=r.replace(n,"");let a=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,s=RegExp("((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})","gi");for(;;){let e=a.exec(r);if(null===e){if(null===(e=s.exec(r)))break;a.lastIndex=s.lastIndex}else s.lastIndex=a.lastIndex;t.push(e[0])}return t}async function eF(e,t){let r=[],n=[];return e.forEach(r=>{if("cssRules"in r)try{G(r.cssRules||[]).forEach((e,a)=>{if(e.type===CSSRule.IMPORT_RULE){let s=a+1,i=e.href,o=eP(i).then(e=>eO(e,t)).then(e=>eM(e).forEach(e=>{try{r.insertRule(e,e.startsWith("@import")?s+=1:r.cssRules.length)}catch(t){console.error("Error inserting rule from remote css",{rule:e,error:t})}})).catch(e=>{console.error("Error loading remote css",e.toString())});n.push(o)}})}catch(s){let a=e.find(e=>null==e.href)||document.styleSheets[0];null!=r.href&&n.push(eP(r.href).then(e=>eO(e,t)).then(e=>eM(e).forEach(e=>{a.insertRule(e,a.cssRules.length)})).catch(e=>{console.error("Error loading remote stylesheet",e)})),console.error("Error inlining remote css file",s)}}),Promise.all(n).then(()=>(e.forEach(e=>{if("cssRules"in e)try{G(e.cssRules||[]).forEach(e=>{r.push(e)})}catch(t){console.error(`Error while reading CSS rules from ${e.href}`,t)}}),r))}async function eD(e,t){if(null==e.ownerDocument)throw Error("Provided element is not within a Document");let r=G(e.ownerDocument.styleSheets);return(await eF(r,t)).filter(e=>e.type===CSSRule.FONT_FACE_RULE).filter(e=>ej(e.style.getPropertyValue("src")))}function eT(e){return e.trim().replace(/["']/g,"")}async function eH(e,t){let r=await eD(e,t),n=function(e){let t=new Set;return function e(r){(r.style.fontFamily||getComputedStyle(r).fontFamily).split(",").forEach(e=>{t.add(eT(e))}),Array.from(r.children).forEach(t=>{t instanceof HTMLElement&&e(t)})}(e),t}(e);return(await Promise.all(r.filter(e=>n.has(eT(e.style.fontFamily))).map(e=>{let r=e.parentStyleSheet?e.parentStyleSheet.href:null;return eS(e.cssText,r,t)}))).join("\n")}async function e_(e,t){let r=null!=t.fontEmbedCSS?t.fontEmbedCSS:t.skipFonts?null:await eH(e,t);if(r){let t=document.createElement("style"),n=document.createTextNode(r);t.appendChild(n),e.firstChild?e.insertBefore(t,e.firstChild):e.appendChild(t)}}async function eR(e,t={}){let{width:r,height:n}=Q(e,t),a=await ey(e,t,!0);return await e_(a,t),await e$(a,t),function(e,t){let{style:r}=e;t.backgroundColor&&(r.backgroundColor=t.backgroundColor),t.width&&(r.width=`${t.width}px`),t.height&&(r.height=`${t.height}px`);let n=t.style;null!=n&&Object.keys(n).forEach(e=>{r[e]=n[e]})}(a,t),await X(a,r,n)}async function eq(e,t={}){let{width:r,height:n}=Q(e,t),a=await eR(e,t),s=await J(a),i=document.createElement("canvas"),o=i.getContext("2d"),l=t.pixelRatio||function(){let e,t;try{t=process}catch(e){}let r=t&&t.env?t.env.devicePixelRatio:null;return r&&Number.isNaN(e=parseInt(r,10))&&(e=1),e||window.devicePixelRatio||1}(),c=t.canvasWidth||r,u=t.canvasHeight||n;if(i.width=c*l,i.height=u*l,!t.skipAutoScale)(i.width>16384||i.height>16384)&&(i.width>16384&&i.height>16384?i.width>i.height?(i.height*=16384/i.width,i.width=16384):(i.width*=16384/i.height,i.height=16384):i.width>16384?(i.height*=16384/i.width,i.width=16384):(i.width*=16384/i.height,i.height=16384));return i.style.width=`${c}`,i.style.height=`${u}`,t.backgroundColor&&(o.fillStyle=t.backgroundColor,o.fillRect(0,0,i.width,i.height)),o.drawImage(s,0,0,i.width,i.height),i}async function eI(e,t={}){return(await eq(e,t)).toDataURL("image/jpeg",t.quality||1)}let eU=i().memo(()=>(0,n.jsx)("header",{className:"fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md",children:(0,n.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,n.jsx)(f.default,{height:60,width:60,src:g.A.src,alt:"Uwhiz Logo",quality:100,className:"object-contain sm:h-20 sm:w-20"}),(0,n.jsx)("h1",{className:"text-lg sm:text-2xl font-bold tracking-tight",children:"Uest Daily Quiz"})]})}));function eW(){let e=(0,o.useRouter)();(0,o.useSearchParams)();let t=y(),[r,i]=(0,s.useState)(!1),[l,g]=(0,s.useState)(!1),[b,C]=(0,s.useState)(!1),[v,k]=(0,s.useState)(!1),[j,S]=(0,s.useState)(!1),[N,E]=(0,s.useState)(!1),[A,z]=(0,s.useState)(!1),[$,L]=(0,s.useState)([]),[P,O]=(0,s.useState)(0),[M,F]=(0,s.useState)(0),[D,T]=(0,s.useState)([]),[H,_]=(0,s.useState)(!1),[R,q]=(0,s.useState)(null),[I,V]=(0,s.useState)(!1);(0,s.useRef)(null);let[G,Z]=(0,s.useState)(""),[K,B]=(0,s.useState)(0),[Q,J]=(0,s.useState)(!1),[Y,X]=(0,s.useState)(null),[ee,et]=(0,s.useState)(!1),[er,en]=(0,s.useState)(null),ea=(0,s.useRef)(null),[es,ei]=(0,s.useState)(!1),[eo,el]=(0,s.useState)(null),ec=(0,s.useCallback)(async()=>{let e=$[P];if(R){let t=R===e.correctAnswer;T(r=>[...r,{questionId:e.id,selectedAnswer:R,isCorrect:t}]),J(!0),setTimeout(()=>{J(!1),q(null),P<$.length-1?O(e=>e+1):_(!0)},1e3)}else T(t=>[...t,{questionId:e.id,selectedAnswer:"skipped",isCorrect:!1}]),p.toast.warning("Question skipped."),P<$.length-1?O(e=>e+1):_(!0)},[R,$,P]),eu=(0,s.useMemo)(()=>D.reduce((e,t)=>e+ +!!t.isCorrect,0),[D]),ed=(0,s.useMemo)(()=>{let e;let t=eu/$.length*100;return e=t>=100?5:t>=90?4:t>=80?3:t>=70?2:+(t>=60),r?5*e:e},[eu,$.length,r]);(0,s.useCallback)(async()=>{if(!t){g(!0);return}try{let e=await c(t);if(!e.success){p.toast.error(e.error),C(!0);return}if("APPROVED"!=e.data.status){C(!0),ei(!0),p.toast.error("Student Profile is not approved yet. Please check your profile.");return}let n=e.data.medium.toUpperCase(),a=await w(t,n,r);a&&Array.isArray(a)?(L(a),F(45),k(!0)):p.toast.error("No questions found or invalid response.")}catch(e){p.toast.error(e)}},[t,r]);let em=async(e=3,t=1)=>{try{if(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement){if(document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen&&await document.mozCancelFullScreen(),await new Promise(e=>setTimeout(e,100)),!document.fullscreenElement&&!document.webkitFullscreenElement&&!document.mozFullScreenElement)return!0;if(t<e)return await em(e,t+1);throw Error("Max attempts reached")}return!0}catch(r){if(console.error(`Failed to exit full-screen mode (attempt ${t}):`,r),t<e)return await new Promise(e=>setTimeout(e,500)),await em(e,t+1);return p.toast.error("Failed to exit full-screen mode. Please press Esc to exit manually."),!1}},eh=()=>{let e=document.documentElement;e.requestFullscreen&&e.requestFullscreen().catch(e=>console.error("Failed to enter fullscreen:",e))};(0,s.useCallback)(async e=>{if(v||l||b||N||I||j)return;let r=["F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"],n=e.ctrlKey&&e.shiftKey&&("I"===e.key||"J"===e.key||"C"===e.key)||e.metaKey&&e.altKey&&"I"===e.key||"F12"===e.key,a=(e.ctrlKey||e.metaKey)&&("c"===e.key||"C"===e.key);if(["Alt","Control","Tab","Shift","Enter"].includes(e.key)||r.includes(e.key)||n||a){if(e.preventDefault(),a){p.toast.warning("Copying is disabled during the quiz.");return}if(!t){B(0);return}V(!0);try{let a=n?"DevTools shortcut":r.includes(e.key)?`Function key "${e.key}"`:`Restricted key "${e.key}"`;await u({studentId:t,reason:a});let s=await d(t);B(s),1===s?(E(!0),p.toast.warning(`${a} detected.`)):2===s?(E(!0),p.toast.warning(`${a} detected. One more violation will terminate the quiz.`)):s>=3&&(z(!0),Z("Quiz terminated due to multiple cheating attempts."),t&&x(t),p.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){p.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{V(!1)}}},[t,v,l,b,N,I,j]),(0,s.useCallback)(async()=>{if(!v&&!l&&!b&&!N&&!I&&!j&&document.hidden){V(!0);try{if(await u({studentId:t,reason:"Tab switch"}),!t){B(0);return}let e=await d(t);B(e),1===e?(E(!0),p.toast.warning("Tab switch detected.")):2===e?(E(!0),p.toast.warning("Again tab switch detected. One more violation will terminate the quiz.")):e>=3&&(z(!0),Z("Quiz terminated due to multiple cheating attempts."),t&&x(t),p.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){p.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{V(!1)}}},[t,v,l,b,N,I,j]),(0,s.useCallback)(async e=>{v||l||b||N||I||j||(e.preventDefault(),p.toast.warning("Right-click is disabled during the quiz."))},[t,v,l,b,N,I,j]),(0,s.useCallback)(async()=>{if(!v&&!l&&!b&&!N&&!I&&!j){V(!0);try{await u({studentId:t,reason:"Window blur"});let e=await d(t);B(e),1===e?(E(!0),p.toast.warning("Window focus lost.")):2==e?(E(!0),p.toast.warning("Window focus lost again. One more violation will terminate the quiz.")):e>=3&&(z(!0),Z("Quiz terminated due to multiple cheating attempts."),t&&x(t),p.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){p.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{V(!1)}}},[t,v,l,b,N,I,j]),(0,s.useCallback)(async()=>{if(!v&&!l&&!b&&!N&&!I&&!j&&!document.fullscreenElement){V(!0);try{if(await u({studentId:t,reason:"Full-screen exit"}),!t){B(0);return}let e=await d(t);B(e),1===e?(E(!0),p.toast.warning("You have exited full-screen mode.")):2===e?(E(!0),p.toast.warning("Again you have exited full-screen mode. One more violation will terminate the quiz.")):e>=3&&(z(!0),Z("Quiz terminated due to multiple cheating attempts."),t&&x(t),p.toast.error("Quiz terminated due to multiple cheating attempts."))}catch(e){p.toast.error("Failed to save termination record.",{description:e instanceof Error?e.message:"Unknown error"})}finally{V(!1)}}},[t,v,l,b,N,I,j]);let ep=async()=>{z(!1),"true"===localStorage.getItem("mobile_request")&&(localStorage.removeItem("mobile_request"),window.location.href="UEST://DailyQuiz"),(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement)&&(await em()||p.toast.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.")),e.push("/mock-exam-card"),setTimeout(()=>{e.push("/mock-exam-card")},1e3)},ef=(0,s.useCallback)(e=>{let t=Math.floor(e/60);return`${t.toString().padStart(2,"0")}:${(e%60).toString().padStart(2,"0")}`},[]),eg=(0,s.useMemo)(()=>$.length>0?(P+1)/$.length*100:0,[P,$]),ew=e=>{let t="w-full h-auto min-h-[60px] sm:min-h-[80px] whitespace-normal text-wrap font-medium rounded-lg py-3 sm:py-4 text-sm sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-start justify-start gap-3 px-3 sm:px-6 shadow-sm border border-gray-200 bg-white";return R===e?`${t} bg-orange-100 border-orange-500`:t},ex=e=>{Q||q(e)};if(l)return(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,n.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,n.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:"Login Required"}),(0,n.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"Please log in as a student to access the quiz."}),(0,n.jsx)(a.$,{onClick:()=>e.push(`/student/login?redirect=/mock-test${r?"?isWeekly=true":""}`),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Login to Continue"})]})});if(b)return(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,n.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,n.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:es?"Profile Not Approved":"Complete Your Profile"}),(0,n.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:es?"Your profile has not been approved yet. Please wait for approval and check notifications.":"Your profile is incomplete. Please complete your profile to proceed."}),(0,n.jsx)(a.$,{onClick:()=>{e.push("/student/profile")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:es?"Update Profile":"Complete Profile"})]})});if(j)return(0,n.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:(0,n.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,n.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-black",children:"Exam Already Taken"}),(0,n.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:r?"You have already attempted the weekly exam this week. Please try again next Sunday.":"You have already attempted the daily exam today. Please try again tomorrow."}),(0,n.jsx)(a.$,{onClick:()=>{S(!1),e.push("/mock-exam-card")},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all",children:"Go to Home"})]})});if(0===$.length)return(0,n.jsxs)("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 text-gray-900",children:[(0,n.jsx)("p",{className:"text-base sm:text-xl font-medium mr-4",children:"Loading questions..."}),(0,n.jsx)(m.A,{className:"w-5 h-5 sm:w-8 sm:h-8 animate-spin text-customOrange"})]});let ey=async()=>{if(ea.current)try{let{width:e}=ea.current.getBoundingClientRect(),t=ea.current.scrollHeight,n=await eI(ea.current,{quality:1,pixelRatio:2,backgroundColor:"#ffffff",canvasWidth:e,canvasHeight:t,style:{margin:"0",padding:"35px 0px 0px 20px  "}}),a=document.createElement("a");a.href=n,a.download=r?"uest-weekly-quiz-result.jpg":"uest-daily-quiz-result.jpg",a.click()}catch(e){console.error("Failed to download card:",e),p.toast.error("Failed to download the quiz result card. Please try again.")}};if(H){let e=`I scored ${eu}/${$.length} with a streak of ${Y?.streak||0} ${r?"weeks":"days"} on U-whiz ${r?"Weekly":"Daily"} Daily Exam! 🎉 Try it out!`,t=localStorage.getItem("student_data"),a=t?JSON.parse(t):null,s=a?`${a.firstName} ${a.lastName}`:"Unknown Student";return(0,n.jsx)("div",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200",children:(0,n.jsxs)("div",{className:"relative z-10 w-full max-w-md text-center",children:[(0,n.jsxs)("div",{ref:ea,className:"bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-md",children:[(0,n.jsxs)("div",{className:"mb-6 text-center text-sm text-gray-600 dark:text-gray-400 flex flex-col items-center",children:[(0,n.jsx)("p",{children:"Powered by"}),(0,n.jsx)(f.default,{src:"/logo.png",alt:"Preply Logo",width:120,height:40})]}),(0,n.jsx)("div",{className:"absolute inset-0 z-0 pointer-events-none",children:(0,n.jsx)("div",{className:"animate-pulse opacity-20 bg-[radial-gradient(#facc15_1px,transparent_1px)] bg-[length:20px_20px] w-full h-full"})}),(0,n.jsxs)("h1",{className:"text-3xl font-extrabold text-customOrange dark:text-orange-400 mb-4 text-center",children:[r?"Weekly":"Daily"," Quiz Completed \uD83C\uDF89"]}),(0,n.jsxs)("p",{className:"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 text-center",children:["Congratulations, ",(0,n.jsx)("span",{className:"text-customOrange font-bold",children:s})]}),(0,n.jsx)("p",{className:"text-base text-gray-600 dark:text-gray-400 mb-4 text-center",children:"Keep up the momentum. \uD83D\uDCAA"}),(0,n.jsx)("div",{className:"w-1/3 mx-auto h-2 bg-gray-200 dark:bg-gray-600 rounded-full mb-4",children:(0,n.jsx)("div",{className:"h-full bg-customOrange dark:bg-orange-400 rounded-full transition-all duration-500",style:{width:`${$.length>0?eu/$.length*100:0}%`}})}),(0,n.jsxs)("p",{className:"text-base text-gray-800 dark:text-gray-200 mb-4 text-center",children:["Final Score: ",(0,n.jsx)("span",{className:"font-bold",children:eu})," / ",$.length]}),(0,n.jsx)("div",{className:"flex justify-center mb-4",children:(0,n.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,n.jsx)("div",{className:"absolute w-24 h-24 rounded-full bg-orange-500 opacity-30 blur-xl animate-ping"}),(0,n.jsx)("div",{className:"absolute w-16 h-16 rounded-full bg-red-500 opacity-20 blur-md animate-pulse"}),(0,n.jsx)("div",{className:"absolute w-12 h-12 rounded-full bg-yellow-300 opacity-40 blur-sm animate-bounce"}),(0,n.jsx)("div",{className:"relative w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-red-500 flex items-center justify-center shadow-lg animate-burning",children:(0,n.jsx)("span",{className:"text-4xl",children:"\uD83D\uDD25"})}),(0,n.jsx)("span",{className:"mt-2 text-sm font-semibold text-gray-600 dark:text-gray-400 text-center",children:ee?"Loading Streak...":er?"Error Loading Streak":`🔥 Streak: ${Y?.streak||0} ${r?"Weeks":"Days"}`})]})}),(0,n.jsxs)("div",{className:"text-sm text-gray-800 dark:text-gray-300 space-y-1 mb-6 text-center",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Coins Earned:"})," ",ed+(Y?.streak||0)]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Bonus:"})," +",Y?.streak||0," for Streak!"]})]})]}),(0,n.jsxs)("div",{className:"flex flex-col mt-6 bg-white dark:bg-[#1f2937] rounded-3xl shadow-2xl border border-orange-100 dark:border-gray-700 p-6 mx-auto max-w-xl",children:[(0,n.jsxs)("div",{className:"flex flex-col items-center justify-between gap-4",children:[(0,n.jsx)("p",{className:"text-sm text-gray-800 dark:text-gray-300 flex-1",children:"Ask your friends to join the quiz and get a chance to win some coins!"}),(0,n.jsx)("div",{className:"social-media-buttons flex items-center gap-4",children:(0,n.jsx)(W,{url:"https://uest.in/mock-exam-card",title:e,children:(0,n.jsx)(U,{size:32,round:!0})})})]}),(0,n.jsxs)("div",{className:"flex mt-4 gap-4",children:[(0,n.jsx)("button",{onClick:ey,className:"download-button bg-orange-500 text-white px-6 py-3 rounded-lg shadow-md hover:bg-orange-600 min-w-[150px] whitespace-nowrap",children:"\uD83D\uDCF7 Download Result"}),(0,n.jsx)("button",{onClick:ep,className:"bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold px-6 py-3 rounded-lg shadow-md min-w-[150px] whitespace-nowrap",children:"\uD83D\uDE80 Continue Learning"})]})]})]})})}let eb=$[P];return(0,n.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-100 text-gray-900",children:[v&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col overflow-y-auto",children:[(0,n.jsxs)("h2",{className:"text-lg sm:text-2xl font-bold mb-4",children:["Start ",r?"Weekly":"Daily"," Quiz"]}),(0,n.jsx)("p",{className:"font-semibold mb-4 text-sm sm:text-base text-gray-600",children:"Note: This is a mock exam for testing purposes only."}),(0,n.jsxs)("div",{className:"flex-1 overflow-y-auto pr-2 mb-4 text-sm sm:text-base",children:[(0,n.jsx)("p",{className:"font-semibold mb-2",children:"Instructions (English):"}),(0,n.jsxs)("ul",{className:"list-disc list-inside mb-4 text-gray-600",children:[(0,n.jsx)("li",{children:"Do not switch tabs during the quiz."}),(0,n.jsx)("li",{children:"Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys)."}),(0,n.jsx)("li",{children:"Do not open Developer Tools."}),(0,n.jsx)("li",{children:"Do not exit full-screen mode."}),(0,n.jsx)("li",{children:"Do not interact with other windows or applications."}),(0,n.jsx)("li",{children:"Do not change the screen or minimize the quiz window."}),(0,n.jsx)("li",{children:"Do not receive or make calls during the quiz."}),(0,n.jsx)("li",{children:"Do not use split screen or floating windows on your device."})]}),(0,n.jsx)("p",{className:"font-semibold mb-2",children:"સૂચનાઓ (ગુજરાતી):"}),(0,n.jsxs)("ul",{className:"list-disc list-inside text-gray-600",children:[(0,n.jsx)("li",{children:"ક્વિઝ દરમિયાન ટેબ બદલશો નહીં."}),(0,n.jsx)("li",{children:"પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં."}),(0,n.jsx)("li",{children:"ડેવલપર ટૂલ્સ ખોલશો નહીં."}),(0,n.jsx)("li",{children:"ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં."}),(0,n.jsx)("li",{children:"ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં."}),(0,n.jsx)("li",{children:"અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં."}),(0,n.jsx)("li",{children:"સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં."}),(0,n.jsx)("li",{children:"ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં."}),(0,n.jsx)("li",{children:"તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં."})]})]}),(0,n.jsxs)(a.$,{onClick:()=>{k(!1),eh(),$.length>0&&(F(45),el(Date.now()))},className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:["Start ",r?"Weekly":"Daily"," Quiz"]})]})}),N&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,n.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-customOrange",children:"Warning"}),(0,n.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:"You have performed a restricted action. Repeating this will terminate the quiz."}),(0,n.jsx)(a.$,{onClick:()=>E(!1),className:"bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all",children:"OK"})]})}),A&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96",children:[(0,n.jsx)("h2",{className:"text-lg sm:text-2xl font-bold mb-4 text-red-500",children:"Quiz Terminated"}),(0,n.jsx)("p",{className:"mb-4 text-sm sm:text-base text-gray-600",children:G||"Your quiz has been terminated due to multiple cheating attempts."}),(0,n.jsx)(a.$,{onClick:ep,className:"bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all",children:"Go to Home"})]})}),!v&&!l&&!b&&!j&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(eU,{}),(0,n.jsx)("div",{className:"fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200",children:(0,n.jsx)("div",{className:"h-3.5 bg-customOrange rounded-r-full transition-all duration-300",style:{width:`${eg}%`}})}),(0,n.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen",children:(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center w-full max-w-3xl",children:[(0,n.jsxs)("div",{className:"mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg",children:[(0,n.jsx)(h.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse"}),(0,n.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-customOrange",children:ef(M)})]}),(0,n.jsxs)("div",{className:"w-full text-center flex flex-col items-center",children:[(0,n.jsx)("div",{className:"flex justify-center mb-3 sm:mb-4",children:(0,n.jsxs)("span",{className:"text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm",children:["Question ",P+1," of ",$.length]})}),(0,n.jsxs)("div",{className:"bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto",children:[(0,n.jsx)("h2",{className:"text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6",children:eb.question}),(0,n.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 w-full",children:[(0,n.jsxs)(a.$,{variant:"outline",className:ew("optionOne"),onClick:()=>ex("optionOne"),disabled:A||Q,children:[(0,n.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"A"}),(0,n.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eb.optionOne})]}),(0,n.jsxs)(a.$,{variant:"outline",className:ew("optionTwo"),onClick:()=>ex("optionTwo"),disabled:A||Q,children:[(0,n.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"B"}),(0,n.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eb.optionTwo})]}),(0,n.jsxs)(a.$,{variant:"outline",className:ew("optionThree"),onClick:()=>ex("optionThree"),disabled:A||Q,children:[(0,n.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"C"}),(0,n.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eb.optionThree})]}),(0,n.jsxs)(a.$,{variant:"outline",className:ew("optionFour"),onClick:()=>ex("optionFour"),disabled:A||Q,children:[(0,n.jsx)("span",{className:"w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold flex-shrink-0",children:"D"}),(0,n.jsx)("span",{className:"flex-1 text-left whitespace-normal break-words",children:eb.optionFour})]})]})]}),(0,n.jsx)(a.$,{className:"bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed",onClick:()=>ec(),disabled:A||Q,children:P===$.length-1?"Finish":"Next Question"})]}),(0,n.jsxs)("footer",{className:"fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-sm",children:[(0,n.jsx)("span",{children:"Powered by"}),(0,n.jsx)("span",{className:"font-semibold",children:"UEST EdTech"})]})]})})]})]})}eU.displayName="QuizHeader"},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86079:(e,t,r)=>{Promise.resolve().then(r.bind(r,1508))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7013,2800],()=>r(62325));module.exports=n})();