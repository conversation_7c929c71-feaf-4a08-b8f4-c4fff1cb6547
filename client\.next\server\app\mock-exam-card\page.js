(()=>{var e={};e.id=2540,e.ids=[2540],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5014:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("book-check",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["path",{d:"m9 9.5 2 2 4-4",key:"1dth82"}]])},8380:(e,t,a)=>{"use strict";a.d(t,{FU:()=>o,NL:()=>i,dS:()=>s});var r=a(28527);let s=async(e,t=1,a=10)=>{try{let s=await r.S.get(`/mock-exam-leaderboard/leaderboard/${e}?page=${t}&limit=${a}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){return{success:!1,error:`Failed to get mock exam leaderboard data: ${e.response?.data?.error||e.message}`}}},i=async()=>{try{let e=await r.S.get("/mock-exam-leaderboard/previous-day",{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:e.data}}catch(e){return{success:!1,error:`Failed to get yesterday's top performers: ${e.response?.data?.error||e.message}`}}},o=async(e,t,a)=>{try{let s=await r.S.post("/reactions",{studentId:e,reactionType:t,reactorId:a},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:s.data}}catch(e){return{success:!1,error:`Failed to send reaction: ${e.response?.data?.error||e.message}`}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25314:(e,t,a)=>{Promise.resolve().then(a.bind(a,46900))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46900:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\mock-exam-card\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57022:(e,t,a)=>{"use strict";a.d(t,{S:()=>i,q:()=>s});var r=a(28527);let s=async e=>{try{let t=await r.S.post("/mock-exam-result",e,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:t.data}}catch(e){return{success:!1,error:`Failed to save mock exam result: ${e.response?.data?.message||e.message}`}}},i=async(e,t=1,a=10,s={})=>{try{let i=new URLSearchParams({page:t.toString(),limit:a.toString(),...void 0!==s.isWeekly&&{isWeekly:s.isWeekly.toString()}}).toString(),o=await r.S.get(`/mock-exam-result/${e}?${i}`,{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:o.data}}catch(e){return{success:!1,error:`Failed to get mock exam result: ${e.response?.data?.message||e.message}`}}}},59818:(e,t,a)=>{Promise.resolve().then(a.bind(a,60393))},60393:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var r=a(60687),s=a(90269),i=a(46303),o=a(66874),l=a(29523),n=a(30474);let d={src:"/_next/static/media/mockExamImage.b1be346b.svg"};var c=a(33793),x=a(30656),m=a(16189),u=a(92449),p=a(52581),g=a(43210);function h(){let e=(0,m.useRouter)(),[t,a]=(0,g.useState)(!1),[s,i]=(0,g.useState)(null),[o,n]=(0,g.useState)(null);(0,g.useRef)(null),(0,g.useRef)(null);let d=()=>{if(null===s)return null;let e=Math.floor(s/3600),t=Math.floor(s%3600/60),a=s%60;return`${e.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`};return(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(l.$,{className:"w-full mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>{if(!o){p.toast.error("Please log in to attempt the exam."),e.push("/login");return}if(t&&s){p.toast.error(`You can attempt the exam again in ${d()}.`);return}e.push("/mock-test")},disabled:t||!o,children:"Try Daily Quiz"}),t&&s&&(0,r.jsxs)("p",{className:"text-center mt-2 text-gray-500 text-sm",children:["Next attempt available in: ",d()]})]})}a(57022);var y=a(5014);function b(){let e=(0,m.useRouter)(),[t,a]=(0,g.useState)(!0),[s,i]=(0,g.useState)(null),[o,n]=(0,g.useState)(null);(0,g.useRef)(null),(0,g.useRef)(null);let d=()=>{if(null===s)return null;let e=Math.floor(s/86400),t=Math.floor(s%86400/3600),a=Math.floor(s%3600/60),r=s%60;return e>0?`${e}d ${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`};return(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(l.$,{className:"w-full mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>{if(!o){p.toast.error("Please log in to attempt the exam."),e.push("/login");return}if(t&&s){p.toast.error(`You can attempt the exam again in ${d()}.`);return}if(0!==new Date().getDay()){p.toast.error("The weekly exam is only available on Sundays.");return}e.push("/mock-test?isWeekly=true")},disabled:t||!o,children:"Try Weekly Exam"}),t&&s&&(0,r.jsxs)("p",{className:"text-center mt-2 text-gray-500 text-sm",children:["You can attempt it after: ",d()]})]})}var f=a(8380);let v=()=>{let e=(0,m.useRouter)(),t=null;try{let e=localStorage.getItem("student_data");t=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),t=null}let[a,p]=(0,g.useState)([]),[v,k]=(0,g.useState)(!0),[j,w]=(0,g.useState)(null);return(0,g.useEffect)(()=>{(async()=>{try{k(!0);let e=await (0,f.NL)();e.success?p(e.data.data):w(e.error)}catch(e){w(`Failed to load top performers ${e.message}`)}finally{k(!1)}})()},[]),(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-800 dark:text-gray-100 transition-colors duration-300",children:[(0,r.jsx)(s.default,{}),(0,r.jsx)("section",{className:"bg-black py-16 flex justify-center items-center",children:(0,r.jsx)(u.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.8,ease:"easeOut"},className:"relative",children:(0,r.jsx)(n.default,{src:d.src,alt:"Current Affairs Quiz Logo",width:250,height:80,priority:!0,quality:100,className:"object-contain rounded-xl shadow-2xl"})})}),(0,r.jsxs)("section",{className:"text-center mt-12 px-4 sm:px-6",children:[(0,r.jsxs)(u.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-4xl sm:text-5xl font-extrabold tracking-tight text-gray-900 dark:text-white",children:["Daily ",(0,r.jsx)("span",{className:"text-amber-400",children:"Quiz"})]}),(0,r.jsx)(u.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"mt-4 text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Stay informed, test your knowledge, and earn exclusive rewards!"}),(0,r.jsxs)(u.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"mt-8 mx-auto w-full max-w-3xl bg-white dark:bg-gray-900/80 border border-amber-200 dark:border-amber-700/50 rounded-xl p-6 shadow-lg flex flex-col sm:flex-row items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDECD️"}),(0,r.jsxs)("p",{className:"text-base sm:text-lg font-semibold text-gray-800 dark:text-gray-100",children:["The ",(0,r.jsx)("span",{className:"text-amber-500",children:"Store"})," is now ",(0,r.jsx)("strong",{children:"LIVE"})," — redeem your coins for exciting rewards!"]})]}),(0,r.jsx)(l.$,{className:"bg-amber-500 hover:bg-amber-600 text-white font-semibold rounded-lg px-6 py-2.5 transition-all duration-300",onClick:()=>e.push("/store"),children:"Visit Store"})]})]}),(0,r.jsxs)("section",{className:"px-4 sm:px-6 md:px-8 lg:px-12 py-12 space-y-12",children:[(0,r.jsx)(u.P.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8,ease:"easeOut"},className:"py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-5xl mx-auto bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-3xl shadow-2xl p-6 sm:p-10 border border-gray-100 dark:border-gray-700/30",children:[(0,r.jsxs)(u.P.div,{className:"flex items-center justify-center gap-4 mb-10",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.2,ease:"easeOut"},children:[(0,r.jsx)(c.gt3,{className:"text-amber-400 text-3xl sm:text-4xl"}),(0,r.jsxs)("h2",{className:"text-2xl sm:text-3xl lg:text-4xl font-extrabold text-gray-900 dark:text-white tracking-tight",children:[(0,r.jsx)("span",{className:"bg-clip-text  text-amber-400",children:"Top Performers"})," ","of Yesterday"]})]}),v?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-8",children:[void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"flex flex-col items-center bg-gray-100 dark:bg-gray-800/50 rounded-2xl p-6 shadow-sm",children:[(0,r.jsx)("div",{className:"w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"}),(0,r.jsx)("div",{className:"mt-4 w-32 h-5 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"}),(0,r.jsxs)("div",{className:"mt-3 flex gap-3",children:[(0,r.jsx)("div",{className:"w-16 h-4 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"}),(0,r.jsx)("div",{className:"w-16 h-4 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"})]})]},t))}):j?(0,r.jsx)("p",{className:"text-center text-rose-500 font-semibold text-lg tracking-wide",children:j}):0===a.length?(0,r.jsx)("p",{className:"text-center text-gray-500 dark:text-gray-400 font-medium text-lg tracking-wide",children:"No data available for yesterday"}):(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-8",children:a.map((e,t)=>(0,r.jsx)(u.P.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3*t,duration:.5,ease:"easeOut"},className:`flex flex-col items-center ${0===t?"sm:order-2":1===t?"sm:order-1":"sm:order-3"}`,children:(0,r.jsxs)("div",{className:"relative flex flex-col items-center gap-3",children:[(0,r.jsxs)("div",{className:`rounded-full border-4 p-1 transition-all duration-500 ease-in-out ${0===t?"border-amber-400 scale-110 shadow-xl":"border-amber-300 shadow-md"}`,children:[0===t&&(0,r.jsx)(x.GuV,{className:"absolute -top-8 left-1/2 -translate-x-1/2 text-amber-400 w-8 h-8"}),e.profilePhoto?(0,r.jsx)(n.default,{src:`http://localhost:4005/${e.profilePhoto.replace(/\\/g,"/")}`,alt:`${e.firstName||""} ${e.lastName||""}`,width:96,height:96,className:"w-24 h-24 rounded-full object-cover border-2 border-amber-200 dark:border-gray-600"}):(0,r.jsx)("div",{className:"w-24 h-24 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 flex items-center justify-center text-gray-800 dark:text-white font-bold text-xl",children:e.firstName?.[0]||"U"})]}),(0,r.jsx)("div",{className:`z-10 rounded-full flex items-center justify-center font-bold text-white mt-[-1.7rem] ${0===t?"w-8 h-8 bg-amber-500 shadow-lg border-4 border-amber-500":1===t?"w-7 h-7 bg-amber-400 shadow border-4 border-amber-400":"w-6 h-6 bg-amber-300 shadow border-4 border-amber-300"}`,children:e.rank}),(0,r.jsxs)("p",{className:"text-lg font-semibold capitalize text-gray-900 dark:text-white mt-2 tracking-tight",children:[e.firstName," ",e.lastName]}),(0,r.jsxs)("div",{className:"mt-3 flex flex-wrap justify-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1.5 px-4 py-1.5 text-sm font-medium rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-700/50",children:[(0,r.jsx)(c.lHQ,{})," ",e.score]}),(0,r.jsxs)("div",{className:"flex items-center gap-1.5 px-4 py-1.5 text-sm font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700/50",children:["\uD83D\uDD25 ",e.streakCount]})]})]})},e.rank))})]})}),(0,r.jsx)(u.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7,delay:.4},children:(0,r.jsxs)(o.Zp,{className:"max-w-5xl mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl p-6 sm:p-8",children:[(0,r.jsxs)(u.P.div,{className:"flex justify-center items-center gap-3 mb-6",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.5},children:[(0,r.jsx)(x.xnu,{className:"text-amber-400 text-4xl"}),(0,r.jsxs)("h2",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white",children:["Weekly ",(0,r.jsx)("span",{className:"text-amber-400",children:"Challenge"})]})]}),(0,r.jsx)(u.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.6},className:"text-base sm:text-lg text-gray-600 dark:text-gray-300 text-center mb-6",children:"Test your skills every Sunday for a chance to win big rewards!"}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[{icon:c.w_X,text:"Every Sunday"},{icon:c.O6N,text:"25 Questions"},{icon:c.cEG,content:(0,r.jsxs)("span",{className:"flex flex-col items-start text-base font-semibold",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1 text-gray-500 dark:text-gray-400 text-sm line-through",children:[(0,r.jsx)(u.P.div,{whileHover:{scale:1.1},transition:{duration:.2},children:(0,r.jsx)(n.default,{src:"/uest_coin.png",alt:"Coin",width:14,height:14,sizes:"(max-width: 640px) 14px, 16px",loading:"lazy",className:"inline-block"})}),"9 coins"]}),(0,r.jsx)("strong",{className:"text-amber-500 dark:text-amber-400",children:"Free Entry"})]})},{icon:x.gPQ,text:"Earn Coins"}].map((e,t)=>(0,r.jsxs)(u.P.div,{className:"flex items-center gap-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 shadow-sm transition-all duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.7+.1*t},whileHover:{scale:1.02},children:[(0,r.jsx)(e.icon,{className:"text-amber-400 text-xl flex-shrink-0"}),e.content||(0,r.jsx)("span",{className:"text-base font-medium",children:e.text})]},t))}),(0,r.jsx)(u.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.2},className:"flex justify-center",children:(0,r.jsx)(b,{})})]})}),(0,r.jsx)(u.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7,delay:.5},children:(0,r.jsxs)(o.Zp,{className:"max-w-5xl mx-auto bg-white dark:bg-gray-800/90 border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl p-6 sm:p-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"space-y-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5",children:[{icon:c.O6N,text:"Questions: 10"},{icon:x.gPQ,text:"Badges: Streak & Rank-Based"},{icon:c.w_X,text:"Duration: 8 min"},{icon:c.cEG,text:"Earn Up to: 5 Coins"},{icon:y.A,text:"Free Gift: Top 3 students get free classmate books"}].map((e,t)=>(0,r.jsxs)(u.P.div,{className:"flex items-center gap-3 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-lg p-2 transition-all duration-200",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.7+.1*t},whileHover:{scale:1.02},children:[(0,r.jsx)(e.icon,{className:"text-amber-400 text-xl"}),(0,r.jsx)("span",{className:"text-base font-medium",children:e.text})]},t))}),(0,r.jsxs)("div",{className:"flex flex-col justify-between space-y-6",children:[(0,r.jsx)(u.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.2},children:(0,r.jsx)(h,{})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 text-center",children:"Attempt once every 24 hours to earn coins & build your streak!"}),(0,r.jsxs)(u.P.div,{className:"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5 space-y-4 text-gray-800 dark:text-gray-200",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:1.3},children:[(0,r.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Coin Rewards:"}),(0,r.jsx)("ul",{className:"list-none space-y-2",children:["0 coins for 50% score","1 coin for 60% score","2 coins for 70% score","3 coins for 80% score","4 coins for 90% score","5 coins for 100% score"].map((e,t)=>(0,r.jsxs)(u.P.li,{className:"flex items-center gap-2 hover:text-amber-400 transition-colors duration-200",whileHover:{x:5},transition:{duration:.3},children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-amber-400 rounded-full"}),e]},t))}),(0,r.jsxs)("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:[(0,r.jsx)("strong",{children:"Streak Bonus:"})," +1 coin per daily attempt"]})]})]})]}),(0,r.jsx)("div",{className:"mt-8 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4",children:[{src:"/scholer.svg",alt:"100 Coins",text:"100 Coins"},{src:"/Mastermind.svg",alt:"500 Coins",text:"500 Coins"},{src:"/Achiever.svg",alt:"1000 Coins",text:"1000 Coins"},{src:"/Perfect Month.svg",alt:"30 Days Streak",text:"30 Days Streak"},{src:"/Perfect Year.svg",alt:"365 Days Streak",text:"365 Days Streak"},{src:"/Streak.svg",alt:"Daily Streak",text:"Daily Streak"}].map((e,t)=>(0,r.jsxs)(u.P.div,{className:"flex flex-col items-center bg-white dark:bg-gray-800/50 p-3 rounded-lg shadow-sm",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.4+.1*t},whileHover:{scale:1.05},children:[(0,r.jsx)(n.default,{src:e.src,alt:e.alt,width:40,height:40}),(0,r.jsx)("span",{className:"text-sm font-semibold text-gray-700 dark:text-gray-200 mt-1 text-center",children:e.text})]},t))}),(0,r.jsxs)("div",{className:"mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,r.jsx)(u.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.8},children:(0,r.jsx)(l.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-amber-500 hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-base py-3",onClick:()=>e.push(`/mock-exam-result/${t}`),children:"View Quiz Results & Earned Coins"})}),(0,r.jsx)(u.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.9},children:(0,r.jsx)(l.$,{variant:"outline",className:"w-full border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-amber-500 hover:text-white transition-all duration-300 rounded-lg shadow-sm font-medium text-base py-3",onClick:()=>e.push("/Leader-Board"),children:"View Leaderboards"})})]})]})})]}),(0,r.jsx)(i.default,{})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66874:(e,t,a)=>{"use strict";a.d(t,{BT:()=>n,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>c});var r=a(60687);a(43210);var s=a(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88689:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=a(65239),s=a(48088),i=a(88170),o=a.n(i),l=a(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);a.d(t,n);let d={children:["",{children:["mock-exam-card",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,46900)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/mock-exam-card/page",pathname:"/mock-exam-card",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7013,2449,3793,656,2800],()=>a(88689));module.exports=r})();