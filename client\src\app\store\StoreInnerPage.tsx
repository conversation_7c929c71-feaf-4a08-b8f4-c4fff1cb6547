"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { ShoppingBag, Star, Filter, Search, Coins, ShoppingCart } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { isAuthenticated } from "@/lib/utils";
import * as storeApi from "@/services/storeApi";
import * as cartApi from "@/services/cartApi";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StoreItem } from "@/lib/types";


const categories = ["All", "Stationery", "Toys", "Sports", "Other"];

const StoreInnerPage = () => {
  const router = useRouter();
  const [products, setProducts] = useState<StoreItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<StoreItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [sortBy, setSortBy] = useState("name");
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);

  useEffect(() => {
    const authStatus = isAuthenticated();
    setIsUserLoggedIn(authStatus.isAuth);
    loadStoreItems();
  }, []);

  const loadStoreItems = async () => {
    try {
      setLoading(true);
      const result = await storeApi.getAllStoreItems();

      if (!result.success) {
        throw new Error(result.error);
      }

      const items = result.data;
      setProducts(items);
    } catch (error: any) {
      console.error('Failed to load store items:', error);
      toast.error(error.message || 'Failed to load store items');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (product: StoreItem) => {
    if (!isUserLoggedIn) {
      toast.error("Please login to add items to cart");
      return;
    }

    if (product.availableStock === 0) {
      toast.error("Item is out of stock");
      return;
    }

    try {
      const result = await cartApi.addToCart(product.id, 1);
      if (result.success) {
        toast.success("Item added to cart!");
        window.dispatchEvent(new CustomEvent('cartUpdated'));
      } else {
        toast.error(`Only ${product.availableStock} items available`);
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error("Item is out of stock");
    }
  };

  useEffect(() => {
    let filtered = products;

    if (selectedCategory !== "All") {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    switch (sortBy) {
      case "coin-price-low":
        filtered = [...filtered].sort((a, b) => a.coinPrice - b.coinPrice);
        break;
      case "coin-price-high":
        filtered = [...filtered].sort((a, b) => b.coinPrice - a.coinPrice);
        break;
      case "name":
        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "newest":
        filtered = [...filtered].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      default:
        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    setFilteredProducts(filtered);
  }, [products, selectedCategory, searchQuery, sortBy]);



  const handleCardClick = (productId: string) => {
    router.push(`/store/${productId}`);
  };



  return (
    <>
      <Header />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Hero Section */}
        <div className="relative bg-background dark:bg-gray-900 py-20 overflow-hidden border-b">
          <div className="container mx-auto px-4 text-center relative z-10">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="p-3 bg-customOrange/10 rounded-xl">
                <ShoppingBag className="w-12 h-12 text-customOrange" />
              </div>
              <h1 className="text-5xl md:text-6xl font-bold text-foreground">
                UEST Store
              </h1>
            </div>
            <p className="text-xl md:text-2xl mb-8 text-muted-foreground max-w-3xl mx-auto">
              Premium educational products for students
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border">
                <Coins className="w-5 h-5 text-customOrange" />
                <span className="text-sm font-medium text-card-foreground">Pay with UEST Coins</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border">
                <span className="w-2 h-2 bg-customOrange rounded-full"></span>
                <span className="text-sm font-medium text-card-foreground">Quality Products</span>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="container mx-auto px-4 py-8">
          <div className="bg-card rounded-xl shadow-sm border p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                {/* Category Filter */}
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full sm:w-48">
                    <Filter className="w-4 h-4 mr-2 text-customOrange" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="coin-price-low">Coins: Low to High</SelectItem>
                    <SelectItem value="coin-price-high">Coins: High to Low</SelectItem>
                    <SelectItem value="newest">Newest First</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Payment Method Info */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-8 p-6 bg-card rounded-xl border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-customOrange/10 rounded-lg">
                <Coins className="w-5 h-5 text-customOrange" />
              </div>
              <div>
                <h3 className="font-semibold text-card-foreground">Payment Method</h3>
                <p className="text-sm text-muted-foreground">All items are priced in UEST Coins</p>
              </div>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-orange-50 border border-orange-200 rounded-lg">
              <Coins className="w-4 h-4 text-orange-600" />
              <span className="font-medium text-orange-800">UEST Coins Only</span>
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-full mb-2" />
                    <Skeleton className="h-3 w-2/3" />
                  </CardContent>
                  <CardFooter className="p-4">
                    <Skeleton className="h-10 w-full" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredProducts.map((product, index) => (
                <Card
                  key={product.id}
                  className="overflow-hidden group bg-card border shadow-sm hover:shadow-lg transition-all duration-300 animate-fade-in-up cursor-pointer"
                  style={{ animationDelay: `${index * 0.1}s` }}
                  onClick={() => handleCardClick(product.id)}
                >
                <div className="relative h-64 bg-muted/30 flex items-center justify-center">
                    <Image
                      src={
                        product.image?.startsWith('http')
                          ? product.image
                          : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${product.image?.startsWith('/') ? product.image.substring(1) : product.image || 'uploads/store/placeholder.jpg'}`
                      }
                      alt={product.name}
                      className="object-contain w-full h-full transition-transform duration-300 group-hover:scale-105"
                      width={400}
                      height={256}
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "/logo.png";
                      }}
                    />
                    {product.availableStock === 0 && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <Badge variant="destructive">Out of Stock</Badge>
                      </div>
                    )}
                  </div>

                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-1 text-card-foreground line-clamp-1 group-hover:text-customOrange transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                      {product.description}
                    </p>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold text-customOrange flex items-center">
                          <Coins className="w-5 h-5 mr-1" />
                          {product.coinPrice} coins
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {product.category}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div>Available: <span className={`font-medium ${product.availableStock === 0 ? 'text-red-500' : 'text-green-600'}`}>{product.availableStock}</span></div>
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter className="p-4 pt-0">
                    <Button
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click when button is clicked
                        addToCart(product);
                      }}
                      disabled={product.availableStock === 0}
                      className="w-full bg-customOrange hover:bg-orange-600 disabled:opacity-50"
                    >
                      {product.availableStock > 0 ? (
                        <>
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          Add to Cart
                        </>
                      ) : (
                        'Out of Stock'
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}

          {filteredProducts.length === 0 && !loading && (
            <div className="text-center py-16">
              <ShoppingBag className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-card-foreground mb-2">
                No products found
              </h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </div>



        {/* Store Info Section */}
        <div className="bg-muted/30 py-16 mt-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <ShoppingBag className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">Quality Products</h3>
                <p className="text-muted-foreground">Premium educational materials carefully selected for students</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <Coins className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">UEST Coins</h3>
                <p className="text-muted-foreground">Pay with your earned UEST coins for exclusive discounts</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <Star className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">Student Focused</h3>
                <p className="text-muted-foreground">Everything designed with student success in mind</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />


    </>
  );
};

export default StoreInnerPage;
