(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8260],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(95155);s(12115);var a=s(6874),i=s.n(a),l=s(66766),n=s(29911);let o=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(i(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(l.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(i(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(i(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(l.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(i(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(i(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(95155);s(12115);var a=s(66634),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:s,asChild:i=!1,...o}=e,c=i?a.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),t),...o})}},30130:(e,t,s)=>{Promise.resolve().then(s.bind(s,81935))},38564:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},47924:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},54717:(e,t,s)=>{"use strict";s.d(t,{Lq:()=>i,dI:()=>a});var r=s(55077);let a=async e=>{try{let t=new URLSearchParams;(null==e?void 0:e.category)&&t.append("category",e.category),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.search)&&t.append("search",e.search),t.append("status","ACTIVE");let s=await r.S.get("/admin/store?".concat(t.toString()));return{success:!0,data:s.data.data}}catch(e){var t,s;return{success:!1,error:(null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message||"Failed to fetch store items"}}},i=async e=>{try{let t=await r.S.get("/admin/store/".concat(e));return{success:!0,data:t.data.data}}catch(e){var t,s;return{success:!1,error:(null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.message)||e.message||"Failed to fetch store item"}}}},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>c,yv:()=>d});var r=s(95155);s(12115);var a=s(59824),i=s(66474),l=s(5196),n=s(47863),o=s(59434);function c(e){let{...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,r.jsx)(a.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:l,...n}=e;return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[l,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:s,position:i="popper",...l}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...l,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,r.jsx)(f,{})]})})}function x(e){let{className:t,children:s,...i}=e;return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(l.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:s})]})}function h(e){let{className:t,...s}=e;return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(n.A,{className:"size-4"})})}function f(e){let{className:t,...s}=e;return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(i.A,{className:"size-4"})})}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(95155);s(12115);var a=s(59434);function i(e){let{className:t,type:s,...i}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>l,wL:()=>d});var r=s(95155);s(12115);var a=s(59434);function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},66932:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68856:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(95155),a=s(59434);function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",t),...s})}},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var r=s(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r.createContext&&r.createContext(a),l=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(this,arguments)}function o(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,r)}return s}function c(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?o(Object(s),!0).forEach(function(t){var r,a,i;r=e,a=t,i=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in r?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>r.createElement(u,n({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,s)=>r.createElement(t.tag,c({key:s},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var s,{attr:a,size:i,title:o}=e,d=function(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;s[r]=e[r]}return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)s=i[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}(e,l),u=i||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),r.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:s,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&r.createElement("title",null,o),e.children)};return void 0!==i?r.createElement(i.Consumer,null,e=>t(e)):t(a)}},81935:(e,t,s)=>{"use strict";s.d(t,{default:()=>S});var r=s(95155),a=s(12115),i=s(66766),l=s(35695),n=s(86151),o=s(93550),c=s(47924),d=s(66932),u=s(27809),m=s(38564),x=s(30285),h=s(66695),f=s(62523),g=s(26126),p=s(68856),v=s(56671),b=s(70347),j=s(7583),w=s(59434),y=s(54717),N=s(28844),k=s(59409);let O=["All","Stationery","Toys","Sports","Other"],S=()=>{let e=(0,l.useRouter)(),[t,s]=(0,a.useState)([]),[S,A]=(0,a.useState)([]),[E,P]=(0,a.useState)(!0),[C,z]=(0,a.useState)(""),[L,T]=(0,a.useState)("All"),[_,F]=(0,a.useState)("name"),[U,D]=(0,a.useState)(!1);(0,a.useEffect)(()=>{D((0,w.wR)().isAuth),W()},[]);let W=async()=>{try{P(!0);let e=await y.dI();if(!e.success)throw Error(e.error);let t=e.data;s(t)}catch(e){console.error("Failed to load store items:",e),v.toast.error(e.message||"Failed to load store items"),s([])}finally{P(!1)}},I=async e=>{if(!U){v.toast.error("Please login to add items to cart");return}if(0===e.availableStock){v.toast.error("Item is out of stock");return}try{(await N.bE(e.id,1)).success?(v.toast.success("Item added to cart!"),window.dispatchEvent(new CustomEvent("cartUpdated"))):v.toast.error("Only ".concat(e.availableStock," items available"))}catch(e){console.error("Error adding to cart:",e),v.toast.error("Item is out of stock")}};(0,a.useEffect)(()=>{let e=t;switch("All"!==L&&(e=e.filter(e=>e.category===L)),C&&(e=e.filter(e=>e.name.toLowerCase().includes(C.toLowerCase())||e.description.toLowerCase().includes(C.toLowerCase()))),_){case"coin-price-low":e=[...e].sort((e,t)=>e.coinPrice-t.coinPrice);break;case"coin-price-high":e=[...e].sort((e,t)=>t.coinPrice-e.coinPrice);break;case"name":default:e=[...e].sort((e,t)=>e.name.localeCompare(t.name));break;case"newest":e=[...e].sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime())}A(e)},[t,L,C,_]);let q=t=>{e.push("/store/".concat(t))};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.default,{}),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)("div",{className:"relative bg-background dark:bg-gray-900 py-20 overflow-hidden border-b",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-4 mb-6",children:[(0,r.jsx)("div",{className:"p-3 bg-customOrange/10 rounded-xl",children:(0,r.jsx)(n.A,{className:"w-12 h-12 text-customOrange"})}),(0,r.jsx)("h1",{className:"text-5xl md:text-6xl font-bold text-foreground",children:"UEST Store"})]}),(0,r.jsx)("p",{className:"text-xl md:text-2xl mb-8 text-muted-foreground max-w-3xl mx-auto",children:"Premium educational products for students"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-customOrange"}),(0,r.jsx)("span",{className:"text-sm font-medium text-card-foreground",children:"Pay with UEST Coins"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-customOrange rounded-full"}),(0,r.jsx)("span",{className:"text-sm font-medium text-card-foreground",children:"Quality Products"})]})]})]})}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("div",{className:"bg-card rounded-xl shadow-sm border p-6 mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,r.jsx)(f.p,{placeholder:"Search products...",value:C,onChange:e=>z(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)(k.l6,{value:L,onValueChange:T,children:[(0,r.jsxs)(k.bq,{className:"w-full sm:w-48",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2 text-customOrange"}),(0,r.jsx)(k.yv,{placeholder:"Category"})]}),(0,r.jsx)(k.gC,{children:O.map(e=>(0,r.jsx)(k.eb,{value:e,children:e},e))})]}),(0,r.jsxs)(k.l6,{value:_,onValueChange:F,children:[(0,r.jsx)(k.bq,{className:"w-full sm:w-48",children:(0,r.jsx)(k.yv,{placeholder:"Sort by"})}),(0,r.jsxs)(k.gC,{children:[(0,r.jsx)(k.eb,{value:"name",children:"Name (A-Z)"}),(0,r.jsx)(k.eb,{value:"coin-price-low",children:"Coins: Low to High"}),(0,r.jsx)(k.eb,{value:"coin-price-high",children:"Coins: High to Low"}),(0,r.jsx)(k.eb,{value:"newest",children:"Newest First"})]})]})]})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4 mb-8 p-6 bg-card rounded-xl border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-customOrange/10 rounded-lg",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-customOrange"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-card-foreground",children:"Payment Method"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"All items are priced in UEST Coins"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-orange-50 border border-orange-200 rounded-lg",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 text-orange-600"}),(0,r.jsx)("span",{className:"font-medium text-orange-800",children:"UEST Coins Only"})]})]}),E?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,t)=>(0,r.jsxs)(h.Zp,{className:"overflow-hidden",children:[(0,r.jsx)(p.E,{className:"h-48 w-full"}),(0,r.jsxs)(h.Wu,{className:"p-4",children:[(0,r.jsx)(p.E,{className:"h-4 w-3/4 mb-2"}),(0,r.jsx)(p.E,{className:"h-3 w-full mb-2"}),(0,r.jsx)(p.E,{className:"h-3 w-2/3"})]}),(0,r.jsx)(h.wL,{className:"p-4",children:(0,r.jsx)(p.E,{className:"h-10 w-full"})})]},t))}):(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:S.map((e,t)=>{var s,a;return(0,r.jsxs)(h.Zp,{className:"overflow-hidden group bg-card border shadow-sm hover:shadow-lg transition-all duration-300 animate-fade-in-up cursor-pointer",style:{animationDelay:"".concat(.1*t,"s")},onClick:()=>q(e.id),children:[(0,r.jsxs)("div",{className:"relative h-64 bg-muted/30 flex items-center justify-center",children:[(0,r.jsx)(i.default,{src:(null===(s=e.image)||void 0===s?void 0:s.startsWith("http"))?e.image:"".concat("http://localhost:4005/").concat((null===(a=e.image)||void 0===a?void 0:a.startsWith("/"))?e.image.substring(1):e.image||"uploads/store/placeholder.jpg"),alt:e.name,className:"object-contain w-full h-full transition-transform duration-300 group-hover:scale-105",width:400,height:256,onError:e=>{e.target.src="/logo.png"}}),0===e.availableStock&&(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,r.jsx)(g.E,{variant:"destructive",children:"Out of Stock"})})]}),(0,r.jsxs)(h.Wu,{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg mb-1 text-card-foreground line-clamp-1 group-hover:text-customOrange transition-colors",children:e.name}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-customOrange flex items-center",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 mr-1"}),e.coinPrice," coins"]}),(0,r.jsx)(g.E,{variant:"secondary",className:"text-xs",children:e.category})]}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground space-y-1",children:(0,r.jsxs)("div",{children:["Available: ",(0,r.jsx)("span",{className:"font-medium ".concat(0===e.availableStock?"text-red-500":"text-green-600"),children:e.availableStock})]})})]})]}),(0,r.jsx)(h.wL,{className:"p-4 pt-0",children:(0,r.jsx)(x.$,{onClick:t=>{t.stopPropagation(),I(e)},disabled:0===e.availableStock,className:"w-full bg-customOrange hover:bg-orange-600 disabled:opacity-50",children:e.availableStock>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Add to Cart"]}):"Out of Stock"})})]},e.id)})}),0===S.length&&!E&&(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)(n.A,{className:"w-16 h-16 mx-auto text-muted-foreground mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-card-foreground mb-2",children:"No products found"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Try adjusting your search or filter criteria"})]})]}),(0,r.jsx)("div",{className:"bg-muted/30 py-16 mt-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 text-center",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto",children:(0,r.jsx)(n.A,{className:"w-6 h-6 text-customOrange"})}),(0,r.jsx)("h3",{className:"font-semibold text-lg text-card-foreground",children:"Quality Products"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Premium educational materials carefully selected for students"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-customOrange"})}),(0,r.jsx)("h3",{className:"font-semibold text-lg text-card-foreground",children:"UEST Coins"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Pay with your earned UEST coins for exclusive discounts"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-customOrange"})}),(0,r.jsx)("h3",{className:"font-semibold text-lg text-card-foreground",children:"Student Focused"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Everything designed with student success in mind"})]})]})})})]}),(0,r.jsx)(j.default,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,4520,347,8441,1684,7358],()=>t(30130)),_N_E=e.O()}]);