(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7127],{7341:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(95155),a=s(12115),l=s(30285),n=s(17951),i=s(19320),o=s(60760),c=s(70347),d=s(7583),m=s(66766),h=s(29911),x=s(95811),u=s(5585),p=s(8034),f=s(56671),g=s(59434);let b=["Today","Weekly","All time"];function w(){let[e,t]=(0,a.useState)("Today"),[s,w]=(0,a.useState)([]),[v,j]=(0,a.useState)(0),[y,N]=(0,a.useState)(!1),[C,L]=(0,a.useState)(1),[k,S]=(0,a.useState)(!1),[E,I]=(0,a.useState)(null),[F,_]=(0,a.useState)({}),[M,P]=(0,a.useState)(null),z=(0,a.useRef)({}),A=(0,a.useRef)(null),D=null;try{let e=localStorage.getItem("student_data");D=e?JSON.parse(e).id:null}catch(e){console.error("Error retrieving studentId:",e),D=null}let O=e=>e>=100&&e<=499?"/scholer.svg":e>=500&&e<=999?"/Mastermind.svg":e>=1e3?"/Achiever.svg":null,R=e=>{let t=z.current[e];if(t){let e=t.getBoundingClientRect(),s=(e.left+e.width/2)/window.innerWidth,r=(e.top+e.height/2)/window.innerHeight;(0,u.default)({particleCount:100,spread:70,origin:{x:s,y:r},disableForReducedMotion:!0,zIndex:1e3})}},T=async(e,t)=>{try{return(await (0,p.dS)(e,t,10)).data}catch(e){throw Error("Failed to fetch leaderboard: "+e.message)}},H=async(e,t)=>{if(D&&e!==D){let s=F[e];if((null==s?void 0:s.reaction)!==t){_(s=>{var r,a;let l=null===(r=s[e])||void 0===r?void 0:r.reaction,n={...(null===(a=s[e])||void 0===a?void 0:a.counts)||{}};return l&&(n[l]=Math.max((n[l]||1)-1,0)),n[t]=(n[t]||0)+1,{...s,[e]:{reaction:t,counts:n}}});try{await (0,p.FU)(e,t,D),R(e)}catch(e){console.error("Failed to save reaction:",e),I("Failed to save reaction: "+e.message)}P(null)}}else f.toast.success("Please Login to Celebrate!")},U=async()=>{N(!0),I(null);let t=C+1,s=e.toLowerCase().replace(" ","-");try{let e=await T(s,t);w(t=>[...t,...e.data]),_(t=>({...t,...e.reactions})),L(t)}catch(e){I("Failed to load more records: "+(e instanceof Error?e.message:String(e)))}N(!1)};(0,a.useEffect)(()=>{(async()=>{S(!0),I(null),L(1);let t=e.toLowerCase().replace(" ","-");try{let e=await T(t,1);w(e.data),j(e.total),_(e.reactions)}catch(e){I("Failed to fetch leaderboard: "+e.message),w([])}S(!1)})()},[e]),(0,a.useEffect)(()=>{let e=e=>{A.current&&!A.current.contains(e.target)&&P(null)};return M&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[M]);let Z=s.length<v,W=function(e){var t,s;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:120,l=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=((null===(t=e.firstName)||void 0===t?void 0:t.charAt(0))||"")+((null===(s=e.lastName)||void 0===s?void 0:s.charAt(0))||""),o=(0,r.jsx)("div",{style:{width:a,height:a},className:"flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-2xl border-4 border-customOrange overflow-hidden",children:e.profilePhoto&&""!==e.profilePhoto.trim()?(0,r.jsx)(m.default,{src:"".concat("http://localhost:4005/").concat(e.profilePhoto),alt:"".concat(e.firstName||""," ").concat(e.lastName||""),width:a,height:a,className:"object-cover rounded-full w-full h-full"}):(0,r.jsx)("span",{children:n})});return l?(0,r.jsx)(i.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:o}):o},Q=e=>(0,r.jsx)(o.N,{children:(0,r.jsx)(i.P.div,{ref:A,initial:{scale:.8,opacity:0,y:10},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:10},transition:{duration:.2},className:(0,g.cn)("absolute bottom-14 z-30 flex bg-white p-2 rounded-lg shadow-lg border border-gray-100 gap-1.5","flex-col right-2 left-auto","sm:flex-row sm:left-1/2 sm:-translate-x-1/2 sm:right-auto"),children:[{emoji:"\uD83D\uDC4D",label:"thumbsup",color:"bg-blue-50 border-blue-200"},{emoji:"\uD83D\uDE19",label:"whistle",color:"bg-green-50 border-green-200"},{emoji:"\uD83C\uDF89",label:"party",color:"bg-yellow-50 border-yellow-200"},{emoji:"\uD83D\uDC4F",label:"clap",color:"bg-red-50 border-red-200"},{emoji:"\uD83D\uDE23",label:"angry",color:"bg-orange-50 border-orange-200"},{emoji:"\uD83D\uDC4E",label:"thumbsdown",color:"bg-purple-50 border-purple-200"}].map(t=>{var s,a;return(0,r.jsxs)(i.P.button,{onClick:()=>H(e,t.label),"aria-label":t.label,whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center justify-between w-full h-6 rounded-md ".concat(t.color," border transition-all duration-200 px-1 sm:w-12 sm:h-7 sm:px-1.5 min-w-[40px]"),children:[(0,r.jsx)("span",{className:"text-sm",children:t.emoji}),(0,r.jsx)("span",{className:"text-xs font-semibold text-gray-700",children:(null===(a=F[e])||void 0===a?void 0:null===(s=a.counts)||void 0===s?void 0:s[t.label])||0})]},t.label)})})}),B=s.slice(0,3),V=s.slice(3);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.default,{}),(0,r.jsx)("div",{className:"min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-4 flex justify-center",children:(0,r.jsxs)("div",{className:"w-full max-w-5xl space-y-6 sm:space-y-8 pt-8",children:[(0,r.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange",children:"Daily Quiz Leaderboard"}),(0,r.jsx)("p",{className:"text-center text-sm sm:text-base text-muted-foreground font-medium",children:"\uD83C\uDF81 Top 3 students get free classmate books everyday!"}),(0,r.jsx)("div",{className:"flex justify-center gap-4 sm:gap-10 overflow-x-auto",children:b.map(s=>(0,r.jsxs)(l.$,{variant:e===s?"default":"outline",className:"rounded-full px-4 sm:px-6 py-1 sm:py-2 text-sm sm:text-base font-semibold ".concat(e===s?"text-white":"border-orange-400 text-orange-400"," whitespace-nowrap"),"aria-label":"Select ".concat(s," leaderboard"),onClick:()=>{t(s),L(1)},children:[s," ",e===s&&"("+v+")"]},s))}),k&&(0,r.jsx)("p",{className:"text-center text-gray-500",children:"Loading..."}),E&&(0,r.jsx)("p",{className:"text-center text-red-500",children:E}),!k&&!E&&(0,r.jsx)("div",{className:"flex flex-col sm:flex-row justify-around items-center sm:items-end gap-4 sm:gap-6 mt-6 sm:mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg",children:B.map((e,t)=>(0,r.jsx)(i.P.div,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2*t},className:"flex flex-col items-center mt-10 ".concat(0===t?"order-2":1===t?"order-1":"order-3"," relative"),children:(0,r.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,r.jsxs)("div",{className:"relative rounded-full border-4 p-2 ".concat(0===t?"shadow-2xl scale-110 border-customOrange":"border-orange-500"),children:[0===t&&(0,r.jsx)(n.A,{className:"absolute -top-6 sm:-top-8 left-1/2 -translate-x-1/2 text-customOrange w-6 sm:w-8 h-6 sm:h-8"}),W(e,64,0===t),(0,r.jsx)("div",{className:"absolute -bottom-4 sm:-bottom-5 left-1/2 -translate-x-1/2 rounded-full flex items-center justify-center font-bold ".concat(0===t?"w-7 h-7 sm:w-9 sm:h-9 bg-orange-500 text-white shadow-lg border-4 border-orange-500":1===t?"w-6 h-6 sm:w-8 sm:h-8 bg-orange-500 text-white shadow border-4 border-orange-500":"w-5 h-5 sm:w-7 sm:h-7 bg-orange-500 text-white border-4 border-orange-500"),children:e.rank})]}),(0,r.jsxs)("p",{ref:t=>{z.current[e.studentId]=t},className:"mt-6 sm:mt-8 font-semibold text-base sm:text-lg text-center relative",children:[e.firstName," ",e.lastName]}),(0,r.jsx)("div",{className:"mt-2 w-full flex justify-center",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 sm:gap-3",children:[O(e.coinEarnings)&&(0,r.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,r.jsx)(m.default,{src:O(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,r.jsx)(x.A,{badge:e.badge})]})}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-2 sm:gap-5 mt-2",children:[(0,r.jsx)("div",{className:"px-3 sm:px-4 py-1 rounded-full border border-orange-300 text-orange-600 font-bold text-xs sm:text-sm ".concat(0===t?"animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.lHQ,{})," ",e.score]})}),(0,r.jsxs)("div",{className:"px-3 sm:px-4 py-1 rounded-full border border-green-300 text-green-600 font-bold text-xs sm:text-sm flex items-center gap-1 ".concat(0===t?"animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-green-100"),children:[(0,r.jsx)(m.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,r.jsxs)("div",{className:"px-3 sm:px-4 py-1 rounded-full border border-blue-300 text-blue-600 font-bold text-xs sm:text-sm ".concat(0===t?"animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]":"bg-blue-100"),children:["\uD83D\uDD25 ",e.streakCount]})]}),e.studentId!==D&&(0,r.jsxs)("div",{className:"mt-3 relative flex justify-center",children:[(0,r.jsx)(l.$,{variant:"outline",className:"rounded-full px-4 py-1 text-sm border-gray-300 text-gray-600 hover:bg-gray-100 min-w-[80px] sm:min-w-[100px]",onClick:()=>P(M===e.studentId?null:e.studentId),children:(()=>{var t;let s=null===(t=F[e.studentId])||void 0===t?void 0:t.counts;if(s){let e=Object.entries(s).reduce((e,t)=>{let[s,r]=t;return r>e.count?{reaction:s,count:r}:e},{reaction:"",count:-1});if(e.count>0&&e.reaction in s)return(0,r.jsxs)("span",{className:"flex items-center gap-1 truncate",children:[{thumbsup:"\uD83D\uDC4D",whistle:"\uD83D\uDE19",party:"\uD83C\uDF89",clap:"\uD83D\uDC4F",angry:"\uD83D\uDE23",thumbsdown:"\uD83D\uDC4E"}[e.reaction]," ",e.count]})}return"Celebrate"})()}),M===e.studentId&&Q(e.studentId)]})]})},e.studentId))}),!k&&!E&&(0,r.jsx)("div",{className:"rounded-lg mt-6 sm:mt-10 bg-white space-y-3 sm:space-y-4",children:V.map(e=>(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between p-3 sm:p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 sm:gap-4 w-full sm:w-auto",children:[(0,r.jsx)("div",{className:"relative flex items-center justify-center w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-orange-100 text-orange-500 font-bold text-sm sm:text-lg",children:e.rank}),W(e,48),(0,r.jsxs)("p",{ref:t=>{z.current[e.studentId]=t},className:"font-semibold text-base sm:text-lg text-black relative",children:[e.firstName," ",e.lastName]}),e.studentId!==D&&(0,r.jsxs)("div",{className:"mt-3 relative",children:[(0,r.jsx)(l.$,{variant:"outline",className:"rounded-full px-4 py-1 text-sm border-gray-300 text-gray-600 hover:bg-gray-100",onClick:()=>P(M===e.studentId?null:e.studentId),children:(()=>{var t;let s=null===(t=F[e.studentId])||void 0===t?void 0:t.counts;if(s){let e=Object.entries(s).reduce((e,t)=>{let[s,r]=t;return r>e.count?{reaction:s,count:r}:e},{reaction:"",count:-1});if(e.count>0)return(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[{thumbsup:"\uD83D\uDC4D",whistle:"\uD83D\uDE19",party:"\uD83C\uDF89",clap:"\uD83D\uDC4F",angry:"\uD83D\uDE23",thumbsdown:"\uD83D\uDC4E"}[e.reaction]," ",e.count]})}return"Celebrate"})()}),M===e.studentId&&Q(e.studentId)]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-3 sm:gap-5 mt-3 sm:mt-0 w-full sm:w-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[O(e.coinEarnings)&&(0,r.jsx)("div",{className:"pt-1 sm:pt-2 flex items-center gap-1",children:(0,r.jsx)(m.default,{src:O(e.coinEarnings),alt:"Budget Icon",width:40,height:40,sizes:"(max-width: 640px) 40px, 48px",className:"h-10 w-10 sm:h-12 sm:w-12 object-contain",loading:"lazy"})}),(0,r.jsx)(x.A,{badge:e.badge})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap justify-center",children:[(0,r.jsx)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-orange-300 bg-orange-100 text-orange-600",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.lHQ,{className:"mr-1"})," ",e.score]})}),(0,r.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-green-300 bg-green-100 text-green-700 flex items-center gap-1",children:[(0,r.jsx)(m.default,{src:"/uest_coin.png",alt:"Coin",width:12,height:12,sizes:"(max-width: 640px) 12px, 16px",loading:"lazy"}),e.coinEarnings]}),(0,r.jsxs)("div",{className:"min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-blue-300 bg-blue-100 text-blue-700",children:["\uD83D\uDD25 ",e.streakCount]})]})]})]},e.studentId))}),Z&&(0,r.jsx)("div",{className:"flex justify-center mt-6 sm:mt-8",children:(0,r.jsx)(l.$,{onClick:U,disabled:y,className:"px-6 sm:px-8 py-2 sm:py-3 rounded-full bg-customOrange text-white hover:bg-orange-600 disabled:bg-gray-300 text-sm sm:text-base font-semibold min-w-[120px]","aria-label":"Load more records",children:y?"Loading...":"Load More"})})]})}),(0,r.jsx)(d.default,{})]})}},7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(95155);s(12115);var a=s(6874),l=s.n(a),n=s(66766),i=s(29911);let o=()=>(0,r.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,r.jsx)(n.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:i.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:i.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:i.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:i.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:i.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:i.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:i.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:a}=e;return(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:a,children:(0,r.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},a)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,r.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,r.jsx)("p",{children:"Head Office"}),(0,r.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,r.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,r.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,r.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,r.jsx)(n.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,r.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,r.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},8034:(e,t,s)=>{"use strict";s.d(t,{FU:()=>n,NL:()=>l,dS:()=>a});var r=s(55077);let a=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{let a=await r.S.get("/mock-exam-leaderboard/leaderboard/".concat(e,"?page=").concat(t,"&limit=").concat(s),{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:a.data}}catch(e){var a,l;return{success:!1,error:"Failed to get mock exam leaderboard data: ".concat((null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.error)||e.message)}}},l=async()=>{try{let e=await r.S.get("/mock-exam-leaderboard/previous-day",{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:e.data}}catch(s){var e,t;return{success:!1,error:"Failed to get yesterday's top performers: ".concat((null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||s.message)}}},n=async(e,t,s)=>{try{let a=await r.S.post("/reactions",{studentId:e,reactionType:t,reactorId:s},{headers:{"Server-Select":"uwhizServer"}});return{success:!0,data:a.data}}catch(e){var a,l;return{success:!1,error:"Failed to send reaction: ".concat((null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.error)||e.message)}}}},8989:(e,t,s)=>{Promise.resolve().then(s.bind(s,7341))},17951:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},60760:(e,t,s)=>{"use strict";s.d(t,{N:()=>b});var r=s(95155),a=s(12115),l=s(90869),n=s(82885),i=s(97494),o=s(80845),c=s(27351),d=s(51508);class m extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:s,anchorX:l,root:n}=e,i=(0,a.useId)(),o=(0,a.useRef)(null),c=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,a.useContext)(d.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:a,right:d}=c.current;if(s||!o.current||!e||!t)return;o.current.dataset.motionPopId=i;let m=document.createElement("style");h&&(m.nonce=h);let x=null!=n?n:document.head;return x.appendChild(m),m.sheet&&m.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===l?"left: ".concat(a):"right: ".concat(d),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{x.removeChild(m),x.contains(m)&&x.removeChild(m)}},[s]),(0,r.jsx)(m,{isPresent:s,childRef:o,sizeRef:c,children:a.cloneElement(t,{ref:o})})}let x=e=>{let{children:t,initial:s,isPresent:l,onExitComplete:i,custom:c,presenceAffectsLayout:d,mode:m,anchorX:x,root:p}=e,f=(0,n.M)(u),g=(0,a.useId)(),b=!0,w=(0,a.useMemo)(()=>(b=!1,{id:g,initial:s,isPresent:l,custom:c,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;i&&i()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[l,f,i]);return d&&b&&(w={...w}),(0,a.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[l]),a.useEffect(()=>{l||f.size||!i||i()},[l]),"popLayout"===m&&(t=(0,r.jsx)(h,{isPresent:l,anchorX:x,root:p,children:t})),(0,r.jsx)(o.t.Provider,{value:w,children:t})};function u(){return new Map}var p=s(32082);let f=e=>e.key||"";function g(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:s,initial:o=!0,onExitComplete:c,presenceAffectsLayout:d=!0,mode:m="sync",propagate:h=!1,anchorX:u="left",root:b}=e,[w,v]=(0,p.xQ)(h),j=(0,a.useMemo)(()=>g(t),[t]),y=h&&!w?[]:j.map(f),N=(0,a.useRef)(!0),C=(0,a.useRef)(j),L=(0,n.M)(()=>new Map),[k,S]=(0,a.useState)(j),[E,I]=(0,a.useState)(j);(0,i.E)(()=>{N.current=!1,C.current=j;for(let e=0;e<E.length;e++){let t=f(E[e]);y.includes(t)?L.delete(t):!0!==L.get(t)&&L.set(t,!1)}},[E,y.length,y.join("-")]);let F=[];if(j!==k){let e=[...j];for(let t=0;t<E.length;t++){let s=E[t],r=f(s);y.includes(r)||(e.splice(t,0,s),F.push(s))}return"wait"===m&&F.length&&(e=F),I(g(e)),S(j),null}let{forceRender:_}=(0,a.useContext)(l.L);return(0,r.jsx)(r.Fragment,{children:E.map(e=>{let t=f(e),a=(!h||!!w)&&(j===E||y.includes(t));return(0,r.jsx)(x,{isPresent:a,initial:(!N.current||!!o)&&void 0,custom:s,presenceAffectsLayout:d,mode:m,root:b,onExitComplete:a?void 0:()=>{if(!L.has(t))return;L.set(t,!0);let e=!0;L.forEach(t=>{t||(e=!1)}),e&&(null==_||_(),I(C.current),h&&(null==v||v()),c&&c())},anchorX:u,children:e},t)})})}},95811:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(95155),a=s(19320);s(12115);let l=e=>{let{count:t}=e;return(0,r.jsxs)("svg",{className:"h-10 w-10 sm:h-12 sm:w-12",viewBox:"0 0 1550 1808",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M574.764 165.821C692.4 74.0944 857.6 74.0944 975.236 165.821L1262.05 386.652C1354.36 459.21 1407.96 571.58 1407.96 690.458V1117.54C1407.96 1236.42 1354.36 1348.79 1262.05 1421.35L975.236 1642.18C857.6 1733.91 692.4 1733.91 574.764 1642.18L287.951 1421.35C195.639 1348.79 142.037 1236.42 142.037 1117.54V690.458C142.037 571.58 195.639 459.21 287.951 386.652L574.764 165.821Z",fill:"#FDFEF9"}),(0,r.jsx)("mask",{id:"mask0",maskUnits:"userSpaceOnUse",x:"71",y:"180",width:"1408",height:"1484",children:(0,r.jsx)("path",{d:"M574.315 248.124C692.703 157.412 857.297 157.412 975.685 248.124L1262.5 468.955C1354.81 541.513 1408.41 653.884 1408.41 772.762V1199.84C1408.41 1318.72 1354.81 1431.09 1262.5 1503.65L975.685 1724.48C857.297 1815.19 692.703 1815.19 574.315 1724.48L287.502 1503.65C195.19 1431.09 141.588 1318.72 141.588 1199.84V772.762C141.588 653.884 195.19 541.513 287.502 468.955L574.315 248.124Z",fill:"#CCCCCC"})}),(0,r.jsx)("g",{mask:"url(#mask0)",children:(0,r.jsx)("rect",{x:"48",y:"146",width:"1454",height:"821",fill:"#CCCCCC"})}),(0,r.jsx)("path",{d:"M658.6 1303.6C658.6 1257.73 713.743 1221.6 783 1221.6C852.257 1221.6 907.4 1257.73 907.4 1303.6C907.4 1328.13 861.847 1343.6 783 1343.6C704.153 1343.6 658.6 1328.13 658.6 1303.6Z",fill:"#CCCCCC"}),(0,r.jsx)("path",{d:"M771.553 390C809.071 390 837.553 418.482 837.553 456C837.553 475.694 829.359 495.486 814.097 508.145L776.947 539.162C772.316 543.05 766.79 545.197 761.062 545.197C755.335 545.197 749.809 543.05 745.178 539.162L707.303 508.09C691.856 495.367 683.553 475.418 683.553 455.5C683.553 417.982 712.035 390 749.553 390H771.553Z",fill:"white"}),(0,r.jsx)("path",{d:"M774.5 828C743.884 828 719 852.884 719 883.5C719 914.116 743.884 939 774.5 939C805.116 939 830 914.116 830 883.5C830 852.884 805.116 828 774.5 828Z",fill:"#CCCCCC"}),(0,r.jsx)("path",{d:"M1043.07 450.583L1028.87 437.844C1028.55 437.56 1028.07 437.593 1027.79 437.917L1002.56 467.325C1002.28 467.648 1002.31 468.13 1002.64 468.413L1016.84 481.152C1017.16 481.436 1017.64 481.403 1017.93 481.079L1043.16 451.671C1043.44 451.348 1043.41 450.866 1043.07 450.583Z",fill:"white"}),(0,r.jsx)("path",{d:"M1044.49 451.93C1044.25 451.709 1043.87 451.743 1043.66 452.012L1018.13 484.509C1017.92 484.778 1017.95 485.158 1018.22 485.371L1020.46 487.151C1020.73 487.365 1021.11 487.331 1021.32 487.062L1046.85 454.565C1047.06 454.296 1047.03 453.916 1046.76 453.702L1044.49 451.93Z",fill:"white"}),(0,r.jsx)("path",{d:"M1044.53 453.612L1018.39 486.109C1018.17 486.385 1018.2 486.794 1018.46 487.033L1023.27 491.522C1023.53 491.76 1023.95 491.73 1024.18 491.454L1050.32 458.957C1050.54 458.681 1050.51 458.272 1050.25 458.033L1045.44 453.544C1045.18 453.306 1044.77 453.336 1044.53 453.612Z",fill:"white"}),(0,r.jsx)("text",{x:"50%",y:"80%",textAnchor:"middle",fill:"#222",fontSize:"300",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:t})]})};var n=s(66766);function i(e){var t;let{badge:s}=e;return(null==s?void 0:null===(t=s.badges)||void 0===t?void 0:t.length)?(0,r.jsx)("div",{className:"flex gap-3 mt-2",children:s.badges.map((e,t)=>{var s,i,o;return(0,r.jsx)(a.P.div,{className:"relative",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},children:"DailyStreak"===e.badgeType?(0,r.jsx)(l,{count:null!==(s=e.count)&&void 0!==s?s:0}):(0,r.jsx)(n.default,{src:null!==(i=e.badgeSrc)&&void 0!==i?i:"/placeholder.png",alt:null!==(o=e.badgeAlt)&&void 0!==o?o:"Badge",width:48,height:48,className:"object-contain sm:w-12 sm:h-12 w-10 h-10"})},t)})}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,2265,5585,347,8441,1684,7358],()=>t(8989)),_N_E=e.O()}]);