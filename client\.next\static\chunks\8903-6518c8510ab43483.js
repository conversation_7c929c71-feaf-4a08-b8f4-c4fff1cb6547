"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8903],{7583:(e,t,a)=>{a.d(t,{default:()=>o});var s=a(95155);a(12115);var r=a(6874),l=a.n(r),i=a(66766),n=a(29911);let o=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:r}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:r,children:(0,s.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},r)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},8019:(e,t,a)=>{a.d(t,{o:()=>r});var s=a(55077);let r=async(e,t)=>{try{return(await s.S.get("check-attempt?studentId=".concat(e,"&examId=").concat(t),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var a,r;return{success:!1,error:"Failed To Get Student And Exam Detail: ".concat((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||e.message)}}}},22429:(e,t,a)=>{a.d(t,{J:()=>r,w:()=>l});var s=a(55077);let r=async()=>{try{return(await s.S.get("/referral/discount/student")).data}catch(a){var e,t;return{success:!1,error:"Failed to get discount info: ".concat((null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||a.message)}}},l=(e,t)=>{let a=Math.round(Number(e)*t/100);return Number(e)-a}},24944:(e,t,a)=>{a.d(t,{k:()=>i});var s=a(95155);a(12115);var r=a(55863),l=a(59434);function i(e){let{className:t,value:a,...i}=e;return(0,s.jsx)(r.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...i,children:(0,s.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}},31291:(e,t,a)=>{a.d(t,{n:()=>r});var s=a(55077);let r=async(e,t)=>{try{return(await s.S.post("/examApplication",{examId:e,applicantId:t},{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var a,r;throw Error((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.error)||"Failed to apply for exam")}}},39414:(e,t,a)=>{a.d(t,{A:()=>d});var s=a(95155),r=a(12115),l=a(35695),i=a(30285),n=a(61672),o=a(34540),c=a(56671);let d=e=>{let{exam:t,hasApplied:a,isMaxLimitReached:d,hasAttempted:h,onApplyClick:m}=e,u=(0,l.useRouter)(),x=(0,o.d4)(e=>e.user.isAuthenticated),[f,v]=(0,r.useState)("countdown"),[g,p]=(0,r.useState)(!1),[w,j]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{let e=()=>{let e=new Date(t.start_date).getTime(),a=t.start_registration_date?new Date(t.start_registration_date).getTime():null;if(isNaN(e)){console.error("Invalid start_date for exam ".concat(t.id,": ").concat(t.start_date)),v("finished"),p(!1),j(!1);return}let s=(0,n.L_)(new Date,"Asia/Kolkata").getTime(),r=e+6e4*t.duration;a&&s<a?j(!1):j(!0),s<e?(v("countdown"),p(!1)):s>=e&&s<=r?(v("start"),p(!0)):(v("finished"),p(!1))};e();let a=setInterval(e,1e3);return()=>clearInterval(a)},[t.start_date,t.duration,t.id,t.start_registration_date]),h)?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center gap-4 mb-4 mx-5",children:(0,s.jsx)(i.$,{className:"w-full bg-gray-400 text-white font-semibold py-2 rounded-lg cursor-not-allowed",disabled:!0,children:"Attempted"})}):(0,s.jsx)("div",{className:"flex flex-col items-center justify-center gap-4 mb-4 mx-5",children:"countdown"===f?(0,s.jsx)(i.$,{onClick:()=>{if(x){c.toast.error("You are currently logged in as a tutor. Please log out and then log in as a student to apply for UWhiz.");return}m()},className:"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",disabled:d||a||!w,children:a?"Applied":d?"Max Limit Reached":w?"Apply Now":"Registration Will Start Soon"}):"start"===f?(0,s.jsx)(i.$,{onClick:()=>{u.push("/uwhiz-exam/".concat(t.id))},className:"w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",disabled:!g||!a,children:a?"Start Exam Now":"You Have Not Applied"}):(0,s.jsx)(i.$,{disabled:!0,onClick:()=>{u.push("/uwhiz-details/".concat(t.id))},className:"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",children:"Result Will Announce Soon"})})}},46523:(e,t,a)=>{a.d(t,{Dl:()=>r,LP:()=>l});var s=a(55077);let r=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0;try{return(await s.S.get("/exams?page=".concat(e,"&limit=").concat(t).concat(a?"&applicantId=".concat(a):""),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var r,l;return{success:!1,error:"Failed to fetch Exam: ".concat((null===(l=e.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message)||e.message)}}},l=async(e,t)=>{try{return(await s.S.get("/exams/".concat(e).concat(t?"?applicantId=".concat(t):""),{headers:{"Server-Select":"uwhizServer"}})).data}catch(e){var a,r;return{success:!1,error:"Failed to fetch Exam: ".concat((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||e.message)}}}}}]);