(()=>{var e={};e.id=6968,e.ids=[6968],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6931:(e,t,r)=>{"use strict";r.d(t,{toast:()=>a});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\node_modules\\sonner\\dist\\index.mjs","Toaster");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\node_modules\\sonner\\dist\\index.mjs","toast");(0,s.registerClientReference)(function(){throw Error("Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\node_modules\\sonner\\dist\\index.mjs","useSonner")},8063:(e,t,r)=>{Promise.resolve().then(r.bind(r,6931)),Promise.resolve().then(r.bind(r,97808))},10521:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["classes-details",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73209)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/classes-details/[id]/page",pathname:"/classes-details/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>c,yv:()=>d});var s=r(60687);r(43210);var a=r(50039),n=r(78272),i=r(13964),o=r(3589),l=r(4780);function c({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function d({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...i}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...n}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,s.jsx)(h,{}),(0,s.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(m,{})]})})}function f({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"size-4"})})}function m({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22131:(e,t,r)=>{"use strict";r.d(t,{default:()=>L});var s=r(60687),a=r(43210),n=r(30474),i=r(16189),o=r(33793),l=r(20255),c=r(44255),d=r(90471),u=r(29523),p=r(90269),f=r(46303),h=r(28527),m=r(35817),x=r(54864),g=r(64398),y=r(80462),w=r(88233),v=r(15079),b=r(52581),j=r(93500),_=r(45880),N=r(27605),S=r(63442),E=r(80942);let P=async(e,t=1,r=5)=>{try{return(await h.S.get(`/reviews/class/${e}`,{params:{page:t,limit:r}})).data}catch(e){throw Error(`Failed to fetch reviews for class: ${e.message}`)}},A=async e=>{try{return(await h.S.get(`/reviews/average/${e}`)).data.averageRating}catch(e){throw Error(`Failed to get average rating: ${e.message}`)}},O=async e=>{try{return(await h.S.post("/reviews",e)).data}catch(s){let e=s.response?.data?.message||s.message,t=s.response?.data?.alreadyReviewed||!1,r=Error(e);throw r.alreadyReviewed=t,r}},C=async e=>{try{return(await h.S.delete(`/reviews/${e}`)).data}catch(e){throw Error(`Failed to delete review: ${e.message}`)}};var k=r(4780),$=r(32584);let T=_.z.object({message:_.z.string().min(10,"Message must be at least 10 characters").max(500,"Message cannot exceed 500 characters"),rating:_.z.number().min(1,"Please select a rating"),classId:_.z.string().min(1,"Class ID is required")}),R=({classId:e,userData:t,onReviewSubmit:r})=>{let[n,i]=(0,a.useState)(0),[o,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[p,f]=(0,a.useState)(null),[h,m]=(0,a.useState)("ALL"),[x,_]=(0,a.useState)([]),[A,R]=(0,a.useState)(1),[D,F]=(0,a.useState)(!1),[z,I]=(0,a.useState)(!1),[M,L]=(0,a.useState)(!1);(0,a.useEffect)(()=>{L((0,k.xh)());let e=()=>{L((0,k.xh)())};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[]);let q=(0,N.mN)({resolver:(0,S.u)(T),defaultValues:{message:"",rating:0,classId:e}}),U=(0,a.useCallback)(async(r=1,s="ALL")=>{try{let a=await P(e,r,5),n="",i=localStorage.getItem("student_data");if(i)try{let e=JSON.parse(i);e.firstName&&e.lastName&&(n=`${e.firstName} ${e.lastName}`)}catch(e){console.error("Error parsing student data:",e)}!n&&t&&t.firstName&&t.lastName&&(n=`${t.firstName} ${t.lastName}`);let o=a.reviews.map(e=>e.studentName&&!e.message.startsWith(e.studentName)?{...e,message:`${e.studentName}: ${e.message}`}:e);if(1===r){if("ALL"===s)_(o);else if("ME"===s&&n){let e=o.filter(e=>{if(!e.studentId)return e.message.split(":")[0]===n;{let t=JSON.parse(i||"{}");return e.studentId===t.id}});_(e)}}else if("ALL"===s)_(e=>[...e,...o]);else if("ME"===s&&n){let e=o.filter(e=>{if(!e.studentId)return e.message.split(":")[0]===n;{let t=JSON.parse(i||"{}");return e.studentId===t.id}});_(t=>[...t,...e])}return F(a.hasMore),R(a.currentPage),a}catch(e){return console.error("Error fetching reviews:",e),b.toast.error("Failed to fetch reviews"),null}},[e,t]),V=async()=>{I(!0);try{await U(A+1,h)}finally{I(!1)}};(0,a.useEffect)(()=>{R(1),U(1,h)},[h,e,U]);let W=e=>{f(e),d(!0)},B=async()=>{if(!p){console.error("No review ID to delete");return}if(!(0,k.xh)()){b.toast.error("You must be logged in to delete a review"),d(!1),f(null);return}try{await C(p),b.toast.success("Review deleted successfully!"),await U(1,h),r&&r()}catch(e){console.error("Error deleting review:",e),b.toast.error(e.message||"Failed to delete review")}finally{d(!1),f(null)}},G=e=>{i(e),q.setValue("rating",e)},J=async s=>{l(!0);try{let a=localStorage.getItem("student_data"),n="",o="";if(a)try{let e=JSON.parse(a);e.firstName&&e.lastName&&(n=`${e.firstName} ${e.lastName}`,o=e.id)}catch(e){console.error("Error parsing student data:",e)}if(!n&&t&&t.firstName&&t.lastName&&(n=`${t.firstName} ${t.lastName}`),!(0,k.xh)()){b.toast.error("Only students can submit reviews");return}let l={classId:s.classId,rating:s.rating,message:s.message,studentName:n,studentId:o};await O(l),b.toast.success("Review submitted successfully! The class owner will be notified."),q.reset({message:"",rating:0,classId:e}),i(0),await U(1,h),r&&r()}catch(e){console.error("Error submitting review:",e),b.toast.error(e.message||"Failed to submit review")}finally{l(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.Lt,{open:c,onOpenChange:d,children:(0,s.jsxs)(j.EO,{className:"dark:bg-siderbar",children:[(0,s.jsxs)(j.wd,{children:[(0,s.jsx)(j.r7,{children:"Are you sure?"}),(0,s.jsx)(j.$v,{children:"This action cannot be undone. This will permanently delete your testimonial."})]}),(0,s.jsxs)(j.ck,{children:[(0,s.jsx)(j.Zr,{className:"dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",children:"Cancel"}),(0,s.jsx)(j.Rx,{onClick:B,className:"bg-red-500 text-white hover:bg-red-600",children:"Delete"})]})]})}),(0,s.jsxs)("div",{className:"w-full max-w-9xl mx-auto",children:[M?(0,s.jsxs)("div",{className:"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"Write a Review"}),(0,s.jsx)(E.lV,{...q,children:(0,s.jsxs)("form",{onSubmit:q.handleSubmit(J),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Your Rating"}),(0,s.jsx)("div",{className:"flex gap-1",children:[1,2,3,4,5].map(e=>(0,s.jsx)("button",{type:"button",onClick:()=>G(e),className:"focus:outline-none",children:(0,s.jsx)(g.A,{className:`w-8 h-8 ${e<=n?"fill-[#FD904B] text-[#FD904B]":"text-gray-300"}`})},e))}),q.formState.errors.rating&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:q.formState.errors.rating.message})]}),(0,s.jsx)(E.zB,{control:q.control,name:"message",render:({field:e})=>(0,s.jsxs)(E.eI,{className:"mb-6",children:[(0,s.jsx)(E.lR,{className:"block text-sm font-medium mb-2",children:"Your Message"}),(0,s.jsx)(E.MJ,{children:(0,s.jsx)("textarea",{...e,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FD904B] focus:border-transparent",rows:4,placeholder:"Share your experience (10-500 characters)..."})}),(0,s.jsx)(E.C5,{})]})}),(0,s.jsx)("button",{type:"submit",disabled:o,className:"w-full bg-[#FD904B] text-white py-2 px-4 rounded-lg hover:bg-[#FD904B]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:o?"Submitting...":"Submit Review"})]})})]}):(0,s.jsx)("div",{className:"dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8 text-center",children:(0,s.jsx)("p",{className:"text-lg mb-4",children:"Please log in as a student to write a review"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold",children:"Class Reviews"}),M&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsxs)(v.l6,{value:h,onValueChange:e=>m(e),children:[(0,s.jsx)(v.bq,{className:"w-[120px]",children:(0,s.jsx)(v.yv,{placeholder:"Filter"})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"ALL",children:"All Reviews"}),(0,s.jsx)(v.eb,{value:"ME",children:"My Reviews"})]})]})]})]}),x.length>0?(0,s.jsxs)("div",{className:"space-y-4",children:[x.map(e=>(0,s.jsxs)("div",{className:"dark:bg-slidebar border border-gray-400 rounded-lg shadow-sm p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)("div",{className:"relative w-12 h-12 rounded-full overflow-hidden border-2 border-[#FD904B]/80 flex justify-center items-center",children:(0,s.jsx)($.eu,{children:(0,s.jsx)($.q5,{className:"bg-white text-black",children:e?.student?.firstName&&e?.student?.lastName?`${e.student.firstName[0]}${e.student.lastName[0]}`.toUpperCase():"ST"})})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-600 dark:text-white",children:e.studentName||e.message.split(":")[0]}),M&&(()=>{let t="",r="";try{let e=localStorage.getItem("student_data");if(e){let s=JSON.parse(e);s.firstName&&s.lastName&&(t=`${s.firstName} ${s.lastName}`,r=s.id)}}catch(e){console.error("Error parsing student data:",e)}return e.studentId&&r&&e.studentId===r||t&&(e.studentName&&e.studentName===t||e.message.split(":")[0]===t)?(0,s.jsx)("button",{onClick:()=>W(e.id),className:"text-red-500 hover:text-red-700",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})}):null})()]}),(0,s.jsx)("div",{className:"flex items-center gap-1 mt-1",children:[1,2,3,4,5].map((t,r)=>(0,s.jsx)(g.A,{className:`w-4 h-4 ${r<e.rating?"fill-[#FD904B] text-[#FD904B]":"text-gray-300"}`},r))})]})]}),(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 break-words mb-3",children:e.message.includes(":")?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("span",{children:e.message.split(":").slice(1).join(":").trim()})}):e.message}),(0,s.jsx)("div",{className:"flex justify-between items-center text-sm text-gray-500",children:(0,s.jsxs)("span",{children:["Posted on ",new Date(e.createdAt).toLocaleDateString()]})})]},e.id)),D&&(0,s.jsx)("div",{className:"flex justify-center mt-6",children:(0,s.jsx)(u.$,{onClick:V,variant:"outline",className:"px-6 py-2 border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10",disabled:z,children:z?"Loading...":"Load More"})})]}):(0,s.jsx)("p",{className:"text-muted-foreground",children:"ALL"===h?"No reviews yet.":"You haven't written any reviews yet."})]})]})]})};var D=r(53774);let F=async(e,t)=>{try{return(await h.S.post("/class-view-log/log-view",{classId:e,studentId:t})).data}catch(e){return console.error("Error logging class view:",e),{success:!1,message:e.response?.data?.message||"Failed to log class view"}}};var z=r(63503),I=r(41862);let M=[{key:"education",label:"Education",icon:(0,s.jsx)(o.VHr,{})},{key:"work",label:"Work Experience",icon:(0,s.jsx)(l.C12,{})},{key:"certifications",label:"Certifications",icon:(0,s.jsx)(d.VqV,{})},{key:"tuition",label:"Tuition Classes",icon:(0,s.jsx)(c.VQk,{})}],L=()=>{let[e,t]=(0,a.useState)("education"),[r,l]=(0,a.useState)(null),[g,y]=(0,a.useState)(0),[w,v]=(0,a.useState)(0),[j,_]=(0,a.useState)(!1),[N,S]=(0,a.useState)(null),[E,O]=(0,a.useState)(!1),[C,$]=(0,a.useState)(!1),{id:T}=(0,i.useParams)(),L=(0,i.useRouter)(),q=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=async()=>{try{let e=await A(T);y(e)}catch(e){console.error("Failed to fetch average rating",e)}},t=async()=>{try{let e=await P(T,1,1);v(e.total)}catch(e){console.error("Failed to fetch review count",e)}},r=async()=>{let e=(0,k.xh)();if(O(e),e&&T&&!q.current){q.current=!0;try{let e=localStorage.getItem("student_data");if(e){let t=JSON.parse(e);await F(T,t.id),console.log("Class view logged successfully")}}catch(e){console.error("Error logging class view:",e)}}};(async()=>{try{let e=await h.S.get(`classes/details/${T}`);l(e.data)}catch(e){console.error("Failed to fetch teacher data",e)}})(),e(),t(),r()},[T]),(0,a.useEffect)(()=>{(async()=>{if(E&&T)try{let e=await (0,D.Kh)(T);_(e.inWishlist),e.wishlistItem&&S(e.wishlistItem.id)}catch(e){console.error("Error checking wishlist status:",e)}})()},[E,T]);let U=(0,x.d4)(e=>e.user.user),V=async()=>{try{let e=await A(T);y(e);let t=await P(T,1,1);v(t.total)}catch(e){console.error("Failed to update review stats",e)}},W=async()=>{if(!E){$(!0);return}try{if(j&&N)await (0,D.Qg)(N),_(!1),S(null),b.toast.success("Removed from wishlist");else{let e=await (0,D.U4)(T);_(!0),e.data?.id&&S(e.data.id),b.toast.success("Added to wishlist")}}catch(e){b.toast.error(e.message||"Failed to update wishlist")}};if(!r)return(0,s.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,s.jsx)(I.A,{className:"w-8 h-8 animate-spin text-orange-500"})});let{firstName:B,lastName:G,education:J=[],experience:X=[],certificates:K=[],ClassAbout:Z={},tuitionClasses:H=[],status:Y={}}=r,Q=`${B} ${G}`,ee=Z?.profilePhoto?`http://localhost:4005/${Z.profilePhoto}`:"/teacher-profile.jpg",et=Z?.classesLogo?`http://localhost:4005/${Z.classesLogo}`:"/teacher-profile.jpg";return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.default,{}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12",children:[(0,s.jsxs)("section",{className:"grid md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"md:col-span-3 space-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 p-6 rounded-2xl shadow-sm border",children:[(0,s.jsx)("div",{className:"relative w-full sm:w-64 h-64 rounded-xl overflow-hidden shadow-lg",children:(0,s.jsx)(n.default,{src:et,alt:"Teacher",fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[Q,Y?.status==="APPROVED"&&(0,s.jsx)(d.VqV,{className:"text-green-500"})]}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground font-medium",children:Z?.catchyHeadline||"Professional Educator"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:Z?.tutorBio||"No bio available."})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold",children:"Resume"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-4 border-b pb-2",children:M.map(({key:r,label:a,icon:n})=>(0,s.jsxs)("button",{className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${e===r?"bg-orange-100 text-orange-600 font-semibold":"text-muted-foreground hover:bg-gray-100"}`,onClick:()=>t(r),children:[n,a]},r))}),(0,s.jsxs)("div",{className:"space-y-4",children:["education"===e&&(0,s.jsx)("div",{className:"grid gap-4",children:J.length>0&&J[0].isDegree?J.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,s.jsxs)("div",{className:"p-4 rounded-lg shadow-sm border",children:[(0,s.jsx)("p",{className:"font-semibold text-foreground",children:e.university}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.degree," — ",e.degreeType]}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Passout Year: ",e.passoutYear]})]},t)):(0,s.jsx)("p",{className:"text-muted-foreground",children:"No education details available."})}),"work"===e&&(0,s.jsx)("div",{className:"grid gap-4",children:X.length&&X[0].isExperience?X.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,s.jsxs)("div",{className:"p-4 rounded-lg shadow-sm border",children:[(0,s.jsx)("p",{className:"font-semibold text-foreground",children:e.title}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["From: ",new Date(e.from).toLocaleDateString()," — To:"," ",new Date(e.to).toLocaleDateString()]})]},t)):(0,s.jsx)("p",{className:"text-muted-foreground",children:"No work experience available."})}),"certifications"===e&&(0,s.jsx)("div",{className:"grid gap-4",children:K.length>0&&K[0].isCertificate?K.filter(e=>"APPROVED"===e.status).map((e,t)=>(0,s.jsx)("div",{className:"p-4 rounded-lg shadow-sm border",children:(0,s.jsx)("p",{className:"font-semibold text-foreground",children:e.title})},t)):(0,s.jsx)("p",{className:"text-muted-foreground",children:"No certifications available."})}),"tuition"===e&&(0,s.jsx)("div",{className:"grid gap-4",children:H.length>0?H.map((e,t)=>(0,s.jsxs)("div",{className:"p-6 rounded-lg shadow-sm border",children:[(0,s.jsxs)("p",{className:"text-lg font-semibold text-foreground",children:["Tuition #",t+1]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 text-sm text-muted-foreground",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Category:"})," ",e.education||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Coaching Type:"})," ",(0,m.sA)(e.coachingType)]}),"Education"===e.education?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Board:"})," ",(0,m.sA)(e.boardType)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Medium:"})," ",(0,m.sA)(e.medium)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Section:"})," ",(0,m.sA)(e.section)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Subject:"})," ",(0,m.sA)(e.subject)]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Details:"})," ",(0,m.sA)(e.details)]})]}),e.timeSlots?.length>0&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"Time Slots:"}),(0,s.jsx)("ul",{className:"list-disc ml-6 mt-1 text-sm text-muted-foreground",children:e.timeSlots.map((e,t)=>(0,s.jsxs)("li",{children:[e.from," — ",e.to]},t))})]})]},t)):(0,s.jsx)("p",{className:"text-muted-foreground",children:"No tuition classes listed yet."})})]})]})]}),(0,s.jsxs)("aside",{className:"sticky top-24 rounded-2xl p-6 shadow-sm border space-y-6",children:[(0,s.jsx)("div",{className:"relative w-full h-48 rounded-xl overflow-hidden",children:(0,s.jsx)(n.default,{src:ee,alt:"Profile",fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-2xl font-bold text-yellow-500",children:["★ ",g.toFixed(1)]}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[w," reviews"]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(u.$,{variant:"default",className:"w-full flex gap-2 bg-orange-500 hover:bg-orange-600 transition-colors",onClick:()=>{if(!E){$(!0);return}let e=`${r.firstName} ${r.lastName}`;L.push(`/student/chat?userId=${r.id}&userName=${encodeURIComponent(e)}`)},children:[(0,s.jsx)(c.VQk,{})," Send Message"]}),(0,s.jsxs)(u.$,{variant:"outline",className:`w-full flex gap-2 hover:bg-orange-50 transition-colors ${j?"bg-orange-50 text-orange-600":""}`,onClick:W,children:[j?(0,s.jsx)(o.Mbv,{className:"text-orange-500"}):(0,s.jsx)(o.sOK,{}),j?"Saved to Wishlist":"Save to My List"]})]})]})]}),(0,s.jsx)(R,{classId:T,userData:U,onReviewSubmit:V})]}),(0,s.jsx)(f.default,{}),(0,s.jsx)(z.lG,{open:C,onOpenChange:$,children:(0,s.jsxs)(z.Cf,{className:"sm:max-w-md",children:[(0,s.jsx)(z.c7,{children:(0,s.jsx)(z.L3,{className:"text-center",children:"Login Required"})}),(0,s.jsx)("div",{className:"space-y-4 py-4",children:(0,s.jsx)("p",{className:"text-center text-muted-foreground",children:"Please login as a student to add this class to your wishlist or send a message."})})]})})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35817:(e,t,r)=>{"use strict";r.d(t,{Ow:()=>n,Wz:()=>a,sA:()=>i});var s=r(50346);let a=(e,t)=>{e.contactNo&&t((0,s.ac)(s._3.PROFILE)),e.ClassAbout?.tutorBio?.length>50&&t((0,s.ac)(s._3.DESCRIPTION)),e.ClassAbout?.profilePhoto&&e.ClassAbout?.classesLogo&&t((0,s.ac)(s._3.PHOTO_LOGO)),e.education?.length>0&&t((0,s.ac)(s._3.EDUCATION)),e.certificates?.length>0&&t((0,s.ac)(s._3.CERTIFICATES)),e.experience?.length>0&&t((0,s.ac)(s._3.EXPERIENCE)),e.tuitionClasses?.length>0&&t((0,s.ac)(s._3.TUTIONCLASS)),e.address&&t((0,s.ac)(s._3.ADDRESS))},n=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch{return[e]}},i=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch{return e||"N/A"}};new TextEncoder().encode("secret123")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},45015:(e,t,r)=>{Promise.resolve().then(r.bind(r,52581)),Promise.resolve().then(r.bind(r,22131))},53774:(e,t,r)=>{"use strict";r.d(t,{Kh:()=>o,Qg:()=>i,U4:()=>n,o3:()=>l});var s=r(4780);let a="http://localhost:4005/api/v1",n=async e=>{try{let t=(0,s.ZO)();if(!t)throw Error("Authentication required");let r=await fetch(`${a}/student-wishlist`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({classId:e}),credentials:"include"});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to add to wishlist")}return await r.json()}catch(e){throw console.error("Error adding to wishlist:",e),e}},i=async e=>{try{let t=(0,s.ZO)();if(!t)throw Error("Authentication required");let r=await fetch(`${a}/student-wishlist/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`},credentials:"include"});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to remove from wishlist")}return await r.json()}catch(e){throw console.error("Error removing from wishlist:",e),e}},o=async e=>{try{let t=(0,s.ZO)();if(!t)return{inWishlist:!1};let r=await fetch(`${a}/student-wishlist/check/${e}`,{method:"GET",headers:{Authorization:`Bearer ${t}`},credentials:"include"});if(!r.ok)return{inWishlist:!1};return(await r.json()).data}catch(e){return console.error("Error checking wishlist status:",e),{inWishlist:!1}}},l=async(e=1,t=10)=>{try{let r=(0,s.ZO)();if(!r)throw Error("Authentication required");let n=await fetch(`${a}/student-wishlist?page=${e}&limit=${t}`,{method:"GET",headers:{Authorization:`Bearer ${r}`},credentials:"include"});if(!n.ok){let e=await n.json();throw Error(e.message||"Failed to fetch wishlist")}return await n.json()}catch(e){throw console.error("Error fetching wishlist:",e),e}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61170:(e,t,r)=>{"use strict";r.d(t,{b:()=>d});var s=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var n=r(60687),i=Symbol("radix.slottable");function o(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var i;let e,o;let l=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}(t,l):l),s.cloneElement(r,c)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...i}=e,l=s.Children.toArray(a),c=l.find(o);if(c){let e=c.props.children,a=l.map(t=>t!==c?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...i,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),c=s.forwardRef((e,t)=>(0,n.jsx)(l.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var d=c},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},73209:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eY,generateMetadata:()=>eH});var s,a=r(37413),n=r(97808),i=r(94612),o=r(6931);let l="http://localhost:4005/api/v1";console.log("Axios baseURL:",l);let c=i.A.create({baseURL:l,headers:{"Content-Type":"application/json"},withCredentials:!0});c.interceptors.request.use(e=>{let t=e.headers["Server-Select"];return e.baseURL="uwhizServer"===t?"http://localhost:4006":l,e},e=>Promise.reject(e)),c.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&o.toast.error(e.response.data.message||"Unauthorized"),Promise.reject(e)));var d=()=>Math.random().toString(36).substring(7).split("").join(".");d(),d(),()=>`@@redux/PROBE_UNKNOWN_ACTION${d()}`;function u(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}var p=Symbol.for("immer-nothing"),f=Symbol.for("immer-draftable"),h=Symbol.for("immer-state");function m(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var x=Object.getPrototypeOf;function g(e){return!!e&&!!e[h]}function y(e){return!!e&&(v(e)||Array.isArray(e)||!!e[f]||!!e.constructor?.[f]||S(e)||E(e))}var w=Object.prototype.constructor.toString();function v(e){if(!e||"object"!=typeof e)return!1;let t=x(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===w}function b(e,t){0===j(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,s)=>t(s,r,e))}function j(e){let t=e[h];return t?t.type_:Array.isArray(e)?1:S(e)?2:3*!!E(e)}function _(e,t){return 2===j(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function N(e,t,r){let s=j(e);2===s?e.set(t,r):3===s?e.add(r):e[t]=r}function S(e){return e instanceof Map}function E(e){return e instanceof Set}function P(e){return e.copy_||e.base_}function A(e,t){if(S(e))return new Map(e);if(E(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=v(e);if(!0!==t&&("class_only"!==t||r)){let t=x(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[h];let r=Reflect.ownKeys(t);for(let s=0;s<r.length;s++){let a=r[s],n=t[a];!1===n.writable&&(n.writable=!0,n.configurable=!0),(n.get||n.set)&&(t[a]={configurable:!0,writable:!0,enumerable:n.enumerable,value:e[a]})}return Object.create(x(e),t)}}function O(e,t=!1){return k(e)||g(e)||!y(e)||(j(e)>1&&(e.set=e.add=e.clear=e.delete=C),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>O(t,!0))),e}function C(){m(2)}function k(e){return Object.isFrozen(e)}var $={};function T(e){let t=$[e];return t||m(0,e),t}function R(e,t){t&&(T("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function D(e){F(e),e.drafts_.forEach(I),e.drafts_=null}function F(e){e===s&&(s=e.parent_)}function z(e){return s={drafts_:[],parent_:s,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function I(e){let t=e[h];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function M(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[h].modified_&&(D(t),m(4)),y(e)&&(e=L(t,e),t.parent_||U(t,e)),t.patches_&&T("Patches").generateReplacementPatches_(r[h].base_,e,t.patches_,t.inversePatches_)):e=L(t,r,[]),D(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==p?e:void 0}function L(e,t,r){if(k(t))return t;let s=t[h];if(!s)return b(t,(a,n)=>q(e,s,t,a,n,r)),t;if(s.scope_!==e)return t;if(!s.modified_)return U(e,s.base_,!0),s.base_;if(!s.finalized_){s.finalized_=!0,s.scope_.unfinalizedDrafts_--;let t=s.copy_,a=t,n=!1;3===s.type_&&(a=new Set(t),t.clear(),n=!0),b(a,(a,i)=>q(e,s,t,a,i,r,n)),U(e,t,!1),r&&e.patches_&&T("Patches").generatePatches_(s,r,e.patches_,e.inversePatches_)}return s.copy_}function q(e,t,r,s,a,n,i){if(g(a)){let i=L(e,a,n&&t&&3!==t.type_&&!_(t.assigned_,s)?n.concat(s):void 0);if(N(r,s,i),!g(i))return;e.canAutoFreeze_=!1}else i&&r.add(a);if(y(a)&&!k(a)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;L(e,a),(!t||!t.scope_.parent_)&&"symbol"!=typeof s&&Object.prototype.propertyIsEnumerable.call(r,s)&&U(e,a)}}function U(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&O(t,r)}var V={get(e,t){if(t===h)return e;let r=P(e);if(!_(r,t))return function(e,t,r){let s=G(t,r);return s?"value"in s?s.value:s.get?.call(e.draft_):void 0}(e,r,t);let s=r[t];return e.finalized_||!y(s)?s:s===B(e.base_,t)?(X(e),e.copy_[t]=K(s,e)):s},has:(e,t)=>t in P(e),ownKeys:e=>Reflect.ownKeys(P(e)),set(e,t,r){let s=G(P(e),t);if(s?.set)return s.set.call(e.draft_,r),!0;if(!e.modified_){let s=B(P(e),t),a=s?.[h];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===s?0!==r||1/r==1/s:r!=r&&s!=s)&&(void 0!==r||_(e.base_,t)))return!0;X(e),J(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==B(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,X(e),J(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=P(e),s=Reflect.getOwnPropertyDescriptor(r,t);return s?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:s.enumerable,value:r[t]}:s},defineProperty(){m(11)},getPrototypeOf:e=>x(e.base_),setPrototypeOf(){m(12)}},W={};function B(e,t){let r=e[h];return(r?P(r):e)[t]}function G(e,t){if(!(t in e))return;let r=x(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=x(r)}}function J(e){!e.modified_&&(e.modified_=!0,e.parent_&&J(e.parent_))}function X(e){e.copy_||(e.copy_=A(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function K(e,t){let r=S(e)?T("MapSet").proxyMap_(e,t):E(e)?T("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),a={type_:+!!r,scope_:t?t.scope_:s,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},n=a,i=V;r&&(n=[a],i=W);let{revoke:o,proxy:l}=Proxy.revocable(n,i);return a.draft_=l,a.revoke_=o,l}(e,t);return(t?t.scope_:s).drafts_.push(r),r}b(V,(e,t)=>{W[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),W.deleteProperty=function(e,t){return W.set.call(this,e,t,void 0)},W.set=function(e,t,r){return V.set.call(this,e[0],t,r,e[0])};var Z=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let s;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let s=this;return function(e=r,...a){return s.produce(e,e=>t.call(this,e,...a))}}if("function"!=typeof t&&m(6),void 0!==r&&"function"!=typeof r&&m(7),y(e)){let a=z(this),n=K(e,void 0),i=!0;try{s=t(n),i=!1}finally{i?D(a):F(a)}return R(a,r),M(s,a)}if(e&&"object"==typeof e)m(1,e);else{if(void 0===(s=t(e))&&(s=e),s===p&&(s=void 0),this.autoFreeze_&&O(s,!0),r){let t=[],a=[];T("Patches").generateReplacementPatches_(e,s,t,a),r(t,a)}return s}},this.produceWithPatches=(e,t)=>{let r,s;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,s=t}),r,s]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;y(e)||m(8),g(e)&&(g(t=e)||m(10,t),e=function e(t){let r;if(!y(t)||k(t))return t;let s=t[h];if(s){if(!s.modified_)return s.base_;s.finalized_=!0,r=A(t,s.scope_.immer_.useStrictShallowCopy_)}else r=A(t,!0);return b(r,(t,s)=>{N(r,t,e(s))}),s&&(s.finalized_=!1),r}(t));let r=z(this),s=K(e,void 0);return s[h].isManual_=!0,F(r),s}finishDraft(e,t){let r=e&&e[h];r&&r.isManual_||m(9);let{scope_:s}=r;return R(s,t),M(void 0,s)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let s=t[r];if(0===s.path.length&&"replace"===s.op){e=s.value;break}}r>-1&&(t=t.slice(r+1));let s=T("Patches").applyPatches_;return g(e)?s(e,t):this.produce(e,e=>s(e,t))}},H=Z.produce;Z.produceWithPatches.bind(Z),Z.setAutoFreeze.bind(Z),Z.setUseStrictShallowCopy.bind(Z),Z.applyPatches.bind(Z),Z.createDraft.bind(Z),Z.finishDraft.bind(Z),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__,"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var Y=e=>e&&"function"==typeof e.match;function Q(e,t){function r(...s){if(t){let r=t(...s);if(!r)throw Error(eB(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:s[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(function(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)})(t)&&"type"in t&&"string"==typeof t.type&&t.type===e,r}function ee(e){return["type","payload","error","meta"].indexOf(e)>-1}var et=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function er(e){return y(e)?H(e,()=>{}):e}function es(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var ea=e=>t=>{setTimeout(t,e)},en=(e={type:"raf"})=>t=>(...r)=>{let s=t(...r),a=!0,n=!1,i=!1,o=new Set,l="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:ea(10):"callback"===e.type?e.queueNotification:ea(e.timeout),c=()=>{i=!1,n&&(n=!1,o.forEach(e=>e()))};return Object.assign({},s,{subscribe(e){let t=s.subscribe(()=>a&&e());return o.add(e),()=>{t(),o.delete(e)}},dispatch(e){try{return(n=!(a=!e?.meta?.RTK_autoBatch))&&!i&&(i=!0,l(c)),s.dispatch(e)}finally{a=!0}}})};function ei(e){let t;let r={},s=[],a={addCase(e,t){let s="string"==typeof e?e:e.type;if(!s)throw Error(eB(28));if(s in r)throw Error(eB(29));return r[s]=t,a},addMatcher:(e,t)=>(s.push({matcher:e,reducer:t}),a),addDefaultCase:e=>(t=e,a)};return e(a),[r,s,t]}var eo=(e,t)=>Y(e)?e.match(t):e(t);function el(...e){return t=>e.some(e=>eo(e,t))}var ec=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},ed=["name","message","stack","code"],eu=class{constructor(e,t){this.payload=e,this.meta=t}_type},ep=class{constructor(e,t){this.payload=e,this.meta=t}_type},ef=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of ed)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},eh="External signal was aborted";function em(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var ex=Symbol.for("rtk-slice-createasyncthunk"),eg=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(eg||{}),ey=function({creators:e}={}){let t=e?.asyncThunk?.[ex];return function(e){let r;let{name:s,reducerPath:a=s}=e;if(!s)throw Error(eB(11));let n=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},i=Object.keys(n),o={},l={},c={},d=[],u={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(eB(12));if(r in l)throw Error(eB(13));return l[r]=t,u},addMatcher:(e,t)=>(d.push({matcher:e,reducer:t}),u),exposeAction:(e,t)=>(c[e]=t,u),exposeCaseReducer:(e,t)=>(o[e]=t,u)};function p(){let[t={},r=[],s]="function"==typeof e.extraReducers?ei(e.extraReducers):[e.extraReducers],a={...t,...l};return function(e,t){let r;let[s,a,n]=ei(t);if("function"==typeof e)r=()=>er(e());else{let t=er(e);r=()=>t}function i(e=r(),t){let o=[s[t.type],...a.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===o.filter(e=>!!e).length&&(o=[n]),o.reduce((e,r)=>{if(r){if(g(e)){let s=r(e,t);return void 0===s?e:s}if(y(e))return H(e,e=>r(e,t));{let s=r(e,t);if(void 0===s){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return s}}return e},e)}return i.getInitialState=r,i}(e.initialState,e=>{for(let t in a)e.addCase(t,a[t]);for(let t of d)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);s&&e.addDefaultCase(s)})}i.forEach(r=>{let a=n[r],i={reducerName:r,type:`${s}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===a._reducerDefinitionType?function({type:e,reducerName:t},r,s,a){if(!a)throw Error(eB(18));let{payloadCreator:n,fulfilled:i,pending:o,rejected:l,settled:c,options:d}=r,u=a(e,n,d);s.exposeAction(t,u),i&&s.addCase(u.fulfilled,i),o&&s.addCase(u.pending,o),l&&s.addCase(u.rejected,l),c&&s.addMatcher(u.settled,c),s.exposeCaseReducer(t,{fulfilled:i||ew,pending:o||ew,rejected:l||ew,settled:c||ew})}(i,a,u,t):function({type:e,reducerName:t,createNotation:r},s,a){let n,i;if("reducer"in s){if(r&&"reducerWithPrepare"!==s._reducerDefinitionType)throw Error(eB(17));n=s.reducer,i=s.prepare}else n=s;a.addCase(e,n).exposeCaseReducer(t,n).exposeAction(t,i?Q(e,i):Q(e))}(i,a,u)});let f=e=>e,h=new Map;function m(e,t){return r||(r=p()),r(e,t)}function x(){return r||(r=p()),r.getInitialState()}function w(t,r=!1){function s(e){let s=e[t];return void 0===s&&r&&(s=x()),s}function a(t=f){let s=es(h,r,()=>new WeakMap);return es(s,t,()=>{let s={};for(let[a,n]of Object.entries(e.selectors??{}))s[a]=function(e,t,r,s){function a(n,...i){let o=t(n);return void 0===o&&s&&(o=r()),e(o,...i)}return a.unwrapped=e,a}(n,t,x,r);return s})}return{reducerPath:t,getSelectors:a,get selectors(){return a(s)},selectSlice:s}}let v={name:s,reducer:m,actions:c,caseReducers:o,getInitialState:x,...w(a),injectInto(e,{reducerPath:t,...r}={}){let s=t??a;return e.inject({reducerPath:s,reducer:m},r),{...v,...w(s,!0)}}};return v}}();function ew(){}function ev(e){return function(t,r){let s=t=>{isAction(r)&&Object.keys(r).every(ee)?e(r.payload,t):e(r,t)};return(null)(t)?(s(t),t):createNextState3(t,s)}}function eb(e,t){return t(e)}function ej(e){return Array.isArray(e)||(e=Object.values(e)),e}var e_=class{constructor(e){this.code=e,this.message=`task cancelled (reason: ${e})`}name="TaskAbortError";message},eN=(e,t)=>{if("function"!=typeof e)throw TypeError(eB(32))},eS=()=>{},eE=(e,t=eS)=>(e.catch(t),e),eP=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),eA=(e,t)=>{let r=e.signal;!r.aborted&&("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},eO=e=>{if(e.aborted){let{reason:t}=e;throw new e_(t)}};function eC(e,t){let r=eS;return new Promise((s,a)=>{let n=()=>a(new e_(e.reason));if(e.aborted){n();return}r=eP(e,n),t.finally(()=>r()).then(s,a)}).finally(()=>{r=eS})}var ek=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof e_?"cancelled":"rejected",error:e}}finally{t?.()}},e$=e=>t=>eE(eC(e,t).then(t=>(eO(e),t))),eT=e=>{let t=e$(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:eR}=Object,eD="listenerMiddleware",eF=e=>{let{type:t,actionCreator:r,matcher:s,predicate:a,effect:n}=e;if(t)a=Q(t).match;else if(r)t=r.type,a=r.match;else if(s)a=s;else if(a);else throw Error(eB(21));return eN(n,"options.listener"),{predicate:a,type:t,effect:n}},ez=eR(e=>{let{type:t,predicate:r,effect:s}=eF(e);return{id:ec(),effect:s,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(eB(22))}}},{withTypes:()=>ez}),eI=e=>{e.pending.forEach(e=>{eA(e,null)})},eM=eR(Q(`${eD}/add`),{withTypes:()=>eM}),eL=eR(Q(`${eD}/remove`),{withTypes:()=>eL}),eq=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,eU=Symbol.for("rtk-state-proxy-original"),eV=e=>!!e&&!!e[eU],eW=new WeakMap;function eB(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}let eG=ey({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let r=t.payload;e.completedForms[r]||(e.completedForms[r]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:eJ,setCurrentStep:eX}=eG.actions;eG.reducer;let eK=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch{return e||"N/A"}};async function eZ(e){try{return(await c.get(`classes/details/${e}`)).data}catch(e){return console.error("Failed to fetch teacher data for SEO",e),null}}async function eH({params:e}){let{id:t}=e,{firstName:r,lastName:s,ClassAbout:a,tuitionClasses:n}=await eZ(t),i=`${r} ${s}`,o=a?.profilePhoto?`http://localhost:4005/${a.profilePhoto}`:"/default-teacher-profile.jpg",l=a?.catchyHeadline||"Professional Educator",c=a?.tutorBio||"Explore the expertise of our educators.",d=n?.length>0?eK(n[0].subject||n[0].details||[]):"Educational Services";return{title:`${i} - ${l} | Uest`,description:`${c} Specializing in ${d}. Book your classes with ${i} on Uest today!`,keywords:[i,l,d,"online education","tuition classes","Uest","teacher profile"],openGraph:{title:`${i} - ${l} | Uest`,description:`${c} Specializing in ${d}.`,url:`https://www.uest.in/classes-details/${t}`,type:"website",images:[{url:o,width:800,height:600,alt:`${i} Profile`}]},robots:{index:!0,follow:!0}}}function eY(){return(0,a.jsx)(n.default,{})}new TextEncoder().encode("secret123")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},80942:(e,t,r)=>{"use strict";r.d(t,{lV:()=>d,MJ:()=>g,Rr:()=>y,zB:()=>p,eI:()=>m,lR:()=>x,C5:()=>w});var s=r(60687),a=r(43210),n=r(11329),i=r(27605),o=r(4780),l=r(61170);function c({className:e,...t}){return(0,s.jsx)(l.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}let d=i.Op,u=a.createContext({}),p=({...e})=>(0,s.jsx)(u.Provider,{value:{name:e.name},children:(0,s.jsx)(i.xI,{...e})}),f=()=>{let e=a.useContext(u),t=a.useContext(h),{getFieldState:r}=(0,i.xW)(),s=(0,i.lN)({name:e.name}),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...n}},h=a.createContext({});function m({className:e,...t}){let r=a.useId();return(0,s.jsx)(h.Provider,{value:{id:r},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...t})})}function x({className:e,...t}){let{error:r,formItemId:a}=f();return(0,s.jsx)(c,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...t})}function g({...e}){let{error:t,formItemId:r,formDescriptionId:a,formMessageId:i}=f();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${a} ${i}`:`${a}`,"aria-invalid":!!t,...e})}function y({className:e,...t}){let{formDescriptionId:r}=f();return(0,s.jsx)("p",{"data-slot":"form-description",id:r,className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function w({className:e,...t}){let{error:r,formMessageId:a}=f(),n=r?String(r?.message??""):t.children;return n?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,o.cn)("text-destructive text-sm",e),...t,children:n}):null}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},97808:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\classes-details\\\\[id]\\\\InnerPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\InnerPage.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7013,2105,3099,3793,471,255,4255,4612,2800],()=>r(10521));module.exports=s})();