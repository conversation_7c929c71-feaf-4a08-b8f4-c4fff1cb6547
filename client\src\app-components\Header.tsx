"use client";

import Link from "next/link";
import Image from "next/image";
import {
  Menu,
  X,
  User,
  ShoppingBag,
  Share2,
  UserCircle,
  LayoutDashboard,
  MessageSquare,
  Coins,
  BadgeCent,
  ShoppingCart,
  Plus,
  Minus,
  CreditCard,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useAppDispatch } from "@/store/hooks";
import { useEffect, useState, useRef } from "react";
import { isStudentAuthenticated, clearStudentAuthToken } from "@/lib/utils";
import ProfileCompletionIndicator from "./ProfileCompletionIndicator";
import NotificationBell from "./NotificationBell";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from "sonner";
import { logoutStudent } from "@/services/studentAuthServices";
import { clearUser } from "@/store/slices/userSlice";
import { clearStudentProfileData } from "@/store/slices/studentProfileSlice";
import { fetchStudentProfile } from "@/store/thunks/studentProfileThunks";
import { useRouter } from "next/navigation";
import { axiosInstance } from "@/lib/axios";
import { useMotionValue, useAnimationFrame } from "framer-motion";
import { generateJWT } from "@/services/AuthService";
import StreakDisplay from "@/components/ui/streakcountdisplay";
import * as cartApi from '@/services/cartApi';
import * as storePurchaseApi from '@/services/storePurchaseApi';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";


const Header = () => {
  const { isAuthenticated, user } = useSelector(
    (state: RootState) => state.user
  );
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);
  const [studentData, setStudentData] = useState<any>(null);
  const [classStatus, setClassStatus] = useState<string | null>(null);
  const [cart, setCart] = useState<cartApi.CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const dispatch = useAppDispatch();
  const router = useRouter();

  const contentRef = useRef<HTMLDivElement>(null);
  const [contentWidth, setContentWidth] = useState(0);
  const x = useMotionValue(0);
  const speed = contentWidth / 20;

  const loadCartItems = async () => {
    try {
      const result = await cartApi.getCartItems();
      if (result.success && result.data) {
        setCart(result.data);
      }
    } catch (error) {
      console.error('Error loading cart:', error);
    }
  };

  useEffect(() => {
    const isLoggedIn = isStudentAuthenticated();
    setIsStudentLoggedIn(isLoggedIn);

    if (isLoggedIn) {
      const storedData = localStorage.getItem("student_data");
      if (storedData) {
        setStudentData(JSON.parse(storedData));
      }
      dispatch(fetchStudentProfile());
      loadCartItems();
    }

    if (isAuthenticated) {
      loadCartItems();
    }
    const fetchClassStatus = async () => {
      if (isAuthenticated && user?.id) {
        try {
          const response = await axiosInstance.get(`/classes/details/${user.id}`);
          if (response.data && response.data.status) {
            setClassStatus(response.data.status.status);
          }
        } catch (error) {
          console.error('Error fetching class status:', error);
        }
      }
    };

    fetchClassStatus();

    const handleStorageChange = () => {
      const newLoginStatus = isStudentAuthenticated();
      setIsStudentLoggedIn(newLoginStatus);
      if (newLoginStatus) {
        const storedData = localStorage.getItem("student_data");
        if (storedData) {
          setStudentData(JSON.parse(storedData));
        }
        dispatch(fetchStudentProfile());
        loadCartItems();
      } else {
        setStudentData(null);
        setCart([]);
      }
    };

    const handleCartUpdate = () => {
      if (isLoggedIn || isAuthenticated) {
        loadCartItems();
      }
    };

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("cartUpdated", handleCartUpdate);

    if (contentRef.current) {
      const width = contentRef.current.getBoundingClientRect().width;
      setContentWidth(width);
    }

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("cartUpdated", handleCartUpdate);
    };
  }, [dispatch]);

  useAnimationFrame((_, delta) => {
    if (contentWidth === 0) return;
    const currentX = x.get();
    const deltaX = (speed * delta) / 1000;
    let newX = currentX - deltaX;
    if (newX <= -contentWidth) {
      newX = 0;
    }
    x.set(newX);
  });

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleStudentLogout = async () => {
    try {
      const response = await logoutStudent();
      if (response.success !== false) {
        clearStudentAuthToken();
        setIsStudentLoggedIn(false);
        setStudentData(null);
        localStorage.removeItem("student_data");
        dispatch(clearStudentProfileData());
        toast.success("Logged out successfully");
        window.dispatchEvent(new Event("storage"));
      } else {
        toast.error(response.message || "Failed to logout");
        clearStudentAuthToken();
        setIsStudentLoggedIn(false);
        setStudentData(null);
        localStorage.removeItem("student_data");
        dispatch(clearStudentProfileData());
      }
    } catch (error) {
      console.log("Failed to logout", error);
      toast.error("Failed to logout");
      localStorage.removeItem("student_data");
      clearStudentAuthToken();
      setIsStudentLoggedIn(false);
      setStudentData(null);
      dispatch(clearStudentProfileData());
    }
  };

  const removeFromCart = async (productId: string) => {
    try {
      const result = await cartApi.removeFromCart(productId);
      if (result.success) {
        await loadCartItems();
        toast.success("Item removed from cart!");
      } else {
        toast.error(result.error || "Failed to remove item from cart");
      }
    } catch (error) {
      console.error('Error removing from cart:', error);
      toast.error("Failed to remove item from cart");
    }
  };

  const updateCartQuantity = async (productId: string, newQuantity: number) => {
    try {
      const cartItem = cart.find(item => item.itemId === productId);

      if (cartItem && newQuantity > cartItem.item.availableStock) {
        toast.error(`Only ${cartItem.item.availableStock} items available in stock`);
        return;
      }

      const result = await cartApi.updateCartItemQuantity(productId, newQuantity);
      if (result.success) {
        await loadCartItems();
        if (newQuantity === 0) {
          toast.success("Item removed from cart!");
        }
      } else {
        const errorMessage = result.error || "Failed to update cart item";
        if (errorMessage.includes("stock") || errorMessage.includes("available")) {
          toast.error("Item is out of stock or insufficient quantity available");
        } else {
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      console.error('Error updating cart:', error);
      toast.error("Failed to update cart item");
    }
  };

  const getTotalCartPrice = () => {
    return cart.reduce((total, item) => {
      return total + (item.item.coinPrice * item.quantity);
    }, 0);
  };

  const handleCheckout = async () => {
    if (cart.length === 0) {
      toast.error("Your cart is empty");
      return;
    }

    if (!isStudentLoggedIn && !isAuthenticated) {
      toast.error("Please login to checkout");
      return;
    }

    try {
      setIsCheckingOut(true);

      const cartItems = cart.map(item => ({
        id: item.itemId,
        name: item.item.name,
        coinPrice: item.item.coinPrice,
        quantity: item.quantity,
        image: item.item.image || ''
      }));

      const totalCoins = getTotalCartPrice();

      const purchaseData: storePurchaseApi.PurchaseData = {
        cartItems,
        totalCoins
      };

      const result = await storePurchaseApi.purchaseItems(purchaseData);

      if (!result.success) {
        if (result.error === 'PROFILE_NOT_APPROVED') {
          const errorMessage = result.data?.message || 'Your profile is not approved yet. Please complete your profile and wait for admin approval.';
          toast.error(errorMessage);
          return;
        }
        throw new Error(result.error);
      }

      // Clear cart after successful purchase
      await cartApi.clearCart();
      await loadCartItems();

      toast.success('Purchase completed successfully!');
      setShowCart(false);

      // Redirect to orders page
      if (isStudentLoggedIn) {
        router.push('/student/my-orders');
      } else {
        router.push('/classes/my-orders');
      }

    } catch (error: any) {
      console.error('Checkout error:', error);
      toast.error(error.message || 'Checkout failed. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  const accessClassDashboard = async () => {
    try {
      const response = await generateJWT(user?.contactNo, user?.password);

      if (response.success) {
        const { token } = response.data;
        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;
        window.location.href = redirectUrl;
      } else {
        toast.error(response.message || "Failed to generate token");
      }
    } catch (error) {
      console.error("Failed to generate token", error);
      toast.error("Failed to generate token");
    }
  };

  const navLinks = [
    { href: "/verified-classes", label: "Find Tutor" },
    { href: "/uwhiz", label: "U - Whiz" },
    {
      href: "/mock-exam-card",
      label: (
        <span className="flex items-center gap-2">
          <span>Daily Quiz</span>
          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}
        </span>
      ),
      isNew: true
    },
    { href: "/careers", label: "Career" },
    { href: "/store", label: "Store" },
  ];

  const classMenuItems = [
    {
      href: "/classes/profile",
      icon: <UserCircle className="w-5 h-5 mr-2" />,
      label: "Profile",
    },
    {
      href: "/classes/chat",
      icon: <MessageSquare className="w-5 h-5 mr-2" />,
      label: "Messages",
    },
    {
      href: "/coins",
      icon: <Coins className="w-5 h-5 mr-2" />,
      label: "Coins",
    },
    {
      href: "/classes/my-orders",
      icon: <ShoppingBag className="w-5 h-5 mr-2" />,
      label: "My Orders",
    },
    {
      onClick: accessClassDashboard,
      icon: <LayoutDashboard className="w-5 h-5 mr-2" />,
      label: "My Dashboard",
    },
    {
      href: "/classes/referral-dashboard",
      icon: <Share2 className="w-5 h-5 mr-2" />,
      label: "Referral Dashboard",
    },
    ...(classStatus === 'APPROVED' ? [{
      href: "/classes/payment",
      icon: <BadgeCent className="w-5 h-5 mr-2" />,
      label: "Payment Details",
    }] : [])
  ];

  return (
    <>
      <header className="sticky top-0 z-50 w-full bg-black">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <Link
              href="/"
              className="flex items-center space-x-2 transition-transform hover:scale-105"
            >
              <Image
                src="/logo_black.png"
                alt="Preply Logo"
                width={120}
                height={40}
                className="rounded-sm"
              />
            </Link>

            <nav className="hidden md:flex items-center space-x-6">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400"
                >
                  {link.label}
                  {link.isNew && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse">
                      Trending
                    </span>
                  )}
                </Link>
              ))}
            </nav>

            <div className="flex items-center space-x-3">
              {isAuthenticated || isStudentLoggedIn ? (
                <>
                  <NotificationBell
                    userType={isAuthenticated ? "class" : "student"}
                  />
                  {cart.length > 0 && (
                    <div className="relative">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowCart(true)}
                        className="text-white hover:bg-gray-800 rounded-full relative"
                      >
                        <ShoppingCart className="h-5 w-5" />

                        <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                          {cart.reduce((total, item) => total + item.quantity, 0)}
                        </span>

                      </Button>
                    </div>
                  )}
                  <Popover>
                    <PopoverTrigger asChild>
                      <Avatar className="cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity">
                        <AvatarFallback className="bg-white text-black flex items-center justify-center text-sm font-semibold">
                          {isAuthenticated
                            ? user?.firstName && user?.lastName
                              ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                              : "CT"
                            : studentData?.firstName && studentData?.lastName
                              ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                              : "ST"}
                        </AvatarFallback>
                      </Avatar>
                    </PopoverTrigger>
                    <PopoverContent className="w-64 bg-white p-4 rounded-lg shadow-lg">
                      <div className="flex items-center gap-3 mb-4">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-white text-black">
                            {isAuthenticated
                              ? user?.firstName && user?.lastName
                                ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                                : "CT"
                              : studentData?.firstName && studentData?.lastName
                                ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                                : "ST"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-black">
                            {isAuthenticated
                              ? user?.firstName && user?.lastName
                                ? `${user.firstName} ${user.lastName}`
                                : user?.className || "Class Account"
                              : studentData?.firstName && studentData?.lastName
                                ? `${studentData.firstName} ${studentData.lastName}`
                                : "Student Account"}
                          </p>
                          <p className="text-xs text-gray-600">
                            {isAuthenticated
                              ? user?.contactNo || "<EMAIL>"
                              : studentData?.contactNo || "<EMAIL>"}
                          </p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {isAuthenticated ? (
                          <>
                            {classMenuItems.map((item) => (
                              <Button
                                asChild
                                variant="ghost"
                                className="w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground"
                                key={item.href || item.label}
                              >
                                {item.href ? (
                                  <Link
                                    href={item.href}
                                    className="flex items-center"
                                  >
                                    {item.icon}
                                    <span>{item.label}</span>
                                  </Link>
                                ) : (
                                  <div
                                    onClick={item.onClick}
                                    className="flex items-center w-full"
                                  >
                                    {item.icon}
                                    <span>{item.label}</span>
                                  </div>
                                )}
                              </Button>
                            ))}
                            <Button
                              variant="ghost"
                              className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                              onClick={async () => {
                                try {
                                  const response = await axiosInstance.post(
                                    "/auth-client/logout",
                                    {}
                                  );
                                  if (response.data.success) {
                                    router.push("/");
                                    dispatch(clearUser());
                                    localStorage.removeItem("token");
                                    toast.success("Logged out successfully");
                                  }
                                } catch (error) {
                                  console.error("Logout error:", error);
                                  toast.error("Failed to logout");
                                }
                              }}
                            >
                              <User className="w-5 h-5 mr-2" />
                              <span>Logout</span>
                            </Button>
                          </>
                        ) : (
                          <>
                            <div className="space-y-2">
                              {[
                                {
                                  href: "/student/profile",
                                  icon: <UserCircle className="w-5 h-5 mr-2" />,
                                  label: "Profile",
                                },
                                {
                                  href: "/student/chat",
                                  icon: (
                                    <MessageSquare className="w-5 h-5 mr-2" />
                                  ),
                                  label: "Messages",
                                },
                                {
                                  href: "/coins",
                                  icon: <Coins className="w-5 h-5 mr-2" />,
                                  label: "Coins",
                                },
                                {
                                  href: "/student/wishlist",
                                  icon: (
                                    <ShoppingBag className="w-5 h-5 mr-2" />
                                  ),
                                  label: "My Wishlist",
                                },
                                {
                                  href: "/student/referral-dashboard",
                                  icon: <Share2 className="w-5 h-5 mr-2" />,
                                  label: "Referral Dashboard",
                                },
                                {
                                  href: "/student/my-orders",
                                  icon: (
                                    <ShoppingBag className="w-5 h-5 mr-2" />
                                  ),
                                  label: "My Orders",
                                },
                              ].map((item) => (
                                <Button
                                  asChild
                                  variant="ghost"
                                  className="w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground"
                                  key={item.href}
                                >
                                  <Link
                                    href={item.href}
                                    className="flex items-center"
                                  >
                                    {item.icon}
                                    <span>{item.label}</span>
                                  </Link>
                                </Button>
                              ))}
                              <Button
                                onClick={handleStudentLogout}
                                className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                              >
                                <User className="w-5 h-5 mr-2" />
                                <span>Logout</span>
                              </Button>
                            </div>
                          </>
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>
                </>
              ) : (
                <>
                  <div className="hidden md:flex items-center gap-2">
                    <Button
                      className="bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md"
                      asChild
                    >
                      <Link href="/class/login">Join as Tutor</Link>
                    </Button>

                    <Button
                      variant="ghost"
                      className="bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700"
                      asChild
                    >
                      <Link href="/student/login">Student Login</Link>
                    </Button>
                  </div>
                </>
              )}

              <Button
                variant="ghost"
                size="icon"
                className="md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full"
                onClick={toggleMenu}
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div>
        <div
          className={`fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ${isMenuOpen ? "translate-x-0" : "translate-x-full"
            }`}
        >
          <div className="flex flex-col h-full p-6">
            <div className="flex items-center justify-between mb-6">
              <Image
                src="/logo_black.png"
                alt="Uest Logo"
                width={100}
                height={32}
                className="rounded-sm"
              />
              <div className="flex items-center gap-2">
                {(isAuthenticated || isStudentLoggedIn) && (
                  <div className="relative">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        setShowCart(true);
                        toggleMenu();
                      }}
                      className="text-orange-400 hover:bg-orange-500/10 rounded-full relative"
                    >
                      <ShoppingCart className="h-5 w-5" />
                      {cart.length > 0 && (
                        <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                          {cart.reduce((total, item) => total + item.quantity, 0)}
                        </span>
                      )}
                    </Button>
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-orange-400 hover:bg-orange-500/10 rounded-full"
                  onClick={toggleMenu}
                >
                  <X className="h-6 w-6" />
                </Button>
              </div>
            </div>

            {(isAuthenticated || isStudentLoggedIn) && (
              <div className="mb-6">
                <div className="flex items-center gap-3 p-3 bg-gray-900 rounded-lg">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-white text-black">
                      {isAuthenticated
                        ? user?.firstName && user?.lastName
                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                          : "CT"
                        : studentData?.firstName && studentData?.lastName
                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                          : "ST"}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-white">
                      {isAuthenticated
                        ? user?.firstName && user?.lastName
                          ? `${user.firstName} ${user.lastName}`
                          : user?.className || "Class Account"
                        : studentData?.firstName && studentData?.lastName
                          ? `${studentData.firstName} ${studentData.lastName}`
                          : "Student Account"}
                    </p>
                    <p className="text-xs text-gray-400">
                      {isAuthenticated
                        ? user?.contactNo || "<EMAIL>"
                        : studentData?.contactNo || "<EMAIL>"}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <nav className="flex flex-col space-y-2">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors"
                  onClick={toggleMenu}
                >
                  <div className="flex items-center gap-3">
                    {typeof link.label === "string" ? (
                      <span>{link.label}</span>
                    ) : (
                      link.label
                    )}
                  </div>
                  {link.isNew && (
                    <span className="text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white">
                      Trending
                    </span>
                  )}
                </Link>
              ))}
            </nav>

            <div className="mt-auto space-y-2">
              {isAuthenticated && (
                <>
                  {classMenuItems.map((item) => (
                    <Button
                      asChild
                      variant="ghost"
                      className="w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground"
                      key={item.href || item.label}
                    >
                      {item.href ? (
                        <Link
                          href={item.href}
                          className="flex items-center"
                          onClick={toggleMenu}
                        >
                          {item.icon}
                          <span>{item.label}</span>
                        </Link>
                      ) : (
                        <div
                          onClick={() => {
                            toggleMenu();
                          }}
                          className="flex items-center w-full"
                        >
                          {item.icon}
                          <span>{item.label}</span>
                        </div>
                      )}
                    </Button>
                  ))}
                  <Button
                    variant="ghost"
                    className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                    onClick={async () => {
                      try {
                        const response = await axiosInstance.post(
                          "/auth-client/logout",
                          {}
                        );
                        if (response.data.success) {
                          router.push("/");
                          dispatch(clearUser());
                          localStorage.removeItem("token");
                          toast.success("Logged out successfully");
                        }
                      } catch (error) {
                        console.error("Logout error:", error);
                        toast.error("Failed to logout");
                      }
                      toggleMenu();
                    }}
                  >
                    <User className="w-5 h-5 mr-2" />
                    <span>Logout</span>
                  </Button>
                </>
              )}

              {isStudentLoggedIn && (
                <>
                  {studentData?.firstName && studentData?.lastName && (
                    <div className="p-3 border border-[#ff914d]/20 rounded-lg bg-white">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12 border-2 border-[#ff914d]">
                          <AvatarFallback className="bg-white text-black">
                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-black">{`${studentData.firstName} ${studentData.lastName}`}</p>
                          <p className="text-xs text-gray-600">{studentData.contactNo}</p>
                        </div>
                      </div>
                    </div>
                  )}
                  <Button
                    asChild
                    className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white"
                    onClick={toggleMenu}
                  >
                    <Link href="/student/profile" className="flex items-center justify-center gap-3">
                      <UserCircle className="h-5 w-5" />
                      <span>Profile</span>
                    </Link>
                  </Button>
                  <Button
                    asChild
                    className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white"
                    onClick={toggleMenu}
                  >
                    <Link href="/student/wishlist" className="flex items-center justify-center gap-3">
                      <ShoppingBag className="h-5 w-5" />
                      <span>My Wishlist</span>
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                    onClick={() => {
                      handleStudentLogout();
                      toggleMenu();
                    }}
                  >
                    <User className="w-5 h-5 mr-2" />
                    <span>Logout</span>
                  </Button>
                </>
              )}

              {!isAuthenticated && !isStudentLoggedIn && (
                <div className="space-y-2">
                  <Button
                    className="w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3"
                    asChild
                  >
                    <Link href="/class/login" onClick={toggleMenu}>
                      Tutor Login
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700"
                    asChild
                  >
                    <Link href="/student/login" onClick={toggleMenu}>
                      Student Login
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {isStudentLoggedIn && <ProfileCompletionIndicator />}
      </div>

      {/* Shopping Cart Dialog */}
      <Dialog open={showCart} onOpenChange={setShowCart}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ShoppingCart className="w-5 h-5" />
              Shopping Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)
            </DialogTitle>
            <DialogDescription>
              Review your items before checkout
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {cart.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingCart className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Your cart is empty</p>
              </div>
            ) : (
              <>
                {cart.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg bg-card">
                    <Image
                      src={
                        item.item.image?.startsWith('http')
                          ? item.item.image
                          : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${item.item.image?.startsWith('/') ? item.item.image.substring(1) : item.item.image || 'uploads/store/placeholder.jpg'}`
                      }
                      alt={item.item.name}
                      width={60}
                      height={60}
                      className="rounded object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "/logo.png";
                      }}
                    />
                    <div className="flex-1">
                      <h4 className="font-medium text-card-foreground">{item.item.name}</h4>
                      <p className="text-orange-500 font-semibold flex items-center">
                        <Coins className="w-4 h-4 mr-1" />
                        {item.item.coinPrice} coins
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateCartQuantity(item.itemId, item.quantity - 1)}
                      >
                        <Minus className="w-3 h-3" />
                      </Button>
                      <span className="w-8 text-center">{item.quantity}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateCartQuantity(item.itemId, item.quantity + 1)}
                      >
                        <Plus className="w-3 h-3" />
                      </Button>
                    </div>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => removeFromCart(item.itemId)}
                    >
                      Remove
                    </Button>
                  </div>
                ))}

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center text-lg font-semibold">
                    <span>Total:</span>
                    <span className="text-orange-500 flex items-center">
                      <Coins className="w-5 h-5 mr-1" />
                      {getTotalCartPrice()} coins
                    </span>
                  </div>
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCart(false)}>
              Continue Shopping
            </Button>
            {cart.length > 0 && (
              <Button
                onClick={handleCheckout}
                disabled={isCheckingOut}
                className="bg-orange-500 hover:bg-orange-600 disabled:opacity-50"
              >
                {isCheckingOut ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="w-4 h-4 mr-2" />
                    Checkout ({getTotalCartPrice()} coins)
                  </>
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Header;
