(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{12675:(e,a,t)=>{Promise.resolve().then(t.bind(t,16981))},16981:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>er});var s=t(95155),r=t(12115),i=t(30285),l=t(53896),o=t(41066),n=t(30227),c=t(76028),d=t(80465),x=t(47835),m=t(42148),u=t(53311),h=t(14738),p=t(22226),g=t(82137),b=t(38619),f=t(79397),v=t(2708),j=t(47298),y=t(57100),N=t(92138),w=t(38564),k=t(35169),F=t(16785),B=t(14186),A=t(59964),P=t(70347),D=t(7583),C=t(77958),S=t(19320),E=t(56787),V=t(66766),I=t(55077),z=t(35695),L=t(56671),O=t(66695),T=t(27677),R=t(97469);t(2252),t(59408),t(56970);var H=t(40224);let M=e=>{let{thoughts:a}=e,t="http://localhost:4005/".replace(/\/+$/,""),r=e=>{e.currentTarget.style.display="none"},i={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},l={hidden:{opacity:0,x:-20},visible:e=>({opacity:1,x:0,transition:{delay:.2*e,duration:.5,ease:"easeOut"}})},o=a.filter(e=>"APPROVED"===e.status);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("style",{children:"\n  .swiper-container {\n    position: relative;\n  }\n  .swiper-pagination {\n    position: absolute;\n    bottom: 20px !important;\n  }\n  .swiper-pagination-bullet {\n    background: #d1d5db;\n    opacity: 0.5;\n    width: 12px;\n    height: 12px;\n    margin: 0 6px !important;\n    border-radius: 12px;\n    transition: all 0.3s ease;\n  }\n  .swiper-pagination-bullet-active {\n    background: #FD904B;\n    opacity: 0.5;\n    width: 36px;\n    border-radius: 12px;\n    transform: none;\n  }\n"}),(0,s.jsx)(T.RC,{modules:[R.Vx,R.dK,R.Ij],spaceBetween:30,slidesPerView:1,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},pagination:{clickable:!0},autoplay:{delay:4e3,disableOnInteraction:!1},className:"w-full max-w-6xl mx-auto swiper-container",children:o.map(e=>{var a;let o=(null===(a=e.class.ClassAbout)||void 0===a?void 0:a.classesLogo)?"".concat(t).concat(e.class.ClassAbout.classesLogo.startsWith("/")?"":"/").concat(e.class.ClassAbout.classesLogo):"";return(0,s.jsx)(T.qr,{children:(0,s.jsxs)(S.P.div,{variants:i,initial:"hidden",animate:"visible",className:"relative dark:bg-siderbar rounded-2xl p-8 sm:p-8 flex flex-col md:flex-row items-center gap-6 sm:gap-8 overflow-hidden border border-gray-200 dark:border-gray-700/50 backdrop-blur-lg shadow-sm mb-12",children:[(0,s.jsx)("div",{className:"absolute top-4 left-4 opacity-20",children:(0,s.jsx)(H.A,{className:"w-12 h-12 text-[#FD904B]"})}),(0,s.jsx)("div",{className:"flex-shrink-0 relative",children:(0,s.jsx)(S.P.div,{transition:{duration:.3},className:"h-24 w-24 sm:h-28 sm:w-28 rounded-full overflow-hidden border-4 border-[#FD904B]/20 shadow-sm",children:o?(0,s.jsx)(V.default,{width:200,height:200,src:o,alt:"Class Logo",className:"h-full w-full object-cover",onError:r}):(0,s.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-100 dark:bg-gray-700",children:(0,s.jsx)("span",{className:"text-gray-400 dark:text-gray-500 text-xs",children:"No logo"})})})}),(0,s.jsxs)("div",{className:"flex-1 text-center md:text-left space-y-3 relative z-10",children:[(0,s.jsxs)(S.P.p,{custom:0,variants:l,initial:"hidden",animate:"visible",className:"text-xl sm:text-2xl font-semibold text-gray-900 dark:text-gray-50 leading-tight tracking-wide",children:['"',e.thoughts,'"']}),(0,s.jsx)(S.P.p,{custom:1,variants:l,initial:"hidden",animate:"visible",className:"text-lg font-medium text-gray-700 dark:text-gray-300",children:e.class.className}),(0,s.jsxs)(S.P.p,{custom:2,variants:l,initial:"hidden",animate:"visible",className:"text-md font-light text-gray-600 dark:text-gray-400 italic",children:["— ",e.class.firstName," ",e.class.lastName]})]})]})},e.id)})})]})},_=async function(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;try{return(await I.S.get("/classes-thought",{params:{status:e||void 0,classId:a||void 0,page:t,limit:s}})).data}catch(e){var r,i;throw Error((null===(i=e.response)||void 0===i?void 0:null===(r=i.data)||void 0===r?void 0:r.message)||"Failed to fetch thoughts: ".concat(e.message))}},W=e=>[void 0,void 0,void 0,void 0,void 0].map((a,t)=>(0,s.jsx)(w.A,{className:"w-4 h-4 ".concat(t<e?"fill-[#FD904B] text-[#FD904B]":"text-gray-300")},t)),G=e=>{let{testimonial:a}=e,t=a.class.fullName||a.class.className,r=a.class.className,i=a.message,l=a.rating,o=a.class.classesLogo?"".concat("http://localhost:4005/").concat(a.class.classesLogo):a.class.profilePhoto?"".concat("http://localhost:4005/").concat(a.class.profilePhoto):"/teacher-profile.jpg";return(0,s.jsx)("div",{className:"inline-flex flex-shrink-0 w-[360px] mx-4",children:(0,s.jsxs)(S.P.div,{className:"dark:bg-siderbar rounded-3xl p-8 w-full relative overflow-hidden border-2 border-gray-200 dark:border-gray-700",whileHover:{scale:1.02,borderColor:"#FD904B",zIndex:1},children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)(S.P.div,{className:"relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border-2 border-gray-200 dark:border-gray-700",whileHover:{scale:1.1},children:(0,s.jsx)(V.default,{src:o,alt:t,fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"font-semibold text-base dark:text-white text-gray-800 truncate",children:t}),(0,s.jsx)("p",{className:"text-orange-500 text-sm font-medium truncate",children:r}),(0,s.jsx)("div",{className:"flex items-center gap-1 mt-1",children:W(l)})]})]}),(0,s.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,s.jsxs)("p",{className:"text-gray-700 text-base leading-relaxed break-words line-clamp-3 italic dark:text-white",children:['"',i,'"']})})]})})},$=e=>{let{direction:a=1,testimonials:t}=e,[i,l]=(0,r.useState)(0),o=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=()=>{o.current&&l(392*t.length)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[t.length]);let n=[];for(let e=0;e<6;e++)n.push(...t);return(0,s.jsx)("div",{className:"overflow-hidden",children:(0,s.jsx)(S.P.div,{ref:o,className:"flex",animate:{x:a>0?[-i,0]:[0,-i]},transition:{x:{repeat:1/0,repeatType:"loop",duration:40,ease:"linear",times:[0,1]}},style:{gap:"32px"},children:n.map((e,a)=>(0,s.jsx)(G,{testimonial:e},"".concat(e.id,"-").concat(a)))})})},q=()=>{let[e,a]=(0,r.useState)([]),[t,i]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{(async()=>{try{i(!0);let e=await I.S.get("/testimonials/approved");a(e.data)}catch(e){console.error("Error fetching testimonials:",e)}finally{i(!1)}})()},[]),t||0!==e.length)?(0,s.jsx)("section",{className:"py-20 dark:bg-slidebar",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16 px-4",children:[(0,s.jsx)(S.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"text-4xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent dark:text-white",children:"What Our Clients Say"}),(0,s.jsx)(S.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-gray-600 text-lg",children:"Trusted by thousands of satisfied customers"})]}),t?(0,s.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FD904B]"})}):(0,s.jsxs)("div",{className:"space-y-12",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)($,{direction:-1,testimonials:e}),(0,s.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-40  z-10"}),(0,s.jsx)("div",{className:"absolute right-0 top-0 bottom-0 w-40 z-10"})]}),e.length>5&&(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)($,{direction:1,testimonials:e}),(0,s.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-40 z-10"}),(0,s.jsx)("div",{className:"absolute right-0 top-0 bottom-0 w-40 z-10"})]})]})]})}):null};var Y=t(6874),Z=t.n(Y),U=t(51154),K=t(36754),J=t(54568);let Q=()=>{let[e,a]=(0,r.useState)([]),[t,l]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{(async()=>{try{l(!0);let e=await (0,K.BU)(1,3);a(e.blogs)}catch(e){console.error("Failed to fetch recent blogs:",e)}finally{l(!1)}})()},[]),0!==e.length||t)?(0,s.jsxs)("section",{className:"py-20 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,s.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,s.jsxs)(S.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,s.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Latest Blogs"}),(0,s.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"Our Latest Articles"}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Stay updated with our latest news, tips, and insights"})]}),t?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)(U.A,{className:"h-8 w-8 animate-spin text-primary"})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-10",children:e.map(e=>(0,s.jsx)(S.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.1},whileHover:{y:-5},children:(0,s.jsx)(J.A,{blog:e})},e.id))}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(Z(),{href:"/blogs",passHref:!0,children:(0,s.jsx)(i.$,{variant:"outline",className:"px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300",children:"Visit More Blogs"})})})]})]})]}):null};var X=t(17580),ee=t(35376),ea=t(87949);let et=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1500,[t,s]=(0,r.useState)(0);return(0,r.useEffect)(()=>{let t=performance.now(),r=i=>{let l=Math.min((i-t)/a,1);s(Math.floor(l*e)),l<1?requestAnimationFrame(r):s(e)};requestAnimationFrame(r)},[e,a]),t};function es(e){let{totalTutors:a,totalStudent:t}=e,r=et(a),i=et(16),l=et(t),o=[{icon:(0,s.jsx)(X.A,{className:"w-8 h-8"}),count:r,suffix:"+",label:"Verified Classes"},{icon:(0,s.jsx)(ee.A,{className:"w-8 h-8"}),count:i,suffix:"+",label:"Categories"},{icon:(0,s.jsx)(ea.A,{className:"w-8 h-8"}),count:l,suffix:"+",label:"Students"}];return(0,s.jsxs)("section",{className:"py-20 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,s.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8",children:o.map((e,a)=>(0,s.jsxs)(S.P.div,{className:"relative group",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:.15*a},viewport:{once:!0},children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FD904B]/20 to-transparent rounded-2xl blur-2xl group-hover:blur-3xl transition-all duration-300 opacity-0 group-hover:opacity-100"}),(0,s.jsxs)("div",{className:"relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border shadow-sm hover:shadow-sm transition-all duration-300",children:[(0,s.jsx)("div",{className:"text-[#FD904B] mb-4 transform group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,s.jsxs)("h3",{className:"text-4xl font-bold mb-2 bg-gradient-to-br from-foreground to-foreground/80 bg-clip-text text-transparent",children:[e.count,e.suffix]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e.label})]})]},a))})})]})}t(49042);let er=()=>{let e=(0,z.useRouter)(),[a,t]=(0,r.useState)({}),[H,W]=(0,r.useState)([]),[G,$]=(0,r.useState)([]),[Y,U]=(0,r.useState)(!0),[K,J]=(0,r.useState)(!0),[X,ee]=(0,r.useState)([]),ea=e=>({Education:(0,s.jsx)(l.A,{className:"w-10 h-10"}),Drama:(0,s.jsx)(o.A,{className:"w-10 h-10"}),Music:(0,s.jsx)(n.A,{className:"w-10 h-10"}),"Art & Craft":(0,s.jsx)(c.A,{className:"w-10 h-10"}),Sports:(0,s.jsx)(d.A,{className:"w-10 h-10"}),"Foreign Languages":(0,s.jsx)(x.A,{className:"w-10 h-10"}),Technology:(0,s.jsx)(m.A,{className:"w-10 h-10"}),Dance:(0,s.jsx)(u.A,{className:"w-10 h-10"}),"Computer Classes":(0,s.jsx)(h.A,{className:"w-10 h-10"}),"Cooking Classes":(0,s.jsx)(p.A,{className:"w-10 h-10"}),"Garba Classes":(0,s.jsx)(g.A,{className:"w-10 h-10"}),"Vaidik Maths":(0,s.jsx)(b.A,{className:"w-10 h-10"}),"Gymnastic Classes":(0,s.jsx)(f.A,{className:"w-10 h-10"}),"Yoga Classes":(0,s.jsx)(v.A,{className:"w-10 h-10"}),"Aviation Classes":(0,s.jsx)(j.A,{className:"w-10 h-10"}),"Designing Classes":(0,s.jsx)(y.A,{className:"w-10 h-10"})})[e]||(0,s.jsx)(l.A,{className:"w-10 h-10"}),et=async()=>{try{let e=await I.S.get("/constant/TuitionClasses");if(e.data&&e.data.details){let a=e.data.details.map(e=>({name:e.name,icon:ea(e.name)}));ee(a)}}catch(e){console.error("Failed to fetch categories:",e),ee([])}finally{}},[er,ei]=(0,r.useState)(0),[el,eo]=(0,r.useState)(0);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await I.S.get("/student/count");eo(e.data||0)}catch(e){console.error("Error fetching total students:",e),eo(0)}};(async()=>{try{let e=await I.S.get("/classes/category-counts");t(e.data)}catch(e){console.error("Error fetching category counts:",e)}})(),e()},[]);let en=a=>{e.push("/verified-classes?education=".concat(a))},ec=async()=>{U(!0);try{let e=await I.S.get("/classes/approved-tutors",{params:{page:1,limit:4,sortByRating:!0,sortByReviewCount:!0}});if(e.data&&"object"==typeof e.data){if(void 0!==e.data.success&&void 0!==e.data.data){let a=e.data.data;ei(a.totalClasses||0),W(a.data||[])}else ei(e.data.totalClasses||0),W(e.data.data||[])}else ei(0),W([])}catch(e){console.error("Failed to fetch tutors:",e),L.toast.error("Failed to fetch tutors"),ei(0),W([])}finally{U(!1)}},ed=async()=>{J(!0);try{var e;let a=await _("APPROVED",void 0,1,5),t=(null===(e=a.thoughts)||void 0===e?void 0:e.filter(e=>"APPROVED"===e.status))||[];$(t)}catch(e){console.error("Error fetching thoughts:",e),L.toast.error("Failed to fetch thoughts"),$([])}finally{J(!1)}};(0,r.useEffect)(()=>{ec(),ed(),et()},[]);let ex={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:100}}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(P.default,{}),(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground overflow-hidden",children:[(0,s.jsx)(r.Suspense,{children:(0,s.jsx)(C.A,{})}),(0,s.jsxs)("main",{className:"relative",children:[(0,s.jsx)("section",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)(S.P.div,{className:"container mx-auto text-center relative z-10",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,s.jsx)(S.P.div,{className:"inline-block mb-6 rounded-full bg-[#fff9f3] backdrop-blur-sm border border-border px-6 py-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,s.jsx)("span",{className:"text-[#FD904B] font-medium",children:"Your Gateway to Educational Excellence"})}),(0,s.jsxs)(S.P.h1,{className:"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-foreground via-[#FD904B] to-foreground bg-clip-text text-transparent drop-shadow-sm",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:["Lessons,",(0,s.jsx)("span",{className:"text-[#FD904B] mx-2",children:"you’ll love"}),"Guaranteed."]}),(0,s.jsx)(S.P.p,{className:"text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:"Try another classes for free if you’re not satisfied."}),(0,s.jsxs)(S.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-10",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:[(0,s.jsx)(Z(),{href:"/verified-classes",children:(0,s.jsx)(i.$,{size:"lg",variant:"outline",className:"px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300",children:"Explore Now"})}),(0,s.jsx)(Z(),{href:"https://play.google.com/store/apps/details?id=com.uest",children:(0,s.jsxs)(i.$,{size:"lg",variant:"outline",className:"px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300",children:[(0,s.jsx)(V.default,{src:"/googlePlay.png",alt:"Google Play Store",width:32,height:32,className:"object-contain"}),"Download App",(0,s.jsx)(N.A,{className:"w-5"})]})})]}),(0,s.jsxs)(S.P.div,{className:"flex justify-center items-center gap-8 flex-wrap mt-20",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(V.default,{src:"/DPIIT-white.png",height:200,width:200,alt:"Startup India"}),(0,s.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Backed by Startup India"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(V.default,{src:"/iso-white.png",height:100,width:200,alt:"ISO Certified"}),(0,s.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"ISO 9001:2015 Certified"})]})]})]})}),(0,s.jsx)(es,{totalTutors:er,totalStudent:el}),(0,s.jsxs)("section",{className:"py-20 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,s.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,s.jsxs)(S.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,s.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Featured Classes"}),(0,s.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"Meet Our Top Tutors"}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Connect with our top verified tutors and start your learning journey today."})]}),Y?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,a)=>(0,s.jsx)("div",{className:"h-96 w-full rounded-xl bg-gray-200 dark:bg-gray-700 animate-pulse"},a))}):0===H.length?(0,s.jsx)(S.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-10",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No tutors found at the moment."})}):(0,s.jsx)(S.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:H.map((a,t)=>(0,s.jsx)(S.P.div,{variants:ex,whileHover:{y:-5},className:"h-full",children:(0,s.jsxs)(O.Zp,{className:"h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300",children:[(0,s.jsxs)(O.aR,{className:"flex flex-row items-center gap-4",children:[(0,s.jsx)(S.P.div,{className:"relative w-20 h-20 rounded-full overflow-hidden ring-2 ring-[#FD904B]/20",whileHover:{scale:1.05},children:(0,s.jsx)(V.default,{src:a.ClassAbout&&a.ClassAbout.classesLogo?"".concat("http://localhost:4005/").concat(a.ClassAbout.classesLogo):"/default-profile.jpg",alt:a.firstName,fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold hover:text-[#FD904B] transition-colors",children:[a.firstName," ",a.lastName]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:a.className})]})]}),(0,s.jsx)(O.Wu,{className:"flex-1 space-y-4",children:(0,s.jsx)("p",{className:"line-clamp-2 text-sm text-muted-foreground",children:a.ClassAbout&&a.ClassAbout.tutorBio||"No bio available."})}),(0,s.jsxs)(O.wL,{className:"flex flex-col items-start gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 pt-2",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,s.jsx)("span",{className:"font-semibold text-foreground",children:a.averageRating?a.averageRating.toFixed(1):"0"}),(0,s.jsxs)("span",{children:["(",a.reviewCount||0," reviews)"]})]}),(0,s.jsx)(i.$,{className:"w-full bg-orange-500 hover:bg-orange-600",onClick:()=>e.push("/classes-details/".concat(a.id)),children:"View Profile"})]})]})},t))})]})]}),(0,s.jsx)(E.x,{transition:{duration:.4},children:(0,s.jsxs)("section",{className:"py-20 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,s.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,s.jsxs)(S.P.div,{className:"text-center mb-12",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,s.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Categories"}),(0,s.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"Explore Your Interests"}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Discover classes across various categories with our verified tutors."})]}),(0,s.jsxs)("div",{className:"flex justify-end items-center gap-4 mb-6",children:[(0,s.jsx)("button",{className:"swiper-button-prev-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition",children:(0,s.jsx)(k.A,{size:20})}),(0,s.jsx)("button",{className:"swiper-button-next-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition",children:(0,s.jsx)(N.A,{size:20})})]}),(0,s.jsx)(T.RC,{modules:[R.Vx,R.Ij],autoplay:{delay:3e3,disableOnInteraction:!1},navigation:{nextEl:".swiper-button-next-custom",prevEl:".swiper-button-prev-custom"},spaceBetween:20,breakpoints:{320:{slidesPerView:1.2},640:{slidesPerView:2},1024:{slidesPerView:3},1280:{slidesPerView:4}},className:"!px-2 !pt-3",children:X.map((e,t)=>(0,s.jsx)(T.qr,{children:(0,s.jsx)(S.P.div,{className:"group cursor-pointer",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:.1*t},viewport:{once:!0},whileHover:{y:-5,transition:{duration:.2}},onClick:()=>en(e.name),children:(0,s.jsxs)("div",{className:"relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B] transition-all duration-300 overflow-hidden shadow-sm group-hover:shadow-md",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FD904B]/0 to-transparent group-hover:from-[#FD904B]/10 rounded-2xl transition-all duration-300"}),!Y&&(0,s.jsx)("div",{className:"absolute top-3 right-3 z-10",children:(0,s.jsxs)("span",{className:"text-sm font-bold bg-[#FD904B] text-white px-3 py-1.5 rounded-full shadow-sm flex items-center justify-center min-w-[40px] transform transition-all duration-300 group-hover:scale-110",children:[a[e.name]||0,(0,s.jsx)("span",{className:"ml-1 text-xs hidden group-hover:inline",children:"classes"})]})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"text-[#FD904B] mb-6 p-4 bg-[#FD904B]/10 rounded-full inline-flex transform group-hover:scale-110 transition-all duration-300 group-hover:shadow-md group-hover:bg-[#FD904B]/20",children:e.icon}),(0,s.jsx)("div",{className:"mb-3",children:(0,s.jsx)("h3",{className:"text-2xl font-bold text-foreground",children:e.name})}),(0,s.jsxs)("p",{className:"text-muted-foreground group-hover:text-[#FD904B] transition-colors duration-300 flex items-center gap-1 font-medium",children:["Explore courses"," ",(0,s.jsx)("span",{className:"transform transition-transform group-hover:translate-x-1",children:"→"})]})]})]})},t)},t))})]})]})}),(G.length>0||K)&&(0,s.jsxs)("section",{className:"py-20 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,s.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,s.jsxs)(S.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,s.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Thoughts"}),(0,s.jsx)("h2",{className:"text-4xl font-bold bg-clip-text mb-4",children:"What Our Community Thinks"}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Hear from our verified students and tutors about their experiences."})]}),K?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"})}):(0,s.jsx)(M,{thoughts:G})]})]}),(0,s.jsxs)("section",{className:"py-20 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"}),(0,s.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,s.jsxs)(S.P.div,{className:"text-center mb-16",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},children:[(0,s.jsx)("span",{className:"text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block",children:"Process"}),(0,s.jsx)("h2",{className:"text-4xl font-bold bg-clip-text",children:"How UEST Works"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:[{icon:(0,s.jsx)(F.A,{className:"w-8 h-8"}),title:"Find Your Perfect Match",description:"Browse through our verified classes and find your ideal match"},{icon:(0,s.jsx)(B.A,{className:"w-8 h-8"}),title:"Schedule Lessons",description:"Book lessons at times that work best for your schedule"},{icon:(0,s.jsx)(A.A,{className:"w-8 h-8"}),title:"Start Learning",description:"Begin your learning journey with personalized guidance"}].map((e,a)=>(0,s.jsxs)(S.P.div,{className:"group relative",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:.2*a},viewport:{once:!0},children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FD904B]/5 to-transparent rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"}),(0,s.jsxs)("div",{className:"relative p-8 h-52 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B]/50 transition-all duration-300 shadow-sm",children:[(0,s.jsx)("div",{className:"text-[#FD904B] mb-6 transform group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,s.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-foreground",children:e.title}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e.description})]})]},a))})]})]}),(0,s.jsx)(q,{}),(0,s.jsx)(Q,{})]})]}),(0,s.jsx)(D.default,{})]})}},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>n,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l,wL:()=>d});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",a),...t})}},77958:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var s=t(12115),r=t(35695),i=t(56671),l=t(34540),o=t(92560);let n=()=>{let e=(0,r.useSearchParams)().get("authError"),a=(0,l.wA)();return(0,s.useEffect)(()=>{"1"===e&&(i.toast.error("Login Expired, Please login to continue"),a((0,o.lM)()))},[e]),null}}},e=>{var a=a=>e(e.s=a);e.O(0,[1892,6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,2265,3290,347,5881,8441,1684,7358],()=>a(12675)),_N_E=e.O()}]);