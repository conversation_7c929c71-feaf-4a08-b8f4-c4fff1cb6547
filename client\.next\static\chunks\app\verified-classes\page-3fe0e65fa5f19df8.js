(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1554],{7583:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(95155);s(12115);var l=s(6874),i=s.n(l),r=s(66766),n=s(29911);let c=()=>(0,a.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,a.jsx)(i(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(r.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:s,label:l}=e;return(0,a.jsx)("div",{className:"flex flex-col items-center",children:(0,a.jsx)(i(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:l,children:(0,a.jsx)(s,{className:"text-xl text-white hover:text-gray-400 transition"})})},l)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,a.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,a.jsx)("p",{children:"Head Office"}),(0,a.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,a.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,a.jsx)(i(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,a.jsx)(r.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(i(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,a.jsx)(i(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},20185:(e,t,s)=>{"use strict";s.d(t,{Ow:()=>i,Wz:()=>l,sA:()=>r});var a=s(94314);let l=(e,t)=>{var s,l,i,r,n,c,o,d;e.contactNo&&t((0,a.ac)(a._3.PROFILE)),(null===(l=e.ClassAbout)||void 0===l?void 0:null===(s=l.tutorBio)||void 0===s?void 0:s.length)>50&&t((0,a.ac)(a._3.DESCRIPTION)),(null===(i=e.ClassAbout)||void 0===i?void 0:i.profilePhoto)&&(null===(r=e.ClassAbout)||void 0===r?void 0:r.classesLogo)&&t((0,a.ac)(a._3.PHOTO_LOGO)),(null===(n=e.education)||void 0===n?void 0:n.length)>0&&t((0,a.ac)(a._3.EDUCATION)),(null===(c=e.certificates)||void 0===c?void 0:c.length)>0&&t((0,a.ac)(a._3.CERTIFICATES)),(null===(o=e.experience)||void 0===o?void 0:o.length)>0&&t((0,a.ac)(a._3.EXPERIENCE)),(null===(d=e.tuitionClasses)||void 0===d?void 0:d.length)>0&&t((0,a.ac)(a._3.TUTIONCLASS)),e.address&&t((0,a.ac)(a._3.ADDRESS))},i=e=>{if(!e)return[];try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[t]}catch(t){return[e]}},r=e=>{try{let t="string"==typeof e?JSON.parse(e):e;return Array.isArray(t)?t.join(", "):t||"N/A"}catch(t){return e||"N/A"}};new TextEncoder().encode("secret123")},38564:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},42355:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},46102:(e,t,s)=>{"use strict";s.d(t,{Bc:()=>r,ZI:()=>o,k$:()=>c,m_:()=>n});var a=s(95155);s(12115);var l=s(71739),i=s(59434);function r(e){let{delayDuration:t=0,...s}=e;return(0,a.jsx)(l.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...s})}function n(e){let{...t}=e;return(0,a.jsx)(r,{children:(0,a.jsx)(l.bL,{"data-slot":"tooltip",...t})})}function c(e){let{...t}=e;return(0,a.jsx)(l.l9,{"data-slot":"tooltip-trigger",...t})}function o(e){let{className:t,sideOffset:s=0,children:r,...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{"data-slot":"tooltip-content",sideOffset:s,className:(0,i.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...n,children:[r,(0,a.jsx)(l.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>r,wL:()=>d});var a=s(95155);s(12115);var l=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,l.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},66932:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68856:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(95155),l=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,l.cn)("bg-accent animate-pulse rounded-md",t),...s})}},69244:(e,t,s)=>{Promise.resolve().then(s.bind(s,75580))},75580:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(95155),l=s(12115),i=s(35695),r=s(19320),n=s(66695),c=s(30285),o=s(66932),d=s(54416),m=s(38564),u=s(42355),x=s(13052),h=s(55077),g=s(70347),p=s(7583),f=s(56671),j=s(66766),v=s(68856);let N=e=>{let{label:t,value:s,onChange:i}=e,r=l.useRef(null);return(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-muted-foreground mb-1",children:t}),(0,a.jsx)("input",{ref:r,type:"text",placeholder:"Enter ".concat(t),className:"border bg-white dark:bg-black rounded-lg px-3 py-2 text-sm text-black dark:text-white   focus:border-customOrange focus:ring focus:ring-customOrange/20 focus:outline-none   transition-all duration-200",value:s,onChange:e=>{let t=e.target.value.replace(/\s+/g," ").trimStart();e.target.value=t,i(e)},maxLength:50})]})};var b=s(20185),y=s(54165),w=s(46102),C=s(51013),k=s(29911);let A=()=>{let e=(0,i.useRouter)(),[t,s]=(0,l.useState)([]),[g,p]=(0,l.useState)(null),[A,S]=(0,l.useState)(!0),[E,T]=(0,l.useState)(1),[O,P]=(0,l.useState)(1),[_,F]=(0,l.useState)(!1),[R]=(0,l.useState)(!1),[I,L]=(0,l.useState)(!1),D=(0,i.useSearchParams)(),[U,z]=(0,l.useState)(!1),[B,$]=(0,l.useState)(null),[Z,H]=(0,l.useState)(1e3),[M,V]=(0,l.useState)(null),[W,q]=(0,l.useState)({education:D.get("education")||"",details:D.get("details")||"",boardType:D.get("boardType")||"",medium:D.get("medium")||"",section:D.get("section")||"",coachingType:D.get("coachingType")||"",subject:D.get("subject")||"",firstName:D.get("firstName")||"",lastName:D.get("lastName")||"",className:D.get("className")||"",sortByRating:!0,sortByReviewCount:!0}),G=e=>{if(!g)return[];let t=g.details.find(e=>"Education"===e.name);if(!t)return[];let s=t.subDetails.find(t=>t.name===e);return s?s.values.map(e=>({id:e.id,value:e.name})):[]},Y=async()=>{try{let e=await h.S.get("/constant/TuitionClasses");p(e.data)}catch(e){f.toast.error("Failed to fetch tuition class categories")}},J=()=>{if(!navigator.geolocation){V("Geolocation is not supported by your browser.");return}navigator.geolocation.getCurrentPosition(e=>{$({lat:e.coords.latitude,lng:e.coords.longitude}),V(null)},()=>{V("Unable to retrieve your location.")})},Q=async()=>{if(B){S(!0);try{let e=await h.S.get("/classes/nearby",{params:{lat:B.lat,lng:B.lng,radius:Z}});s(e.data.data||[]),P(1)}catch(e){f.toast.error("Failed to fetch nearby classes")}finally{S(!1)}}},X=async e=>{S(!0);try{let t=e||W,a=await h.S.get("/classes/approved-tutors",{params:{page:E,limit:9,...t}}),l=a.data.data;s(l),P(a.data.totalPages)}catch(e){f.toast.error("Failed to fetch tutors")}finally{S(!1)}};(0,l.useEffect)(()=>{Y()},[]),(0,l.useEffect)(()=>{U&&B?Q():X()},[E,U,B,Z]);let K={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},ee={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{type:"spring",stiffness:100}}},et=e=>{let{label:t,value:s,onChange:l,options:i}=e;return(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-muted-foreground mb-1",children:t}),(0,a.jsxs)("select",{className:"border rounded-lg px-3 py-2 text-sm   dark:bg-black   text-gray-900 dark:text-white",value:s,onChange:l,children:[(0,a.jsxs)("option",{value:"",className:"bg-white dark:bg-zinc-900",children:["All ",t]}),i.map(e=>(0,a.jsx)("option",{value:e.value,className:"bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100",children:e.value},e.id))]})]})};return(0,l.useEffect)(()=>{U&&!B&&J()},[U,B]),(0,a.jsxs)(r.P.section,{initial:"hidden",animate:"visible",variants:K,className:"container mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,a.jsxs)(r.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold",children:"Find Your Perfect Tutor"}),(0,a.jsx)("p",{className:"mt-4 text-lg font-medium bg-gradient-to-r from-gray-700 to-gray-500 dark:from-gray-300 dark:to-gray-400 bg-clip-text text-transparent",children:"Discover experienced tutors who can help you achieve your learning goals"})]}),(0,a.jsxs)(r.P.div,{className:"mb-8 bg-white/30 dark:bg-black/30 backdrop-blur-lg rounded-xl p-6",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-customOrange"}),(0,a.jsx)("h3",{className:"text-xl font-semibold",children:"Filters"})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsx)(c.$,{variant:"outline",className:"hover:bg-customOrange/10",onClick:()=>F(!_),children:_?(0,a.jsx)(d.A,{className:"w-4 h-4"}):(0,a.jsx)(o.A,{className:"w-4 h-4"})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 transition-all duration-300 ".concat(_?"block":"hidden"),children:[(0,a.jsx)(et,{label:"Category",value:W.education,onChange:e=>{q(t=>({...t,education:e.target.value,details:"",boardType:"Education"!==e.target.value?"":t.boardType,medium:"Education"!==e.target.value?"":t.medium,section:"Education"!==e.target.value?"":t.section,subject:"Education"!==e.target.value?"":t.subject}))},options:g?g.details.map(e=>({id:e.id,value:e.name})):[]}),"Education"===W.education&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(et,{label:"Board Type",value:W.boardType,onChange:e=>q(t=>({...t,boardType:e.target.value})),options:G("Board Type")}),(0,a.jsx)(et,{label:"Medium",value:W.medium,onChange:e=>q(t=>({...t,medium:e.target.value})),options:G("Medium")}),(0,a.jsx)(et,{label:"Section",value:W.section,onChange:e=>q(t=>({...t,section:e.target.value})),options:G("Section")}),(0,a.jsx)(et,{label:"Subject",value:W.subject,onChange:e=>q(t=>({...t,subject:e.target.value})),options:G("Subject")})]}),W.education&&"Education"!==W.education&&(0,a.jsx)(et,{label:"Details",value:W.details,onChange:e=>q(t=>({...t,details:e.target.value})),options:(e=>{if(!g)return[];let t=g.details.find(t=>t.name===e);return t?"Education"===e?[]:t.subDetails.map(e=>({id:e.id,value:e.name})):[]})(W.education)}),(0,a.jsx)(et,{label:"Coaching Type",value:W.coachingType,onChange:e=>q(t=>({...t,coachingType:e.target.value})),options:[{id:"personal",value:"Personal"},{id:"group",value:"Group"},{id:"online",value:"Online"},{id:"hybrid",value:"Hybrid"}]}),(0,a.jsx)(N,{label:"First Name",value:W.firstName,onChange:e=>q(t=>({...t,firstName:e.target.value}))}),(0,a.jsx)(N,{label:"Class Name",value:W.className,onChange:e=>q(t=>({...t,className:e.target.value}))})]}),(0,a.jsxs)("div",{className:"flex gap-4 mt-4 ".concat(_?"block":"hidden"),children:[(0,a.jsx)(c.$,{className:"w-[200px] bg-customOrange hover:bg-customOrange/90",onClick:()=>{T(1),X()},children:"Apply Filters"}),(0,a.jsx)(c.$,{variant:"default",className:"w-[200px]",onClick:()=>{let e={education:"",details:"",boardType:"",medium:"",section:"",coachingType:"",subject:"",firstName:"",lastName:"",className:"",sortByRating:!0,sortByReviewCount:!0};q(e),T(1),X(e)},children:"Reset Filters"})]})]}),(0,a.jsxs)("div",{className:"flex flex-nowrap items-center gap-6 w-full mb-5",children:[(0,a.jsxs)("label",{className:"flex items-center gap-2 mb-0 whitespace-nowrap flex-shrink-0",children:[(0,a.jsx)("input",{type:"checkbox",checked:U,onChange:()=>z(e=>!e),className:"accent-customOrange"}),(0,a.jsx)("span",{children:"Show Nearby Classes"})]}),U&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,a.jsxs)("select",{className:"border rounded px-2 py-1 min-w-[90px]",value:Z,onChange:e=>H(Number(e.target.value)),children:[(0,a.jsx)("option",{value:100,children:"100m"}),(0,a.jsx)("option",{value:500,children:"500m"}),(0,a.jsx)("option",{value:1e3,children:"1km"}),(0,a.jsx)("option",{value:5e3,children:"5km"}),(0,a.jsx)("option",{value:1e4,children:"10km"}),(0,a.jsx)("option",{value:1e4,children:"60km"})]}),(0,a.jsx)("span",{className:"whitespace-nowrap",children:Z>=1e3?"".concat(Z/1e3,"km Radius"):"".concat(Z,"m Radius")})]}),M&&(0,a.jsx)("span",{className:"text-red-500 ml-2 flex-shrink-0",children:M})]})]}),A?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,a.jsx)(v.E,{className:"h-96 w-full rounded-xl"},t))}):0===t.length?(0,a.jsx)(r.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-10",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No tutors found. Try adjusting your filters."})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.P.div,{variants:K,initial:"hidden",animate:"visible",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map((t,s)=>{var l;return(0,a.jsx)(r.P.div,{variants:ee,whileHover:{y:-5},className:"h-full",children:(0,a.jsxs)(n.Zp,{className:"h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center gap-4",children:[(0,a.jsx)(r.P.div,{className:"relative w-30 h-30 rounded-full overflow-hidden ring-2 ring-customOrange/20",whileHover:{scale:1.05},children:(0,a.jsx)(j.default,{src:t.ClassAbout&&t.ClassAbout.classesLogo?"".concat("http://localhost:4005/").concat(t.ClassAbout.classesLogo):"/default-profile.jpg",alt:t.firstName,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold hover:text-customOrange transition-colors",children:[t.firstName," ",t.lastName]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:t.className})]}),(0,a.jsxs)(w.m_,{children:[(0,a.jsx)(w.k$,{asChild:!0,children:(0,a.jsx)("div",{className:"relative group cursor-pointer",children:(0,a.jsx)(C.VqV,{className:"text-green-500"})})}),(0,a.jsx)(w.ZI,{className:"text-xs",children:"Verified by Uest"})]})]}),(0,a.jsxs)(n.Wu,{className:"flex-1 space-y-4",children:[(0,a.jsxs)(w.m_,{children:[(0,a.jsx)(w.k$,{asChild:!0,children:(null===(l=t.education)||void 0===l?void 0:l.filter(e=>e.isDegree&&"APPROVED"===e.status).length)>0&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground mt-1 flex items-center gap-1 flex-wrap",children:[(0,a.jsx)(k.YNd,{className:"text-customOrange text-lg"}),(0,a.jsxs)("span",{className:"text-md",children:["Degrees:"," ",t.education.filter(e=>e.isDegree&&"APPROVED"===e.status).map(e=>e.degree).join(", ")]})]})}),(0,a.jsx)(w.ZI,{className:"text-xs",children:"Verified by Uest"})]}),(0,a.jsx)("p",{className:"line-clamp-2 text-sm text-muted-foreground",children:t.ClassAbout&&t.ClassAbout.tutorBio||"No bio available."}),Array.isArray(t.tuitionClasses)&&t.tuitionClasses.length>0&&(0,a.jsx)("div",{className:"space-y-2 p-4 rounded-lg bg-black/5 dark:bg-white/5",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Category"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,b.sA)(t.tuitionClasses[0].education)||"N/A"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Coaching Type"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,b.sA)(t.tuitionClasses[0].coachingType)||"N/A"})]}),"Education"===t.tuitionClasses[0].education&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Board"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,b.sA)(t.tuitionClasses[0].boardType)||"N/A"})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Medium"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,b.sA)(t.tuitionClasses[0].medium)||"N/A"})]})]})]})}),void 0!==t.distance&&(0,a.jsxs)("div",{className:"text-sm text-customOrange font-semibold",children:["Distance: ",(t.distance/1e3).toFixed(2)," km"]})]}),(0,a.jsxs)(n.wL,{className:"flex flex-col items-start gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 pt-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,a.jsx)("span",{className:"font-semibold text-foreground",children:t.averageRating?t.averageRating.toFixed(1):"0"}),(0,a.jsxs)("span",{children:["(",t.reviewCount||0," reviews)"]})]}),(0,a.jsxs)("div",{className:"flex gap-2 w-full",children:[(0,a.jsx)(c.$,{className:"flex-1 bg-customOrange hover:bg-[#E88143]",onClick:()=>e.push("/classes-details/".concat(t.id)),children:"View Profile"}),(0,a.jsx)(c.$,{variant:"outline",className:"flex-1 hover:bg-orange-50",onClick:()=>{if(!R){L(!0);return}let s="".concat(t.firstName," ").concat(t.lastName);e.push("/student/chat?userId=".concat(t.id,"&userName=").concat(encodeURIComponent(s)))},children:"Message"})]})]})]})},s)})}),(0,a.jsxs)("div",{className:"flex justify-center items-center mt-8 gap-4",children:[(0,a.jsxs)(c.$,{variant:"outline",disabled:1===E,onClick:()=>T(E-1),className:"flex gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"})," Previous"]}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",E," of ",O]}),(0,a.jsxs)(c.$,{variant:"outline",disabled:E===O,onClick:()=>T(E+1),className:"flex gap-2",children:["Next ",(0,a.jsx)(x.A,{className:"h-4 w-4"})]})]}),(0,a.jsx)(y.lG,{open:I,onOpenChange:L,children:(0,a.jsxs)(y.Cf,{className:"sm:max-w-md",children:[(0,a.jsx)(y.c7,{children:(0,a.jsx)(y.L3,{className:"text-center",children:"Login Required"})}),(0,a.jsx)("div",{className:"space-y-4 py-4",children:(0,a.jsx)("p",{className:"text-center text-muted-foreground",children:"Please login as a student to add this class to send a message."})})]})})]})]})},S=()=>(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(g.default,{}),(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,a.jsx)(v.E,{className:"h-96 w-full rounded-xl"},t))})}),children:(0,a.jsx)(A,{})}),(0,a.jsx)(p.default,{})]})},94314:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>c,_3:()=>l,ac:()=>r});var a=s(51990),l=function(e){return e.PROFILE="about",e.DESCRIPTION="description",e.PHOTO_LOGO="photo_logo",e.EDUCATION="education",e.EXPERIENCE="experience",e.CERTIFICATES="certificates",e.TUTIONCLASS="tution_class",e.ADDRESS="address",e}({});let i=(0,a.Z0)({name:"formProgress",initialState:{completedSteps:0,totalSteps:8,currentStep:1,completedForms:{about:!1,description:!1,photo_logo:!1,education:!1,certificates:!1,experience:!1,tution_class:!1,address:!1}},reducers:{completeForm:(e,t)=>{let s=t.payload;e.completedForms[s]||(e.completedForms[s]=!0,e.completedSteps=Math.min(e.completedSteps+1,e.totalSteps))},setCurrentStep:(e,t)=>{e.currentStep=t.payload}}}),{completeForm:r,setCurrentStep:n}=i.actions,c=i.reducer}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,512,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,2265,1739,347,8441,1684,7358],()=>t(69244)),_N_E=e.O()}]);