(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8150],{5040:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},7583:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var s=a(95155);a(12115);var r=a(6874),l=a.n(r),i=a(66766),n=a(29911);let o=()=>(0,s.jsx)("footer",{className:"bg-black text-gray-300 px-6 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-7xl space-y-16",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-6",children:[(0,s.jsx)(l(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(i.default,{src:"/logo_black.png",alt:"Logo",width:200,height:40,className:"object-contain"})}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-1",children:[{href:"mailto:<EMAIL>",icon:n.maD,label:"Email Us"},{href:"https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09",icon:n.feZ,label:"Twitter"},{href:"https://www.facebook.com/share/1FNYcyqawH/",icon:n.ok6,label:"Facebook"},{href:"https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==",icon:n.ao$,label:"Instagram"},{href:"https://www.linkedin.com/company/uest-edtech/",icon:n.H1h,label:"LinkedIn"},{href:"https://pin.it/1Di0EFtAa",icon:n.aR7,label:"Pinterest"},{href:"https://www.tumblr.com/uestedtech?source=share",icon:n.kUm,label:"Tumblr"}].map(e=>{let{href:t,icon:a,label:r}=e;return(0,s.jsx)("div",{className:"flex flex-col items-center",children:(0,s.jsx)(l(),{href:t,className:"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition",title:r,children:(0,s.jsx)(a,{className:"text-xl text-white hover:text-gray-400 transition"})})},r)})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"About"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Tutors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/support",className:"hover:text-white transition",children:"Support"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/careers",className:"hover:text-white transition",children:"Careers"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"For Students"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/student/login",className:"hover:text-white transition",children:"Student Login"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/verified-classes",className:"hover:text-white transition",children:"Find Online Tutor"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/uwhiz",className:"hover:text-white transition",children:"Uwhiz"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-sm space-y-1 leading-relaxed",children:[(0,s.jsx)("p",{children:"Head Office"}),(0,s.jsx)("p",{children:"4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641"}),(0,s.jsx)("p",{children:"Contact: +91 96 877 877 88"}),(0,s.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Apps"}),(0,s.jsx)(l(),{href:"https://play.google.com/store/apps/details?id=com.uest",target:"_blank",children:(0,s.jsx)(i.default,{src:"/playstore.png",alt:"Google Play Store",width:180,height:50,className:"object-contain"})})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("p",{children:"\xa9 2025 uest.in. All rights reserved."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(l(),{href:"/terms-and-conditions",className:"hover:text-white transition",children:"Terms & Conditions"}),(0,s.jsx)(l(),{href:"/privacy-policy",className:"hover:text-white transition",children:"Privacy Policy"})]})]})]})})},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},36754:(e,t,a)=>{"use strict";a.d(t,{$5:()=>n,BU:()=>r,c5:()=>o,cc:()=>c,dZ:()=>i,sq:()=>l});var s=a(55077);let r=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{return(await s.S.get("/blogs/approved",{params:{page:e,limit:t}})).data}catch(e){var a,r;throw Error((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||"Failed to fetch approved blogs: ".concat(e.message))}},l=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0;try{return(await s.S.get("/blogs/my-blogs",{params:{page:e,limit:t,status:a}})).data}catch(e){var r,l;throw Error((null===(l=e.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message)||"Failed to fetch your blogs: ".concat(e.message))}},i=async e=>{try{return(await s.S.get("/blogs/".concat(e))).data.data}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to fetch blog: ".concat(e.message))}},n=async e=>{try{let t=new FormData;return t.append("blogTitle",e.blogTitle),t.append("blogDescription",e.blogDescription),e.blogImage&&t.append("blogImage",e.blogImage),(await s.S.post("/blogs",t,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to create blog: ".concat(e.message))}},o=async(e,t)=>{try{let a=new FormData;return t.blogTitle&&a.append("blogTitle",t.blogTitle),t.blogDescription&&a.append("blogDescription",t.blogDescription),t.blogImage&&a.append("blogImage",t.blogImage),t.status&&a.append("status",t.status),(await s.S.put("/blogs/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})).data.data}catch(e){var a,r;throw Error((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||"Failed to update blog: ".concat(e.message))}},c=async e=>{try{await s.S.delete("/blogs/".concat(e))}catch(e){var t,a;throw Error((null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)||"Failed to delete blog: ".concat(e.message))}}},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>d});var s=a(95155);a(12115);var r=a(59434);function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},69074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},73496:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(95155),r=a(12115),l=a(35695),i=a(66766),n=a(36754),o=a(7632),c=a(30285),d=a(51154),h=a(35169),m=a(71007),u=a(5040),x=a(69074),g=a(70347),p=a(7583),f=a(66695);let b=e=>{let t,{params:a}=e,b=(0,l.useRouter)(),[v,j]=(0,r.useState)(null),[y,w]=(0,r.useState)(!0),[N,k]=(0,r.useState)("");if((0,r.useEffect)(()=>{(async()=>{let{id:e}=await a;k(e)})()},[a]),(0,r.useEffect)(()=>{let e=async()=>{try{if(!N)return;w(!0);let e=await (0,n.dZ)(N);j(e)}catch(e){b.push("/blogs")}finally{w(!1)}};N&&e()},[N,b]),y)return(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,s.jsx)(g.default,{}),(0,s.jsx)("div",{className:"flex justify-center items-center h-[70vh]",children:(0,s.jsx)(d.A,{className:"h-8 w-8 animate-spin text-primary"})}),(0,s.jsx)(p.default,{})]});if(!v)return(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,s.jsx)(g.default,{}),(0,s.jsxs)("div",{className:"flex flex-col justify-center items-center h-[70vh]",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Blog not found"}),(0,s.jsx)(c.$,{onClick:()=>b.push("/blogs"),children:"Go back to blogs"})]}),(0,s.jsx)(p.default,{})]});let O="http://localhost:4005/".replace(/\/$/,""),E=v.blogImage?v.blogImage.startsWith("/")?v.blogImage:"/".concat(v.blogImage):"",P=v.blogImage?"".concat(O).concat(E):"";return(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,s.jsx)(g.default,{}),(0,s.jsxs)("main",{className:"container mx-auto py-12 px-4 max-w-5xl",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)(c.$,{variant:"outline",size:"icon",className:"mr-3 rounded-full hover:bg-[#FD904B]/10 hover:text-[#FD904B] transition-colors",onClick:()=>b.back(),children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Back to Blogs"})]}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6 leading-tight",children:v.blogTitle}),v.class&&(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 text-[#FD904B]"}),(0,s.jsxs)("span",{children:[v.class.firstName," ",v.class.lastName]})]}),(0,s.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-gray-300 dark:bg-gray-600"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 text-[#FD904B]"}),(0,s.jsx)("span",{children:v.class.className})]}),(0,s.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-gray-300 dark:bg-gray-600"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-[#FD904B]"}),(0,s.jsx)("span",{children:(t=v.createdAt,(0,o.GP)(new Date(t),"MMMM dd, yyyy"))})]})]})]}),(0,s.jsx)("div",{className:"relative w-full h-[300px] md:h-[500px] mb-10 rounded-2xl overflow-hidden shadow-lg",children:v.blogImage&&(0,s.jsx)("div",{className:"relative w-full h-full",children:(0,s.jsx)(i.default,{src:P,alt:v.blogTitle,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 80vw",priority:!0})})}),(0,s.jsx)(f.Zp,{className:"border-0 shadow-none mb-12",children:(0,s.jsx)(f.Wu,{className:"p-0 md:p-4",children:(0,s.jsx)("article",{className:"blog-content",dangerouslySetInnerHTML:{__html:v.blogDescription}})})})]}),(0,s.jsx)(p.default,{})]})}},74436:(e,t,a)=>{"use strict";a.d(t,{k5:()=>d});var s=a(12115),r={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=s.createContext&&s.createContext(r),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(this,arguments)}function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,s)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach(function(t){var s,r,l;s=e,r=t,l=a[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in s?Object.defineProperty(s,r,{value:l,enumerable:!0,configurable:!0,writable:!0}):s[r]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function d(e){return t=>s.createElement(h,n({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,a)=>s.createElement(t.tag,c({key:a},t.attr),e(t.child)))}(e.child))}function h(e){var t=t=>{var a,{attr:r,size:l,title:o}=e,d=function(e,t){if(null==e)return{};var a,s,r=function(e,t){if(null==e)return{};var a={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;a[s]=e[s]}return a}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)a=l[s],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}(e,i),h=l||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),s.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,r,d,{className:a,style:c(c({color:e.color||t.color},t.style),e.style),height:h,width:h,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==l?s.createElement(l.Consumer,null,e=>t(e)):t(r)}},80871:(e,t,a)=>{Promise.resolve().then(a.bind(a,73496))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,7632,347,8441,1684,7358],()=>t(80871)),_N_E=e.O()}]);