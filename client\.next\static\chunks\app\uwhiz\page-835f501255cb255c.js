(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7742],{12767:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},40318:(e,t,s)=>{Promise.resolve().then(s.bind(s,98984))},42355:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},52278:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>d});var a=s(95155);s(12115);var r=s(59434);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var a=s(12115),r={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(r),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(this,arguments)}function o(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,a)}return s}function c(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?o(Object(s),!0).forEach(function(t){var a,r,n;a=e,r=t,n=s[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in a?Object.defineProperty(a,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>a.createElement(x,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,s)=>a.createElement(t.tag,c({key:s},t.attr),e(t.child)))}(e.child))}function x(e){var t=t=>{var s,{attr:r,size:n,title:o}=e,d=function(e,t){if(null==e)return{};var s,a,r=function(e,t){if(null==e)return{};var s={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)s=n[a],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(r[s]=e[s])}return r}(e,l),x=n||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),a.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,r,d,{className:s,style:c(c({color:e.color||t.color},t.style),e.style),height:x,width:x,xmlns:"http://www.w3.org/2000/svg"}),o&&a.createElement("title",null,o),e.children)};return void 0!==n?a.createElement(n.Consumer,null,e=>t(e)):t(r)}},98984:(e,t,s)=>{"use strict";s.d(t,{default:()=>q});var a=s(95155),r=s(70347);let n={src:"/_next/static/media/exam-logo.367af320.jpg"};var l=s(66695),i=s(30285),o=s(46523),c=s(55077);let d=async(e,t,s)=>{try{return(await c.S.post("/examApplicantEmail/send-exam-applicant-email",{examId:e,exam_name:t,email:s})).data}catch(e){var a,r;throw Error((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message)||"Failed to send exam applicant email")}};var x=s(31291),m=s(12115),u=s(64315),h=s(56671),g=s(8019),p=s(51154),f=s(93550),j=s(52278),b=s(42355),v=s(13052),w=s(12767),y=s(7583),N=s(93588),k=s(24944),O=s(93347),_=s(25703),A=s(89447),S=s(50958),C=s(61672);let E=(0,m.memo)(e=>{let{exam:t}=e,[s,r]=(0,m.useState)("registration"),[n,l]=(0,m.useState)({days:0,hours:0,minutes:0,seconds:0}),[i,o]=(0,m.useState)({minutes:10,seconds:0,isLate:!1});return(0,m.useEffect)(()=>{let e=()=>{let e=t.start_registration_date?new Date(t.start_registration_date):null,s=new Date(t.start_date);if(isNaN(s.getTime())){console.error("Invalid start_date for exam ".concat(t.id,": ").concat(t.start_date)),r("expired"),l({days:0,hours:0,minutes:0,seconds:0}),o({minutes:0,seconds:0,isLate:!0});return}let a=(0,C.L_)(new Date,"Asia/Kolkata"),n=(0,C.L_)(s,"Asia/Kolkata"),i=n.getTime(),c=(function(e,t,s){let a=(0,A.a)(e,void 0);return a.setTime(a.getTime()+t*_.Cg),a})(n,t.duration).getTime(),d=a.getTime();if(e&&!isNaN(e.getTime())&&d<e.getTime()){let t=(0,S.O)(e,a),s=Math.floor(t/86400),n=Math.floor(t%86400/3600),i=Math.floor(t%3600/60);r("registration"),l({days:s,hours:n,minutes:i,seconds:t%60}),o({minutes:10,seconds:0,isLate:!1})}else if(d<i){let e=(0,S.O)(n,a),t=Math.floor(e/86400),s=Math.floor(e%86400/3600),i=Math.floor(e%3600/60);r("application"),l({days:t,hours:s,minutes:i,seconds:e%60}),o({minutes:10,seconds:0,isLate:!1})}else if(d>=i&&d<=c){let e=(0,S.O)(new Date(c),a),t=Math.floor(e/60);r("exam"),l({days:0,hours:0,minutes:0,seconds:0}),o({minutes:t,seconds:e%60,isLate:!1})}else r("late"),l({days:0,hours:0,minutes:0,seconds:0}),o({minutes:0,seconds:0,isLate:!0})};e();let s=setInterval(e,1e3);return()=>clearInterval(s)},[t.start_date,t.start_registration_date,t.id]),(0,a.jsxs)("div",{className:"flex items-center justify-center w-full gap-2 border-2 px-4 bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300",children:[(0,a.jsx)(O.MzU,{className:"text-2xl text-customOrange animate-pulse"}),(0,a.jsx)("span",{className:"font-semibold text-customOrange text-sm",children:"registration"===s?(0,a.jsxs)("span",{children:["Registration Starts in: ",n.days,"d ",n.hours,"h ",n.minutes,"m"," ",n.seconds,"s"]}):"application"===s?(0,a.jsxs)("span",{children:["Exam Starts in: ",n.days,"d ",n.hours,"h ",n.minutes,"m"," ",n.seconds,"s"]}):"exam"===s?(0,a.jsxs)("span",{children:["You May Starts In: ",i.minutes,"m ",i.seconds,"s"]}):"late"===s?(0,a.jsx)("span",{children:"You Are Late"}):(0,a.jsx)("span",{children:"Expired"})})]})});var P=s(39414),z=s(66766),M=s(35695),L=s(22429);function D(e){let{open:t,onClose:s}=e,r=(0,m.useRef)(null);return(0,m.useEffect)(()=>{let e=r.current;e&&(t&&!e.open?e.showModal():!t&&e.open&&e.close())},[t]),(0,a.jsx)("dialog",{ref:r,className:"w-[600px] h-[600px] p-0 border-none shadow-xl rounded-lg backdrop:bg-black/50 fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",onClose:s,children:(0,a.jsxs)("div",{className:"relative w-full h-full",children:[(0,a.jsx)("button",{className:"absolute top-3 right-3 text-gray-600 hover:text-red-500 text-2xl z-10",onClick:s,children:"\xd7"}),(0,a.jsx)(z.default,{src:"/MathsMarvelWinner.png",alt:"Uwhiz Winner",width:400,height:250,className:"w-full h-full object-cover rounded-lg"})]})})}let q=()=>{let[e,t]=(0,m.useState)([]),[s,O]=(0,m.useState)(!1),[_,A]=(0,m.useState)(!1),[S,C]=(0,m.useState)(null),[q,T]=(0,m.useState)(1),[F,R]=(0,m.useState)(1),[$]=(0,m.useState)(9),[U,I]=(0,m.useState)(!1),[J,K]=(0,m.useState)(null),[V,W]=(0,m.useState)(null),[Y,B]=(0,m.useState)(!1),[Z,Q]=(0,m.useState)(0),[X,G]=(0,m.useState)(!1),[H,ee]=(0,m.useState)(!1),[et,es]=(0,m.useState)(null),ea=()=>{try{let e=localStorage.getItem("student_data");return e?JSON.parse(e).id:""}catch(e){return""}},er=()=>{ee(!0)},en=()=>{ee(!1)},el=(0,m.useMemo)(()=>e,[e]),ei=(0,M.useRouter)(),eo=ea(),ec=el.filter(e=>{let t=new Date,s=new Date(e.start_date),a=6e4*e.duration;return new Date(s.getTime()+a).getTime()>t.getTime()}),ed=el.filter(e=>{let t=new Date,s=new Date(e.start_date),a=6e4*e.duration;return new Date(s.getTime()+a).getTime()<t.getTime()});(0,m.useEffect)(()=>{(async()=>{if(eo)try{let e=await (0,L.J)();e.success&&W(e.data)}catch(e){console.error("Error fetching discount info:",e)}})()},[eo]),(0,m.useEffect)(()=>{(async()=>{I(!0);try{let e=await (0,o.Dl)(q,$,eo),s=e.exams;s=eo?await Promise.all(s.map(async e=>{try{let t=await (0,g.o)(eo,e.id);return{...e,hasAttempted:!1!==t.success&&t}}catch(t){return console.error("Error checking attempt for exam ".concat(e.id,":"),t),{...e,hasAttempted:!1}}})):s.map(e=>({...e,hasAttempted:!1})),t(s),R(e.totalPages||1)}catch(e){console.error("Error fetching exams:",e),h.toast.error(e.message||"Failed to load exams")}finally{I(!1)}})()},[q,$,eo]);let ex=e=>{C(e),K(null),A(!0)},em=async()=>{try{return(await c.S.get("/coins/get-total-coins/student")).data.coins}catch(e){h.toast.error("Failed to load coin data. Please try again."),console.error("Error fetching data",e)}},eu=async()=>{if(!S)return;let e=ea();if(!e){h.toast.error("Please log in as a student to apply for an exam");return}let s=await em();try{let s=await (0,x.n)(S.id,e);if(s.application){try{let e=localStorage.getItem("student_data");if(e){let t=JSON.parse(e).email;t&&await d(S.id,S.exam_name,t)}}catch(e){console.log("Email sending failed",e)}t(e=>e.map(e=>e.id===S.id?{...e,joinedClassesCount:e.joinedClassesCount+1,totalApplicants:(e.totalApplicants||0)+1,hasApplied:!0}:e)),A(!1),O(!0),h.toast.success(s.message||"Successfully applied for the exam"),K(null)}}catch(t){var a,r,n;let e=(null==t?void 0:null===(r=t.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.error)||t.message||"Error applying for exam";if(h.toast.error(e),e.includes("Uwhiz Super Kids Exam")){es(e),A(!1);return}if(e.includes("Required Coin for Applying in Exam")){K(e);let t=null!==(n=Number(S.coins_required))&&void 0!==n?n:0;(null==V?void 0:V.hasDiscount)&&(t*=1-V.discountPercentage/100),Q(Math.floor(Math.floor(t)-s))}else A(!1)}finally{B(!1)}},eh=()=>{O(!1),A(!1),C(null),K(null)},eg=e=>{window.location.href="/uwhiz-info/".concat(e)},ep=(e,t)=>0===t?0:Math.min(100,Math.max(0,e/t*100)),ef=async()=>{B(!0);try{let{order:e}=(await c.S.post("/coins/create-order",{amount:100*Z})).data,t={key:"rzp_test_Opr6M8CKpK12pF",amount:e.amount,currency:"INR",name:"Uest Coins",description:"Add Uest Coins",order_id:e.id,handler:async function(e){try{G(!0),await c.S.post("/coins/verify",{razorpay_order_id:e.razorpay_order_id,razorpay_payment_id:e.razorpay_payment_id,razorpay_signature:e.razorpay_signature,amount:100*Z}),h.toast.success("Coins added successfully!"),eu(),G(!1)}catch(e){h.toast.error("Payment verification failed")}finally{G(!1)}},theme:{color:"#f97316"}};new window.Razorpay(t).open()}catch(e){h.toast.error("Payment initialization failed")}finally{B(!1)}};return(0,m.useEffect)(()=>{let e=document.createElement("script");e.src="https://checkout.razorpay.com/v1/checkout.js",e.async=!0,document.body.appendChild(e)},[]),(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("div",{className:"flex justify-center bg-black pb-10",children:(0,a.jsx)(z.default,{height:400,width:400,src:n.src,alt:"Exam Logo",priority:!0,quality:100})}),s&&S&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-xl max-w-md w-full",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-green-600 mb-4",children:"Application Successful!"}),(0,a.jsxs)("p",{className:"text-gray-700 mb-6",children:["You have successfully applied for"," ",(0,a.jsx)("strong",{children:S.exam_name}),"."]}),(0,a.jsx)(i.$,{onClick:eh,className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg",children:"Close"})]})}),_&&S&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100",children:[(0,a.jsx)("h2",{className:"text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2",children:"Are You Sure?"}),(0,a.jsxs)("p",{className:"text-gray-700 text-lg mb-6 leading-relaxed",children:["Do you want to apply for"," ",(0,a.jsx)("strong",{className:"text-customOrange",children:S.exam_name}),"?",null!=S.coins_required&&(0,a.jsxs)("span",{children:[" ","This will cost"," ",(null==V?void 0:V.hasDiscount)?(0,a.jsxs)("span",{children:[(0,a.jsx)("span",{className:"line-through text-gray-500",children:S.coins_required})," ",(0,a.jsx)("strong",{className:"text-green-600",children:(0,L.w)(S.coins_required,V.discountPercentage)})," ",(0,a.jsxs)("span",{className:"text-green-600 text-sm",children:["(",V.discountPercentage,"% discount applied)"]})]}):(0,a.jsx)("strong",{className:"text-customOrange",children:S.coins_required})," ","coins."]})]}),J&&(0,a.jsxs)("div",{className:"flex-col justify-center items-start gap-2 bg-red-50 p-4 rounded-lg mb-6 border border-red-200",children:[(0,a.jsxs)("div",{className:"flex gap-5 items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-red-600 mt-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"text-red-600 text-sm font-medium",children:J})]}),(0,a.jsx)(i.$,{onClick:()=>ef(),className:"mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:X,children:X?(0,a.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(p.A,{className:"animate-spin w-5 h-5"}),"Processing..."]}):"Add Coins"})]}),ea()?(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(i.$,{onClick:eu,className:"w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",disabled:!!J||Y,children:Y?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Yes, Apply"}),(0,a.jsx)(i.$,{onClick:eh,className:"w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg",children:"Cancel"})]}):(0,a.jsx)(i.$,{onClick:()=>ei.push("/student/login?redirect=/uwhiz"),className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg",children:"Login to Apply"})]})}),et&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-red-600 dark:text-red-400 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300 mb-6",children:et}),(0,a.jsx)("button",{onClick:()=>es(null),className:"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg",children:"Close"})]})}),U?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"Loading exams..."}):(0,a.jsxs)(a.Fragment,{children:[ec.length>0&&(0,a.jsxs)(a.Fragment,{children:[ec.length>0&&(0,a.jsx)("div",{className:"flex justify-center items-center mt-10",children:(0,a.jsxs)("h1",{className:"text-3xl font-bold ml-4",children:["Upcoming ",(0,a.jsx)("span",{className:"text-customOrange",children:"Exams "})]})}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center items-start gap-6 p-4 sm:p-6 md:p-10 lg:p-20",children:ec&&ec.map(e=>{var t,s,r,n;let o=null!==(s=e.total_student_intake)&&void 0!==s?s:0,c=null!==(r=null===(t=e.UwhizPriceRank.find(e=>1===e.rank))||void 0===t?void 0:t.price)&&void 0!==r?r:0;return(0,a.jsxs)(l.Zp,{className:"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center px-3 py-2 space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105",children:e.exam_name}),4!==e.id&&6!==e.id&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xl font-bold",children:[(0,a.jsx)(N.qjb,{className:"text-xl text-customOrange"}),(0,a.jsxs)("span",{className:"dark:text-white",children:["1st Prize: ",(0,a.jsx)("span",{className:"text-customOrange",children:c})]})]})]}),(0,a.jsx)(E,{exam:e}),(0,a.jsxs)("div",{className:"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.lfF,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Total Questions: ",e.total_questions]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.O6N,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Marks: ",e.marks]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.w_X,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Duration: ",e.duration]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(f.A,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Coins: ",(null==V?void 0:V.hasDiscount)?(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"line-through text-gray-500 text-xs",children:e.coins_required}),(0,a.jsx)("span",{className:"text-green-600 font-bold",children:(0,L.w)(e.coins_required,V.discountPercentage)}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["(",V.discountPercentage,"% off)"]})]}):0===Number(e.coins_required)?"Free":e.coins_required]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Student Joined"}),(0,a.jsx)(k.k,{value:ep(null!==(n=e.totalApplicants)&&void 0!==n?n:0,o),className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Limited Seats Available"})})]}),(0,a.jsxs)("div",{className:"flex justify-center px-3",children:[1===e.id&&(0,a.jsx)(i.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>eg(String(e.id)),children:"View details"}),(0,a.jsx)(P.A,{exam:e,hasApplied:e.hasApplied,isMaxLimitReached:e.isMaxLimitReached,hasAttempted:e.hasAttempted,onApplyClick:()=>ex(e)})]}),7===e.id&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-customOrange text-white font-semibold text-sm text-center p-3 flex items-center justify-center gap-2  border-orange-600 shadow-md ",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M12 20.5a8.5 8.5 0 100-17 8.5 8.5 0 000 17z"})}),(0,a.jsx)("span",{children:"This exam is only for those who have participated in the Uwhiz Super Kids Exam."})]})}),1===e.id?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-300 pt-2 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("span",{children:"Sponsored by"}),(0,a.jsx)(z.default,{src:"/nalanda.png",alt:"Nalanda Logo",height:60,width:60,className:"object-contain h-5 w-5"}),(0,a.jsx)("span",{className:"font-semibold",children:"Nalanda Vidhyalay"})]}):null]},e.id)})})]}),ed.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("h1",{className:"text-center text-4xl font-bold dark:text-white mt-10",children:["Past ",(0,a.jsx)("span",{className:"text-customOrange",children:"Exams"})]}),U?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"Loading exams..."}):0===ed.length?(0,a.jsx)("p",{className:"text-center text-white mt-6",children:"No past exams found."}):(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-6 p-4 sm:p-6 md:p-10 lg:p-20",children:ed.map(e=>{var t,s;let r=null!==(s=null===(t=e.UwhizPriceRank.find(e=>1===e.rank))||void 0===t?void 0:t.price)&&void 0!==s?s:0;return(0,a.jsxs)(l.Zp,{className:"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center px-3 py-2 space-y-2 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105",children:e.exam_name}),4!==e.id&&6!==e.id&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xl font-bold",children:[(0,a.jsx)(N.qjb,{className:"text-xl text-customOrange"}),(0,a.jsxs)("span",{className:"dark:text-white",children:["1st Prize: ",(0,a.jsx)("span",{className:"text-customOrange",children:r})]})]})]}),(0,a.jsx)(E,{exam:e}),(0,a.jsxs)("div",{className:"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.lfF,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Total Questions: ",e.total_questions]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.O6N,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Marks: ",e.marks]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(u.w_X,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Duration: ",e.duration]})]}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(f.A,{className:"text-lg text-customOrange"}),(0,a.jsxs)("span",{children:["Coins: ",null!=e.coins_required?(null==V?void 0:V.hasDiscount)?(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"line-through text-gray-500 text-xs",children:e.coins_required}),(0,a.jsx)("span",{className:"text-green-600 font-bold",children:(0,L.w)(e.coins_required,V.discountPercentage)}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["(",V.discountPercentage,"% off)"]})]}):e.coins_required:"Free"]})]})]})]}),5===e.id?(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Classes Joined"}),(0,a.jsx)(k.k,{value:100,className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Seats Full"})})]}):(0,a.jsxs)("div",{className:"flex flex-col px-3",children:[(0,a.jsx)("p",{className:"dark:text-white mb-1 tracking-wider",children:"Student Joined"}),(0,a.jsx)(k.k,{value:100,className:"[&>*]:bg-customOrange bg-slate-300"}),(0,a.jsx)("p",{className:"flex justify-end text-orange-500 text-sm dark:text-white",children:(0,a.jsx)("span",{children:"Seats Full"})})]}),(0,a.jsxs)("div",{className:"flex justify-center px-3",children:[1===e.id&&(0,a.jsxs)("div",{className:"flex gap-2 w-full justify-center",children:[(0,a.jsx)(i.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>eg(String(e.id)),children:"View details"}),(0,a.jsx)(i.$,{className:"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl",onClick:()=>ei.push("uwhiz-super-kids-result"),children:"View Result"})]}),3===e.id&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.$,{className:"bg-customOrange mx-5",onClick:er,children:"View Result"}),(0,a.jsx)(D,{open:H,onClose:en})]}),5===e.id?(0,a.jsx)(i.$,{className:"bg-customOrange mx-5",onClick:()=>ei.push("/uwhiz-details/".concat(5)),children:"View Result"}):3!==e.id&&1!==e.id?(0,a.jsx)(P.A,{exam:e,hasApplied:e.hasApplied,isMaxLimitReached:e.isMaxLimitReached,hasAttempted:e.hasAttempted,onApplyClick:()=>ex(e)}):null]})]},e.id)})})]}),(0,a.jsx)("div",{className:"flex items-center justify-center px-4 py-6 dark:text-white",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>T(1),disabled:1===q,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(j.A,{})}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>T(e=>Math.max(e-1,1)),disabled:1===q,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(b.A,{})}),(0,a.jsxs)("span",{className:"text-sm",children:["Page ",q," of ",F]}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>T(e=>Math.min(e+1,F)),disabled:q===F,className:"hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(v.A,{})}),(0,a.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>T(F),disabled:q===F,className:"bg-white dark:text-white hover:bg-gray-200 cursor-pointer",children:(0,a.jsx)(w.A,{})})]})})]}),(0,a.jsx)(y.default,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6446,7672,9204,7040,5186,4540,1990,4212,6046,4945,4632,5513,7605,5969,4955,347,8903,8441,1684,7358],()=>t(40318)),_N_E=e.O()}]);