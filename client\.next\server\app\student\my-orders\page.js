(()=>{var e={};e.id=7721,e.ids=[7721],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15166:(e,t,r)=>{Promise.resolve().then(r.bind(r,41249))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24003:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=r(65239),n=r(48088),i=r(88170),o=r.n(i),a=r(30893),p={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>a[e]);r.d(t,p);let u={children:["",{children:["student",{children:["my-orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80575)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\my-orders\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,39777)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\my-orders\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/student/my-orders/page",pathname:"/student/my-orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687),n=r(90269),i=r(46303),o=r(31983);function a(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.default,{}),(0,s.jsx)(o.A,{userType:"STUDENT",loginPath:"/student/login"}),(0,s.jsx)(i.default,{})]})}},51958:(e,t,r)=>{Promise.resolve().then(r.bind(r,80575))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80575:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\UEST\\\\uest_app\\\\uest-app\\\\client\\\\src\\\\app\\\\student\\\\my-orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\my-orders\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(60687),n=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,n.cn)("bg-accent animate-pulse rounded-md",e),...t})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7013,2800,1983],()=>r(24003));module.exports=s})();